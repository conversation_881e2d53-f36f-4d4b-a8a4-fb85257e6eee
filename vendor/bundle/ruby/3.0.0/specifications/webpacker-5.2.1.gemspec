# -*- encoding: utf-8 -*-
# stub: webpacker 5.2.1 ruby lib

Gem::Specification.new do |s|
  s.name = "webpacker".freeze
  s.version = "5.2.1"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/rails/webpacker/blob/v5.2.1/CHANGELOG.md", "source_code_uri" => "https://github.com/rails/webpacker/tree/v5.2.1" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze, "Gaurav Tiwari".freeze]
  s.date = "2020-08-17"
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://github.com/rails/webpacker".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.4.0".freeze)
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Use webpack to manage app-like JavaScript modules in Rails".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4
  end

  if s.respond_to? :add_runtime_dependency then
    s.add_runtime_dependency(%q<activesupport>.freeze, [">= 5.2"])
    s.add_runtime_dependency(%q<railties>.freeze, [">= 5.2"])
    s.add_runtime_dependency(%q<rack-proxy>.freeze, [">= 0.6.1"])
    s.add_runtime_dependency(%q<semantic_range>.freeze, [">= 2.3.0"])
    s.add_development_dependency(%q<bundler>.freeze, [">= 1.3.0"])
    s.add_development_dependency(%q<rubocop>.freeze, ["< 0.69"])
    s.add_development_dependency(%q<rubocop-performance>.freeze, [">= 0"])
  else
    s.add_dependency(%q<activesupport>.freeze, [">= 5.2"])
    s.add_dependency(%q<railties>.freeze, [">= 5.2"])
    s.add_dependency(%q<rack-proxy>.freeze, [">= 0.6.1"])
    s.add_dependency(%q<semantic_range>.freeze, [">= 2.3.0"])
    s.add_dependency(%q<bundler>.freeze, [">= 1.3.0"])
    s.add_dependency(%q<rubocop>.freeze, ["< 0.69"])
    s.add_dependency(%q<rubocop-performance>.freeze, [">= 0"])
  end
end

# -*- encoding: utf-8 -*-
# stub: tilt 2.0.10 ruby lib

Gem::Specification.new do |s|
  s.name = "tilt".freeze
  s.version = "2.0.10"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>ko".freeze]
  s.date = "2019-09-23"
  s.description = "Generic interface to multiple Ruby template engines".freeze
  s.email = "<EMAIL>".freeze
  s.executables = ["tilt".freeze]
  s.files = ["bin/tilt".freeze]
  s.homepage = "http://github.com/rtomayko/tilt/".freeze
  s.licenses = ["MIT".freeze]
  s.rdoc_options = ["--line-numbers".freeze, "--inline-source".freeze, "--title".freeze, "Tilt".freeze, "--main".freeze, "Tilt".freeze]
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Generic interface to multiple Ruby template engines".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

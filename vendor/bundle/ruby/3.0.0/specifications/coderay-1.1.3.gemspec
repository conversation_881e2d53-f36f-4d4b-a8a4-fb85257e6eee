# -*- encoding: utf-8 -*-
# stub: coderay 1.1.3 ruby lib

Gem::Specification.new do |s|
  s.name = "coderay".freeze
  s.version = "1.1.3"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["Kornelius Ka<PERSON>bach".freeze]
  s.date = "2020-05-30"
  s.description = "Fast and easy syntax highlighting for selected languages, written in Ruby. Comes with RedCloth integration and LOC counter.".freeze
  s.email = ["<EMAIL>".freeze]
  s.executables = ["coderay".freeze]
  s.extra_rdoc_files = ["README_INDEX.rdoc".freeze]
  s.files = ["README_INDEX.rdoc".freeze, "bin/coderay".freeze]
  s.homepage = "http://coderay.rubychan.de".freeze
  s.licenses = ["MIT".freeze]
  s.rdoc_options = ["-SNw2".freeze, "-mREADME_INDEX.rdoc".freeze, "-t CodeRay Documentation".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 1.8.6".freeze)
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Fast syntax highlighting for selected languages.".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

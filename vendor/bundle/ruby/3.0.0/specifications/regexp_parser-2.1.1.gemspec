# -*- encoding: utf-8 -*-
# stub: regexp_parser 2.1.1 ruby lib

Gem::Specification.new do |s|
  s.name = "regexp_parser".freeze
  s.version = "2.1.1"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "issue_tracker" => "https://github.com/ammar/regexp_parser/issues" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Ammar Ali".freeze]
  s.date = "2021-02-23"
  s.description = "A library for tokenizing, lexing, and parsing Ruby regular expressions.".freeze
  s.email = ["<EMAIL>".freeze]
  s.homepage = "https://github.com/ammar/regexp_parser".freeze
  s.licenses = ["MIT".freeze]
  s.rdoc_options = ["--inline-source".freeze, "--charset=UTF-8".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.0.0".freeze)
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Scanner, lexer, parser for ruby's regular expressions".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

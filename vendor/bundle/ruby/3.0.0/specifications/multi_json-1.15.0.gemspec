# -*- encoding: utf-8 -*-
# stub: multi_json 1.15.0 ruby lib

Gem::Specification.new do |s|
  s.name = "multi_json".freeze
  s.version = "1.15.0"

  s.required_rubygems_version = Gem::Requirement.new(">= 1.3.5".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/intridea/multi_json/issues", "changelog_uri" => "https://github.com/intridea/multi_json/blob/v1.15.0/CHANGELOG.md", "documentation_uri" => "https://www.rubydoc.info/gems/multi_json/1.15.0", "source_code_uri" => "https://github.com/intridea/multi_json/tree/v1.15.0", "wiki_uri" => "https://github.com/intridea/multi_json/wiki" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze, "<PERSON>".freeze, "<PERSON>".freeze, "<PERSON>".freeze]
  s.date = "2020-07-10"
  s.description = "A common interface to multiple JSON libraries, including Oj, Yajl, the JSON gem (with C-extensions), the pure-Ruby JSON gem, NSJSONSerialization, gson.rb, JrJackson, and OkJson.".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze, "<EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://github.com/intridea/multi_json".freeze
  s.licenses = ["MIT".freeze]
  s.rubygems_version = "3.2.3".freeze
  s.summary = "A common interface to multiple JSON libraries.".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4
  end

  if s.respond_to? :add_runtime_dependency then
    s.add_development_dependency(%q<rake>.freeze, ["~> 10.5"])
    s.add_development_dependency(%q<rspec>.freeze, ["~> 3.9"])
  else
    s.add_dependency(%q<rake>.freeze, ["~> 10.5"])
    s.add_dependency(%q<rspec>.freeze, ["~> 3.9"])
  end
end

# -*- encoding: utf-8 -*-
# stub: zeitwerk 2.4.2 ruby lib

Gem::Specification.new do |s|
  s.name = "zeitwerk".freeze
  s.version = "2.4.2"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/fxn/zeitwerk/issues", "changelog_uri" => "https://github.com/fxn/zeitwerk/blob/master/CHANGELOG.md", "homepage_uri" => "https://github.com/fxn/zeitwerk", "source_code_uri" => "https://github.com/fxn/zeitwerk" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Xavier Noria".freeze]
  s.date = "2020-11-27"
  s.description = "    Zeitwerk implements constant autoloading with Ruby semantics. Each gem\n    and application may have their own independent autoloader, with its own\n    configuration, inflector, and logger. Supports autoloading,\n    reloading, and eager loading.\n".freeze
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/fxn/zeitwerk".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.4.4".freeze)
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Efficient and thread-safe constant autoloader".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

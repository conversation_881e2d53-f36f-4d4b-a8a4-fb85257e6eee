# -*- encoding: utf-8 -*-
# stub: builder 3.2.4 ruby lib

Gem::Specification.new do |s|
  s.name = "builder".freeze
  s.version = "3.2.4"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2019-12-10"
  s.description = "Builder provides a number of builder objects that make creating structured data\nsimple to do.  Currently the following builder objects are supported:\n\n* XML Markup\n* XML Events\n".freeze
  s.email = "<EMAIL>".freeze
  s.extra_rdoc_files = ["CHANGES".freeze, "MIT-LICENSE".freeze, "README.md".freeze, "Rakefile".freeze, "builder.blurb".freeze, "builder.gemspec".freeze, "doc/releases/builder-1.2.4.rdoc".freeze, "doc/releases/builder-2.0.0.rdoc".freeze, "doc/releases/builder-2.1.1.rdoc".freeze]
  s.files = ["CHANGES".freeze, "MIT-LICENSE".freeze, "README.md".freeze, "Rakefile".freeze, "builder.blurb".freeze, "builder.gemspec".freeze, "doc/releases/builder-1.2.4.rdoc".freeze, "doc/releases/builder-2.0.0.rdoc".freeze, "doc/releases/builder-2.1.1.rdoc".freeze]
  s.homepage = "http://onestepback.org".freeze
  s.licenses = ["MIT".freeze]
  s.rdoc_options = ["--title".freeze, "Builder -- Easy XML Building".freeze, "--main".freeze, "README.rdoc".freeze, "--line-numbers".freeze]
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Builders for MarkUp.".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

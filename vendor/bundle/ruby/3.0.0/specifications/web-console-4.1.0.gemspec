# -*- encoding: utf-8 -*-
# stub: web-console 4.1.0 ruby lib

Gem::Specification.new do |s|
  s.name = "web-console".freeze
  s.version = "4.1.0"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON> Somerville".freeze, "Genadi Samokovarov".freeze, "<PERSON>".freeze, "Ryan Dao".freeze]
  s.date = "2020-11-05"
  s.email = ["<EMAIL>".freeze, "gsa<PERSON><PERSON><PERSON>@gmail.com".freeze, "guille<PERSON><PERSON><EMAIL>".freeze, "<EMAIL>".freeze]
  s.homepage = "https://github.com/rails/web-console".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5".freeze)
  s.rubygems_version = "3.2.3".freeze
  s.summary = "A debugging tool for your Ruby on Rails applications.".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4
  end

  if s.respond_to? :add_runtime_dependency then
    s.add_runtime_dependency(%q<railties>.freeze, [">= 6.0.0"])
    s.add_runtime_dependency(%q<activemodel>.freeze, [">= 6.0.0"])
    s.add_runtime_dependency(%q<actionview>.freeze, [">= 6.0.0"])
    s.add_runtime_dependency(%q<bindex>.freeze, [">= 0.4.0"])
  else
    s.add_dependency(%q<railties>.freeze, [">= 6.0.0"])
    s.add_dependency(%q<activemodel>.freeze, [">= 6.0.0"])
    s.add_dependency(%q<actionview>.freeze, [">= 6.0.0"])
    s.add_dependency(%q<bindex>.freeze, [">= 0.4.0"])
  end
end

# -*- encoding: utf-8 -*-
# stub: racc 1.5.2 ruby lib
# stub: ext/racc/cparse/extconf.rb

Gem::Specification.new do |s|
  s.name = "racc".freeze
  s.version = "1.5.2"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["Minero Aoki".freeze, "<PERSON>".freeze]
  s.date = "2020-12-26"
  s.description = "Racc is a LALR(1) parser generator.\n  It is written in Ruby itself, and generates Ruby program.\n\n  NOTE: Ruby 1.8.x comes with Racc runtime module.  You\n  can run your parsers generated by racc 1.4.x out of the\n  box.\n".freeze
  s.email = [nil, "<EMAIL>".freeze]
  s.executables = ["racc".freeze]
  s.extensions = ["ext/racc/cparse/extconf.rb".freeze]
  s.extra_rdoc_files = ["README.ja.rdoc".freeze, "README.rdoc".freeze, "rdoc/en/NEWS.en.rdoc".freeze, "rdoc/en/grammar.en.rdoc".freeze, "rdoc/ja/NEWS.ja.rdoc".freeze, "rdoc/ja/debug.ja.rdoc".freeze, "rdoc/ja/grammar.ja.rdoc".freeze, "rdoc/ja/parser.ja.rdoc".freeze]
  s.files = ["README.ja.rdoc".freeze, "README.rdoc".freeze, "bin/racc".freeze, "ext/racc/cparse/extconf.rb".freeze, "rdoc/en/NEWS.en.rdoc".freeze, "rdoc/en/grammar.en.rdoc".freeze, "rdoc/ja/NEWS.ja.rdoc".freeze, "rdoc/ja/debug.ja.rdoc".freeze, "rdoc/ja/grammar.ja.rdoc".freeze, "rdoc/ja/parser.ja.rdoc".freeze]
  s.homepage = "http://i.loveruby.net/en/projects/racc/".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.rdoc_options = ["--main".freeze, "README.rdoc".freeze]
  s.rubygems_version = "3.2.3".freeze
  s.summary = "Racc is a LALR(1) parser generator".freeze

  s.installed_by_version = "3.2.3" if s.respond_to? :installed_by_version
end

current directory: /home/<USER>/Documents/shop/vendor/bundle/ruby/3.0.0/gems/bootsnap-1.7.2/ext/bootsnap
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210308-29291-cs9ezp.rb extconf.rb
creating Makefile

current directory: /home/<USER>/Documents/shop/vendor/bundle/ruby/3.0.0/gems/bootsnap-1.7.2/ext/bootsnap
make "DESTDIR=" clean

current directory: /home/<USER>/Documents/shop/vendor/bundle/ruby/3.0.0/gems/bootsnap-1.7.2/ext/bootsnap
make "DESTDIR="
compiling bootsnap.c
linking shared-object bootsnap/bootsnap.so

current directory: /home/<USER>/Documents/shop/vendor/bundle/ruby/3.0.0/gems/bootsnap-1.7.2/ext/bootsnap
make "DESTDIR=" install
/usr/bin/install -c -m 0755 bootsnap.so ./.gem.20210308-29291-xrqgfu/bootsnap

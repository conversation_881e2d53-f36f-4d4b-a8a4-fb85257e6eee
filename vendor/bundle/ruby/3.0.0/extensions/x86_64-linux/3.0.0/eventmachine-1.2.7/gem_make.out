current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/eventmachine-1.2.7/ext/fastfilereader
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-8lfxza.rb extconf.rb
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/eventmachine-1.2.7/ext/fastfilereader
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/eventmachine-1.2.7/ext/fastfilereader
make "DESTDIR="
compiling mapper.cpp
compiling rubymain.cpp
linking shared-object fastfilereaderext.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/eventmachine-1.2.7/ext/fastfilereader
make "DESTDIR=" install
/usr/bin/install -c -m 0755 fastfilereaderext.so ./.gem.20210324-29918-7wo2ai

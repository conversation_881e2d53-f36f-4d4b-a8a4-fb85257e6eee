current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/debug_inspector-1.1.0/ext/debug_inspector
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-fsrbfn.rb extconf.rb
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/debug_inspector-1.1.0/ext/debug_inspector
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/debug_inspector-1.1.0/ext/debug_inspector
make "DESTDIR="
compiling debug_inspector.c
linking shared-object debug_inspector.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/debug_inspector-1.1.0/ext/debug_inspector
make "DESTDIR=" install
/usr/bin/install -c -m 0755 debug_inspector.so ./.gem.20210324-29918-exn6rq

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/sqlite3-1.4.2/ext/sqlite3
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-bft2jz.rb extconf.rb
checking for sqlite3.h... yes
checking for pthread_create() in -lpthread... yes
checking for -ldl... yes
checking for sqlite3_libversion_number() in -lsqlite3... yes
checking for rb_proc_arity()... yes
checking for rb_integer_pack()... yes
checking for sqlite3_initialize()... yes
checking for sqlite3_backup_init()... yes
checking for sqlite3_column_database_name()... yes
checking for sqlite3_enable_load_extension()... yes
checking for sqlite3_load_extension()... yes
checking for sqlite3_open_v2()... yes
checking for sqlite3_prepare_v2()... yes
checking for sqlite3_int64 in sqlite3.h... yes
checking for sqlite3_uint64 in sqlite3.h... yes
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/sqlite3-1.4.2/ext/sqlite3
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/sqlite3-1.4.2/ext/sqlite3
make "DESTDIR="
compiling aggregator.c
compiling backup.c
compiling database.c
database.c: In function ‘rb_sqlite3_open_v2’:
database.c:38:9: warning: unused variable ‘flags’ [-Wunused-variable]
   VALUE flags;
         ^~~~~
database.c: In function ‘exec_batch’:
database.c:726:57: warning: passing argument 3 of ‘sqlite3_exec’ from incompatible pointer type [-Wincompatible-pointer-types]
     status = sqlite3_exec(ctx->db, StringValuePtr(sql), hash_callback_function, callback_ary, &errMsg);
                                                         ^~~~~~~~~~~~~~~~~~~~~~
In file included from ./sqlite3_ruby.h:25:0,
                 from database.c:1:
/usr/include/sqlite3.h:403:16: note: expected ‘int (*)(void *, int,  char **, char **)’ but argument is of type ‘int (*)(VALUE,  int,  char **, char **) {aka int (*)(long unsigned int,  int,  char **, char **)}’
 SQLITE_API int sqlite3_exec(
                ^~~~~~~~~~~~
database.c:726:81: warning: passing argument 4 of ‘sqlite3_exec’ makes pointer from integer without a cast [-Wint-conversion]
     status = sqlite3_exec(ctx->db, StringValuePtr(sql), hash_callback_function, callback_ary, &errMsg);
                                                                                 ^~~~~~~~~~~~
In file included from ./sqlite3_ruby.h:25:0,
                 from database.c:1:
/usr/include/sqlite3.h:403:16: note: expected ‘void *’ but argument is of type ‘VALUE {aka long unsigned int}’
 SQLITE_API int sqlite3_exec(
                ^~~~~~~~~~~~
database.c:728:57: warning: passing argument 3 of ‘sqlite3_exec’ from incompatible pointer type [-Wincompatible-pointer-types]
     status = sqlite3_exec(ctx->db, StringValuePtr(sql), regular_callback_function, callback_ary, &errMsg);
                                                         ^~~~~~~~~~~~~~~~~~~~~~~~~
In file included from ./sqlite3_ruby.h:25:0,
                 from database.c:1:
/usr/include/sqlite3.h:403:16: note: expected ‘int (*)(void *, int,  char **, char **)’ but argument is of type ‘int (*)(VALUE,  int,  char **, char **) {aka int (*)(long unsigned int,  int,  char **, char **)}’
 SQLITE_API int sqlite3_exec(
                ^~~~~~~~~~~~
database.c:728:84: warning: passing argument 4 of ‘sqlite3_exec’ makes pointer from integer without a cast [-Wint-conversion]
     status = sqlite3_exec(ctx->db, StringValuePtr(sql), regular_callback_function, callback_ary, &errMsg);
                                                                                    ^~~~~~~~~~~~
In file included from ./sqlite3_ruby.h:25:0,
                 from database.c:1:
/usr/include/sqlite3.h:403:16: note: expected ‘void *’ but argument is of type ‘VALUE {aka long unsigned int}’
 SQLITE_API int sqlite3_exec(
                ^~~~~~~~~~~~
database.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
compiling exception.c
compiling sqlite3.c
compiling statement.c
linking shared-object sqlite3/sqlite3_native.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/sqlite3-1.4.2/ext/sqlite3
make "DESTDIR=" install
/usr/bin/install -c -m 0755 sqlite3_native.so ./.gem.20210324-29918-11tia7/sqlite3

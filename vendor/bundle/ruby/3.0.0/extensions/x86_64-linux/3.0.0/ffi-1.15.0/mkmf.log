"pkg-config --exists libffi"
| pkg-config --libs libffi
=> "-lffi\n"
"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.    -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby  -lm   -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.    -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi -lm   -lc"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

| pkg-config --cflags-only-I libffi
=> "\n"
| pkg-config --cflags-only-other libffi
=> "\n"
| pkg-config --libs-only-l libffi
=> "-lffi\n"
package configuration for libffi
incflags: 
cflags: 
ldflags: 
libs: -lffi

have_library: checking for ffi_prep_closure_loc() in -lffi... -------------------- yes

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic      -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ffi.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_prep_closure_loc; return !p; }
/* end */

--------------------

have_func: checking for ffi_prep_cif_var()... -------------------- yes

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘ffi_prep_cif_var’ undeclared (first use in this function)
 int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_prep_cif_var; return !p; }
                                                         ^~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
conftest.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_prep_cif_var; return !p; }
/* end */

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void ffi_prep_cif_var();
15: int t(void) { ffi_prep_cif_var(); return 0; }
/* end */

--------------------

have_func: checking for ffi_raw_call()... -------------------- yes

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘ffi_raw_call’ undeclared (first use in this function); did you mean ‘rb_proc_call’?
 int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_raw_call; return !p; }
                                                         ^~~~~~~~~~~~
                                                         rb_proc_call
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
conftest.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_raw_call; return !p; }
/* end */

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void ffi_raw_call();
15: int t(void) { ffi_raw_call(); return 0; }
/* end */

--------------------

have_func: checking for ffi_prep_raw_closure()... -------------------- yes

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
conftest.c: In function ‘t’:
conftest.c:14:57: error: ‘ffi_prep_raw_closure’ undeclared (first use in this function)
 int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_prep_raw_closure; return !p; }
                                                         ^~~~~~~~~~~~~~~~~~~~
conftest.c:14:57: note: each undeclared identifier is reported only once for each function it appears in
conftest.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: int t(void) { void ((*volatile p)()); p = (void ((*)()))ffi_prep_raw_closure; return !p; }
/* end */

"gcc -o conftest -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/x86_64-linux -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/backward -I/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0 -I.     -O3 -ggdb3 -Wall -Wextra -Wdeprecated-declarations -Wduplicated-cond -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wwrite-strings -Wimplicit-fallthrough=0 -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-packed-bitfield-compat -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wsuggest-attribute=format -Wsuggest-attribute=noreturn -Wunused-variable  -fPIC  conftest.c  -L. -L/usr/share/rvm/rubies/ruby-3.0.0/lib -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L. -fstack-protector-strong -rdynamic -Wl,-export-dynamic     -lffi  -lffi -Wl,-rpath,/usr/share/rvm/rubies/ruby-3.0.0/lib -L/usr/share/rvm/rubies/ruby-3.0.0/lib -lruby -lffi  -lffi -lm   -lc"
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: /*top*/
 4: extern int t(void);
 5: int main(int argc, char **argv)
 6: {
 7:   if (argc > 1000000) {
 8:     int (* volatile tp)(void)=(int (*)(void))&t;
 9:     printf("%d", (*tp)());
10:   }
11: 
12:   return !!argv[argc];
13: }
14: extern void ffi_prep_raw_closure();
15: int t(void) { ffi_prep_raw_closure(); return 0; }
/* end */

--------------------

extconf.h is:
/* begin */
1: #ifndef EXTCONF_H
2: #define EXTCONF_H
3: #define HAVE_FFI_PREP_CIF_VAR 1
4: #define HAVE_FFI_RAW_CALL 1
5: #define HAVE_FFI_PREP_RAW_CLOSURE 1
6: #define HAVE_RAW_API 1
7: #endif
/* end */


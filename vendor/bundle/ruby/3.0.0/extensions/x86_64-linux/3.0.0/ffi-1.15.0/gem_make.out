current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/ffi-1.15.0/ext/ffi_c
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-xcatgy.rb extconf.rb
checking for ffi_prep_closure_loc() in -lffi... yes
checking for ffi_prep_cif_var()... yes
checking for ffi_raw_call()... yes
checking for ffi_prep_raw_closure()... yes
creating extconf.h
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/ffi-1.15.0/ext/ffi_c
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/ffi-1.15.0/ext/ffi_c
make "DESTDIR="
compiling AbstractMemory.c
compiling ArrayType.c
compiling Buffer.c
compiling Call.c
compiling ClosurePool.c
compiling DynamicLibrary.c
compiling Function.c
compiling FunctionInfo.c
compiling LastError.c
compiling LongDouble.c
compiling MappedType.c
compiling MemoryPointer.c
compiling MethodHandle.c
compiling Platform.c
compiling Pointer.c
compiling Struct.c
compiling StructByValue.c
compiling StructLayout.c
compiling Thread.c
compiling Type.c
compiling Types.c
compiling Variadic.c
compiling ffi.c
linking shared-object ffi_c.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/ffi-1.15.0/ext/ffi_c
make "DESTDIR=" install
/usr/bin/install -c -m 0755 ffi_c.so ./.gem.20210324-29918-5ckop2

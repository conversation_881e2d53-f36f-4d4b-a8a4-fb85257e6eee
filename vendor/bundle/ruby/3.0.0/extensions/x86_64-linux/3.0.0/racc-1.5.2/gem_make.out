current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/racc-1.5.2/ext/racc/cparse
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-2eo5s0.rb extconf.rb
checking for rb_ary_subseq()... yes
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/racc-1.5.2/ext/racc/cparse
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/racc-1.5.2/ext/racc/cparse
make "DESTDIR="
compiling cparse.c
linking shared-object racc/cparse.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/racc-1.5.2/ext/racc/cparse
make "DESTDIR=" install
/usr/bin/install -c -m 0755 cparse.so ./.gem.20210324-29918-5wb3df/racc

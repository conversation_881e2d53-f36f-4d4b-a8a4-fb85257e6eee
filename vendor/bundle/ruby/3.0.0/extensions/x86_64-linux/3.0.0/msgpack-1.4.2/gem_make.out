current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/msgpack-1.4.2/ext/msgpack
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-m9qbe1.rb extconf.rb
checking for ruby/st.h... yes
checking for st.h... no
checking for rb_str_replace() in ruby.h... yes
checking for rb_intern_str() in ruby.h... yes
checking for rb_enc_interned_str() in ruby.h... yes
checking for rb_sym2str() in ruby.h... yes
checking for rb_str_intern() in ruby.h... yes
checking for rb_block_lambda() in ruby.h... yes
checking for rb_hash_dup() in ruby.h... yes
checking for rb_hash_clear() in ruby.h... yes
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/msgpack-1.4.2/ext/msgpack
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/msgpack-1.4.2/ext/msgpack
make "DESTDIR="
compiling buffer.c
compiling buffer_class.c
buffer_class.c: In function ‘read_until_eof’:
buffer_class.c:261:17: warning: passing argument 3 of ‘rb_rescue2’ from incompatible pointer type [-Wincompatible-pointer-types]
                 read_until_eof_error, (VALUE)(void*) args,
                 ^~~~~~~~~~~~~~~~~~~~
In file included from /usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/ruby.h:38:0,
                 from /usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby.h:38,
                 from compat.h:22,
                 from buffer_class.c:19:
/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/internal/iterator.h:51:7: note: expected ‘VALUE (*)(VALUE,  VALUE) {aka long unsigned int (*)(long unsigned int,  long unsigned int)}’ but argument is of type ‘VALUE (*)(VALUE) {aka long unsigned int (*)(long unsigned int)}’
 VALUE rb_rescue2(VALUE(*)(VALUE),VALUE,VALUE(*)(VALUE,VALUE),VALUE,...);
       ^~~~~~~~~~
buffer_class.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
compiling extension_value_class.c
compiling factory_class.c
compiling packer.c
In file included from packer.h:22:0,
                 from packer.c:19:
packer.c: In function ‘msgpack_packer_write_other_value’:
packer_ext_registry.h:129:9: warning: ‘lookup_class’ may be used uninitialized in this function [-Wmaybe-uninitialized]
         rb_hash_aset(pkrg->cache, lookup_class, superclass_type);
         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
packer_ext_registry.h:85:11: note: ‘lookup_class’ was declared here
     VALUE lookup_class;
           ^~~~~~~~~~~~
packer.c: At top level:
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
compiling packer_class.c
compiling packer_ext_registry.c
compiling rbinit.c
compiling rmem.c
compiling unpacker.c
compiling unpacker_class.c
unpacker_class.c: In function ‘Unpacker_each’:
unpacker_class.c:316:17: warning: passing argument 3 of ‘rb_rescue2’ from incompatible pointer type [-Wincompatible-pointer-types]
                 Unpacker_rescue_EOFError, self,
                 ^~~~~~~~~~~~~~~~~~~~~~~~
In file included from /usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/ruby.h:38:0,
                 from /usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby.h:38,
                 from compat.h:22,
                 from buffer.h:21,
                 from unpacker.h:21,
                 from unpacker_class.c:19:
/usr/share/rvm/rubies/ruby-3.0.0/include/ruby-3.0.0/ruby/internal/iterator.h:51:7: note: expected ‘VALUE (*)(VALUE,  VALUE) {aka long unsigned int (*)(long unsigned int,  long unsigned int)}’ but argument is of type ‘VALUE (*)(VALUE) {aka long unsigned int (*)(long unsigned int)}’
 VALUE rb_rescue2(VALUE(*)(VALUE),VALUE,VALUE(*)(VALUE,VALUE),VALUE,...);
       ^~~~~~~~~~
At top level:
unpacker_class.c:225:14: warning: ‘Unpacker_peek_next_type’ defined but not used [-Wunused-function]
 static VALUE Unpacker_peek_next_type(VALUE self)
              ^~~~~~~~~~~~~~~~~~~~~~~
cc1: warning: unrecognized command line option ‘-Wno-self-assign’
cc1: warning: unrecognized command line option ‘-Wno-parentheses-equality’
cc1: warning: unrecognized command line option ‘-Wno-constant-logical-operand’
cc1: warning: unrecognized command line option ‘-Wno-cast-function-type’
compiling unpacker_ext_registry.c
linking shared-object msgpack/msgpack.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/msgpack-1.4.2/ext/msgpack
make "DESTDIR=" install
/usr/bin/install -c -m 0755 msgpack.so ./.gem.20210324-29918-8vmxo1/msgpack

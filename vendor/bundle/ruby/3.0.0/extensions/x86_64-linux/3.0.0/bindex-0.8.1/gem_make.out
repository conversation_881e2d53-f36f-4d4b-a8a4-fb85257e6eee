current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/bindex-0.8.1/ext/skiptrace
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-di9mhq.rb extconf.rb
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/bindex-0.8.1/ext/skiptrace
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/bindex-0.8.1/ext/skiptrace
make "DESTDIR="
compiling cruby.c
linking shared-object skiptrace/internal/cruby.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/bindex-0.8.1/ext/skiptrace
make "DESTDIR=" install
/usr/bin/install -c -m 0755 cruby.so ./.gem.20210324-29918-s7n4oj/skiptrace/internal

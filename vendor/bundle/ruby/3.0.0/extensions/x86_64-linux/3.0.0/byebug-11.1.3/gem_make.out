current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/byebug-11.1.3/ext/byebug
/usr/share/rvm/rubies/ruby-3.0.0/bin/ruby -I /usr/share/rvm/rubies/ruby-3.0.0/lib/ruby/3.0.0 -r ./siteconf20210324-29918-zg87h7.rb extconf.rb
creating Makefile

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/byebug-11.1.3/ext/byebug
make "DESTDIR=" clean

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/byebug-11.1.3/ext/byebug
make "DESTDIR="
compiling breakpoint.c
compiling byebug.c
compiling context.c
compiling locker.c
compiling threads.c
linking shared-object byebug/byebug.so

current directory: /home/<USER>/Desktop/tet/shop/vendor/bundle/ruby/3.0.0/gems/byebug-11.1.3/ext/byebug
make "DESTDIR=" install
/usr/bin/install -c -m 0755 byebug.so ./.gem.20210324-29918-s1c9mi/byebug

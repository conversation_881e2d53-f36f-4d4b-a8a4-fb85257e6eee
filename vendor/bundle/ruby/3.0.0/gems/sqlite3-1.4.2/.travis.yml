language: ruby
cache: bundler
before_install:
  - gem update --system 2.7.7
  - gem install bundler -v 1.16.2
addons:
  apt:
    packages:
      - libgmp-dev

after_failure:
  - "find . -name mkmf.log -exec cat {} \\;"

after_success:
  - "find . -name mkmf.log -exec cat {} \\;"

env:
  - USE_MINI_PORTILE=true
  - USE_MINI_PORTILE=false
rvm:
  - 1.9.3
  - 2.0.0
  - 2.1
  - 2.2
  - 2.3
  - 2.4
  - 2.5
  - 2.6
  - 2.7
  - ruby-head
matrix:
  allow_failures:
    - env: USE_MINI_PORTILE=false

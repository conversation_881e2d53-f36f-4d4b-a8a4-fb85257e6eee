.gemtest
.travis.yml
API_CHANGES.rdoc
CHANGELOG.rdoc
ChangeLog.cvs
Gemfile
LICENSE
Manifest.txt
README.rdoc
Rakefile
appveyor.yml
ext/sqlite3/aggregator.c
ext/sqlite3/aggregator.h
ext/sqlite3/backup.c
ext/sqlite3/backup.h
ext/sqlite3/database.c
ext/sqlite3/database.h
ext/sqlite3/exception.c
ext/sqlite3/exception.h
ext/sqlite3/extconf.rb
ext/sqlite3/sqlite3.c
ext/sqlite3/sqlite3_ruby.h
ext/sqlite3/statement.c
ext/sqlite3/statement.h
faq/faq.rb
faq/faq.yml
lib/sqlite3.rb
lib/sqlite3/constants.rb
lib/sqlite3/database.rb
lib/sqlite3/errors.rb
lib/sqlite3/pragmas.rb
lib/sqlite3/resultset.rb
lib/sqlite3/statement.rb
lib/sqlite3/translator.rb
lib/sqlite3/value.rb
lib/sqlite3/version.rb
rakelib/faq.rake
rakelib/gem.rake
rakelib/native.rake
rakelib/vendor_sqlite3.rake
setup.rb
test/helper.rb
test/test_backup.rb
test/test_collation.rb
test/test_database.rb
test/test_database_flags.rb
test/test_database_readonly.rb
test/test_database_readwrite.rb
test/test_deprecated.rb
test/test_encoding.rb
test/test_integration.rb
test/test_integration_aggregate.rb
test/test_integration_open_close.rb
test/test_integration_pending.rb
test/test_integration_resultset.rb
test/test_integration_statement.rb
test/test_result_set.rb
test/test_sqlite3.rb
test/test_statement.rb
test/test_statement_execute.rb

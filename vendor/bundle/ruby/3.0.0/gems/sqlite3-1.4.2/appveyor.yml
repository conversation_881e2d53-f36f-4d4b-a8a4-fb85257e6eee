---
version: "{build}"
branches:
  only:
    - master
    - 1-3-stable
clone_depth: 10
install:
  - SET PATH=C:\Ruby%ruby_version%\bin;%PATH%
  - ruby --version
  - gem --version
  - gem install bundler --quiet --no-ri --no-rdoc
  - bundler --version
  - bundle install
build_script:
  - rake native gem
test_script:
  - rake test
artifacts:
  - path: pkg\*.gem

environment:
  matrix:
    - ruby_version: "193"
    - ruby_version: "200"
    - ruby_version: "200-x64"
    - ruby_version: "21"
    - ruby_version: "21-x64"
    - ruby_version: "22"
    - ruby_version: "22-x64"
    - ruby_version: "23"
    - ruby_version: "23-x64"
    - ruby_version: "24"
    - ruby_version: "24-x64"
    - ruby_version: "25"
    - ruby_version: "25-x64"

## Getting Involved

New contributors are always welcome, when it doubt please ask questions. We strive to be an open and welcoming community. Please be nice to one another.

### Coding

* Pick a task:
  * Offer feedback on open [pull requests](https://github.com/geemus/formatador/pulls).
  * Review open [issues](https://github.com/geemus/formatador/issues) for things to help on.
  * [Create an issue](https://github.com/geemus/formatador/issues/new) to start a discussion on additions or features.
* Fork the project, add your changes and tests to cover them in a topic branch.
* Commit your changes and rebase against `geemus/formatador` to ensure everything is up to date.
* [Submit a pull request](https://github.com/geemus/formatador/compare/).

### Non-Coding

* Offer feedback on open [issues](https://github.com/geemus/formatador/issues).
* Organize or volunteer at events.

<%= @exception.cause.class.to_s %> in <%= @request.parameters["controller"].camelize if @request.parameters["controller"] %>#<%= @request.parameters["action"] %>

Showing <%= @exception.file_name %> where line #<%= @exception.line_number %> raised:
<%= @exception.message %>
<%= @exception.sub_template_message %>
<%= render template: "rescues/_trace", format: :text %>
<%= render template: "rescues/_request_and_response", format: :text %>

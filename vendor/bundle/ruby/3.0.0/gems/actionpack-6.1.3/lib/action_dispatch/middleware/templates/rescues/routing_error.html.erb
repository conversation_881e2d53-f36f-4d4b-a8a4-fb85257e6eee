<header>
  <h1>Routing Error</h1>
</header>
<div id="container">
  <h2><%= h @exception.message %></h2>
  <% unless @exception.failures.empty? %>
    <p>
      <h2>Failure reasons:</h2>
      <ol>
      <% @exception.failures.each do |route, reason| %>
        <li><code><%= route.inspect.delete('\\') %></code> failed because <%= reason.downcase %></li>
      <% end %>
      </ol>
    </p>
  <% end %>

  <%= render "rescues/trace", traces: @traces, trace_to_show: @trace_to_show %>

  <% if @routes_inspector %>
    <h2>
      Routes
    </h2>

    <p>
      Routes match in priority from top to bottom
    </p>

    <%= @routes_inspector.format(ActionDispatch::Routing::HtmlTableFormatter.new(self)) %>
  <% end %>

  <%= render template: "rescues/_request_and_response" %>
</div>

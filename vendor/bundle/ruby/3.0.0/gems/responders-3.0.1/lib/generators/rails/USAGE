Description:
    Stubs out a scaffolded controller and its views. Different from rails
    scaffold_controller, it uses respond_with instead of respond_to blocks.
    Pass the model name, either CamelCased or under_scored. The controller
    name is retrieved as a pluralized version of the model name.

    To create a controller within a module, specify the model name as a
    path like 'parent_module/controller_name'.

    This generates a controller class in app/controllers and invokes helper,
    template engine and test framework generators.

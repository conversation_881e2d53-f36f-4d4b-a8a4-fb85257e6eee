# selenium-webdriver

This gem provides Ruby bindings for WebDriver
and has been tested to work on MRI (2.0 through 2.2),

## Install

    gem install selenium-webdriver

## Links

* http://rubygems.org/gems/selenium-webdriver
* http://seleniumhq.github.io/selenium/docs/api/rb/index.html
* https://github.com/SeleniumHQ/selenium/wiki/Ruby-Bindings
* https://github.com/SeleniumHQ/selenium/issues

## License

Copyright 2009-2018 Software Freedom Conservancy

Licensed to the Software Freedom Conservancy (SFC) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The SFC licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

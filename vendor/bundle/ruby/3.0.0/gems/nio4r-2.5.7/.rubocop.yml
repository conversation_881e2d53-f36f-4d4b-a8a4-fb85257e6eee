AllCops:
  TargetRubyVersion: 2.4
  DisplayCopNames: true

Layout/HashAlignment:
  Enabled: false

Layout/LineLength:
  Max: 128

Layout/SpaceAroundMethodCallOperator:
  Enabled: false

Layout/SpaceInsideBlockBraces:
  Enabled: false

Style/IfUnlessModifier:
  Enabled: false

Style/UnpackFirst:
  Enabled: false

#
# Lint
#

Lint/SuppressedException:
  Enabled: false

Lint/Loop:
  Enabled: false

Lint/RaiseException:
  Enabled: false

Lint/StructNewOverride:
  Enabled: false
#
# Metrics
#

Metrics/AbcSize:
  Max: 35

Metrics/BlockLength:
  Max: 128
  Enabled: false

Metrics/ClassLength:
  Max: 128

Metrics/MethodLength:
  CountComments: false
  Max: 50

Metrics/CyclomaticComplexity:
  Max: 15

Metrics/PerceivedComplexity:
  Max: 15

#
# Style
#

Style/ExponentialNotation:
  Enabled: false

Style/FormatStringToken:
  Enabled: false

Style/FrozenStringLiteralComment:
  Enabled: true

Style/GlobalVars:
  Enabled: false

Style/HashEachMethods:
  Enabled: false

Style/HashTransformKeys:
  Enabled: false

Style/HashTransformValues:
  Enabled: false

Style/NumericPredicate:
  Enabled: false

Style/RescueModifier:
  Enabled: false

Style/SafeNavigation:
  Enabled: false

Style/StringLiterals:
  EnforcedStyle: double_quotes

Style/TrivialAccessors:
  Enabled: false

# Contributing

1. Fork it ( https://github.com/gsa<PERSON><PERSON><PERSON>/skiptrace/fork )
2. Create your feature branch (`git checkout -b my-new-feature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin my-new-feature`)
5. Create a new Pull Request

## Etiquette

If you want to contribute code, which is not your own or is heavily inspired by
someone else's code, please give them a warm shoutout in the pull request (or
the commit message) and the code itself.

Of course, don't try to sneak in non MIT compatible code.

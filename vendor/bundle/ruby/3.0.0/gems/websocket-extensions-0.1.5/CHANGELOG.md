### 0.1.5 / 2020-06-02

- Remove a ReDoS vulnerability in the header parser (CVE-2020-7663)

### 0.1.4 / 2019-06-10

- Fix a deprecation warning for using the `=~` operator on `true`
- Change license from MIT to Apache 2.0

### 0.1.3 / 2017-11-11

- Accept extension names and parameters including uppercase letters

### 0.1.2 / 2015-02-19

- Make it safe to call `Extensions#close` if the handshake is not complete

### 0.1.1 / 2014-12-14

- Explicitly require `strscan` which is not loaded in a vanilla Ruby environment

### 0.1.0 / 2014-12-13

- Initial release

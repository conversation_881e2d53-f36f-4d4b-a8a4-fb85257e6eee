<h2>Edit <%= resource_name.to_s.humanize %></h2>

<%= simple_form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %>
  <%= f.error_notification %>

  <div class="form-inputs">
    <%= f.input :email, required: true, autofocus: true %>

    <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
      <p>Currently waiting confirmation for: <%= resource.unconfirmed_email %></p>
    <% end %>

    <%= f.input :password,
                hint: "leave it blank if you don't want to change it",
                required: false,
                input_html: { autocomplete: "new-password" } %>
    <%= f.input :password_confirmation,
                required: false,
                input_html: { autocomplete: "new-password" } %>
    <%= f.input :current_password,
                hint: "we need your current password to confirm your changes",
                required: true,
                input_html: { autocomplete: "current-password" } %>
  </div>

  <div class="form-actions">
    <%= f.button :submit, "Update" %>
  </div>
<% end %>

<h3>Cancel my account</h3>

<p>Unhappy? <%= link_to "Cancel my account", registration_path(resource_name), data: { confirm: "Are you sure?" }, method: :delete %></p>

<%= link_to "Back", :back %>

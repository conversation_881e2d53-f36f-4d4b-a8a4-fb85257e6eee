This directory contains the necessary files and assets to run [speedscope](https://github.com/jlfwong/speedscope). The files come from a ZIP file that can be downloaded from the releases page of the speedscope repository on GitHub. If you wish to upgrade speedscope to the latest version, run `bundle exec rake speedscope_upgrade` in the root directory of this repository.

The rake task will download the ZIP file of the latest speedscope release from Github, remove the old files from the `lib/html/speedscope` directory and extract the ZIP file into the same directory. It will also make a small change to the `index.html` file to replace a link to a Google Fonts stylesheet with a link to a local copy in the `fonts` directory. This is done to make sure speedscope doesn't load any assets from third-parties so that it can work when you're on an intranet.

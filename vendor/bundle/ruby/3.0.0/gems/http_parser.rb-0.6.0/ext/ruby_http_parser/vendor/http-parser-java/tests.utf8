name  :utf-8 path request
raw   :"GET /δ¶/δt/pope?q=1#narf HTTP/1.1\r\nHost: github.com\r\n\r\n"
type  :HTTP_REQUEST
method: HTTP_GET
status_code :0
request_path:/δ¶/δt/pope
request_url :/δ¶/δt/pope?q=1#narf
fragment    :narf
query_string:q=1
body        :""
body_size   :0
header_0 :{ "Host": "github.com"}
should_keep_alive         :1
upgrade                   :0
http_major                :1
http_minor                :1


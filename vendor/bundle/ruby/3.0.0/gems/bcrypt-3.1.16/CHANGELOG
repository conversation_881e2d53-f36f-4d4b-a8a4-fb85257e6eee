3.1.16 Sep 3 2020
  - Fix compilation on FreeBSD. [GH #234]

3.1.15 July 21 2020
  - Remove GVL optimization.  Apparently it breaks things [GH #230]

3.1.14 July 21 2020
  - Start calibration from the minimum cost supported by the algorithm [GH #206 by @sergey-alekseev]

3.1.13 May 31 2019
  - No longer include compiled binaries for Windows. See GH #173.
  - Update C and Java implementations to latest versions [GH #182 by @fonica]
  - Bump default cost to 12 [GH #181 by @bdewater]
  - Remove explicit support for Rubies 1.8 and 1.9
  - Define SKIP_GNU token when building extension (Fixes FreeBSD >= 12) [GH #189 by @adam12]

3.1.12 May 16 2018
  - Add support for Ruby 2.3, 2.4, and 2.5 in compiled Windows binaries
  - Fix compatibility with libxcrypt [GH #164 by @besser82]

3.1.11 Mar 06 2016
  - Add support for Ruby 2.2 in compiled Windows binaries

3.1.10 Jan 28 2015
  - Fix issue with dumping a BCrypt::Password instance to YAML in Ruby 2.2 [GH #107 by @mattwi<PERSON><PERSON>]

3.1.9  Oct 23 2014
  - Rebuild corrupt binaries

3.1.8  Oct 23 2014
  - Add support for Ruby 2.1 in compiled Windows binaries [GH #102]

3.1.7  Feb 24 2014
  - Rebuild corrupt Java binary version of gem [GH #90]
  - The 2.1 support for Windows binaries alleged in 3.1.3 was a lie -- documentation removed

3.1.6  Feb 21 2014
  - Dummy version of "bcrypt-ruby" needed a couple version bumps to fix some
    bugs. It felt wrong to have that at a higher version than the real gem, so
    the real gem is getting bumped to 3.1.6.

3.1.3  Feb 21 2014
  - Add support for Ruby 2.1 in compiled Windows binaries
  - Rename gem from "bcrypt-ruby" to just "bcrypt". [GH #86 by @sferik]

3.1.2  Aug 26 2013
  - Add support for Ruby 1.8 and 2.0 (in addition to 1.9) in compiled Windows binaries
  - Add support for 64-bit Windows

3.1.1  Jul 10 2013
  - Remove support for Ruby 1.8 in compiled win32 binaries

3.1.0  May 07 2013
  - Add BCrypt::Password.valid_hash?(str) to check if a string is a valid bcrypt password hash
  - BCrypt::Password cost should be set to DEFAULT_COST if nil
  - Add BCrypt::Engine.cost attribute for getting/setting a default cost externally

3.0.1  Sep 12 2011
  - create raises an exception if the cost is higher than 31. GH #27

3.0.0  Aug 24 2011
  - Bcrypt C implementation replaced with a public domain implementation.
  - License changed to MIT

2.1.2  Sep 16 2009
  - Fixed support for Solaris, OpenSolaris.

2.1.1  Aug 14 2009
  - JVM 1.4/1.5 compatibility [Hongli Lai]

2.1.0  Aug 12 2009
  - Improved code coverage, unit tests, and build chain. [Hongli Lai]
  - Ruby 1.9 compatibility fixes. [Hongli Lai]
  - JRuby support, using Damien Miller's jBCrypt. [Hongli Lai]
  - Ruby 1.9 GIL releasing for high-cost hashes. [Hongli Lai]

2.0.5  Mar 11 2009
  - Fixed Ruby 1.8.5 compatibility. [Mike Pomraning]

2.0.4  Mar 09 2009
  - Added Ruby 1.9 compatibility. [Genki Takiuchi]
  - Fixed segfaults on some different types of empty strings. [Mike Pomraning]

2.0.3  May 07 2008
 - Made exception classes descend from StandardError, not Exception [Dan42]
 - Changed BCrypt::Engine.hash to BCrypt::Engine.hash_secret to avoid Merb
   sorting issues. [Lee Pope]

2.0.2  Jun 06 2007
 - Fixed example code in the README [Winson]
 - Fixed Solaris compatibility [Jeremy LaTrasse, Twitter crew]

2.0.1  Mar 09 2007
 - Fixed load path issues
 - Fixed crashes when hashing weird values (e.g., false, etc.)

2.0.0  Mar 07 2007
 - Removed BCrypt::Password#exactly_equals -- use BCrypt::Password#eql? instead.
 - Added BCrypt::Password#is_password?.
 - Refactored out BCrypt::Internals into more useful BCrypt::Engine.
 - Added validation of secrets -- nil is not healthy.

1.0.0  Feb 27 2007
 - Initial release.

version: "{branch}-{build}"
build: off
clone_depth: 1

init:
  # Install Ruby head
  - if %RUBY_VERSION%==head (
      appveyor DownloadFile https://github.com/oneclick/rubyinstaller2/releases/download/rubyinstaller-head/rubyinstaller-head-x86.exe -FileName C:\head_x86.exe &
      C:\head_x86.exe /verysilent /dir=C:\Ruby%RUBY_VERSION%
    )
  - if %RUBY_VERSION%==head-x64 (
      appveyor DownloadFile https://github.com/oneclick/rubyinstaller2/releases/download/rubyinstaller-head/rubyinstaller-head-x64.exe -FileName C:\head_x64.exe &
      C:\head_x64.exe /verysilent /dir=C:\Ruby%RUBY_VERSION%
    )

  # Add Ruby to the path
  - set PATH=C:\Ruby%RUBY_VERSION%\bin;%PATH%

environment:
  matrix:
    - RUBY_VERSION: "head"
    - RUBY_VERSION: "head-x64"
    - RUBY_VERSION: "25"
    - RUBY_VERSION: "25-x64"
    - RUBY_VERSION: "24"
    - RUBY_VERSION: "24-x64"
    - RUBY_VERSION: "23"
    - RUBY_VERSION: "23-x64"
    - RUBY_VERSION: "22"
    - RUBY_VERSION: "22-x64"
    - RUBY_VERSION: "21"
    - RUBY_VERSION: "21-x64"
    - RUBY_VERSION: "200"
    - RUBY_VERSION: "200-x64"

install:
  - ps: "Set-Content -Value 'gem: --no-ri --no-rdoc ' -Path C:\\ProgramData\\gemrc"
  - if %RUBY_VERSION%==head     ( gem install bundler -v'< 2' )
  - if %RUBY_VERSION%==head-x64 ( gem install bundler -v'< 2' )
  - bundle install

before_build:
  - ruby -v
  - gem -v

build_script:
  - bundle exec rake compile -rdevkit

test_script:
  - bundle exec rake spec

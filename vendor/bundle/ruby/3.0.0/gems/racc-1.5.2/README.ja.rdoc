= Racc

* http://i.loveruby.net/en/projects/racc/
* http://racc.rubyforge.org/

== DESCRIPTION:

  Racc は LALR(1) パーサジェネレータです。
  yacc の Ruby 版に相当します。

  NOTE:
  Ruby 1.8.0 からは Racc のランタイムが標準添付されているので、
  Racc で生成したパーサを安心して配布できます。また Ruby 1.6 系に
  対応させたい場合は racc -E で生成してください。


== 必要環境

  *  Ruby 1.8 以降
 (*) C コンパイラと make


== インストール

  gem インストール:

    $ gem install racc

  setup.rb インストル：

  パッケージのトップディレクトリで次のように入力してください。
  ($ は通常ユーザ、# はルートのプロンプトです)

      $ ruby setup.rb config
      $ ruby setup.rb setup
     ($ su)
      # ruby setup.rb install

  これで通常のパスに Racc がインストールされます。自分の好き
  なディレクトリにインストールしたいときは、setup.rb config に
  各種オプションをつけて実行してください。オプションのリストは

      $ ruby setup.rb --help

  で見られます。


  コンパイラがない場合
  --------------------

  config を以下のようにすれば、拡張モジュールなしで
  インストールできます。

      $ ruby setup.rb config --without-ext


== テスト

  sample/ 以下にいくつか Racc の文法ファイルのサンプルが用意
  してあります。動くのも動かないのもありますが、少なくとも
  calc-ja.y は動くのでこれを処理してみましょう。Racc をインス
  トールしたあと

      $ racc -ocalc.rb calc-ja.y

  として下さい。処理は一瞬から数秒で終わるので、

      $ ruby calc.rb

  を実行してください。ちゃんと動いてますか？

  Racc の文法など詳しいことは doc.ja/ ディレクトリ以下の HTML を
  見てください。


== ライセンス

  このパッケージに付属するファイルの著作権は青木峰郎が保持します。
  ライセンスは Ruby ライセンスです。ただしユーザが書いた規則
  ファイルや、Racc がそこから生成した Ruby スクリプトはその対象
  外です。好きなライセンスで配布してください。


== バグなど

  Racc を使っていてバグらしき現象に遭遇したら、下記のアドレスまで
  メールをください。作者にはバグを修正する義務はありませんがその
  意思はあります。また、そのときはできるだけバグを再現できる文法
  ファイルを付けてください。


                                         青木峰郎(あおきみねろう)
                                              <EMAIL>
                                            http://i.loveruby.net


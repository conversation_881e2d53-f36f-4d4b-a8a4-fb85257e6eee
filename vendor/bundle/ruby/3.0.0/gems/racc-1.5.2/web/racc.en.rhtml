% require 'makefile'
% version = Makefile.get_parameter('Makefile', 'version')
<h1>Racc</h1>
<p>
$Id$
</p>

<table summary="package descriptor">
<tr><th>Version</th><td><%= version %></td></tr>
<tr><th>Type</th><td>Parser Generator</td></tr>
<tr><th>Format</th><td>Ruby script + Ruby extention</td></tr>
<tr><th>Requirement</th><td>ruby (&gt;=1.6)</td></tr>
<tr><th>License</th><td>LGPL</td></tr>
</table>
<p>
-- <a href="/archive/racc/racc-<%= version %>-all.tar.gz">Download (.tar.gz)</a>
-- <a href="/archive/racc/">Old Versions</a>
-- <a href="doc/">Online Manual</a>
--
</p>

<p>
Racc (Ruby yACC) is a LALR(1) parser generator for Ruby.
Version 1.4.x is stable release.
</p>
<p>
Parsers generated by Racc requires "Racc Runtime Module".
Ruby 1.8.x comes with this runtime.
If you want to run your parsers with ruby 1.6.x,
use "racc -E" command.  For details, see <a href="doc/">online manual</a>.
</p>

<h2>Anonymous CVS</h2>
<p>
You can true latest version of Racc via anonymous CVS.
To check out working copy, type:
</p>
<pre>
$ cvs -d :pserver:<EMAIL>:/src login
Password: (Just hit [Enter])
$ cvs -d :pserver:<EMAIL>:/src co racc
</pre>

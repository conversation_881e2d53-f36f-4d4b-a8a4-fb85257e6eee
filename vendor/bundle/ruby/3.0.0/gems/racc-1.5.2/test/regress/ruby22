#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


require 'parser'

Parser.check_for_encoding_support

module Parser
  class Ruby22 < Racc::Parser

module_eval(<<'...end ruby22.y/module_eval...', 'ruby22.y', 2374)

  def version
    22
  end

  def default_encoding
    Encoding::UTF_8
  end
...end ruby22.y/module_eval...
##### State transition tables begin ###

clist = [
'-476,-97,268,214,215,-98,-105,-476,-476,-476,-490,568,-476,-476,-476',
'610,-476,580,217,612,-288,581,214,215,-476,-491,-476,-476,-476,647,268',
'268,214,215,-104,589,-476,-476,568,-476,-476,-476,-476,-476,568,568',
'212,109,568,815,-100,121,-100,-83,-102,-99,214,215,268,218,-288,806',
'-69,574,646,-102,-97,-476,-476,-476,-476,-476,-476,-476,-476,-476,-476',
'-476,-476,-476,-476,-98,609,-476,-476,-476,611,-476,-476,715,267,-476',
'206,-96,-476,-476,263,-476,218,-476,207,-476,-105,-476,-476,-99,-476',
'-476,-476,-476,-476,-88,-476,-479,-476,-89,-96,218,267,267,-479,-479',
'-479,263,-101,-479,-479,-479,-476,-479,113,-476,-476,-476,-476,112,-476',
'-479,-476,-479,-479,-479,588,-476,-95,715,267,-101,715,-479,-479,218',
'-479,-479,-479,-479,-479,113,208,814,-91,-91,112,113,113,113,842,113',
'112,112,112,-100,112,-102,-99,-93,-100,213,-102,-99,-479,-479,-479,-479',
'-479,-479,-479,-479,-479,-479,-479,-479,-479,-479,113,257,-479,-479',
'-479,112,-479,-479,-574,-93,-479,316,-103,-479,-479,317,-479,597,-479',
'-574,-479,-90,-479,-479,444,-479,-479,-479,-479,-479,-291,-479,218,-479',
'-91,214,215,-291,-291,-291,647,260,527,-291,-291,526,-291,-479,261,-101',
'-479,-479,-479,-479,-101,-479,113,-479,-571,113,113,112,-479,-92,112',
'112,-291,-291,386,-291,-291,-291,-291,-291,-91,646,-93,-91,-575,396',
'-105,399,599,598,398,397,-91,548,597,545,544,543,747,546,443,91,92,-291',
'-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-476',
'-572,-291,-291,-291,-93,630,-476,-93,-490,-291,-94,-571,-291,91,92,-476',
'-93,-291,113,-291,515,-291,-291,112,-291,-291,-291,-291,-291,597,-291',
'-578,-291,445,-571,-491,-476,446,-578,-578,-578,599,598,-476,-578,-578',
'-291,-578,647,-291,-291,749,-94,-479,-291,-88,-578,217,833,-578,-479',
'-103,93,94,-97,-572,477,-578,-578,-479,-578,-578,-578,-578,-578,548',
'-104,545,544,543,515,546,-89,646,527,647,597,529,-572,597,486,-98,93',
'94,599,598,595,-412,-578,-578,-578,-578,-578,-578,-578,-578,-578,-578',
'-578,-578,-578,-578,-578,625,-578,-578,-578,597,631,-578,646,113,-578',
'626,-574,-578,112,-95,-578,597,-578,770,-578,-578,-578,-578,-104,-578',
'-578,-578,-578,-578,488,-578,-578,-578,597,-578,490,597,-412,599,598',
'595,599,598,600,-412,957,-578,-91,756,-578,-578,-578,-92,-412,-578,662',
'-100,-93,-578,-578,-578,-101,498,-578,-578,-578,-102,-578,-479,599,598',
'602,-412,-578,-100,-479,-578,-578,-578,-578,-578,599,598,604,771,-574',
'-68,-578,-578,-578,-578,-578,-578,-578,-578,-90,218,860,599,598,608',
'599,598,613,-99,214,215,-486,-578,548,-485,545,544,543,-486,546,501',
'-485,-578,-578,-578,-578,-578,-578,-578,-578,-578,-578,-578,-578,-578',
'-578,553,502,-578,-578,-578,-487,772,-578,-102,509,-578,556,-487,-578',
'-578,701,-578,272,-578,218,-578,-99,-578,-578,704,-578,-578,-578,-578',
'-578,553,-578,-578,-578,527,-484,79,529,113,-481,263,556,-484,112,564',
'563,-481,-578,80,557,-578,-578,-578,-578,238,-578,-291,-578,81,-482',
'210,512,-101,-291,-291,-291,-482,211,-291,-291,-291,-334,-291,440,564',
'563,209,516,-334,557,441,-483,-291,-291,-291,238,235,-334,-483,442,237',
'236,-291,-291,260,-291,-291,-291,-291,-291,548,261,545,544,543,-488',
'546,548,218,545,544,543,-488,546,548,530,545,544,543,527,546,-488,529',
'-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291,-291',
'238,701,-291,-291,-291,-489,773,-291,701,238,-291,531,-489,-291,-291',
'701,-291,704,-291,490,-291,-489,-291,-291,904,-291,-291,-291,-291,-291',
'113,-291,235,-291,574,112,237,236,238,233,234,235,214,215,388,237,236',
'-291,233,234,-291,-291,-291,-291,113,-291,113,-291,578,112,579,112,-103',
'5,69,70,71,9,57,614,235,507,63,64,237,236,617,67,508,65,66,68,30,31',
'72,73,218,506,-263,981,619,29,28,27,101,100,102,103,741,742,19,218,743',
'107,108,635,8,45,7,10,105,104,106,95,56,97,96,98,623,99,107,108,624',
'91,92,263,42,43,41,238,242,247,248,249,244,246,254,255,250,251,-281',
'231,232,517,634,252,253,-281,40,637,518,33,564,563,58,59,-281,238,60',
'442,35,235,238,241,44,237,236,238,233,234,245,243,239,20,240,837,806',
'238,89,79,82,83,576,84,86,85,87,837,806,577,218,80,88,218,256,218,-240',
'-83,575,62,666,81,93,94,290,69,70,71,9,57,218,520,584,63,64,677,682',
'683,67,583,65,66,68,30,31,72,73,685,585,689,692,693,29,28,27,101,100',
'102,103,695,697,19,699,707,708,709,620,8,45,292,10,105,104,106,95,56',
'97,96,98,711,99,107,108,574,91,92,718,42,43,41,238,242,247,248,249,244',
'246,254,255,250,251,-292,231,232,-292,736,252,253,-292,40,746,-292,294',
'750,751,58,59,-292,-264,60,-292,35,235,757,241,44,237,236,477,233,234',
'245,243,239,20,240,477,218,257,89,79,82,83,584,84,86,85,87,488,490,939',
'799,80,88,677,256,218,263,263,585,62,677,81,93,94,5,69,70,71,9,57,238',
'806,584,63,64,218,218,831,67,939,65,66,68,30,31,72,73,218,585,806,841',
'218,29,28,27,101,100,102,103,218,850,19,-265,859,861,862,635,8,45,7',
'10,105,104,106,95,56,97,96,98,692,99,107,108,865,91,92,867,42,43,41',
'238,242,247,248,249,244,246,254,255,250,251,-291,231,232,-488,869,252',
'253,-291,40,871,-488,33,-575,218,58,59,-291,873,60,-488,35,235,874,241',
'44,237,236,877,233,234,245,243,239,20,240,879,880,677,89,79,82,83,-489',
'84,86,85,87,882,-263,-489,886,80,88,888,256,891,692,893,-489,62,895',
'81,93,94,290,69,70,71,9,57,897,899,986,63,64,899,218,905,67,987,65,66',
'68,30,31,72,73,907,985,909,915,918,29,28,27,101,100,102,103,218,936',
'19,545,544,543,922,546,8,45,292,10,105,104,106,95,56,97,96,98,-266,99',
'107,108,933,91,92,940,42,43,41,238,242,247,248,249,244,246,254,255,250',
'251,-291,231,232,-281,941,252,253,-291,40,950,-281,33,-575,951,58,59',
'-291,959,60,-281,35,235,961,241,44,237,236,962,233,234,245,243,239,20',
'240,967,736,692,89,79,82,83,-292,84,86,85,87,971,973,-292,975,80,88',
'977,256,977,988,989,-292,62,899,81,93,94,290,69,70,71,9,57,899,899,994',
'63,64,959,-575,-574,67,682,65,66,68,30,31,72,73,116,117,118,119,120',
'29,28,27,101,100,102,103,959,936,19,545,544,543,1013,546,8,45,292,10',
'105,104,106,95,56,97,96,98,1014,99,107,108,1015,91,92,977,42,43,41,238',
'242,247,248,249,244,246,254,255,250,251,-291,231,232,977,977,252,253',
'-291,40,218,899,33,-575,959,58,59,-291,977,60,,35,235,,241,44,237,236',
',233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,256',
',,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72',
'73,116,117,118,119,120,29,28,27,101,100,102,103,,,19,116,117,118,119',
'120,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,,252,253,,40',
',,294,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240',
',,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70',
'71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102',
'103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,',
'252,253,,40,,,294,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245',
'243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81',
'93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29',
'28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96',
'98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244,246,254,255',
'250,251,,231,232,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237',
'236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88',
'218,256,,,,,62,,81,93,94,5,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30',
'31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,7,10,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244',
'246,254,255,250,251,,231,232,,,252,253,,40,,,33,,,58,59,,,60,,35,235',
',241,44,237,236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85',
'87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67',
',65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292',
'10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247',
'248,249,244,246,254,255,250,251,,231,232,,,252,253,,40,,,33,,,58,59',
',,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70,71,9,57,,',
',63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19',
',,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,,252,253,,40',
',,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240',
',,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70',
'71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102',
'103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,',
'252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243',
'239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93',
'94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28',
'27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,238,242,247,248,249,244,246,254,255,250',
'251,,231,232,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236',
',233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,256',
',,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72',
'73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244,246',
'254,255,250,251,,231,232,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241',
'44,237,236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,',
',,,80,88,,256,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10',
'105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247',
'248,249,244,246,254,255,250,251,,231,232,,,252,253,,40,,,33,,,58,59',
',,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70,71,9,57,,',
',63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19',
',,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,,252,253,,40',
',,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240',
',,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93,94,290,69,70',
'71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102',
'103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,238,242,247,248,249,244,246,254,255,250,251,,231,232,,',
'252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243',
'239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,256,,,,,62,,81,93',
'94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28',
'27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,238,242,247,248,249,244,246,254,255,250',
'251,,-597,-597,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236',
',233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,',
',,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72',
'73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244,246',
'254,255,250,251,,-597,-597,,,252,253,,40,,,33,,,58,59,,,60,,35,235,',
'241,44,237,236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85',
'87,,,,,80,88,,,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,548,19,545,544,543',
',546,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,238,-597,-597,-597,-597,244,246,,701,-597,-597,,,,,,252,253,,40,',
',33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243,239,20,240',
',,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,290,69,70,71',
'9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102',
'103,,548,19,545,544,543,,546,8,45,292,10,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,238,,,548,,545,544,543,701,546,,,,,,,252',
'253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,701,,239',
'20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,290',
'69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101',
'100,102,103,,548,19,545,544,543,,546,8,45,292,10,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,238,,,,,,,,701,,,,,,,,252,253,,40',
',,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,,,239,20,240,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,290,69,70,71,9,57',
',,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,',
',19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,',
'42,43,41,238,,,,,,,,,,,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241',
'44,237,236,,233,234,,,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,,,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30',
'31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,,,,,,,,,,,,,,,,252',
'253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,,,239,20',
'240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,290,69',
'70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100',
'102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,238,-597,-597,-597,-597,244,246,,,-597,-597,,,,,,252',
'253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245,243,239',
'20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,290',
'69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101',
'100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,238,-597,-597,-597,-597,244,246,,,-597,-597,,,',
',,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,245',
'243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93',
'94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28',
'27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,238,-597,-597,-597,-597,244,246,,,-597',
'-597,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233',
'234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62',
',81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,',
',,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,238,-597,-597,-597,-597,244,246,',
',-597,-597,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237,236',
',233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,',
',,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72',
'73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,238,-597,-597,-597,-597,244',
'246,,,-597,-597,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237',
'236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88',
',,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31',
'72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244',
'246,,,250,251,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44,237',
'236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,,80,88',
',,,,,,62,,81,93,94,290,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31',
'72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,242,247,248,249,244',
'246,254,,250,251,,,,,,252,253,,40,,,33,,,58,59,,,60,,35,235,,241,44',
'237,236,,233,234,245,243,239,20,240,,,,89,79,82,83,,84,86,85,87,,,,',
'80,88,,,,,,,62,,81,93,94,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31',
'72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,7,10,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,,,,,,,,,,,,,,,,252,253',
',40,,,33,,,58,59,,,60,,35,235,,241,44,237,236,,233,234,,,,20,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,238,,,,,,',
',,,,,,,,,252,253,,223,,,229,,,58,59,,,60,,,235,,241,44,237,236,,233',
'234,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57',
'81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102',
'103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92',
',42,43,41,238,,,,,,,,,,,,,,,,252,253,,223,,,229,,,58,59,,,60,,281,235',
'279,,44,237,236,285,233,234,,,,228,,,,,89,282,82,83,,84,86,85,87,,,',
',80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73',
',,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97',
'96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,',
',58,59,,,60,,281,,279,,44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85',
'87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31',
'72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95',
'56,97,96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,',
',229,,,58,59,,,60,,281,,279,,44,,,285,,,,,,228,,,,,89,282,82,83,,84',
'86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68',
'309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,',
'303,,,299,,,58,59,,,60,,298,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87',
',,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,303,,,229,',
',58,59,,,60,,548,,545,544,543,553,546,,,,,,,,,,556,,89,79,82,83,,84',
'86,85,87,,,,,80,88,,,,315,,551,62,,81,93,94,69,70,71,,57,564,563,,63',
'64,557,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103',
',,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,',
',,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,',
',,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79',
'82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67',
',65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,',
',45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,,,44,,,285,,,,,,228,,,',
',89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41',
',,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,285,,,,,,228,,,',
',89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,',
',45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,',
',,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86',
'85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30',
'31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,113,,,,,112,62,,81,93,94,69,70,71,,57,,,,63,64,,,,67,,65,66,68,309',
'310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,348',
',,33,,,58,59,,,60,,35,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,',
'80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73',
',,,,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,353,56',
'97,96,354,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,360,,,355,,,229',
',,58,59,,,60,,,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,',
',,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,353,56,97,96,354',
',99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,355,,,229,,,58,59,,,60',
',548,,545,544,543,553,546,,,,,,,,,,556,,89,79,82,83,,84,86,85,87,,,',
',80,88,,,,,,551,62,,81,93,94,69,70,71,9,57,564,563,,63,64,557,,,67,',
'65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,8,45,7',
'10,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,',
',,,,,,,,,,40,,,33,,,58,59,,,60,,35,,,,44,,,,,,,,,20,,,,,89,79,82,83',
',84,86,85,87,,,,,80,88,,,,,,388,62,,81,93,94,69,70,71,,57,,,,63,64,',
',,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,',
'45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,',
',,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83',
',84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66',
'68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223',
',,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,',
',,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73',
',,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96',
'98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59',
',,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27',
'101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94',
'69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101',
'100,102,103,,,19,,,,,,8,45,,10,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,40,,,33,,,58,59,,,60,,35,,,,44,',
',,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81',
'93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103',
',,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,404,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,',
',,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,',
',,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79',
'82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67',
',65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,',
',105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,,',
',,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279,,44,,,285,,,,,,228,,,,,89',
'282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,',
',67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,',
'45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,',
',,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,404,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84',
'86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68',
'30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,',
',229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,',
',,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73',
',,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96',
'98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59',
',,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27',
'101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81',
'93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103',
',,19,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,',
'89,79,82,83,,84,86,85,87,,,,,80,88,218,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,',
',,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,',
',,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79',
'82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67',
',65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,',
',45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,',
',,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,',
',,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83',
',84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66',
'68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86',
'85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309',
'310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223',
',,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87',
',,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,',
',,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58',
'59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,',
',,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,',
'60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,',
'44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62',
'57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101',
'100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57',
'81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,',
',,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102',
'103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,',
'42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,',
',,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,',
',,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79',
'82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67',
',65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,',
',45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,',
',,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,',
',,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83',
',84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66',
'68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86',
'85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309',
'310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223',
',,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87',
',,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,',
',,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58',
'59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,',
',,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,',
'60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,',
'91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101',
'100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101',
'100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,218,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,',
'44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62',
'57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101',
'100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57',
'81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,',
',,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93,94,69',
'70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100',
'102,103,,,19,,,,,,8,45,,10,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,,,,,,,,,,,,,,,,,,,,40,,,33,,,58,59,,,60,,35,,,,44,,,,,',
',,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102',
'103,,,230,,,,,,,307,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92',
',,,313,,,,,,,,,,,,,,,,,,,,303,,,229,,,58,59,,,60,,548,,545,544,543,553',
'546,,,,,,,,,,556,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,504,,551,62',
',81,93,94,69,70,71,,57,564,563,,63,64,557,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,303,,,299,',
',58,59,,,60,,,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,',
',69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,520,,58,59',
',,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27',
'101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81',
'93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103',
',,19,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,',
'89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,',
',45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,',
',,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86',
'85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30',
'31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,',
',,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58',
'59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,',
',,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29',
'28,27,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284',
'99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60',
',281,,279,,44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88',
',,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,',
'305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96',
'98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59',
',,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,',
'44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62',
'57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101',
'100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,',
',,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57',
'81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,',
'91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,659,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,',
'44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,,62,,81,93',
'94,69,70,71,9,57,,,,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27',
'101,100,102,103,,,19,,,,,,8,45,292,10,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,40,,,33,,,58,59,,,60,,35',
',,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,,,388,62',
',81,93,94,69,70,71,,57,,,,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,303,,,299,,,58,59,,,60',
',,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62',
'57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,',
'91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279',
',44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,307,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,303,,,299,,,58,59,,,60,,,,,,,,',
',,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102',
'103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,',
'42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,',
',45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,',
',,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,',
',,,,,,,,,,,,223,,,229,,,58,59,,,60,,659,,,,44,,,285,,,,,,228,,,,,89',
'282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,',
',67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,',
',,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41',
',,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,285,,,,,,228,,,',
',89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,',
',,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,',
',,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,',
',,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279,,44,,,285,,,,,,228,,,,',
'89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,',
',,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41,,',
',,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279,,44,,,285,,,,,,228',
',,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,754,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41',
',,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,659,,279,,44,,,285,,,,,,228',
',,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42',
'43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,279,,44,,,285,,,',
',,228,,,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,',
',230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,',
',,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,',
',,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105',
'104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,',
',,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86',
'85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30',
'31,72,73,,,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,',
',229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,',
',,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73',
',,,,,29,28,27,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58',
'59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,',
',,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305',
'306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98',
',99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,',
'60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,',
'44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62',
'57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101',
'100,102,103,,,230,,,,,,,307,,,105,104,106,95,56,97,96,98,,99,107,108',
',91,92,,,,313,,,,,,,,,,,,,,,,,,,,303,,,299,,,58,59,,,60,,,,,,,,,,,,',
',,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94',
'63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103',
',,230,,,,,,,307,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,,,313',
',,,,,,,,,,,,,,,,,,,303,,,299,,,58,59,,,60,,,,,,,,,,,,,,,,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,',
',,,,,,,,,223,,,229,,,58,59,,,60,,404,,,,44,,,,,,,,,228,,,,,89,79,82',
'83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65',
'66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45',
',,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,',
',,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83',
',84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66',
'68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104',
'106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223',
',,229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,',
',,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73',
',,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106,95,56,97,96',
'98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59',
',,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69',
'70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306',
'312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99',
'107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,',
',,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71',
'62,57,81,93,94,63,64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100',
'102,103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91',
'92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,',
',,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102',
'103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,',
'42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,',
'230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43',
'41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,',
',89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64',
',,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230',
',,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,',
',,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89',
'79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,',
'67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,',
',,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,',
',,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79',
'82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67',
',65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102,103,,,230,,,,,',
',307,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,,,,313,,,,,,,,',
',,,,,,,,,,,876,,,229,,,58,59,,,60,,,,,,,,,,,,,,,,,,,,89,79,82,83,,84',
'86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68',
'30,31,72,73,,,,,,29,28,27,101,100,102,103,,,19,,,,,,,45,,,105,104,106',
'95,56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,',
',229,,,58,59,,,60,,,,,,44,,,,,,,,,20,,,,,89,79,82,83,,84,86,85,87,,',
',,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,659,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,',
',80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73',
',,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95,56',
'97,96,98,284,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,279,,44,,,285,,,,,,228,,,,,89,282,82,83,,84,86,85,87',
',,,,80,88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72',
'73,,,,,,305,306,312,101,100,102,103,,,230,,,,,,,45,,,105,104,106,95',
'56,97,96,98,,99,107,108,,91,92,,42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229',
',,58,59,,,60,,,,,,44,,,,,,,,,228,,,,,89,79,82,83,,84,86,85,87,,,,,80',
'88,,,,69,70,71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,',
',,,305,306,312,101,100,102,103,,,230,,,,,,,307,,,105,104,106,95,56,97',
'96,98,,99,107,108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,876,,,229,,,58,59',
',,60,,,,,,,,,,,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70',
'71,62,57,81,93,94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312',
'101,100,102,103,,,230,,,,,,,307,,,105,104,106,95,56,97,96,98,,99,107',
'108,,91,92,,,,313,,,,,,,,,,,,,,,,,,,,947,,,229,,,58,59,,,60,,,,,,,,',
',,,,,,,,,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93',
'94,63,64,,,,67,,65,66,68,309,310,72,73,,,,,,305,306,312,101,100,102',
'103,,,230,,,,,,,45,,,105,104,106,95,56,97,96,98,,99,107,108,,91,92,',
'42,43,41,,,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,,,,,44,,,,,,,,,228',
',,,,89,79,82,83,,84,86,85,87,,,,,80,88,,,,69,70,71,62,57,81,93,94,63',
'64,,,,67,,65,66,68,30,31,72,73,,,,,,29,28,27,101,100,102,103,,,230,',
',,,,,45,,,105,104,106,95,56,97,96,98,284,99,107,108,,91,92,,42,43,41',
',,,,,,,,,,,,,,,,,,,223,,,229,,,58,59,,,60,,281,,279,,44,,,285,,,,,,228',
',,,,89,282,82,83,,84,86,85,87,,,,,80,88,,,,,-282,,62,,81,93,94,-282',
'-282,-282,,,-282,-282,-282,,-282,,,,,,,,,,-282,-282,-282,,,,,,,,-282',
'-282,,-282,-282,-282,-282,-282,,,,,,,,,,,,,,,,,,,,,,,,-282,-282,-282',
'-282,-282,-282,-282,-282,-282,-282,-282,-282,-282,-282,,,-282,-282,-282',
',,-282,,,-282,,,-282,-282,,-282,,-282,,-282,,-282,-282,,-282,-282,-282',
'-282,-282,,-282,,-282,,,,,,,,,,,,,,-282,,,-282,-282,-282,-282,-579,-282',
',-282,,,,-579,-579,-579,,,-579,-579,-579,,-579,,,,,,,,,-579,-579,-579',
'-579,,,,,,,,-579,-579,,-579,-579,-579,-579,-579,,,,,,,,,,,,,,,,,,,,',
',,,-579,-579,-579,-579,-579,-579,-579,-579,-579,-579,-579,-579,-579',
'-579,,,-579,-579,-579,,,-579,,,-579,,,-579,-579,,-579,,-579,,-579,,-579',
'-579,,-579,-579,-579,-579,-579,,-579,-579,-579,,,,,,,,,,,,,,-579,,,-579',
'-579,-579,-579,-580,-579,,-579,,,,-580,-580,-580,,,-580,-580,-580,,-580',
',,,,,,,,-580,-580,-580,-580,,,,,,,,-580,-580,,-580,-580,-580,-580,-580',
',,,,,,,,,,,,,,,,,,,,,,,-580,-580,-580,-580,-580,-580,-580,-580,-580',
'-580,-580,-580,-580,-580,,,-580,-580,-580,,,-580,,,-580,,,-580,-580',
',-580,,-580,,-580,,-580,-580,,-580,-580,-580,-580,-580,,-580,-580,-580',
',,,,,,,,,,,,,-580,,,-580,-580,-580,-580,-411,-580,,-580,,,,-411,-411',
'-411,,,-411,-411,-411,,-411,,,,,,,,,-411,-411,-411,,,,,,,,,-411,-411',
',-411,-411,-411,-411,-411,,,,,,,,,,,,,,,,,,,,,,,,-411,-411,-411,-411',
'-411,-411,-411,-411,-411,-411,-411,-411,-411,-411,,,-411,-411,-411,',
',-411,,263,-411,,,-411,-411,,-411,,-411,,-411,,-411,-411,,-411,-411',
'-411,-411,-411,-298,-411,-411,-411,,,,-298,-298,-298,,,-298,-298,-298',
',-298,-411,,,-411,-411,,-411,,-411,-298,-298,,,,,,,,,-298,-298,,-298',
'-298,-298,-298,-298,,,,,,,,,,,,,,,,,,,,,,,,-298,-298,-298,-298,-298',
'-298,-298,-298,-298,-298,-298,-298,-298,-298,,,-298,-298,-298,,,-298',
',272,-298,,,-298,-298,,-298,,-298,,-298,,-298,-298,,-298,-298,-298,-298',
'-298,,-298,-246,-298,,,,,,-246,-246,-246,,,-246,-246,-246,-298,-246',
',-298,-298,,-298,,-298,,-246,-246,-246,,,,,,,,,-246,-246,,-246,-246',
'-246,-246,-246,,,,,,,,,,,,,,,,,,,,,,,,-246,-246,-246,-246,-246,-246',
'-246,-246,-246,-246,-246,-246,-246,-246,,,-246,-246,-246,,,-246,,263',
'-246,,,-246,-246,,-246,,-246,,-246,,-246,-246,,-246,-246,-246,-246,-246',
',-246,-246,-246,,,,,,,,,,,,,,-246,,-246,-246,-246,,-246,,-246,-246,-246',
'-246,,,-246,-246,-246,548,-246,545,544,543,553,546,,,,-246,-246,,,,',
'556,,,,,-246,-246,,-246,-246,-246,-246,-246,,,,,,,,,,551,548,,545,544',
'543,553,546,,561,560,564,563,,,,557,556,548,,545,544,543,553,546,-246',
',,,,,,-246,,556,,,263,-246,551,534,,218,,,,,,561,560,564,563,,,,557',
'551,,,,-246,-246,,,,561,560,564,563,,,,557,,,-246,,,-246,,,,,-246,173',
'184,174,197,170,190,180,179,200,201,195,178,177,172,198,202,203,182',
'171,185,189,191,183,176,,,,192,199,194,193,186,196,181,169,188,187,',
',,,,168,175,166,167,163,164,165,124,126,123,,125,,,,,,,,,157,158,,154',
'136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,,,,,,,,,151',
'150,,135,156,153,152,161,148,149,143,141,133,155,134,,,162,89,,,,,,',
',,,,,,,88,173,184,174,197,170,190,180,179,200,201,195,178,177,172,198',
'202,203,182,171,185,189,191,183,176,,,,192,199,194,193,186,196,181,169',
'188,187,,,,,,168,175,166,167,163,164,165,124,126,,,125,,,,,,,,,157,158',
',154,136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,,,,,',
',,,151,150,,135,156,153,152,161,148,149,143,141,133,155,134,,,162,89',
',,,,,,,,,,,,,88,173,184,174,197,170,190,180,179,200,201,195,178,177',
'172,198,202,203,182,171,185,189,191,183,176,,,,192,199,194,193,186,196',
'181,169,188,187,,,,,,168,175,166,167,163,164,165,124,126,,,125,,,,,',
',,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160,146,147',
',,,,,,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155,134',
',,162,89,,,,,,,,,,,,,,88,173,184,174,197,170,190,180,179,200,201,195',
'178,177,172,198,202,203,182,171,185,189,191,183,176,,,,192,199,194,193',
'186,196,181,169,188,187,,,,,,168,175,166,167,163,164,165,124,126,,,125',
',,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160,146',
'147,,,,,,,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155',
'134,,,162,89,,,,,,,,,,,,,,88,173,184,174,197,170,190,180,179,200,201',
'195,178,177,172,198,202,203,182,171,185,189,191,183,176,,,,192,199,194',
'371,370,372,369,169,188,187,,,,,,168,175,166,167,366,367,368,364,126',
'97,96,365,,99,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,',
',,159,160,146,147,,,,,,376,,,,,,,,151,150,,135,156,153,152,161,148,149',
'143,141,133,155,134,,,162,173,184,174,197,170,190,180,179,200,201,195',
'178,177,172,198,202,203,182,171,185,189,191,183,176,,,,192,199,194,193',
'186,196,181,169,188,187,,,,,,168,175,166,167,163,164,165,124,126,,,125',
',,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160,146',
'147,,,,,,,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155',
'134,413,417,162,,414,,,,,,,,,157,158,,154,136,137,138,145,142,144,,',
'139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153,152',
'161,148,149,143,141,133,155,134,420,424,162,,419,,,,,,,,,157,158,,154',
'136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,',
',151,150,,135,156,153,152,161,148,149,143,141,133,155,134,475,417,162',
',476,,,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160',
'146,147,,,,,,,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133',
'155,134,638,417,162,,639,,,,,,,,,157,158,,154,136,137,138,145,142,144',
',,139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153,152',
'161,148,149,143,141,133,155,134,640,424,162,,641,,,,,,,,,157,158,,154',
'136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,',
',151,150,,135,156,153,152,161,148,149,143,141,133,155,134,670,417,162',
',671,,,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160',
'146,147,,,,,,263,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141',
'133,155,134,673,424,162,,674,,,,,,,,,157,158,,154,136,137,138,145,142',
'144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153',
'152,161,148,149,143,141,133,155,134,638,417,162,,639,,,,,,,,,157,158',
',154,136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263,',
',,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155,134,640,424',
'162,,641,,,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159',
'160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153,152,161,148,149,143',
'141,133,155,134,721,417,162,,722,,,,,,,,,157,158,,154,136,137,138,145',
'142,144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156',
'153,152,161,148,149,143,141,133,155,134,723,424,162,,724,,,,,,,,,157',
'158,,154,136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263',
',,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155,134,726',
'424,162,,727,,,,,,,,,157,158,,154,136,137,138,145,142,144,,,139,140',
',,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153,152,161,148',
'149,143,141,133,155,134,475,417,162,,476,,,,,,,,,157,158,,154,136,137',
'138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150',
',135,156,153,152,161,148,149,143,141,133,155,134,983,424,162,,982,,',
',,,,,,157,158,,154,136,137,138,145,142,144,,,139,140,,,,159,160,146',
'147,,,,,,263,,,,,,,,151,150,,135,156,153,152,161,148,149,143,141,133',
'155,134,1006,417,162,,1007,,,,,,,,,157,158,,154,136,137,138,145,142',
'144,,,139,140,,,,159,160,146,147,,,,,,263,,,,,,,,151,150,,135,156,153',
'152,161,148,149,143,141,133,155,134,1008,424,162,,1009,,,,,,,,,157,158',
',154,136,137,138,145,142,144,,,139,140,,,,159,160,146,147,,,,,,263,',
',,,,,,151,150,,135,156,153,152,161,148,149,143,141,133,155,134,,548',
'162,545,544,543,553,546,,548,,545,544,543,553,546,,556,,,,,,,548,556',
'545,544,543,553,546,,,,,,,,,551,556,,,,,,,551,561,560,564,563,,,,557',
'561,560,564,563,,,551,557,548,,545,544,543,553,546,561,560,564,563,',
',,557,548,556,545,544,543,553,546,,548,,545,544,543,553,546,,556,,,',
',,,551,556,548,,545,544,543,553,546,561,560,564,563,,,551,557,,556,',
',,,551,561,560,564,563,,,,557,561,560,564,563,,,,557,551,548,,545,544',
'543,553,546,,561,560,564,563,,,,557,556,548,,545,544,543,553,546,,548',
',545,544,543,553,546,,556,,,,,551,,548,556,545,544,543,553,546,,,564',
'563,,,,557,551,556,,,,,,,551,,,564,563,,,,557,561,560,564,563,,,551',
'557,548,,545,544,543,553,546,,,564,563,,,,557,548,556,545,544,543,553',
'546,,548,,545,544,543,553,546,548,556,545,544,543,553,546,,551,556,',
',,,,,556,,,564,563,,,551,557,,,,,,,551,,,564,563,,,551,557,,,564,563',
',,,557,,564,563,,,,557' ]
        racc_action_table = arr = ::Array.new(25292, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'95,345,61,437,437,346,349,95,95,95,221,338,95,95,95,383,95,355,19,384',
'58,355,594,594,95,222,95,95,95,473,650,26,17,17,578,359,95,95,339,95',
'95,95,95,95,719,885,15,1,908,687,721,7,1006,665,1007,1020,680,680,308',
'19,58,913,665,913,473,722,221,95,95,95,95,95,95,95,95,95,95,95,95,95',
'95,222,383,95,95,95,384,95,95,570,61,95,10,15,95,95,26,95,437,95,12',
'95,15,95,95,844,95,95,95,95,95,345,95,98,95,346,349,594,650,26,98,98',
'98,308,1008,98,98,98,95,98,338,95,95,95,95,338,95,98,95,98,98,98,359',
'95,578,571,308,723,845,98,98,680,98,98,98,98,98,339,13,687,721,670,339',
'719,885,359,719,908,719,885,359,1006,908,1007,1020,722,1006,16,1007',
'1020,98,98,98,98,98,98,98,98,98,98,98,98,98,98,570,22,98,98,98,570,98',
'98,1008,671,98,37,724,98,98,40,98,605,98,723,98,844,98,98,224,98,98',
'98,98,98,419,98,45,98,670,590,590,419,419,419,481,24,328,419,419,328',
'419,98,24,1008,98,98,98,98,1008,98,571,98,353,845,3,571,98,723,845,3',
'419,419,109,419,419,419,419,419,670,481,671,670,724,123,224,204,605',
'605,123,123,670,704,607,704,704,704,605,704,223,41,41,419,419,419,419',
'419,419,419,419,419,419,419,419,419,419,353,354,419,419,419,671,419',
'353,671,38,419,724,353,419,313,313,353,671,419,590,419,443,419,419,590',
'419,419,419,419,419,378,419,420,419,225,353,39,364,226,420,420,420,607',
'607,364,420,420,419,420,482,419,419,607,419,354,419,38,420,230,704,673',
'354,419,41,41,38,354,262,420,420,354,420,420,420,420,420,904,443,904',
'904,904,317,904,39,482,329,651,492,329,354,379,276,39,313,313,378,378',
'378,801,420,420,420,420,420,420,420,420,420,420,420,420,420,420,673',
'413,420,420,420,380,420,673,651,288,420,414,673,420,288,317,673,381',
'420,638,420,726,420,420,317,420,420,420,420,420,277,420,420,420,382',
'673,280,385,801,492,492,492,379,379,379,801,904,420,413,621,420,420',
'640,420,801,420,492,413,414,640,640,640,420,292,640,640,640,414,640',
'365,380,380,380,801,726,638,365,640,640,640,640,726,381,381,381,639',
'726,293,640,640,726,640,640,640,640,640,621,295,774,382,382,382,385',
'385,385,621,337,337,366,726,551,367,551,551,551,366,551,296,367,640',
'640,640,640,640,640,640,640,640,640,640,640,640,640,689,297,640,640',
'640,368,640,640,639,303,640,689,368,640,640,551,640,306,640,307,640',
'774,640,640,551,640,640,640,640,640,862,640,640,640,332,369,77,332,342',
'370,312,862,369,342,689,689,370,640,77,689,640,640,640,640,449,640,641',
'640,77,371,14,314,640,641,641,641,371,14,641,641,641,46,641,220,862',
'862,14,318,46,862,220,372,641,641,641,321,449,46,372,220,449,449,641',
'641,374,641,641,641,641,641,833,374,833,833,833,300,833,701,326,701',
'701,701,300,701,831,330,831,831,831,686,831,300,686,641,641,641,641',
'641,641,641,641,641,641,641,641,641,641,468,833,641,641,641,301,641',
'641,701,469,641,331,301,641,641,831,641,701,641,333,641,301,641,641',
'831,641,641,641,641,641,589,641,468,641,343,589,468,468,450,468,468',
'469,523,523,344,469,469,641,469,469,641,641,641,641,848,641,853,641',
'348,848,350,853,641,0,0,0,0,0,0,394,450,302,0,0,450,450,400,0,302,0',
'0,0,0,0,0,0,934,302,403,934,405,0,0,0,0,0,0,0,598,598,0,409,598,598',
'598,432,0,0,0,0,0,0,0,0,0,0,0,0,411,0,0,0,412,0,0,421,0,0,0,432,432',
'432,432,432,432,432,432,432,432,432,304,432,432,319,429,432,432,304',
'0,439,319,0,692,692,0,0,304,451,0,319,0,432,452,432,0,432,432,453,432',
'432,432,432,432,0,432,710,710,454,0,0,0,0,347,0,0,0,0,996,996,347,479',
'0,0,483,432,499,432,500,347,0,503,0,0,0,33,33,33,33,33,33,505,510,357',
'33,33,513,521,522,33,357,33,33,33,33,33,33,33,524,357,536,537,539,33',
'33,33,33,33,33,33,540,541,33,550,558,562,565,408,33,33,33,33,33,33,33',
'33,33,33,33,33,567,33,33,33,572,33,33,573,33,33,33,408,408,408,408,408',
'408,408,408,408,408,408,509,408,408,581,592,408,408,509,33,602,581,33',
'610,612,33,33,509,618,33,581,33,408,622,408,33,408,408,627,408,408,408',
'408,408,33,408,632,642,649,33,33,33,33,875,33,33,33,33,656,658,875,664',
'33,33,667,408,669,672,675,875,33,676,33,33,33,121,121,121,121,121,121',
'679,681,938,121,121,684,688,703,121,938,121,121,121,121,121,121,121',
'705,938,712,717,720,121,121,121,121,121,121,121,729,734,121,753,758',
'775,776,643,121,121,121,121,121,121,121,121,121,121,121,121,777,121',
'121,121,779,121,121,780,121,121,121,643,643,643,643,643,643,643,643',
'643,643,643,674,643,643,944,781,643,643,674,121,783,944,121,674,784',
'121,121,674,785,121,944,121,643,786,643,121,643,643,790,643,643,643',
'643,643,121,643,794,795,800,121,121,121,121,945,121,121,121,121,804',
'807,945,808,121,121,811,643,816,817,821,945,121,822,121,121,121,206',
'206,206,206,206,206,824,825,946,206,206,827,830,832,206,946,206,206',
'206,206,206,206,206,835,946,838,847,851,206,206,206,206,206,206,206',
'852,873,206,873,873,873,855,873,206,206,206,206,206,206,206,206,206',
'206,206,206,856,206,206,206,872,206,206,876,206,206,206,21,21,21,21',
'21,21,21,21,21,21,21,727,21,21,948,878,21,21,727,206,889,948,206,727',
'890,206,206,727,906,206,948,206,21,910,21,206,21,21,911,21,21,21,21',
'21,206,21,917,921,924,206,206,206,206,988,206,206,206,206,927,928,988',
'929,206,206,930,21,932,947,952,988,206,953,206,206,206,229,229,229,229',
'229,229,954,955,956,229,229,958,982,983,229,984,229,229,229,229,229',
'229,229,6,6,6,6,6,229,229,229,229,229,229,229,995,981,229,981,981,981',
'997,981,229,229,229,229,229,229,229,229,229,229,229,229,998,229,229',
'229,999,229,229,1000,229,229,229,274,274,274,274,274,274,274,274,274',
'274,274,1009,274,274,1001,1002,274,274,1009,229,1005,1010,229,1009,1011',
'229,229,1009,1022,229,,229,274,,274,229,274,274,,274,274,274,274,274',
'229,274,,,,229,229,229,229,,229,229,229,229,,,,,229,229,,274,,,,,229',
',229,229,229,294,294,294,294,294,294,,,,294,294,,,,294,,294,294,294',
'294,294,294,294,291,291,291,291,291,294,294,294,294,294,294,294,,,294',
'497,497,497,497,497,294,294,294,294,294,294,294,294,294,294,294,294',
',294,294,294,,294,294,,294,294,294,427,427,427,427,427,427,427,427,427',
'427,427,,427,427,,,427,427,,294,,,294,,,294,294,,,294,,294,427,,427',
'294,427,427,,427,427,427,427,427,294,427,,,,294,294,294,294,,294,294',
'294,294,,,,,294,294,,427,,,,,294,,294,294,294,299,299,299,299,299,299',
',,,299,299,,,,299,,299,299,299,299,299,299,299,,,,,,299,299,299,299',
'299,299,299,,,299,,,,,,299,299,299,299,299,299,299,299,299,299,299,299',
',299,299,299,,299,299,,299,299,299,519,519,519,519,519,519,519,519,519',
'519,519,,519,519,,,519,519,,299,,,299,,,299,299,,,299,,299,519,,519',
'299,519,519,,519,519,519,519,519,299,519,,,,299,299,299,299,,299,299',
'299,299,,,,,299,299,,519,,,,,299,,299,299,299,324,324,324,324,324,324',
',,,324,324,,,,324,,324,324,324,324,324,324,324,,,,,,324,324,324,324',
'324,324,324,,,324,,,,,,324,324,324,324,324,324,324,324,324,324,324,324',
',324,324,324,,324,324,,324,324,324,644,644,644,644,644,644,644,644,644',
'644,644,,644,644,,,644,644,,324,,,324,,,324,324,,,324,,324,644,,644',
'324,644,644,,644,644,644,644,644,324,644,,,,324,324,324,324,,324,324',
'324,324,,,,,324,324,644,644,,,,,324,,324,324,324,498,498,498,498,498',
'498,,,,498,498,,,,498,,498,498,498,498,498,498,498,,,,,,498,498,498',
'498,498,498,498,,,498,,,,,,498,498,498,498,498,498,498,498,498,498,498',
'498,,498,498,498,,498,498,,498,498,498,678,678,678,678,678,678,678,678',
'678,678,678,,678,678,,,678,678,,498,,,498,,,498,498,,,498,,498,678,',
'678,498,678,678,,678,678,678,678,678,498,678,,,,498,498,498,498,,498',
'498,498,498,,,,,498,498,,678,,,,,498,,498,498,498,566,566,566,566,566',
'566,,,,566,566,,,,566,,566,566,566,566,566,566,566,,,,,,566,566,566',
'566,566,566,566,,,566,,,,,,566,566,566,566,566,566,566,566,566,566,566',
'566,,566,566,566,,566,566,,566,566,566,755,755,755,755,755,755,755,755',
'755,755,755,,755,755,,,755,755,,566,,,566,,,566,566,,,566,,566,755,',
'755,566,755,755,,755,755,755,755,755,566,755,,,,566,566,566,566,,566',
'566,566,566,,,,,566,566,,755,,,,,566,,566,566,566,569,569,569,569,569',
'569,,,,569,569,,,,569,,569,569,569,569,569,569,569,,,,,,569,569,569',
'569,569,569,569,,,569,,,,,,569,569,569,569,569,569,569,569,569,569,569',
'569,,569,569,569,,569,569,,569,569,569,760,760,760,760,760,760,760,760',
'760,760,760,,760,760,,,760,760,,569,,,569,,,569,569,,,569,,569,760,',
'760,569,760,760,,760,760,760,760,760,569,760,,,,569,569,569,569,,569',
'569,569,569,,,,,569,569,,760,,,,,569,,569,569,569,591,591,591,591,591',
'591,,,,591,591,,,,591,,591,591,591,591,591,591,591,,,,,,591,591,591',
'591,591,591,591,,,591,,,,,,591,591,591,591,591,591,591,591,591,591,591',
'591,,591,591,591,,591,591,,591,591,591,762,762,762,762,762,762,762,762',
'762,762,762,,762,762,,,762,762,,591,,,591,,,591,591,,,591,,591,762,',
'762,591,762,762,,762,762,762,762,762,591,762,,,,591,591,591,591,,591',
'591,591,591,,,,,591,591,,762,,,,,591,,591,591,591,648,648,648,648,648',
'648,,,,648,648,,,,648,,648,648,648,648,648,648,648,,,,,,648,648,648',
'648,648,648,648,,,648,,,,,,648,648,648,648,648,648,648,648,648,648,648',
'648,,648,648,648,,648,648,,648,648,648,765,765,765,765,765,765,765,765',
'765,765,765,,765,765,,,765,765,,648,,,648,,,648,648,,,648,,648,765,',
'765,648,765,765,,765,765,765,765,765,648,765,,,,648,648,648,648,,648',
'648,648,648,,,,,648,648,,765,,,,,648,,648,648,648,653,653,653,653,653',
'653,,,,653,653,,,,653,,653,653,653,653,653,653,653,,,,,,653,653,653',
'653,653,653,653,,,653,,,,,,653,653,653,653,653,653,653,653,653,653,653',
'653,,653,653,653,,653,653,,653,653,653,767,767,767,767,767,767,767,767',
'767,767,767,,767,767,,,767,767,,653,,,653,,,653,653,,,653,,653,767,',
'767,653,767,767,,767,767,767,767,767,653,767,,,,653,653,653,653,,653',
'653,653,653,,,,,653,653,,767,,,,,653,,653,653,653,654,654,654,654,654',
'654,,,,654,654,,,,654,,654,654,654,654,654,654,654,,,,,,654,654,654',
'654,654,654,654,,,654,,,,,,654,654,654,654,654,654,654,654,654,654,654',
'654,,654,654,654,,654,654,,654,654,654,769,769,769,769,769,769,769,769',
'769,769,769,,769,769,,,769,769,,654,,,654,,,654,654,,,654,,654,769,',
'769,654,769,769,,769,769,769,769,769,654,769,,,,654,654,654,654,,654',
'654,654,654,,,,,654,654,,769,,,,,654,,654,654,654,730,730,730,730,730',
'730,,,,730,730,,,,730,,730,730,730,730,730,730,730,,,,,,730,730,730',
'730,730,730,730,,,730,,,,,,730,730,730,730,730,730,730,730,730,730,730',
'730,,730,730,730,,730,730,,730,730,730,858,858,858,858,858,858,858,858',
'858,858,858,,858,858,,,858,858,,730,,,730,,,730,730,,,730,,730,858,',
'858,730,858,858,,858,858,858,858,858,730,858,,,,730,730,730,730,,730',
'730,730,730,,,,,730,730,,858,,,,,730,,730,730,730,735,735,735,735,735',
'735,,,,735,735,,,,735,,735,735,735,735,735,735,735,,,,,,735,735,735',
'735,735,735,735,,,735,,,,,,735,735,735,735,735,735,735,735,735,735,735',
'735,,735,735,735,,735,735,,735,735,735,969,969,969,969,969,969,969,969',
'969,969,969,,969,969,,,969,969,,735,,,735,,,735,735,,,735,,735,969,',
'969,735,969,969,,969,969,969,969,969,735,969,,,,735,735,735,735,,735',
'735,735,735,,,,,735,735,,969,,,,,735,,735,735,735,745,745,745,745,745',
'745,,,,745,745,,,,745,,745,745,745,745,745,745,745,,,,,,745,745,745',
'745,745,745,745,,,745,,,,,,745,745,745,745,745,745,745,745,745,745,745',
'745,,745,745,745,,745,745,,745,745,745,447,447,447,447,447,447,447,447',
'447,447,447,,447,447,,,447,447,,745,,,745,,,745,745,,,745,,745,447,',
'447,745,447,447,,447,447,447,447,447,745,447,,,,745,745,745,745,,745',
'745,745,745,,,,,745,745,,,,,,,745,,745,745,745,793,793,793,793,793,793',
',,,793,793,,,,793,,793,793,793,793,793,793,793,,,,,,793,793,793,793',
'793,793,793,,,793,,,,,,793,793,793,793,793,793,793,793,793,793,793,793',
',793,793,793,,793,793,,793,793,793,448,448,448,448,448,448,448,448,448',
'448,448,,448,448,,,448,448,,793,,,793,,,793,793,,,793,,793,448,,448',
'793,448,448,,448,448,448,448,448,793,448,,,,793,793,793,793,,793,793',
'793,793,,,,,793,793,,,,,,,793,,793,793,793,806,806,806,806,806,806,',
',,806,806,,,,806,,806,806,806,806,806,806,806,,,,,,806,806,806,806,806',
'806,806,,905,806,905,905,905,,905,806,806,806,806,806,806,806,806,806',
'806,806,806,,806,806,806,,806,806,,806,806,806,458,458,458,458,458,458',
'458,,905,458,458,,,,,,458,458,,806,,,806,,,806,806,,,806,,806,458,,458',
'806,458,458,,458,458,458,458,458,806,458,,,,806,806,806,806,,806,806',
'806,806,,,,,806,806,,,,,,,806,,806,806,806,814,814,814,814,814,814,',
',,814,814,,,,814,,814,814,814,814,814,814,814,,,,,,814,814,814,814,814',
'814,814,,957,814,957,957,957,,957,814,814,814,814,814,814,814,814,814',
'814,814,814,,814,814,814,,814,814,,814,814,814,459,,,959,,959,959,959',
'957,959,,,,,,,459,459,,814,,,814,,,814,814,,,814,,814,459,,459,814,459',
'459,,459,459,959,,459,814,459,,,,814,814,814,814,,814,814,814,814,,',
',,814,814,,,,,,,814,,814,814,814,815,815,815,815,815,815,,,,815,815',
',,,815,,815,815,815,815,815,815,815,,,,,,815,815,815,815,815,815,815',
',994,815,994,994,994,,994,815,815,815,815,815,815,815,815,815,815,815',
'815,,815,815,815,,815,815,,815,815,815,460,,,,,,,,994,,,,,,,,460,460',
',815,,,815,,,815,815,,,815,,815,460,,460,815,460,460,,460,460,,,460',
'815,460,,,,815,815,815,815,,815,815,815,815,,,,,815,815,,,,,,,815,,815',
'815,815,839,839,839,839,839,839,,,,839,839,,,,839,,839,839,839,839,839',
'839,839,,,,,,839,839,839,839,839,839,839,,,839,,,,,,839,839,839,839',
'839,839,839,839,839,839,839,839,,839,839,839,,839,839,,839,839,839,461',
',,,,,,,,,,,,,,,461,461,,839,,,839,,,839,839,,,839,,839,461,,461,839',
'461,461,,461,461,,,461,839,461,,,,839,839,839,839,,839,839,839,839,',
',,,839,839,,,,,,,839,,839,839,839,840,840,840,840,840,840,,,,840,840',
',,,840,,840,840,840,840,840,840,840,,,,,,840,840,840,840,840,840,840',
',,840,,,,,,840,840,840,840,840,840,840,840,840,840,840,840,,840,840',
'840,,840,840,,840,840,840,462,,,,,,,,,,,,,,,,462,462,,840,,,840,,,840',
'840,,,840,,840,462,,462,840,462,462,,462,462,,,462,840,462,,,,840,840',
'840,840,,840,840,840,840,,,,,840,840,,,,,,,840,,840,840,840,843,843',
'843,843,843,843,,,,843,843,,,,843,,843,843,843,843,843,843,843,,,,,',
'843,843,843,843,843,843,843,,,843,,,,,,843,843,843,843,843,843,843,843',
'843,843,843,843,,843,843,843,,843,843,,843,843,843,463,463,463,463,463',
'463,463,,,463,463,,,,,,463,463,,843,,,843,,,843,843,,,843,,843,463,',
'463,843,463,463,,463,463,463,463,463,843,463,,,,843,843,843,843,,843',
'843,843,843,,,,,843,843,,,,,,,843,,843,843,843,849,849,849,849,849,849',
',,,849,849,,,,849,,849,849,849,849,849,849,849,,,,,,849,849,849,849',
'849,849,849,,,849,,,,,,849,849,849,849,849,849,849,849,849,849,849,849',
',849,849,849,,849,849,,849,849,849,464,464,464,464,464,464,464,,,464',
'464,,,,,,464,464,,849,,,849,,,849,849,,,849,,849,464,,464,849,464,464',
',464,464,464,464,464,849,464,,,,849,849,849,849,,849,849,849,849,,,',
',849,849,,,,,,,849,,849,849,849,882,882,882,882,882,882,,,,882,882,',
',,882,,882,882,882,882,882,882,882,,,,,,882,882,882,882,882,882,882',
',,882,,,,,,882,882,882,882,882,882,882,882,882,882,882,882,,882,882',
'882,,882,882,,882,882,882,465,465,465,465,465,465,465,,,465,465,,,,',
',465,465,,882,,,882,,,882,882,,,882,,882,465,,465,882,465,465,,465,465',
'465,465,465,882,465,,,,882,882,882,882,,882,882,882,882,,,,,882,882',
',,,,,,882,,882,882,882,943,943,943,943,943,943,,,,943,943,,,,943,,943',
'943,943,943,943,943,943,,,,,,943,943,943,943,943,943,943,,,943,,,,,',
'943,943,943,943,943,943,943,943,943,943,943,943,,943,943,943,,943,943',
',943,943,943,466,466,466,466,466,466,466,,,466,466,,,,,,466,466,,943',
',,943,,,943,943,,,943,,943,466,,466,943,466,466,,466,466,466,466,466',
'943,466,,,,943,943,943,943,,943,943,943,943,,,,,943,943,,,,,,,943,,943',
'943,943,960,960,960,960,960,960,,,,960,960,,,,960,,960,960,960,960,960',
'960,960,,,,,,960,960,960,960,960,960,960,,,960,,,,,,960,960,960,960',
'960,960,960,960,960,960,960,960,,960,960,960,,960,960,,960,960,960,467',
'467,467,467,467,467,467,,,467,467,,,,,,467,467,,960,,,960,,,960,960',
',,960,,960,467,,467,960,467,467,,467,467,467,467,467,960,467,,,,960',
'960,960,960,,960,960,960,960,,,,,960,960,,,,,,,960,,960,960,960,966',
'966,966,966,966,966,,,,966,966,,,,966,,966,966,966,966,966,966,966,',
',,,,966,966,966,966,966,966,966,,,966,,,,,,966,966,966,966,966,966,966',
'966,966,966,966,966,,966,966,966,,966,966,,966,966,966,470,470,470,470',
'470,470,470,,,470,470,,,,,,470,470,,966,,,966,,,966,966,,,966,,966,470',
',470,966,470,470,,470,470,470,470,470,966,470,,,,966,966,966,966,,966',
'966,966,966,,,,,966,966,,,,,,,966,,966,966,966,968,968,968,968,968,968',
',,,968,968,,,,968,,968,968,968,968,968,968,968,,,,,,968,968,968,968',
'968,968,968,,,968,,,,,,968,968,968,968,968,968,968,968,968,968,968,968',
',968,968,968,,968,968,,968,968,968,471,471,471,471,471,471,471,471,',
'471,471,,,,,,471,471,,968,,,968,,,968,968,,,968,,968,471,,471,968,471',
'471,,471,471,471,471,471,968,471,,,,968,968,968,968,,968,968,968,968',
',,,,968,968,,,,,,,968,,968,968,968,5,5,5,5,5,,,,5,5,,,,5,,5,5,5,5,5',
'5,5,,,,,,5,5,5,5,5,5,5,,,5,,,,,,5,5,5,5,5,5,5,5,5,5,5,5,,5,5,5,,5,5',
',5,5,5,455,,,,,,,,,,,,,,,,455,455,,5,,,5,,,5,5,,,5,,5,455,,455,5,455',
'455,,455,455,,,,5,,,,,5,5,5,5,,5,5,5,5,,,,,5,5,,,,20,20,20,5,20,5,5',
'5,20,20,,,,20,,20,20,20,20,20,20,20,,,,,,20,20,20,20,20,20,20,,,20,',
',,,,,20,,,20,20,20,20,20,20,20,20,,20,20,20,,20,20,,20,20,20,456,,,',
',,,,,,,,,,,,456,456,,20,,,20,,,20,20,,,20,,,456,,456,20,456,456,,456',
'456,,,,20,,,,,20,20,20,20,,20,20,20,20,,,,,20,20,,,,29,29,29,20,29,20',
'20,20,29,29,,,,29,,29,29,29,29,29,29,29,,,,,,29,29,29,29,29,29,29,,',
'29,,,,,,,29,,,29,29,29,29,29,29,29,29,29,29,29,29,,29,29,,29,29,29,457',
',,,,,,,,,,,,,,,457,457,,29,,,29,,,29,29,,,29,,29,457,29,,29,457,457',
'29,457,457,,,,29,,,,,29,29,29,29,,29,29,29,29,,,,,29,29,,,,30,30,30',
'29,30,29,29,29,30,30,,,,30,,30,30,30,30,30,30,30,,,,,,30,30,30,30,30',
'30,30,,,30,,,,,,,30,,,30,30,30,30,30,30,30,30,30,30,30,30,,30,30,,30',
'30,30,,,,,,,,,,,,,,,,,,,,30,,,30,,,30,30,,,30,,30,,30,,30,,,30,,,,,',
'30,,,,,30,30,30,30,,30,30,30,30,,,,,30,30,,,,31,31,31,30,31,30,30,30',
'31,31,,,,31,,31,31,31,31,31,31,31,,,,,,31,31,31,31,31,31,31,,,31,,,',
',,,31,,,31,31,31,31,31,31,31,31,31,31,31,31,,31,31,,31,31,31,,,,,,,',
',,,,,,,,,,,,31,,,31,,,31,31,,,31,,31,,31,,31,,,31,,,,,,31,,,,,31,31',
'31,31,,31,31,31,31,,,,,31,31,,,,34,34,34,31,34,31,31,31,34,34,,,,34',
',34,34,34,34,34,34,34,,,,,,34,34,34,34,34,34,34,,,34,,,,,,,34,,,34,34',
'34,34,34,34,34,34,,34,34,34,,34,34,,,,34,,,,,,,,,,,,,,,,,,,,34,,,34',
',,34,34,,,34,,34,,,,,,,,,,,,,,,,,,34,34,34,34,,34,34,34,34,,,,,34,34',
',,,35,35,35,34,35,34,34,34,35,35,,,,35,,35,35,35,35,35,35,35,,,,,,35',
'35,35,35,35,35,35,,,35,,,,,,,35,,,35,35,35,35,35,35,35,35,,35,35,35',
',35,35,,,,35,,,,,,,,,,,,,,,,,,,,35,,,35,,,35,35,,,35,,697,,697,697,697',
'697,697,,,,,,,,,,697,,35,35,35,35,,35,35,35,35,,,,,35,35,,,,35,,697',
'35,,35,35,35,42,42,42,,42,697,697,,42,42,697,,,42,,42,42,42,42,42,42',
'42,,,,,,42,42,42,42,42,42,42,,,42,,,,,,,42,,,42,42,42,42,42,42,42,42',
',42,42,42,,42,42,,42,42,42,,,,,,,,,,,,,,,,,,,,42,,,42,,,42,42,,,42,',
',,,,42,,,,,,,,,42,,,,,42,42,42,42,,42,42,42,42,,,,,42,42,,,,43,43,43',
'42,43,42,42,42,43,43,,,,43,,43,43,43,43,43,43,43,,,,,,43,43,43,43,43',
'43,43,,,43,,,,,,,43,,,43,43,43,43,43,43,43,43,,43,43,43,,43,43,,43,43',
'43,,,,,,,,,,,,,,,,,,,,43,,,43,,,43,43,,,43,,,,,,43,,,,,,,,,43,,,,,43',
'43,43,43,,43,43,43,43,,,,,43,43,,,,44,44,44,43,44,43,43,43,44,44,,,',
'44,,44,44,44,44,44,44,44,,,,,,44,44,44,44,44,44,44,,,44,,,,,,,44,,,44',
'44,44,44,44,44,44,44,,44,44,44,,44,44,,44,44,44,,,,,,,,,,,,,,,,,,,,44',
',,44,,,44,44,,,44,,,,,,44,,,,,,,,,44,,,,,44,44,44,44,,44,44,44,44,,',
',,44,44,,,,59,59,59,44,59,44,44,44,59,59,,,,59,,59,59,59,59,59,59,59',
',,,,,59,59,59,59,59,59,59,,,59,,,,,,,59,,,59,59,59,59,59,59,59,59,59',
'59,59,59,,59,59,,59,59,59,,,,,,,,,,,,,,,,,,,,59,,,59,,,59,59,,,59,,59',
',,,59,,,59,,,,,,59,,,,,59,59,59,59,,59,59,59,59,,,,,59,59,,,,60,60,60',
'59,60,59,59,59,60,60,,,,60,,60,60,60,60,60,60,60,,,,,,60,60,60,60,60',
'60,60,,,60,,,,,,,60,,,60,60,60,60,60,60,60,60,60,60,60,60,,60,60,,60',
'60,60,,,,,,,,,,,,,,,,,,,,60,,,60,,,60,60,,,60,,,,,,60,,,60,,,,,,60,',
',,,60,60,60,60,,60,60,60,60,,,,,60,60,,,,63,63,63,60,63,60,60,60,63',
'63,,,,63,,63,63,63,63,63,63,63,,,,,,63,63,63,63,63,63,63,,,63,,,,,,',
'63,,,63,63,63,63,63,63,63,63,,63,63,63,,63,63,,63,63,63,,,,,,,,,,,,',
',,,,,,,63,,,63,,,63,63,,,63,,,,,,63,,,,,,,,,63,,,,,63,63,63,63,,63,63',
'63,63,,,,,63,63,,,,64,64,64,63,64,63,63,63,64,64,,,,64,,64,64,64,64',
'64,64,64,,,,,,64,64,64,64,64,64,64,,,64,,,,,,,64,,,64,64,64,64,64,64',
'64,64,,64,64,64,,64,64,,64,64,64,,,,,,,,,,,,,,,,,,,,64,,,64,,,64,64',
',,64,,,,,,64,,,,,,,,,64,,,,,64,64,64,64,,64,64,64,64,,,,,64,64,,,,67',
'67,67,64,67,64,64,64,67,67,,,,67,,67,67,67,67,67,67,67,,,,,,67,67,67',
'67,67,67,67,,,67,,,,,,,67,,,67,67,67,67,67,67,67,67,,67,67,67,,67,67',
',67,67,67,,,,,,,,,,,,,,,,,,,,67,,,67,,,67,67,,,67,,,,,,67,,,,,,,,,67',
',,,,67,67,67,67,,67,67,67,67,,,,,67,67,67,,,,,67,67,,67,67,67,68,68',
'68,,68,,,,68,68,,,,68,,68,68,68,68,68,68,68,,,,,,68,68,68,68,68,68,68',
',,68,,,,,,,68,,,68,68,68,68,68,68,68,68,,68,68,68,,68,68,,,,68,,,,,',
',,,,,,,,,,,,,,68,,,68,,,68,68,,,68,,68,,,,,,,,,,,,,,,,,,68,68,68,68',
',68,68,68,68,,,,,68,68,,,,69,69,69,68,69,68,68,68,69,69,,,,69,,69,69',
'69,69,69,69,69,,,,,,69,69,69,69,69,69,69,,,69,,,,,,,69,,,69,69,69,69',
'69,69,69,69,,69,69,69,,69,69,,,,69,,,,,,,,,,,,,,,,,69,,,69,,,69,,,69',
'69,,,69,,,,,,,,,,,,,,,,,,,,69,69,69,69,,69,69,69,69,,,,,69,69,,,,70',
'70,70,69,70,69,69,69,70,70,,,,70,,70,70,70,70,70,70,70,,,,,,70,70,70',
'70,70,70,70,,,70,,,,,,,70,,,70,70,70,70,70,70,70,70,,70,70,70,,70,70',
',,,70,,,,,,,,,,,,,,,,,,,,70,,,70,,,70,70,,,70,,869,,869,869,869,869',
'869,,,,,,,,,,869,,70,70,70,70,,70,70,70,70,,,,,70,70,,,,,,869,70,,70',
'70,70,111,111,111,111,111,869,869,,111,111,869,,,111,,111,111,111,111',
'111,111,111,,,,,,111,111,111,111,111,111,111,,,111,,,,,,111,111,111',
'111,111,111,111,111,111,111,111,111,,111,111,111,,111,111,,111,111,111',
',,,,,,,,,,,,,,,,,,,111,,,111,,,111,111,,,111,,111,,,,111,,,,,,,,,111',
',,,,111,111,111,111,,111,111,111,111,,,,,111,111,,,,,,111,111,,111,111',
'111,116,116,116,,116,,,,116,116,,,,116,,116,116,116,116,116,116,116',
',,,,,116,116,116,116,116,116,116,,,116,,,,,,,116,,,116,116,116,116,116',
'116,116,116,,116,116,116,,116,116,,116,116,116,,,,,,,,,,,,,,,,,,,,116',
',,116,,,116,116,,,116,,,,,,116,,,,,,,,,116,,,,,116,116,116,116,,116',
'116,116,116,,,,,116,116,,,,117,117,117,116,117,116,116,116,117,117,',
',,117,,117,117,117,117,117,117,117,,,,,,117,117,117,117,117,117,117',
',,117,,,,,,,117,,,117,117,117,117,117,117,117,117,,117,117,117,,117',
'117,,117,117,117,,,,,,,,,,,,,,,,,,,,117,,,117,,,117,117,,,117,,,,,,117',
',,,,,,,,117,,,,,117,117,117,117,,117,117,117,117,,,,,117,117,,,,118',
'118,118,117,118,117,117,117,118,118,,,,118,,118,118,118,118,118,118',
'118,,,,,,118,118,118,118,118,118,118,,,118,,,,,,,118,,,118,118,118,118',
'118,118,118,118,,118,118,118,,118,118,,118,118,118,,,,,,,,,,,,,,,,,',
',,118,,,118,,,118,118,,,118,,,,,,118,,,,,,,,,118,,,,,118,118,118,118',
',118,118,118,118,,,,,118,118,,,,119,119,119,118,119,118,118,118,119',
'119,,,,119,,119,119,119,119,119,119,119,,,,,,119,119,119,119,119,119',
'119,,,119,,,,,,,119,,,119,119,119,119,119,119,119,119,,119,119,119,',
'119,119,,119,119,119,,,,,,,,,,,,,,,,,,,,119,,,119,,,119,119,,,119,,',
',,,119,,,,,,,,,119,,,,,119,119,119,119,,119,119,119,119,,,,,119,119',
',,,,,,119,,119,119,119,120,120,120,120,120,,,,120,120,,,,120,,120,120',
'120,120,120,120,120,,,,,,120,120,120,120,120,120,120,,,120,,,,,,120',
'120,,120,120,120,120,120,120,120,120,120,,120,120,120,,120,120,,120',
'120,120,,,,,,,,,,,,,,,,,,,,120,,,120,,,120,120,,,120,,120,,,,120,,,',
',,,,,120,,,,,120,120,120,120,,120,120,120,120,,,,,120,120,,,,207,207',
'207,120,207,120,120,120,207,207,,,,207,,207,207,207,207,207,207,207',
',,,,,207,207,207,207,207,207,207,,,207,,,,,,,207,,,207,207,207,207,207',
'207,207,207,,207,207,207,,207,207,,207,207,207,,,,,,,,,,,,,,,,,,,,207',
',,207,,,207,207,,,207,,207,,,,207,,,,,,,,,207,,,,,207,207,207,207,,207',
'207,207,207,,,,,207,207,,,,208,208,208,207,208,207,207,207,208,208,',
',,208,,208,208,208,208,208,208,208,,,,,,208,208,208,208,208,208,208',
',,208,,,,,,,208,,,208,208,208,208,208,208,208,208,,208,208,208,,208',
'208,,208,208,208,,,,,,,,,,,,,,,,,,,,208,,,208,,,208,208,,,208,,,,,,208',
',,,,,,,,208,,,,,208,208,208,208,,208,208,208,208,,,,,208,208,,,,209',
'209,209,208,209,208,208,208,209,209,,,,209,,209,209,209,209,209,209',
'209,,,,,,209,209,209,209,209,209,209,,,209,,,,,,,209,,,209,209,209,209',
'209,209,209,209,209,209,209,209,,209,209,,209,209,209,,,,,,,,,,,,,,',
',,,,,209,,,209,,,209,209,,,209,,209,,209,,209,,,209,,,,,,209,,,,,209',
'209,209,209,,209,209,209,209,,,,,209,209,,,,212,212,212,209,212,209',
'209,209,212,212,,,,212,,212,212,212,212,212,212,212,,,,,,212,212,212',
'212,212,212,212,,,212,,,,,,,212,,,212,212,212,212,212,212,212,212,,212',
'212,212,,212,212,,212,212,212,,,,,,,,,,,,,,,,,,,,212,,,212,,,212,212',
',,212,,,,,,212,,,,,,,,,212,,,,,212,212,212,212,,212,212,212,212,,,,',
'212,212,,,,213,213,213,212,213,212,212,212,213,213,,,,213,,213,213,213',
'213,213,213,213,,,,,,213,213,213,213,213,213,213,,,213,,,,,,,213,,,213',
'213,213,213,213,213,213,213,,213,213,213,,213,213,,213,213,213,,,,,',
',,,,,,,,,,,,,,213,,,213,,,213,213,,,213,,213,,,,213,,,,,,,,,213,,,,',
'213,213,213,213,,213,213,213,213,,,,,213,213,,,,214,214,214,213,214',
'213,213,213,214,214,,,,214,,214,214,214,214,214,214,214,,,,,,214,214',
'214,214,214,214,214,,,214,,,,,,,214,,,214,214,214,214,214,214,214,214',
',214,214,214,,214,214,,214,214,214,,,,,,,,,,,,,,,,,,,,214,,,214,,,214',
'214,,,214,,,,,,214,,,,,,,,,214,,,,,214,214,214,214,,214,214,214,214',
',,,,214,214,,,,215,215,215,214,215,214,214,214,215,215,,,,215,,215,215',
'215,215,215,215,215,,,,,,215,215,215,215,215,215,215,,,215,,,,,,,215',
',,215,215,215,215,215,215,215,215,,215,215,215,,215,215,,215,215,215',
',,,,,,,,,,,,,,,,,,,215,,,215,,,215,215,,,215,,,,,,215,,,,,,,,,215,,',
',,215,215,215,215,,215,215,215,215,,,,,215,215,,,,216,216,216,215,216',
'215,215,215,216,216,,,,216,,216,216,216,216,216,216,216,,,,,,216,216',
'216,216,216,216,216,,,216,,,,,,,216,,,216,216,216,216,216,216,216,216',
',216,216,216,,216,216,,216,216,216,,,,,,,,,,,,,,,,,,,,216,,,216,,,216',
'216,,,216,,,,,,216,,,,,,,,,216,,,,,216,216,216,216,,216,216,216,216',
',,,,216,216,,,,217,217,217,216,217,216,216,216,217,217,,,,217,,217,217',
'217,217,217,217,217,,,,,,217,217,217,217,217,217,217,,,217,,,,,,,217',
',,217,217,217,217,217,217,217,217,,217,217,217,,217,217,,217,217,217',
',,,,,,,,,,,,,,,,,,,217,,,217,,,217,217,,,217,,,,,,217,,,,,,,,,217,,',
',,217,217,217,217,,217,217,217,217,,,,,217,217,217,,,228,228,228,217',
'228,217,217,217,228,228,,,,228,,228,228,228,228,228,228,228,,,,,,228',
'228,228,228,228,228,228,,,228,,,,,,,228,,,228,228,228,228,228,228,228',
'228,,228,228,228,,228,228,,228,228,228,,,,,,,,,,,,,,,,,,,,228,,,228',
',,228,228,,,228,,,,,,228,,,,,,,,,228,,,,,228,228,228,228,,228,228,228',
'228,,,,,228,228,,,,231,231,231,228,231,228,228,228,231,231,,,,231,,231',
'231,231,231,231,231,231,,,,,,231,231,231,231,231,231,231,,,231,,,,,',
',231,,,231,231,231,231,231,231,231,231,,231,231,231,,231,231,,231,231',
'231,,,,,,,,,,,,,,,,,,,,231,,,231,,,231,231,,,231,,,,,,231,,,,,,,,,231',
',,,,231,231,231,231,,231,231,231,231,,,,,231,231,,,,232,232,232,231',
'232,231,231,231,232,232,,,,232,,232,232,232,232,232,232,232,,,,,,232',
'232,232,232,232,232,232,,,232,,,,,,,232,,,232,232,232,232,232,232,232',
'232,,232,232,232,,232,232,,232,232,232,,,,,,,,,,,,,,,,,,,,232,,,232',
',,232,232,,,232,,,,,,232,,,,,,,,,232,,,,,232,232,232,232,,232,232,232',
'232,,,,,232,232,,,,233,233,233,232,233,232,232,232,233,233,,,,233,,233',
'233,233,233,233,233,233,,,,,,233,233,233,233,233,233,233,,,233,,,,,',
',233,,,233,233,233,233,233,233,233,233,,233,233,233,,233,233,,233,233',
'233,,,,,,,,,,,,,,,,,,,,233,,,233,,,233,233,,,233,,,,,,233,,,,,,,,,233',
',,,,233,233,233,233,,233,233,233,233,,,,,233,233,,,,234,234,234,233',
'234,233,233,233,234,234,,,,234,,234,234,234,234,234,234,234,,,,,,234',
'234,234,234,234,234,234,,,234,,,,,,,234,,,234,234,234,234,234,234,234',
'234,,234,234,234,,234,234,,234,234,234,,,,,,,,,,,,,,,,,,,,234,,,234',
',,234,234,,,234,,,,,,234,,,,,,,,,234,,,,,234,234,234,234,,234,234,234',
'234,,,,,234,234,,,,235,235,235,234,235,234,234,234,235,235,,,,235,,235',
'235,235,235,235,235,235,,,,,,235,235,235,235,235,235,235,,,235,,,,,',
',235,,,235,235,235,235,235,235,235,235,,235,235,235,,235,235,,235,235',
'235,,,,,,,,,,,,,,,,,,,,235,,,235,,,235,235,,,235,,,,,,235,,,,,,,,,235',
',,,,235,235,235,235,,235,235,235,235,,,,,235,235,,,,236,236,236,235',
'236,235,235,235,236,236,,,,236,,236,236,236,236,236,236,236,,,,,,236',
'236,236,236,236,236,236,,,236,,,,,,,236,,,236,236,236,236,236,236,236',
'236,,236,236,236,,236,236,,236,236,236,,,,,,,,,,,,,,,,,,,,236,,,236',
',,236,236,,,236,,,,,,236,,,,,,,,,236,,,,,236,236,236,236,,236,236,236',
'236,,,,,236,236,,,,237,237,237,236,237,236,236,236,237,237,,,,237,,237',
'237,237,237,237,237,237,,,,,,237,237,237,237,237,237,237,,,237,,,,,',
',237,,,237,237,237,237,237,237,237,237,,237,237,237,,237,237,,237,237',
'237,,,,,,,,,,,,,,,,,,,,237,,,237,,,237,237,,,237,,,,,,237,,,,,,,,,237',
',,,,237,237,237,237,,237,237,237,237,,,,,237,237,,,,238,238,238,237',
'238,237,237,237,238,238,,,,238,,238,238,238,238,238,238,238,,,,,,238',
'238,238,238,238,238,238,,,238,,,,,,,238,,,238,238,238,238,238,238,238',
'238,,238,238,238,,238,238,,238,238,238,,,,,,,,,,,,,,,,,,,,238,,,238',
',,238,238,,,238,,,,,,238,,,,,,,,,238,,,,,238,238,238,238,,238,238,238',
'238,,,,,238,238,,,,239,239,239,238,239,238,238,238,239,239,,,,239,,239',
'239,239,239,239,239,239,,,,,,239,239,239,239,239,239,239,,,239,,,,,',
',239,,,239,239,239,239,239,239,239,239,,239,239,239,,239,239,,239,239',
'239,,,,,,,,,,,,,,,,,,,,239,,,239,,,239,239,,,239,,,,,,239,,,,,,,,,239',
',,,,239,239,239,239,,239,239,239,239,,,,,239,239,,,,240,240,240,239',
'240,239,239,239,240,240,,,,240,,240,240,240,240,240,240,240,,,,,,240',
'240,240,240,240,240,240,,,240,,,,,,,240,,,240,240,240,240,240,240,240',
'240,,240,240,240,,240,240,,240,240,240,,,,,,,,,,,,,,,,,,,,240,,,240',
',,240,240,,,240,,,,,,240,,,,,,,,,240,,,,,240,240,240,240,,240,240,240',
'240,,,,,240,240,,,,241,241,241,240,241,240,240,240,241,241,,,,241,,241',
'241,241,241,241,241,241,,,,,,241,241,241,241,241,241,241,,,241,,,,,',
',241,,,241,241,241,241,241,241,241,241,,241,241,241,,241,241,,241,241',
'241,,,,,,,,,,,,,,,,,,,,241,,,241,,,241,241,,,241,,,,,,241,,,,,,,,,241',
',,,,241,241,241,241,,241,241,241,241,,,,,241,241,,,,242,242,242,241',
'242,241,241,241,242,242,,,,242,,242,242,242,242,242,242,242,,,,,,242',
'242,242,242,242,242,242,,,242,,,,,,,242,,,242,242,242,242,242,242,242',
'242,,242,242,242,,242,242,,242,242,242,,,,,,,,,,,,,,,,,,,,242,,,242',
',,242,242,,,242,,,,,,242,,,,,,,,,242,,,,,242,242,242,242,,242,242,242',
'242,,,,,242,242,,,,243,243,243,242,243,242,242,242,243,243,,,,243,,243',
'243,243,243,243,243,243,,,,,,243,243,243,243,243,243,243,,,243,,,,,',
',243,,,243,243,243,243,243,243,243,243,,243,243,243,,243,243,,243,243',
'243,,,,,,,,,,,,,,,,,,,,243,,,243,,,243,243,,,243,,,,,,243,,,,,,,,,243',
',,,,243,243,243,243,,243,243,243,243,,,,,243,243,,,,244,244,244,243',
'244,243,243,243,244,244,,,,244,,244,244,244,244,244,244,244,,,,,,244',
'244,244,244,244,244,244,,,244,,,,,,,244,,,244,244,244,244,244,244,244',
'244,,244,244,244,,244,244,,244,244,244,,,,,,,,,,,,,,,,,,,,244,,,244',
',,244,244,,,244,,,,,,244,,,,,,,,,244,,,,,244,244,244,244,,244,244,244',
'244,,,,,244,244,,,,245,245,245,244,245,244,244,244,245,245,,,,245,,245',
'245,245,245,245,245,245,,,,,,245,245,245,245,245,245,245,,,245,,,,,',
',245,,,245,245,245,245,245,245,245,245,,245,245,245,,245,245,,245,245',
'245,,,,,,,,,,,,,,,,,,,,245,,,245,,,245,245,,,245,,,,,,245,,,,,,,,,245',
',,,,245,245,245,245,,245,245,245,245,,,,,245,245,,,,246,246,246,245',
'246,245,245,245,246,246,,,,246,,246,246,246,246,246,246,246,,,,,,246',
'246,246,246,246,246,246,,,246,,,,,,,246,,,246,246,246,246,246,246,246',
'246,,246,246,246,,246,246,,246,246,246,,,,,,,,,,,,,,,,,,,,246,,,246',
',,246,246,,,246,,,,,,246,,,,,,,,,246,,,,,246,246,246,246,,246,246,246',
'246,,,,,246,246,,,,247,247,247,246,247,246,246,246,247,247,,,,247,,247',
'247,247,247,247,247,247,,,,,,247,247,247,247,247,247,247,,,247,,,,,',
',247,,,247,247,247,247,247,247,247,247,,247,247,247,,247,247,,247,247',
'247,,,,,,,,,,,,,,,,,,,,247,,,247,,,247,247,,,247,,,,,,247,,,,,,,,,247',
',,,,247,247,247,247,,247,247,247,247,,,,,247,247,,,,248,248,248,247',
'248,247,247,247,248,248,,,,248,,248,248,248,248,248,248,248,,,,,,248',
'248,248,248,248,248,248,,,248,,,,,,,248,,,248,248,248,248,248,248,248',
'248,,248,248,248,,248,248,,248,248,248,,,,,,,,,,,,,,,,,,,,248,,,248',
',,248,248,,,248,,,,,,248,,,,,,,,,248,,,,,248,248,248,248,,248,248,248',
'248,,,,,248,248,,,,249,249,249,248,249,248,248,248,249,249,,,,249,,249',
'249,249,249,249,249,249,,,,,,249,249,249,249,249,249,249,,,249,,,,,',
',249,,,249,249,249,249,249,249,249,249,,249,249,249,,249,249,,249,249',
'249,,,,,,,,,,,,,,,,,,,,249,,,249,,,249,249,,,249,,,,,,249,,,,,,,,,249',
',,,,249,249,249,249,,249,249,249,249,,,,,249,249,,,,250,250,250,249',
'250,249,249,249,250,250,,,,250,,250,250,250,250,250,250,250,,,,,,250',
'250,250,250,250,250,250,,,250,,,,,,,250,,,250,250,250,250,250,250,250',
'250,,250,250,250,,250,250,,250,250,250,,,,,,,,,,,,,,,,,,,,250,,,250',
',,250,250,,,250,,,,,,250,,,,,,,,,250,,,,,250,250,250,250,,250,250,250',
'250,,,,,250,250,,,,251,251,251,250,251,250,250,250,251,251,,,,251,,251',
'251,251,251,251,251,251,,,,,,251,251,251,251,251,251,251,,,251,,,,,',
',251,,,251,251,251,251,251,251,251,251,,251,251,251,,251,251,,251,251',
'251,,,,,,,,,,,,,,,,,,,,251,,,251,,,251,251,,,251,,,,,,251,,,,,,,,,251',
',,,,251,251,251,251,,251,251,251,251,,,,,251,251,,,,252,252,252,251',
'252,251,251,251,252,252,,,,252,,252,252,252,252,252,252,252,,,,,,252',
'252,252,252,252,252,252,,,252,,,,,,,252,,,252,252,252,252,252,252,252',
'252,,252,252,252,,252,252,,252,252,252,,,,,,,,,,,,,,,,,,,,252,,,252',
',,252,252,,,252,,,,,,252,,,,,,,,,252,,,,,252,252,252,252,,252,252,252',
'252,,,,,252,252,,,,253,253,253,252,253,252,252,252,253,253,,,,253,,253',
'253,253,253,253,253,253,,,,,,253,253,253,253,253,253,253,,,253,,,,,',
',253,,,253,253,253,253,253,253,253,253,,253,253,253,,253,253,,253,253',
'253,,,,,,,,,,,,,,,,,,,,253,,,253,,,253,253,,,253,,,,,,253,,,,,,,,,253',
',,,,253,253,253,253,,253,253,253,253,,,,,253,253,,,,254,254,254,253',
'254,253,253,253,254,254,,,,254,,254,254,254,254,254,254,254,,,,,,254',
'254,254,254,254,254,254,,,254,,,,,,,254,,,254,254,254,254,254,254,254',
'254,,254,254,254,,254,254,,254,254,254,,,,,,,,,,,,,,,,,,,,254,,,254',
',,254,254,,,254,,,,,,254,,,,,,,,,254,,,,,254,254,254,254,,254,254,254',
'254,,,,,254,254,,,,255,255,255,254,255,254,254,254,255,255,,,,255,,255',
'255,255,255,255,255,255,,,,,,255,255,255,255,255,255,255,,,255,,,,,',
',255,,,255,255,255,255,255,255,255,255,,255,255,255,,255,255,,255,255',
'255,,,,,,,,,,,,,,,,,,,,255,,,255,,,255,255,,,255,,,,,,255,,,,,,,,,255',
',,,,255,255,255,255,,255,255,255,255,,,,,255,255,,,,263,263,263,255',
'263,255,255,255,263,263,,,,263,,263,263,263,263,263,263,263,,,,,,263',
'263,263,263,263,263,263,,,263,,,,,,,263,,,263,263,263,263,263,263,263',
'263,263,263,263,263,,263,263,,263,263,263,,,,,,,,,,,,,,,,,,,,263,,,263',
',,263,263,,,263,,263,,263,,263,,,263,,,,,,263,,,,,263,263,263,263,,263',
'263,263,263,,,,,263,263,,,,264,264,264,263,264,263,263,263,264,264,',
',,264,,264,264,264,264,264,264,264,,,,,,264,264,264,264,264,264,264',
',,264,,,,,,,264,,,264,264,264,264,264,264,264,264,264,264,264,264,,264',
'264,,264,264,264,,,,,,,,,,,,,,,,,,,,264,,,264,,,264,264,,,264,,264,',
'264,,264,,,264,,,,,,264,,,,,264,264,264,264,,264,264,264,264,,,,,264',
'264,,,,272,272,272,264,272,264,264,264,272,272,,,,272,,272,272,272,272',
'272,272,272,,,,,,272,272,272,272,272,272,272,,,272,,,,,,,272,,,272,272',
'272,272,272,272,272,272,272,272,272,272,,272,272,,272,272,272,,,,,,',
',,,,,,,,,,,,,272,,,272,,,272,272,,,272,,272,,272,,272,,,272,,,,,,272',
',,,,272,272,272,272,,272,272,272,272,,,,,272,272,272,,,279,279,279,272',
'279,272,272,272,279,279,,,,279,,279,279,279,279,279,279,279,,,,,,279',
'279,279,279,279,279,279,,,279,,,,,,,279,,,279,279,279,279,279,279,279',
'279,,279,279,279,,279,279,,279,279,279,,,,,,,,,,,,,,,,,,,,279,,,279',
',,279,279,,,279,,,,,,279,,,,,,,,,279,,,,,279,279,279,279,,279,279,279',
'279,,,,,279,279,,,,281,281,281,279,281,279,279,279,281,281,,,,281,,281',
'281,281,281,281,281,281,,,,,,281,281,281,281,281,281,281,,,281,,,,,',
',281,,,281,281,281,281,281,281,281,281,,281,281,281,,281,281,,281,281',
'281,,,,,,,,,,,,,,,,,,,,281,,,281,,,281,281,,,281,,,,,,281,,,,,,,,,281',
',,,,281,281,281,281,,281,281,281,281,,,,,281,281,,,,284,284,284,281',
'284,281,281,281,284,284,,,,284,,284,284,284,284,284,284,284,,,,,,284',
'284,284,284,284,284,284,,,284,,,,,,,284,,,284,284,284,284,284,284,284',
'284,,284,284,284,,284,284,,284,284,284,,,,,,,,,,,,,,,,,,,,284,,,284',
',,284,284,,,284,,,,,,284,,,,,,,,,284,,,,,284,284,284,284,,284,284,284',
'284,,,,,284,284,,,,285,285,285,284,285,284,284,284,285,285,,,,285,,285',
'285,285,285,285,285,285,,,,,,285,285,285,285,285,285,285,,,285,,,,,',
',285,,,285,285,285,285,285,285,285,285,,285,285,285,,285,285,,285,285',
'285,,,,,,,,,,,,,,,,,,,,285,,,285,,,285,285,,,285,,,,,,285,,,,,,,,,285',
',,,,285,285,285,285,,285,285,285,285,,,,,285,285,,,,,,,285,,285,285',
'285,290,290,290,290,290,,,,290,290,,,,290,,290,290,290,290,290,290,290',
',,,,,290,290,290,290,290,290,290,,,290,,,,,,290,290,,290,290,290,290',
'290,290,290,290,290,,290,290,290,,290,290,,290,290,290,,,,,,,,,,,,,',
',,,,,,290,,,290,,,290,290,,,290,,290,,,,290,,,,,,,,,290,,,,,290,290',
'290,290,,290,290,290,290,,,,,290,290,,,,298,298,298,290,298,290,290',
'290,298,298,,,,298,,298,298,298,298,298,298,298,,,,,,298,298,298,298',
'298,298,298,,,298,,,,,,,298,,,298,298,298,298,298,298,298,298,,298,298',
'298,,298,298,,,,298,,,,,,,,,,,,,,,,,,,,298,,,298,,,298,298,,,298,,895',
',895,895,895,895,895,,,,,,,,,,895,,298,298,298,298,,298,298,298,298',
',,,,298,298,,,,298,,895,298,,298,298,298,315,315,315,,315,895,895,,315',
'315,895,,,315,,315,315,315,315,315,315,315,,,,,,315,315,315,315,315',
'315,315,,,315,,,,,,,315,,,315,315,315,315,315,315,315,315,,315,315,315',
',315,315,,,,315,,,,,,,,,,,,,,,,,,,,315,,,315,,,315,315,,,315,,,,,,,',
',,,,,,,,,,,,315,315,315,315,,315,315,315,315,,,,,315,315,,,,323,323',
'323,315,323,315,315,315,323,323,,,,323,,323,323,323,323,323,323,323',
',,,,,323,323,323,323,323,323,323,,,323,,,,,,,323,,,323,323,323,323,323',
'323,323,323,,323,323,323,,323,323,,323,323,323,,,,,,,,,,,,,,,,,,,,323',
',,323,323,,323,323,,,323,,,,,,323,,,,,,,,,323,,,,,323,323,323,323,,323',
'323,323,323,,,,,323,323,,,,325,325,325,323,325,323,323,323,325,325,',
',,325,,325,325,325,325,325,325,325,,,,,,325,325,325,325,325,325,325',
',,325,,,,,,,325,,,325,325,325,325,325,325,325,325,,325,325,325,,325',
'325,,325,325,325,,,,,,,,,,,,,,,,,,,,325,,,325,,,325,325,,,325,,,,,,325',
',,,,,,,,325,,,,,325,325,325,325,,325,325,325,325,,,,,325,325,,,,340',
'340,340,325,340,325,325,325,340,340,,,,340,,340,340,340,340,340,340',
'340,,,,,,340,340,340,340,340,340,340,,,340,,,,,,,340,,,340,340,340,340',
'340,340,340,340,,340,340,340,,340,340,,340,340,340,,,,,,,,,,,,,,,,,',
',,340,,,340,,,340,340,,,340,,,,,,340,,,,,,,,,340,,,,,340,340,340,340',
',340,340,340,340,,,,,340,340,,,,341,341,341,340,341,340,340,340,341',
'341,,,,341,,341,341,341,341,341,341,341,,,,,,341,341,341,341,341,341',
'341,,,341,,,,,,,341,,,341,341,341,341,341,341,341,341,,341,341,341,',
'341,341,,341,341,341,,,,,,,,,,,,,,,,,,,,341,,,341,,,341,341,,,341,,',
',,,341,,,,,,,,,341,,,,,341,341,341,341,,341,341,341,341,,,,,341,341',
',,,360,360,360,341,360,341,341,341,360,360,,,,360,,360,360,360,360,360',
'360,360,,,,,,360,360,360,360,360,360,360,,,360,,,,,,,360,,,360,360,360',
'360,360,360,360,360,,360,360,360,,360,360,,360,360,360,,,,,,,,,,,,,',
',,,,,,360,,,360,,,360,360,,,360,,,,,,360,,,,,,,,,360,,,,,360,360,360',
'360,,360,360,360,360,,,,,360,360,,,,376,376,376,360,376,360,360,360',
'376,376,,,,376,,376,376,376,376,376,376,376,,,,,,376,376,376,376,376',
'376,376,,,376,,,,,,,376,,,376,376,376,376,376,376,376,376,,376,376,376',
',376,376,,376,376,376,,,,,,,,,,,,,,,,,,,,376,,,376,,,376,376,,,376,',
',,,,376,,,,,,,,,376,,,,,376,376,376,376,,376,376,376,376,,,,,376,376',
',,,404,404,404,376,404,376,376,376,404,404,,,,404,,404,404,404,404,404',
'404,404,,,,,,404,404,404,404,404,404,404,,,404,,,,,,,404,,,404,404,404',
'404,404,404,404,404,,404,404,404,,404,404,,404,404,404,,,,,,,,,,,,,',
',,,,,,404,,,404,,,404,404,,,404,,,,,,404,,,,,,,,,404,,,,,404,404,404',
'404,,404,404,404,404,,,,,404,404,,,,442,442,442,404,442,404,404,404',
'442,442,,,,442,,442,442,442,442,442,442,442,,,,,,442,442,442,442,442',
'442,442,,,442,,,,,,,442,,,442,442,442,442,442,442,442,442,442,442,442',
'442,,442,442,,442,442,442,,,,,,,,,,,,,,,,,,,,442,,,442,,,442,442,,,442',
',442,,442,,442,,,442,,,,,,442,,,,,442,442,442,442,,442,442,442,442,',
',,,442,442,,,,444,444,444,442,444,442,442,442,444,444,,,,444,,444,444',
'444,444,444,444,444,,,,,,444,444,444,444,444,444,444,,,444,,,,,,,444',
',,444,444,444,444,444,444,444,444,,444,444,444,,444,444,,444,444,444',
',,,,,,,,,,,,,,,,,,,444,,,444,,,444,444,,,444,,,,,,444,,,,,,,,,444,,',
',,444,444,444,444,,444,444,444,444,,,,,444,444,,,,445,445,445,444,445',
'444,444,444,445,445,,,,445,,445,445,445,445,445,445,445,,,,,,445,445',
'445,445,445,445,445,,,445,,,,,,,445,,,445,445,445,445,445,445,445,445',
',445,445,445,,445,445,,445,445,445,,,,,,,,,,,,,,,,,,,,445,,,445,,,445',
'445,,,445,,,,,,445,,,,,,,,,445,,,,,445,445,445,445,,445,445,445,445',
',,,,445,445,,,,446,446,446,445,446,445,445,445,446,446,,,,446,,446,446',
'446,446,446,446,446,,,,,,446,446,446,446,446,446,446,,,446,,,,,,,446',
',,446,446,446,446,446,446,446,446,,446,446,446,,446,446,,446,446,446',
',,,,,,,,,,,,,,,,,,,446,,,446,,,446,446,,,446,,,,,,446,,,,,,,,,446,,',
',,446,446,446,446,,446,446,446,446,,,,,446,446,,,,472,472,472,446,472',
'446,446,446,472,472,,,,472,,472,472,472,472,472,472,472,,,,,,472,472',
'472,472,472,472,472,,,472,,,,,,,472,,,472,472,472,472,472,472,472,472',
',472,472,472,,472,472,,472,472,472,,,,,,,,,,,,,,,,,,,,472,,,472,,,472',
'472,,,472,,,,,,472,,,,,,,,,472,,,,,472,472,472,472,,472,472,472,472',
',,,,472,472,,,,486,486,486,472,486,472,472,472,486,486,,,,486,,486,486',
'486,486,486,486,486,,,,,,486,486,486,486,486,486,486,,,486,,,,,,,486',
',,486,486,486,486,486,486,486,486,486,486,486,486,,486,486,,486,486',
'486,,,,,,,,,,,,,,,,,,,,486,,,486,,,486,486,,,486,,486,,486,,486,,,486',
',,,,,486,,,,,486,486,486,486,,486,486,486,486,,,,,486,486,,,,488,488',
'488,486,488,486,486,486,488,488,,,,488,,488,488,488,488,488,488,488',
',,,,,488,488,488,488,488,488,488,,,488,,,,,,,488,,,488,488,488,488,488',
'488,488,488,488,488,488,488,,488,488,,488,488,488,,,,,,,,,,,,,,,,,,',
',488,,,488,,,488,488,,,488,,,,488,,488,,,488,,,,,,488,,,,,488,488,488',
'488,,488,488,488,488,,,,,488,488,,,,490,490,490,488,490,488,488,488',
'490,490,,,,490,,490,490,490,490,490,490,490,,,,,,490,490,490,490,490',
'490,490,,,490,,,,,,,490,,,490,490,490,490,490,490,490,490,,490,490,490',
',490,490,,490,490,490,,,,,,,,,,,,,,,,,,,,490,,,490,,,490,490,,,490,',
',,,,490,,,,,,,,,490,,,,,490,490,490,490,,490,490,490,490,,,,,490,490',
',,,,,,490,,490,490,490,496,496,496,496,496,,,,496,496,,,,496,,496,496',
'496,496,496,496,496,,,,,,496,496,496,496,496,496,496,,,496,,,,,,496',
'496,496,496,496,496,496,496,496,496,496,496,,496,496,496,,496,496,,496',
'496,496,,,,,,,,,,,,,,,,,,,,496,,,496,,,496,496,,,496,,496,,,,496,,,',
',,,,,496,,,,,496,496,496,496,,496,496,496,496,,,,,496,496,,,,,,496,496',
',496,496,496,504,504,504,,504,,,,504,504,,,,504,,504,504,504,504,504',
'504,504,,,,,,504,504,504,504,504,504,504,,,504,,,,,,,504,,,504,504,504',
'504,504,504,504,504,,504,504,504,,504,504,,,,504,,,,,,,,,,,,,,,,,,,',
'504,,,504,,,504,504,,,504,,,,,,,,,,,,,,,,,,,,504,504,504,504,,504,504',
'504,504,,,,,504,504,,,,506,506,506,504,506,504,504,504,506,506,,,,506',
',506,506,506,506,506,506,506,,,,,,506,506,506,506,506,506,506,,,506',
',,,,,,506,,,506,506,506,506,506,506,506,506,506,506,506,506,,506,506',
',506,506,506,,,,,,,,,,,,,,,,,,,,506,,,506,,,506,506,,,506,,506,,506',
',506,,,506,,,,,,506,,,,,506,506,506,506,,506,506,506,506,,,,,506,506',
',,,512,512,512,506,512,506,506,506,512,512,,,,512,,512,512,512,512,512',
'512,512,,,,,,512,512,512,512,512,512,512,,,512,,,,,,,512,,,512,512,512',
'512,512,512,512,512,,512,512,512,,512,512,,,,512,,,,,,,,,,,,,,,,,,,',
'512,,,512,,,512,512,,,512,,,,,,,,,,,,,,,,,,,,512,512,512,512,,512,512',
'512,512,,,,,512,512,,,,515,515,515,512,515,512,512,512,515,515,,,,515',
',515,515,515,515,515,515,515,,,,,,515,515,515,515,515,515,515,,,515',
',,,,,,515,,,515,515,515,515,515,515,515,515,,515,515,515,,515,515,,515',
'515,515,,,,,,,,,,,,,,,,,,,,515,,,515,,,515,515,,,515,,,,,,515,,,,,,',
',,515,,,,,515,515,515,515,,515,515,515,515,,,,,515,515,,,,516,516,516',
'515,516,515,515,515,516,516,,,,516,,516,516,516,516,516,516,516,,,,',
',516,516,516,516,516,516,516,,,516,,,,,,,516,,,516,516,516,516,516,516',
'516,516,,516,516,516,,516,516,,516,516,516,,,,,,,,,,,,,,,,,,,,516,,',
'516,,,516,516,,,516,,,,,,516,,,,,,,,,516,,,,,516,516,516,516,,516,516',
'516,516,,,,,516,516,,,,520,520,520,516,520,516,516,516,520,520,,,,520',
',520,520,520,520,520,520,520,,,,,,520,520,520,520,520,520,520,,,520',
',,,,,,520,,,520,520,520,520,520,520,520,520,,520,520,520,,520,520,,520',
'520,520,,,,,,,,,,,,,,,,,,,,520,,,520,,,520,520,,,520,,,,,,520,,,,,,',
',,520,,,,,520,520,520,520,,520,520,520,520,,,,,520,520,,,,526,526,526',
'520,526,520,520,520,526,526,,,,526,,526,526,526,526,526,526,526,,,,',
',526,526,526,526,526,526,526,,,526,,,,,,,526,,,526,526,526,526,526,526',
'526,526,526,526,526,526,,526,526,,526,526,526,,,,,,,,,,,,,,,,,,,,526',
',,526,,,526,526,,,526,,526,,,,526,,,526,,,,,,526,,,,,526,526,526,526',
',526,526,526,526,,,,,526,526,,,,529,529,529,526,529,526,526,526,529',
'529,,,,529,,529,529,529,529,529,529,529,,,,,,529,529,529,529,529,529',
'529,,,529,,,,,,,529,,,529,529,529,529,529,529,529,529,529,529,529,529',
',529,529,,529,529,529,,,,,,,,,,,,,,,,,,,,529,,,529,,,529,529,,,529,',
',,,,529,,,529,,,,,,529,,,,,529,529,529,529,,529,529,529,529,,,,,529',
'529,,,,554,554,554,529,554,529,529,529,554,554,,,,554,,554,554,554,554',
'554,554,554,,,,,,554,554,554,554,554,554,554,,,554,,,,,,,554,,,554,554',
'554,554,554,554,554,554,,554,554,554,,554,554,,554,554,554,,,,,,,,,',
',,,,,,,,,,554,,,554,,,554,554,,,554,,,,,,554,,,,,,,,,554,,,,,554,554',
'554,554,,554,554,554,554,,,,,554,554,,,,574,574,574,554,574,554,554',
'554,574,574,,,,574,,574,574,574,574,574,574,574,,,,,,574,574,574,574',
'574,574,574,,,574,,,,,,,574,,,574,574,574,574,574,574,574,574,,574,574',
'574,,574,574,,574,574,574,,,,,,,,,,,,,,,,,,,,574,,,574,,,574,574,,,574',
',574,,,,574,,,,,,,,,574,,,,,574,574,574,574,,574,574,574,574,,,,,574',
'574,,,,575,575,575,574,575,574,574,574,575,575,,,,575,,575,575,575,575',
'575,575,575,,,,,,575,575,575,575,575,575,575,,,575,,,,,,,575,,,575,575',
'575,575,575,575,575,575,575,575,575,575,,575,575,,575,575,575,,,,,,',
',,,,,,,,,,,,,575,,,575,,,575,575,,,575,,575,,575,,575,,,575,,,,,,575',
',,,,575,575,575,575,,575,575,575,575,,,,,575,575,,,,585,585,585,575',
'585,575,575,575,585,585,,,,585,,585,585,585,585,585,585,585,,,,,,585',
'585,585,585,585,585,585,,,585,,,,,,,585,,,585,585,585,585,585,585,585',
'585,585,585,585,585,,585,585,,585,585,585,,,,,,,,,,,,,,,,,,,,585,,,585',
',,585,585,,,585,,585,,585,,585,,,585,,,,,,585,,,,,585,585,585,585,,585',
'585,585,585,,,,,585,585,,,,619,619,619,585,619,585,585,585,619,619,',
',,619,,619,619,619,619,619,619,619,,,,,,619,619,619,619,619,619,619',
',,619,,,,,,,619,,,619,619,619,619,619,619,619,619,,619,619,619,,619',
'619,,619,619,619,,,,,,,,,,,,,,,,,,,,619,,,619,,,619,619,,,619,,619,',
',,619,,,,,,,,,619,,,,,619,619,619,619,,619,619,619,619,,,,,619,619,',
',,620,620,620,619,620,619,619,619,620,620,,,,620,,620,620,620,620,620',
'620,620,,,,,,620,620,620,620,620,620,620,,,620,,,,,,,620,,,620,620,620',
'620,620,620,620,620,,620,620,620,,620,620,,620,620,620,,,,,,,,,,,,,',
',,,,,,620,,,620,,,620,620,,,620,,,,,,620,,,,,,,,,620,,,,,620,620,620',
'620,,620,620,620,620,,,,,620,620,,,,623,623,623,620,623,620,620,620',
'623,623,,,,623,,623,623,623,623,623,623,623,,,,,,623,623,623,623,623',
'623,623,,,623,,,,,,,623,,,623,623,623,623,623,623,623,623,623,623,623',
'623,,623,623,,623,623,623,,,,,,,,,,,,,,,,,,,,623,,,623,,,623,623,,,623',
',623,,623,,623,,,623,,,,,,623,,,,,623,623,623,623,,623,623,623,623,',
',,,623,623,,,,624,624,624,623,624,623,623,623,624,624,,,,624,,624,624',
'624,624,624,624,624,,,,,,624,624,624,624,624,624,624,,,624,,,,,,,624',
',,624,624,624,624,624,624,624,624,624,624,624,624,,624,624,,624,624',
'624,,,,,,,,,,,,,,,,,,,,624,,,624,,,624,624,,,624,,,,624,,624,,,624,',
',,,,624,,,,,624,624,624,624,,624,624,624,624,,,,,624,624,,,,625,625',
'625,624,625,624,624,624,625,625,,,,625,,625,625,625,625,625,625,625',
',,,,,625,625,625,625,625,625,625,,,625,,,,,,,625,,,625,625,625,625,625',
'625,625,625,,625,625,625,,625,625,,625,625,625,,,,,,,,,,,,,,,,,,,,625',
',,625,,,625,625,,,625,,,,,,625,,,,,,,,,625,,,,,625,625,625,625,,625',
'625,625,625,,,,,625,625,,,,626,626,626,625,626,625,625,625,626,626,',
',,626,,626,626,626,626,626,626,626,,,,,,626,626,626,626,626,626,626',
',,626,,,,,,,626,,,626,626,626,626,626,626,626,626,,626,626,626,,626',
'626,,626,626,626,,,,,,,,,,,,,,,,,,,,626,,,626,,,626,626,,,626,,,,,,626',
',,,,,,,,626,,,,,626,626,626,626,,626,626,626,626,,,,,626,626,,,,630',
'630,630,626,630,626,626,626,630,630,,,,630,,630,630,630,630,630,630',
'630,,,,,,630,630,630,630,630,630,630,,,630,,,,,,,630,,,630,630,630,630',
'630,630,630,630,,630,630,630,,630,630,,630,630,630,,,,,,,,,,,,,,,,,',
',,630,,,630,,,630,630,,,630,,,,,,630,,,,,,,,,630,,,,,630,630,630,630',
',630,630,630,630,,,,,630,630,,,,631,631,631,630,631,630,630,630,631',
'631,,,,631,,631,631,631,631,631,631,631,,,,,,631,631,631,631,631,631',
'631,,,631,,,,,,,631,,,631,631,631,631,631,631,631,631,,631,631,631,',
'631,631,,631,631,631,,,,,,,,,,,,,,,,,,,,631,,,631,,,631,631,,,631,,',
',,,631,,,,,,,,,631,,,,,631,631,631,631,,631,631,631,631,,,,,631,631',
',,,634,634,634,631,634,631,631,631,634,634,,,,634,,634,634,634,634,634',
'634,634,,,,,,634,634,634,634,634,634,634,,,634,,,,,,,634,,,634,634,634',
'634,634,634,634,634,,634,634,634,,634,634,,634,634,634,,,,,,,,,,,,,',
',,,,,,634,,,634,,,634,634,,,634,,,,,,634,,,,,,,,,634,,,,,634,634,634',
'634,,634,634,634,634,,,,,634,634,,,,635,635,635,634,635,634,634,634',
'635,635,,,,635,,635,635,635,635,635,635,635,,,,,,635,635,635,635,635',
'635,635,,,635,,,,,,,635,,,635,635,635,635,635,635,635,635,,635,635,635',
',635,635,,635,635,635,,,,,,,,,,,,,,,,,,,,635,,,635,,,635,635,,,635,',
',,,,635,,,,,,,,,635,,,,,635,635,635,635,,635,635,635,635,,,,,635,635',
',,,659,659,659,635,659,635,635,635,659,659,,,,659,,659,659,659,659,659',
'659,659,,,,,,659,659,659,659,659,659,659,,,659,,,,,,,659,,,659,659,659',
'659,659,659,659,659,,659,659,659,,659,659,,659,659,659,,,,,,,,,,,,,',
',,,,,,659,,,659,,,659,659,,,659,,,,,,659,,,,,,,,,659,,,,,659,659,659',
'659,,659,659,659,659,,,,,659,659,,,,662,662,662,659,662,659,659,659',
'662,662,,,,662,,662,662,662,662,662,662,662,,,,,,662,662,662,662,662',
'662,662,,,662,,,,,,,662,,,662,662,662,662,662,662,662,662,,662,662,662',
',662,662,,662,662,662,,,,,,,,,,,,,,,,,,,,662,,,662,,,662,662,,,662,',
',,,,662,,,,,,,,,662,,,,,662,662,662,662,,662,662,662,662,,,,,662,662',
',,,666,666,666,662,666,662,662,662,666,666,,,,666,,666,666,666,666,666',
'666,666,,,,,,666,666,666,666,666,666,666,,,666,,,,,,,666,,,666,666,666',
'666,666,666,666,666,,666,666,666,,666,666,,,,666,,,,,,,,,,,,,,,,,,,',
'666,,,666,,,666,666,,,666,,,,,,,,,,,,,,,,,,,,666,666,666,666,,666,666',
'666,666,,,,,666,666,,,,677,677,677,666,677,666,666,666,677,677,,,,677',
',677,677,677,677,677,677,677,,,,,,677,677,677,677,677,677,677,,,677',
',,,,,,677,,,677,677,677,677,677,677,677,677,,677,677,677,,677,677,,',
',677,,,,,,,,,,,,,,,,,,,,677,,,677,,,677,677,,,677,,,,,,,,,,,,,,,,,,',
',677,677,677,677,,677,677,677,677,,,,,677,677,,,,682,682,682,677,682',
'677,677,677,682,682,,,,682,,682,682,682,682,682,682,682,,,,,,682,682',
'682,682,682,682,682,,,682,,,,,,,682,,,682,682,682,682,682,682,682,682',
',682,682,682,,682,682,,682,682,682,,,,,,,,,,,,,,,,,,,,682,,,682,,,682',
'682,,,682,,682,,,,682,,,,,,,,,682,,,,,682,682,682,682,,682,682,682,682',
',,,,682,682,,,,699,699,699,682,699,682,682,682,699,699,,,,699,,699,699',
'699,699,699,699,699,,,,,,699,699,699,699,699,699,699,,,699,,,,,,,699',
',,699,699,699,699,699,699,699,699,,699,699,699,,699,699,,699,699,699',
',,,,,,,,,,,,,,,,,,,699,,,699,,,699,699,,,699,,,,,,699,,,,,,,,,699,,',
',,699,699,699,699,,699,699,699,699,,,,,699,699,,,,725,725,725,699,725',
'699,699,699,725,725,,,,725,,725,725,725,725,725,725,725,,,,,,725,725',
'725,725,725,725,725,,,725,,,,,,,725,,,725,725,725,725,725,725,725,725',
',725,725,725,,725,725,,725,725,725,,,,,,,,,,,,,,,,,,,,725,,,725,,,725',
'725,,,725,,,,,,725,,,,,,,,,725,,,,,725,725,725,725,,725,725,725,725',
',,,,725,725,,,,731,731,731,725,731,725,725,725,731,731,,,,731,,731,731',
'731,731,731,731,731,,,,,,731,731,731,731,731,731,731,,,731,,,,,,,731',
',,731,731,731,731,731,731,731,731,,731,731,731,,731,731,,731,731,731',
',,,,,,,,,,,,,,,,,,,731,,,731,,,731,731,,,731,,,,,,731,,,,,,,,,731,,',
',,731,731,731,731,,731,731,731,731,,,,,731,731,,,,754,754,754,731,754',
'731,731,731,754,754,,,,754,,754,754,754,754,754,754,754,,,,,,754,754',
'754,754,754,754,754,,,754,,,,,,,754,,,754,754,754,754,754,754,754,754',
',754,754,754,,754,754,,754,754,754,,,,,,,,,,,,,,,,,,,,754,,,754,,,754',
'754,,,754,,,,,,754,,,,,,,,,754,,,,,754,754,754,754,,754,754,754,754',
',,,,754,754,,,,756,756,756,754,756,754,754,754,756,756,,,,756,,756,756',
'756,756,756,756,756,,,,,,756,756,756,756,756,756,756,,,756,,,,,,,756',
',,756,756,756,756,756,756,756,756,,756,756,756,,756,756,,756,756,756',
',,,,,,,,,,,,,,,,,,,756,,,756,,,756,756,,,756,,,,,,756,,,,,,,,,756,,',
',,756,756,756,756,,756,756,756,756,,,,,756,756,,,,770,770,770,756,770',
'756,756,756,770,770,,,,770,,770,770,770,770,770,770,770,,,,,,770,770',
'770,770,770,770,770,,,770,,,,,,,770,,,770,770,770,770,770,770,770,770',
',770,770,770,,770,770,,770,770,770,,,,,,,,,,,,,,,,,,,,770,,,770,,,770',
'770,,,770,,,,,,770,,,,,,,,,770,,,,,770,770,770,770,,770,770,770,770',
',,,,770,770,,,,771,771,771,770,771,770,770,770,771,771,,,,771,,771,771',
'771,771,771,771,771,,,,,,771,771,771,771,771,771,771,,,771,,,,,,,771',
',,771,771,771,771,771,771,771,771,,771,771,771,,771,771,,771,771,771',
',,,,,,,,,,,,,,,,,,,771,,,771,,,771,771,,,771,,,,,,771,,,,,,,,,771,,',
',,771,771,771,771,,771,771,771,771,,,,,771,771,,,,772,772,772,771,772',
'771,771,771,772,772,,,,772,,772,772,772,772,772,772,772,,,,,,772,772',
'772,772,772,772,772,,,772,,,,,,,772,,,772,772,772,772,772,772,772,772',
',772,772,772,,772,772,,772,772,772,,,,,,,,,,,,,,,,,,,,772,,,772,,,772',
'772,,,772,,,,,,772,,,,,,,,,772,,,,,772,772,772,772,,772,772,772,772',
',,,,772,772,,,,773,773,773,772,773,772,772,772,773,773,,,,773,,773,773',
'773,773,773,773,773,,,,,,773,773,773,773,773,773,773,,,773,,,,,,,773',
',,773,773,773,773,773,773,773,773,,773,773,773,,773,773,,773,773,773',
',,,,,,,,,,,,,,,,,,,773,,,773,,,773,773,,,773,,,,,,773,,,,,,,,,773,,',
',,773,773,773,773,,773,773,773,773,,,,,773,773,,,,787,787,787,773,787',
'773,773,773,787,787,,,,787,,787,787,787,787,787,787,787,,,,,,787,787',
'787,787,787,787,787,,,787,,,,,,,787,,,787,787,787,787,787,787,787,787',
',787,787,787,,787,787,,,,787,,,,,,,,,,,,,,,,,,,,787,,,787,,,787,787',
',,787,,,,,,,,,,,,,,,,,,,,787,787,787,787,,787,787,787,787,,,,,787,787',
',,,837,837,837,787,837,787,787,787,837,837,,,,837,,837,837,837,837,837',
'837,837,,,,,,837,837,837,837,837,837,837,,,837,,,,,,,837,,,837,837,837',
'837,837,837,837,837,,837,837,837,,837,837,,837,837,837,,,,,,,,,,,,,',
',,,,,,837,,,837,,,837,837,,,837,,,,,,837,,,,,,,,,837,,,,,837,837,837',
'837,,837,837,837,837,,,,,837,837,,,,842,842,842,837,842,837,837,837',
'842,842,,,,842,,842,842,842,842,842,842,842,,,,,,842,842,842,842,842',
'842,842,,,842,,,,,,,842,,,842,842,842,842,842,842,842,842,,842,842,842',
',842,842,,842,842,842,,,,,,,,,,,,,,,,,,,,842,,,842,,,842,842,,,842,',
'842,,,,842,,,,,,,,,842,,,,,842,842,842,842,,842,842,842,842,,,,,842',
'842,,,,859,859,859,842,859,842,842,842,859,859,,,,859,,859,859,859,859',
'859,859,859,,,,,,859,859,859,859,859,859,859,,,859,,,,,,,859,,,859,859',
'859,859,859,859,859,859,859,859,859,859,,859,859,,859,859,859,,,,,,',
',,,,,,,,,,,,,859,,,859,,,859,859,,,859,,,,859,,859,,,859,,,,,,859,,',
',,859,859,859,859,,859,859,859,859,,,,,859,859,,,,860,860,860,859,860',
'859,859,859,860,860,,,,860,,860,860,860,860,860,860,860,,,,,,860,860',
'860,860,860,860,860,,,860,,,,,,,860,,,860,860,860,860,860,860,860,860',
',860,860,860,,860,860,,860,860,860,,,,,,,,,,,,,,,,,,,,860,,,860,,,860',
'860,,,860,,,,,,860,,,,,,,,,860,,,,,860,860,860,860,,860,860,860,860',
',,,,860,860,,,,874,874,874,860,874,860,860,860,874,874,,,,874,,874,874',
'874,874,874,874,874,,,,,,874,874,874,874,874,874,874,,,874,,,,,,,874',
',,874,874,874,874,874,874,874,874,,874,874,874,,874,874,,,,874,,,,,',
',,,,,,,,,,,,,,874,,,874,,,874,874,,,874,,,,,,,,,,,,,,,,,,,,874,874,874',
'874,,874,874,874,874,,,,,874,874,,,,886,886,886,874,886,874,874,874',
'886,886,,,,886,,886,886,886,886,886,886,886,,,,,,886,886,886,886,886',
'886,886,,,886,,,,,,,886,,,886,886,886,886,886,886,886,886,,886,886,886',
',886,886,,,,886,,,,,,,,,,,,,,,,,,,,886,,,886,,,886,886,,,886,,,,,,,',
',,,,,,,,,,,,886,886,886,886,,886,886,886,886,,,,,886,886,,,,923,923',
'923,886,923,886,886,886,923,923,,,,923,,923,923,923,923,923,923,923',
',,,,,923,923,923,923,923,923,923,,,923,,,,,,,923,,,923,923,923,923,923',
'923,923,923,,923,923,923,,923,923,,923,923,923,,,,,,,,,,,,,,,,,,,,923',
',,923,,,923,923,,,923,,,,,,923,,,,,,,,,923,,,,,923,923,923,923,,923',
'923,923,923,,,,,923,923,,,,985,985,985,923,985,923,923,923,985,985,',
',,985,,985,985,985,985,985,985,985,,,,,,985,985,985,985,985,985,985',
',,985,,,,,,,985,,,985,985,985,985,985,985,985,985,985,985,985,985,,985',
'985,,985,985,985,,,,,,,,,,,,,,,,,,,,985,,,985,,,985,985,,,985,,985,',
'985,,985,,,985,,,,,,985,,,,,985,985,985,985,,985,985,985,985,,,,,985',
'985,,,,,56,,985,,985,985,985,56,56,56,,,56,56,56,,56,,,,,,,,,,56,56',
'56,,,,,,,,56,56,,56,56,56,56,56,,,,,,,,,,,,,,,,,,,,,,,,56,56,56,56,56',
'56,56,56,56,56,56,56,56,56,,,56,56,56,,,56,,,56,,,56,56,,56,,56,,56',
',56,56,,56,56,56,56,56,,56,,56,,,,,,,,,,,,,,56,,,56,56,56,56,424,56',
',56,,,,424,424,424,,,424,424,424,,424,,,,,,,,,424,424,424,424,,,,,,',
',424,424,,424,424,424,424,424,,,,,,,,,,,,,,,,,,,,,,,,424,424,424,424',
'424,424,424,424,424,424,424,424,424,424,,,424,424,424,,,424,,,424,,',
'424,424,,424,,424,,424,,424,424,,424,424,424,424,424,,424,424,424,,',
',,,,,,,,,,,424,,,424,424,424,424,425,424,,424,,,,425,425,425,,,425,425',
'425,,425,,,,,,,,,425,425,425,425,,,,,,,,425,425,,425,425,425,425,425',
',,,,,,,,,,,,,,,,,,,,,,,425,425,425,425,425,425,425,425,425,425,425,425',
'425,425,,,425,425,425,,,425,,,425,,,425,425,,425,,425,,425,,425,425',
',425,425,425,425,425,,425,425,425,,,,,,,,,,,,,,425,,,425,425,425,425',
'27,425,,425,,,,27,27,27,,,27,27,27,,27,,,,,,,,,27,27,27,,,,,,,,,27,27',
',27,27,27,27,27,,,,,,,,,,,,,,,,,,,,,,,,27,27,27,27,27,27,27,27,27,27',
'27,27,27,27,,,27,27,27,,,27,,27,27,,,27,27,,27,,27,,27,,27,27,,27,27',
'27,27,27,28,27,27,27,,,,28,28,28,,,28,28,28,,28,27,,,27,27,,27,,27,28',
'28,,,,,,,,,28,28,,28,28,28,28,28,,,,,,,,,,,,,,,,,,,,,,,,28,28,28,28',
'28,28,28,28,28,28,28,28,28,28,,,28,28,28,,,28,,28,28,,,28,28,,28,,28',
',28,,28,28,,28,28,28,28,28,,28,415,28,,,,,,415,415,415,,,415,415,415',
'28,415,,28,28,,28,,28,,415,415,415,,,,,,,,,415,415,,415,415,415,415',
'415,,,,,,,,,,,,,,,,,,,,,,,,415,415,415,415,415,415,415,415,415,415,415',
'415,415,415,,,415,415,415,,,415,,415,415,,,415,415,,415,,415,,415,,415',
'415,,415,415,415,415,415,,415,415,415,,,,,,,,,,,,,,415,,474,415,415',
',415,,415,474,474,474,,,474,474,474,646,474,646,646,646,646,646,,,,474',
'474,,,,,646,,,,,474,474,,474,474,474,474,474,,,,,,,,,,646,336,,336,336',
'336,336,336,,646,646,646,646,,,,646,336,534,,534,534,534,534,534,474',
',,,,,,474,,534,,,474,474,336,336,,646,,,,,,336,336,336,336,,,,336,534',
',,,474,474,,,,534,534,534,534,,,,534,,,474,,,474,,,,,474,8,8,8,8,8,8',
'8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,,,,8,8,8,8,8,8,8,8,8,8,,,,,,8,8',
'8,8,8,8,8,8,8,8,,8,,,,,,,,,8,8,,8,8,8,8,8,8,8,,,8,8,,,,8,8,8,8,,,,,',
',,,,,,,,8,8,,8,8,8,8,8,8,8,8,8,8,8,8,,,8,8,,,,,,,,,,,,,,8,9,9,9,9,9',
'9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,,,,9,9,9,9,9,9,9,9,9,9,,,,,,9',
'9,9,9,9,9,9,9,9,,,9,,,,,,,,,9,9,,9,9,9,9,9,9,9,,,9,9,,,,9,9,9,9,,,,',
',,,,,,,,,9,9,,9,9,9,9,9,9,9,9,9,9,9,9,,,9,9,,,,,,,,,,,,,,9,395,395,395',
'395,395,395,395,395,395,395,395,395,395,395,395,395,395,395,395,395',
'395,395,395,395,,,,395,395,395,395,395,395,395,395,395,395,,,,,,395',
'395,395,395,395,395,395,395,395,,,395,,,,,,,,,395,395,,395,395,395,395',
'395,395,395,,,395,395,,,,395,395,395,395,,,,,,,,,,,,,,395,395,,395,395',
'395,395,395,395,395,395,395,395,395,395,,,395,395,,,,,,,,,,,,,,395,616',
'616,616,616,616,616,616,616,616,616,616,616,616,616,616,616,616,616',
'616,616,616,616,616,616,,,,616,616,616,616,616,616,616,616,616,616,',
',,,,616,616,616,616,616,616,616,616,616,,,616,,,,,,,,,616,616,,616,616',
'616,616,616,616,616,,,616,616,,,,616,616,616,616,,,,,,,,,,,,,,616,616',
',616,616,616,616,616,616,616,616,616,616,616,616,,,616,616,,,,,,,,,',
',,,,616,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71,71',
'71,71,71,71,,,,71,71,71,71,71,71,71,71,71,71,,,,,,71,71,71,71,71,71',
'71,71,71,71,71,71,,71,,,,,,,71,71,,71,71,71,71,71,71,71,,,71,71,,,,71',
'71,71,71,,,,,,71,,,,,,,,71,71,,71,71,71,71,71,71,71,71,71,71,71,71,',
',71,738,738,738,738,738,738,738,738,738,738,738,738,738,738,738,738',
'738,738,738,738,738,738,738,738,,,,738,738,738,738,738,738,738,738,738',
'738,,,,,,738,738,738,738,738,738,738,738,738,,,738,,,,,,,,,738,738,',
'738,738,738,738,738,738,738,,,738,738,,,,738,738,738,738,,,,,,,,,,,',
',,738,738,,738,738,738,738,738,738,738,738,738,738,738,738,210,210,738',
',210,,,,,,,,,210,210,,210,210,210,210,210,210,210,,,210,210,,,,210,210',
'210,210,,,,,,210,,,,,,,,210,210,,210,210,210,210,210,210,210,210,210',
'210,210,210,211,211,210,,211,,,,,,,,,211,211,,211,211,211,211,211,211',
'211,,,211,211,,,,211,211,211,211,,,,,,211,,,,,,,,211,211,,211,211,211',
'211,211,211,211,211,211,211,211,211,259,259,211,,259,,,,,,,,,259,259',
',259,259,259,259,259,259,259,,,259,259,,,,259,259,259,259,,,,,,,,,,',
',,,259,259,,259,259,259,259,259,259,259,259,259,259,259,259,440,440',
'259,,440,,,,,,,,,440,440,,440,440,440,440,440,440,440,,,440,440,,,,440',
'440,440,440,,,,,,440,,,,,,,,440,440,,440,440,440,440,440,440,440,440',
'440,440,440,440,441,441,440,,441,,,,,,,,,441,441,,441,441,441,441,441',
'441,441,,,441,441,,,,441,441,441,441,,,,,,441,,,,,,,,441,441,,441,441',
'441,441,441,441,441,441,441,441,441,441,507,507,441,,507,,,,,,,,,507',
'507,,507,507,507,507,507,507,507,,,507,507,,,,507,507,507,507,,,,,,507',
',,,,,,,507,507,,507,507,507,507,507,507,507,507,507,507,507,507,508',
'508,507,,508,,,,,,,,,508,508,,508,508,508,508,508,508,508,,,508,508',
',,,508,508,508,508,,,,,,508,,,,,,,,508,508,,508,508,508,508,508,508',
'508,508,508,508,508,508,517,517,508,,517,,,,,,,,,517,517,,517,517,517',
'517,517,517,517,,,517,517,,,,517,517,517,517,,,,,,517,,,,,,,,517,517',
',517,517,517,517,517,517,517,517,517,517,517,517,518,518,517,,518,,',
',,,,,,518,518,,518,518,518,518,518,518,518,,,518,518,,,,518,518,518',
'518,,,,,,518,,,,,,,,518,518,,518,518,518,518,518,518,518,518,518,518',
'518,518,576,576,518,,576,,,,,,,,,576,576,,576,576,576,576,576,576,576',
',,576,576,,,,576,576,576,576,,,,,,576,,,,,,,,576,576,,576,576,576,576',
'576,576,576,576,576,576,576,576,577,577,576,,577,,,,,,,,,577,577,,577',
'577,577,577,577,577,577,,,577,577,,,,577,577,577,577,,,,,,577,,,,,,',
',577,577,,577,577,577,577,577,577,577,577,577,577,577,577,583,583,577',
',583,,,,,,,,,583,583,,583,583,583,583,583,583,583,,,583,583,,,,583,583',
'583,583,,,,,,583,,,,,,,,583,583,,583,583,583,583,583,583,583,583,583',
'583,583,583,584,584,583,,584,,,,,,,,,584,584,,584,584,584,584,584,584',
'584,,,584,584,,,,584,584,584,584,,,,,,584,,,,,,,,584,584,,584,584,584',
'584,584,584,584,584,584,584,584,584,939,939,584,,939,,,,,,,,,939,939',
',939,939,939,939,939,939,939,,,939,939,,,,939,939,939,939,,,,,,939,',
',,,,,,939,939,,939,939,939,939,939,939,939,939,939,939,939,939,986,986',
'939,,986,,,,,,,,,986,986,,986,986,986,986,986,986,986,,,986,986,,,,986',
'986,986,986,,,,,,986,,,,,,,,986,986,,986,986,986,986,986,986,986,986',
'986,986,986,986,987,987,986,,987,,,,,,,,,987,987,,987,987,987,987,987',
'987,987,,,987,987,,,,987,987,987,987,,,,,,987,,,,,,,,987,987,,987,987',
'987,987,987,987,987,987,987,987,987,987,,693,987,693,693,693,693,693',
',695,,695,695,695,695,695,,693,,,,,,,736,695,736,736,736,736,736,,,',
',,,,,693,736,,,,,,,695,693,693,693,693,,,,693,695,695,695,695,,,736',
'695,737,,737,737,737,737,737,736,736,736,736,,,,736,865,737,865,865',
'865,865,865,,867,,867,867,867,867,867,,865,,,,,,,737,867,893,,893,893',
'893,893,893,737,737,737,737,,,865,737,,893,,,,,867,865,865,865,865,',
',,865,867,867,867,867,,,,867,893,897,,897,897,897,897,897,,893,893,893',
'893,,,,893,897,899,,899,899,899,899,899,,971,,971,971,971,971,971,,899',
',,,,897,,973,971,973,973,973,973,973,,,897,897,,,,897,899,973,,,,,,',
'971,,,899,899,,,,899,971,971,971,971,,,973,971,975,,975,975,975,975',
'975,,,973,973,,,,973,977,975,977,977,977,977,977,,989,,989,989,989,989',
'989,1015,977,1015,1015,1015,1015,1015,,975,989,,,,,,,1015,,,975,975',
',,977,975,,,,,,,989,,,977,977,,,1015,977,,,989,989,,,,989,,1015,1015',
',,,1015' ]
        racc_action_check = arr = ::Array.new(25292, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
   750,    47,   nil,   123,   nil,  5120,  1299,   -62, 23249, 23378,
   -21,   nil,   -43,    71,   533,   -41,    34,    -4,   nil,   -72,
  5252,  1173,   168,   nil,   157,   nil,     6, 22760, 22871,  5384,
  5516,  5648,   nil,   891,  5780,  5912,   nil,    71,   227,   254,
   154,   228,  6052,  6184,  6316,    95,   544,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil, 22355,   nil,   -71,  6448,
  6580,   -23,   nil,  6712,  6844,   nil,   nil,  6976,  7116,  7248,
  7380, 23765,   nil,   nil,   nil,   nil,   nil,   470,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     0,   nil,   nil,   113,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   262,
   nil,  7520,   nil,   nil,   nil,   nil,  7660,  7792,  7924,  8056,
  8196,  1032,   nil,   220,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   141,   nil,  1173,  8328,  8460,  8592,
 23941, 24003,  8724,  8856,  8988,  9120,  9252,  9384,   nil,   nil,
   546,   -77,   -62,   233,   131,   196,   256,   nil,  9516,  1314,
   273,  9648,  9780,  9912, 10044, 10176, 10308, 10440, 10572, 10704,
 10836, 10968, 11100, 11232, 11364, 11496, 11628, 11760, 11892, 12024,
 12156, 12288, 12420, 12552, 12684, 12816,   nil,   nil,   nil, 24065,
   nil,   nil,   275, 12948, 13080,   nil,   nil,   nil,   nil,   nil,
   nil,   nil, 13212,   nil,  1314,   nil,   262,   314,   nil, 13344,
   366, 13476,   nil,   nil, 13608, 13740,   nil,   nil,   296,   nil,
 13880,  1440,   368,   362,  1455,   384,   444,   418, 14012,  1596,
   578,   615,   681,   505,   750,   nil,   478,   439,    33,   nil,
   nil,   nil,   501,   258,   478, 14152,   nil,   299,   561,   753,
   nil,   569,   nil, 14284,  1737, 14416,   528,   nil,   105,   259,
   571,   585,   454,   619,   nil,   nil, 23117,   488,    -1,    26,
 14548, 14680,   458,   706,   596,   -23,   -19,   792,   691,   -18,
   724,   nil,   nil,   227,   281,   -34,   nil,   822,   nil,    34,
 14812,   nil,   nil,   nil,   264,   409,   448,   451,   478,   508,
   512,   532,   554,   nil,   567,   nil, 14944,   nil,   275,   335,
   363,   375,   392,   -45,   -41,   395,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   643, 23507,   nil,   nil,   nil,   nil,
   650,   nil,   nil,   642, 15076,   644,   nil,   nil,   891,   658,
   nil,   672,   676,   332,   342, 22984,   nil,   nil,   nil,   224,
   337,   723,   nil,   nil, 22490, 22625,   nil,  1455,   nil,   689,
   nil,   nil,   750,   nil,   nil,   nil,   nil,   -33,   nil,   746,
 24127, 24189, 15208,   239, 15340, 15472, 15604,  3147,  3288,   538,
   659,   778,   783,   788,   798,  5120,  5252,  5384,  3429,  3570,
  3711,  3852,  3993,  4134,  4275,  4416,  4557,  4698,   621,   630,
  4839,  4980, 15736,   -46, 23114,   nil,   nil,   nil,   nil,   747,
   nil,   159,   279,   750,   nil,   nil, 15868,   nil, 16000,   nil,
 16132,   nil,   332,   nil,   nil,   nil, 16272,  1455,  1878,   752,
   751,   nil,   nil,   754, 16412,   767, 16544, 24251, 24313,   891,
   809,   nil, 16676,   769,   nil, 16808, 16940, 24375, 24437,  1596,
 17072,   897,   896,   694,   824,   nil, 17204,   nil,   nil, 17336,
   nil,   nil,   nil,   nil, 23134,   nil,   783,   784,   nil,   785,
   793,   794,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   787,   477,   nil,   nil, 17468,   nil,   nil,   nil,   880,   nil,
   nil,   nil,   881,   nil,   nil,   882,  2019,   935,   nil,  2160,
    63,   119,   935,   945, 17600, 17732, 24499, 24561,    10,   nil,
   nil,   894,   nil, 24623, 24685, 17864,   nil,   nil,   nil,   587,
   193,  2301,   883,   nil,   -14,   nil,   nil,   nil,   733,   nil,
   nil,   nil,   858,   nil,   nil,   151,   nil,   222,   nil,   nil,
   846,   nil,   847,   nil,   nil,   nil, 23636,   nil,   852, 17996,
 18128,   380,   896, 18260, 18392, 18524, 18656,   899,   nil,   nil,
 18788, 18920,   907,   nil, 19052, 19184,   nil,   nil,   350,   416,
   470,   607,   874,  1032,  1737,   nil, 23078,   nil,  2442,   979,
     5,   316,   nil,  2583,  2724,   nil,   882,   nil,   929, 19316,
   nil,   nil, 19448,   nil,   905,   -81, 19580,   888,   nil,   893,
   137,   179,   935,   340,  1032,   936,   895, 19712,  1878,   972,
    20,  1026, 19844,   nil,   913,   nil,   539,    21,   914,   495,
   nil,   nil,   740, 24934,   nil, 24942,   nil,  5959,   nil, 19976,
   nil,   607,   nil,   912,   230,   925,   nil,   nil,   nil,   nil,
   850,   nil,  1044,   nil,   nil,   nil,   nil,  1050,   nil,    32,
   929,    26,    41,   123,   182, 20108,   414,  1173,   nil,   937,
  2865, 20240,   nil,   nil,  1060,  3006, 24957, 24997, 23879,   nil,
   nil,   nil,   nil,   nil,   nil,  3147,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   937, 20372,  2019, 20504,   nil,   938,   nil,
  2160,   nil,  2301,   nil,   nil,  2442,   nil,  2583,   nil,  2724,
 20636, 20768, 20900, 21032,   429,   940,   940,   954,   nil,   958,
   961,   980,   nil,  1009,   992,   991,   989, 21164,   nil,   nil,
  1128,   nil,   nil,  3288,  1031,  1137,   nil,   nil,   nil,   nil,
  1013,   378,   nil,   nil,  1149,   nil,  3429,  1024,  1072,   nil,
   nil,  1072,   nil,   nil,  3570,  3711,  1074,  1032,   nil,   nil,
   nil,  1033,  1036,   nil,  1046,  1047,   nil,  1051,   nil,   nil,
  1055,   614,  1053,   600,   nil,  1188,   nil, 21296,  1190,  3852,
  3993,   nil, 21428,  4134,    81,   122,   nil,  1191,   611,  4275,
   nil,  1192,  1078,   613,   nil,  1087,  1095,   nil,  2865, 21560,
 21692,   nil,   525,   nil,   nil, 25012,   nil, 25020,   nil,  7427,
   nil,   nil,  1123,  1159, 21824,   933,  1181,   nil,  1141,   nil,
   nil,   nil,  4416,   nil,   nil,    33, 21956,   nil,   nil,  1146,
  1255,   nil,   nil, 25037,   nil, 14059,   nil, 25076,   nil, 25093,
   nil,   nil,   nil,   nil,   330,  3415,  1134,   nil,    36,   nil,
  1264,  1269,   nil,    47,   nil,   nil,   nil,  1277,   nil,   nil,
   nil,  1197,   nil, 22088,  1154,   nil,   nil,  1164,  1165,  1167,
  1170,   nil,  1172,   nil,   643,   nil,   nil,   nil,   963, 24747,
   nil,   nil,   nil,  4557,  1035,  1074,  1104,  1252,  1176,   nil,
   nil,   nil,  1174,  1177,  1187,  1188,  1189,  3556,  1192,  3589,
  4698,   nil,   nil,   nil,   nil,   nil,  4839,   nil,  4980,  3006,
   nil, 25101,   nil, 25116,   nil, 25156,   nil, 25171,   nil,   nil,
   nil,  1300,  1237,  1238,  1323, 22220, 24809, 24871,  1215, 25179,
   nil,   nil,   nil,   nil,  3697,  1216,   862,  1347,  1361,  1240,
  1243,  1261,  1262,   nil,   nil,  1270,    40,    42,   112,  1314,
  1268,  1271,   nil,   nil,   nil, 25186,   nil,   nil,   nil,   nil,
    43,   nil,  1275,   nil ]

racc_action_default = [
    -3,  -597,    -1,  -583,    -4,  -597,    -7,  -597,  -597,  -597,
  -597,   -29,  -597,  -597,  -597,  -281,  -597,   -40,   -43,  -585,
  -597,   -48,   -50,   -51,   -52,   -56,  -258,  -258,  -258,  -295,
  -330,  -331,   -68,   -11,   -72,   -80,   -82,  -597,  -488,  -489,
  -597,  -597,  -597,  -597,  -597,  -585,  -239,  -272,  -273,  -274,
  -275,  -276,  -277,  -278,  -279,  -280,  -573,  -283,  -285,  -596,
  -563,  -303,  -391,  -597,  -597,  -308,  -311,  -583,  -597,  -597,
  -597,  -597,  -332,  -333,  -429,  -430,  -431,  -432,  -433,  -454,
  -436,  -437,  -456,  -458,  -441,  -446,  -450,  -452,  -468,  -456,
  -470,  -472,  -473,  -474,  -475,  -571,  -477,  -478,  -572,  -480,
  -481,  -482,  -483,  -484,  -485,  -486,  -487,  -492,  -493,  -597,
    -2,  -584,  -592,  -593,  -594,    -6,  -597,  -597,  -597,  -597,
  -597,    -3,   -17,  -597,  -111,  -112,  -113,  -114,  -115,  -116,
  -117,  -118,  -119,  -123,  -124,  -125,  -126,  -127,  -128,  -129,
  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,  -138,  -139,
  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,  -148,  -149,
  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,  -158,  -159,
  -160,  -161,  -162,  -163,  -164,  -165,  -166,  -167,  -168,  -169,
  -170,  -171,  -172,  -173,  -174,  -175,  -176,  -177,  -178,  -179,
  -180,  -181,  -182,  -183,  -184,  -185,  -186,  -187,  -188,  -189,
  -190,  -191,  -192,  -193,   -22,  -120,   -11,  -597,  -597,  -248,
  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -585,  -586,   -47,
  -597,  -488,  -489,  -597,  -281,  -597,  -597,  -229,  -597,   -11,
  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,
  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,  -597,
  -597,  -597,  -597,  -597,  -597,  -597,  -236,  -398,  -400,  -597,
  -581,  -582,   -57,  -248,  -597,  -302,  -404,  -413,  -415,   -63,
  -410,   -64,  -585,   -65,  -240,  -253,  -262,  -262,  -257,  -597,
  -263,  -597,  -454,  -565,  -597,  -597,   -66,   -67,  -583,   -12,
  -597,   -15,  -597,   -70,   -11,  -585,  -597,   -73,   -76,   -11,
   -88,   -89,  -597,  -597,   -96,  -295,  -298,  -585,  -597,  -330,
  -331,  -334,  -411,  -597,   -78,  -597,   -84,  -292,  -471,  -597,
  -214,  -215,  -230,  -597,   -11,  -597,  -585,  -241,  -589,  -589,
  -597,  -597,  -589,  -597,  -304,  -305,  -521,   -49,  -597,  -597,
  -597,  -597,  -583,  -597,  -584,  -488,  -489,  -597,  -597,  -281,
  -597,  -344,  -345,  -106,  -107,  -597,  -109,  -597,  -281,  -597,
  -597,  -488,  -489,  -323,  -111,  -112,  -153,  -154,  -155,  -171,
  -176,  -183,  -186,  -325,  -597,  -561,  -597,  -434,  -597,  -597,
  -597,  -597,  -597,  -597,  -597,  -597,  1024,    -5,  -595,   -23,
   -24,   -25,   -26,   -27,  -597,  -597,   -19,   -20,   -21,  -121,
  -597,   -30,   -39,  -268,  -597,  -597,  -267,   -31,  -196,  -585,
  -249,  -262,  -262,  -574,  -575,  -258,  -408,  -576,  -577,  -575,
  -574,  -258,  -407,  -409,  -576,  -577,   -37,  -204,   -38,  -597,
   -41,   -42,  -194,  -263,   -44,   -45,   -46,  -585,  -301,  -597,
  -597,  -597,  -248,  -292,  -597,  -597,  -597,  -205,  -206,  -207,
  -208,  -209,  -210,  -211,  -212,  -216,  -217,  -218,  -219,  -220,
  -221,  -222,  -223,  -224,  -225,  -226,  -227,  -228,  -231,  -232,
  -233,  -234,  -597,  -380,  -258,  -574,  -575,   -54,   -58,  -585,
  -259,  -380,  -380,  -585,  -297,  -254,  -597,  -255,  -597,  -260,
  -597,  -264,  -597,  -568,  -570,   -10,  -584,   -14,    -3,  -585,
   -69,  -290,   -85,   -74,  -597,  -585,  -248,  -597,  -597,   -95,
  -597,  -471,  -597,   -81,   -86,  -597,  -597,  -597,  -597,  -235,
  -597,  -421,  -597,  -286,  -597,  -242,  -591,  -590,  -244,  -591,
  -293,  -294,  -564,  -392,  -521,  -395,  -560,  -560,  -504,  -506,
  -506,  -506,  -520,  -522,  -523,  -524,  -525,  -526,  -527,  -528,
  -529,  -597,  -531,  -533,  -535,  -540,  -542,  -543,  -545,  -550,
  -552,  -553,  -555,  -556,  -557,  -597,   -11,  -335,  -336,   -11,
  -597,  -597,  -597,  -597,  -597,  -248,  -597,  -597,  -292,  -316,
  -106,  -107,  -108,  -597,  -597,  -248,  -319,  -494,  -495,  -597,
  -597,   -11,  -499,  -327,  -585,  -435,  -455,  -460,  -597,  -462,
  -438,  -457,  -597,  -459,  -440,  -597,  -443,  -597,  -445,  -448,
  -597,  -449,  -597,  -469,    -8,   -18,  -597,   -28,  -271,  -597,
  -597,  -412,  -597,  -250,  -252,  -597,  -597,   -59,  -247,  -405,
  -597,  -597,   -61,  -406,  -597,  -597,  -300,  -587,  -574,  -575,
  -574,  -575,  -585,  -194,  -585,  -381,  -585,  -383,   -11,   -53,
  -401,  -380,  -245,   -11,   -11,  -296,  -262,  -261,  -265,  -597,
  -566,  -567,  -597,   -13,  -597,   -71,  -597,   -77,   -83,  -585,
  -574,  -575,  -246,   -92,   -94,  -597,   -79,  -597,  -203,  -213,
  -585,  -596,  -596,  -284,  -585,  -289,  -589,  -597,  -585,  -597,
  -502,  -503,  -597,  -597,  -513,  -597,  -516,  -597,  -518,  -597,
  -346,  -597,  -348,  -350,  -357,  -585,  -534,  -544,  -554,  -558,
  -596,  -337,  -596,  -309,  -338,  -339,  -312,  -597,  -315,  -597,
  -585,  -574,  -575,  -578,  -291,  -597,  -106,  -107,  -110,  -585,
   -11,  -597,  -497,  -321,  -597,   -11,  -521,  -521,  -597,  -562,
  -461,  -464,  -465,  -466,  -467,   -11,  -439,  -442,  -444,  -447,
  -451,  -453,  -122,  -269,  -597,  -197,  -597,  -588,  -262,   -33,
  -199,   -34,  -200,   -60,   -35,  -202,   -36,  -201,   -62,  -195,
  -597,  -597,  -597,  -597,  -412,  -597,  -560,  -560,  -362,  -364,
  -364,  -364,  -379,  -597,  -585,  -385,  -529,  -537,  -538,  -548,
  -597,  -403,  -402,   -11,  -597,  -597,  -256,  -266,  -569,   -16,
   -75,   -90,   -87,  -299,  -596,  -342,   -11,  -422,  -596,  -423,
  -424,  -597,  -243,  -393,   -11,   -11,  -597,  -560,  -541,  -559,
  -505,  -506,  -506,  -532,  -506,  -506,  -551,  -506,  -529,  -546,
  -585,  -597,  -355,  -597,  -530,  -597,  -340,  -597,  -597,   -11,
   -11,  -314,  -597,   -11,  -412,  -597,  -412,  -597,  -597,   -11,
  -324,  -597,  -585,  -597,  -328,  -597,  -270,   -32,  -198,  -251,
  -597,  -237,  -597,  -360,  -361,  -370,  -372,  -597,  -375,  -597,
  -377,  -382,  -597,  -597,  -597,  -536,  -597,  -399,  -597,  -414,
  -416,    -9,   -11,  -428,  -343,  -597,  -597,  -426,  -287,  -597,
  -597,  -394,  -501,  -597,  -509,  -597,  -511,  -597,  -514,  -597,
  -517,  -519,  -347,  -349,  -353,  -597,  -358,  -306,  -597,  -307,
  -597,  -597,  -265,  -596,  -317,  -320,  -496,  -597,  -326,  -498,
  -500,  -499,  -463,  -597,  -560,  -539,  -363,  -364,  -364,  -364,
  -364,  -549,  -364,  -384,  -585,  -387,  -389,  -390,  -547,  -597,
  -292,   -55,  -427,   -11,   -97,   -98,  -597,  -597,  -105,  -425,
  -396,  -397,  -506,  -506,  -506,  -506,  -351,  -597,  -356,  -597,
   -11,  -310,  -313,  -417,  -418,  -419,   -11,  -322,   -11,  -238,
  -359,  -597,  -367,  -597,  -369,  -597,  -373,  -597,  -376,  -378,
  -386,  -597,  -291,  -578,  -421,  -248,  -597,  -597,  -104,  -597,
  -507,  -510,  -512,  -515,  -597,  -354,  -596,  -597,  -597,  -364,
  -364,  -364,  -364,  -388,  -420,  -585,  -574,  -575,  -578,  -103,
  -506,  -352,  -341,  -318,  -329,  -597,  -365,  -368,  -371,  -374,
  -412,  -508,  -364,  -366 ]

clist = [
'216,275,275,275,14,327,373,409,573,14,522,276,276,276,266,270,311,311',
'258,2,415,421,334,431,220,681,323,259,122,205,535,127,127,220,220,220',
'406,14,302,302,550,328,428,478,297,130,130,132,132,542,311,311,311,110',
'114,338,339,487,438,342,513,479,735,835,582,318,660,220,220,474,111',
'220,347,357,357,705,621,826,219,6,314,690,691,804,6,783,566,569,713',
'716,378,293,295,780,127,262,269,271,906,903,525,528,115,931,532,379',
'935,660,389,390,391,392,385,484,838,14,937,343,114,1,220,220,220,220',
'14,14,881,816,359,363,648,273,286,287,605,607,500,663,653,654,394,823',
'204,352,402,395,277,277,277,651,616,472,657,13,657,923,330,650,13,375',
'331,335,350,586,374,324,325,684,326,340,958,694,696,698,839,341,329',
'332,840,725,966,275,730,849,591,592,13,738,921,405,6,782,487,784,934',
'405,533,813,416,393,6,700,660,336,687,473,481,931,387,482,14,220,220',
'220,1003,963,220,220,220,220,220,220,995,872,937,830,808,903,688,885',
'377,296,380,14,425,275,275,717,550,381,642,382,383,384,275,740,276,542',
'745,667,415,421,731,821,276,818,925,676,,1011,,,,,13,401,407,220,220',
',426,430,,13,13,826,220,734,636,,311,601,603,606,606,,,601,510,657,657',
',728,492,14,,266,311,14,,270,,302,14,669,,,524,793,774,,,927,823,,964',
',672,,302,652,863,864,,655,514,,672,14,220,,,570,571,,,801,511,495,114',
'665,,,503,220,220,668,823,,1012,786,700,293,499,832,496,,293,505,13',
',,892,914,220,,480,,,497,,,,720,483,,,593,,,220,277,13,729,,,672,844',
',277,622,968,572,114,672,,846,550,,550,,,,628,400,,,,763,633,587,800',
',768,275,615,847,,127,,,851,,,,823,,296,416,,852,853,130,,132,739,796',
'660,,,550,550,,13,431,,220,13,,542,542,,13,894,896,,898,900,812,901',
',333,628,,425,,,843,,970,,,,,,,,13,275,,,700,,700,627,,311,,1004,,632',
',296,,311,416,,296,,26,14,,14,,26,416,,748,302,748,220,809,781,,514',
',302,664,,26,,803,514,,220,657,,,26,26,26,425,26,,,917,,,796,,,425,',
'649,,,834,275,,,,,,,956,700,719,275,,822,,824,26,26,416,,26,,,786,14',
'786,416,14,,,,6,965,220,,,,,990,991,992,993,,220,,,,,,14,,550,,,425',
',,656,,,425,700,403,700,26,792,,,433,26,26,26,26,26,26,714,714,622,',
'775,,785,810,220,220,,,943,220,220,752,,220,127,732,733,1021,686,700',
'791,,,311,13,622,13,14,130,998,132,960,14,14,311,628,,,633,405,811,',
',,785,302,902,1020,854,,786,514,,,,489,302,491,759,761,493,494,802,764',
'766,,,430,919,,,,,,,,622,,,,26,26,26,26,,622,26,26,26,26,26,26,845,',
'13,,,13,848,,,,,26,220,,928,,929,14,220,,,,14,758,,,,13,,,,,14,883,',
'15,,887,,,15,,952,220,127,26,26,710,785,,712,,311,,26,,,,,,,,,,,,1005',
',15,304,304,,26,,875,,26,,,672,14,26,,,13,,16,,618,13,13,16,,14,,,,857',
',,,14,14,349,358,358,26,26,,,,,908,,,,,,,16,999,,26,26,220,,14,14,,790',
'14,,,,794,795,14,777,,311,,,26,,,,15,,,778,,311,,,351,15,15,,26,,,938',
',,,,13,,,14,658,13,333,946,661,,779,,,714,817,13,916,,,,,920,,,,,819',
',,819,,16,980,,,,,,,,16,16,,,658,,,333,,,,,,,,787,,,26,825,855,827,',
'13,14,,,,,,,,706,,275,15,13,,,425,,14,,,13,13,,14,,14,,416,433,,,,,',
'15,,,,,622,,,220,,878,13,13,,26,13,26,,,,,13,,884,26,,,,16,425,,889',
'890,,,429,,,26,753,,,,658,333,,,,,,,16,,,13,,910,911,,,913,15,,,,15',
',,,304,15,,,,337,337,,,337,797,,,798,,26,,304,26,,924,,,777,26,777,15',
'777,,,942,,819,807,26,778,,778,,778,26,,,16,,,13,16,,,829,,16,,,,,,337',
'337,337,337,,13,930,,932,,,13,,13,,,26,26,,16,,26,26,,,26,866,868,870',
',,,953,984,954,,955,,,26,,,,,26,26,787,856,,787,996,787,,787,,,997,',
',,,,38,,,,,38,,,,777,,777,,777,,777,,,,,,778,,778,,778,,778,,,,,,38',
'300,300,434,435,436,437,,,,,,,,,1000,,1001,,1002,777,26,,,,,26,26,,',
',26,778,1010,,345,361,361,361,,912,26,,,,15,,15,,,,,26,304,,,,333,,1022',
'787,304,787,,787,,787,,,,,,,,,,,,38,972,974,976,978,,979,,,38,38,26',
',,,,,,16,,16,,,,26,,787,,,,,,26,26,,,523,15,,,15,,,,,,,,,,,337,337,',
',26,,26,26,,,26,15,,,,,26,,744,,590,,1016,1017,1018,1019,,,,,,,,,,,594',
'16,,,16,,,,38,1023,,,,26,,,,,,,,,,,,,16,,,,,38,15,,,,,15,15,,,,,,,,',
',,,304,,,,,,,,,,,304,,,,,,,,429,,,,,26,,,,,,,,,16,,,,,16,16,39,26,38',
',,39,38,26,,26,300,38,,,,,,,,,,,,,15,,26,300,,15,,,,39,301,301,38,,',
'15,,,,,,,,,,,,,,,680,,,,,,,,,,,,,,346,362,362,362,,,,16,,,,,16,358,',
',,,,15,,,16,,,,,,,,,,15,,,,,,,,15,15,,39,,,,,,,,,39,39,,,,,,,,,,,,15',
'15,,,15,16,,,,,15,,,,,,,,16,,,,,,,,16,16,,,,,,,,358,,,,,,,,15,,,,948',
',,,16,16,,,16,,,,,,16,,,,,,,,,,,,39,,38,,38,,,,,,300,,,,,,,,300,,,16',
',39,,949,,,,,,15,,,,,,,,,,,,,,,,,15,,,,,337,15,,15,,,337,,,,,,,,,,,',
',38,,,38,,,,,,,16,,39,,,,39,,,,301,39,,,,38,,16,,,,,,16,,16,,301,,,',
',,,,,39,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,38,,,,,38,38,,,,,337,,,,',
',,300,,,,,,,,,,,300,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,38,,,,,38,,,,,,,,,,38,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,39,',
'39,361,,,,,301,38,,,,,,,301,,,,,,38,,,,,,,,38,38,,,,,,,,,,,,,,,,,,,',
',,,,38,38,,,38,,,,,,38,,,,,39,,,39,,,,,,,,,,,,,,,,,361,,,,,39,,,38,',
',,944,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,39,,,,,39,39',
'38,,,,,,,,,,,301,,,,,,38,,,,227,301,38,,38,,,,,274,274,274,,,,,,,,,',
',320,321,322,,,,,,,,,,,,,,,274,274,,,,,,,,,,,,,,39,,,,,39,,,,,,,,,,39',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,362,,,,,,39,,,,,,,,,,,,,39',
',,,,,,,39,39,,,,,,,,,,,,,,,,,,,,,,,,39,39,,,39,,,,,,39,,,,,,,,,,,,,',
'274,408,274,,,427,432,,,,,362,,,,,,,,39,,227,,945,447,448,449,450,451',
'452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468',
'469,470,471,,,,,,,,274,274,,,,,,,,274,,,,,,,274,,274,,,274,274,,39,',
',,,,,,,,,,,,,,,39,,,,,,39,,39,,,,,,,,,,,519,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,274,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,274,,427,643,408,,,,,,,,,,,,,,,,,,,,,,',
',,,644,,,,,,,,,,,,,,274,,274,,274,,,,,,,,,,,,,,,,274,,,,,,,,,678,679',
',,,,,,,,,274,,,274,,,,,,,,,,,,,,,,,,,,,,,,,274,,,,,,,,,,,,,,,,,,,,274',
'274,,,,,,,,,,274,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,274,755,,,274,274',
'760,762,,,,765,767,,,643,769,,,,,,,,,,,,,,,,,,,,,,,,274,,,274,,,,,,',
',,,,,,,,,,,,,274,,,,,,,,,,,,,,,,,274,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,274,,858,,,,,,,,,,,,,,760,762,767,765,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,274,,,',
',,,,,,,,,,,,,274,858,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,969,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,274' ]
        racc_goto_table = arr = ::Array.new(3100, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'31,33,33,33,22,69,56,23,94,22,8,70,70,70,73,73,64,64,140,2,37,37,88',
'18,22,10,31,36,15,15,139,57,57,22,22,22,27,22,22,22,168,70,27,39,49',
'60,60,61,61,163,64,64,64,4,97,17,17,75,47,17,51,23,98,92,54,63,178,22',
'22,37,6,22,22,22,22,118,24,174,20,7,50,123,123,11,7,131,91,91,93,93',
'151,45,46,128,57,38,38,38,119,116,71,71,5,175,71,152,133,178,17,17,17',
'17,152,47,11,22,134,4,97,1,22,22,22,22,22,22,12,131,55,55,40,44,44,44',
'155,155,47,14,40,40,2,169,16,19,28,30,72,72,72,42,62,66,76,21,76,67',
'68,74,21,86,87,90,95,96,99,100,101,102,103,104,119,166,166,166,105,106',
'72,72,107,108,109,33,110,111,112,113,21,114,115,70,7,120,75,126,132',
'70,135,136,73,7,7,117,178,137,138,141,143,175,5,144,22,22,22,22,133',
'145,22,22,22,22,22,22,119,131,134,118,146,116,139,147,150,9,153,22,57',
'33,33,94,168,154,23,157,158,159,33,160,70,163,161,51,37,37,162,167,70',
'171,172,51,,119,,,,,21,20,20,22,22,,20,20,,21,21,174,22,8,47,,64,156',
'156,156,156,,,156,31,76,76,,54,151,22,,73,64,22,,73,,22,22,23,,,31,40',
'24,,,128,169,,11,,37,,22,47,123,123,,47,49,,37,22,22,,,17,17,,,24,63',
'4,97,47,,,50,22,22,47,169,,92,168,117,45,46,117,6,,45,46,21,,,123,93',
'22,,44,,,7,,,,23,44,,,36,,,22,72,21,23,,,37,24,,72,31,98,4,97,37,,24',
'168,,168,,,,73,9,,,,39,73,97,51,,39,33,15,8,,57,,,8,,,,169,,9,73,,139',
'139,60,,61,47,75,178,,,168,168,,21,18,,22,21,,163,163,,21,166,166,,166',
'166,71,166,,65,73,,57,,,91,,123,,,,,,,,21,33,,,117,,117,38,,64,,10,',
'38,,9,,64,73,,9,,41,22,,22,,41,73,,156,22,156,22,27,129,,49,,22,2,,41',
',47,49,,22,76,,,41,41,41,57,41,,,8,,,75,,,57,,38,,,47,33,,,,,,,117,117',
'70,33,,129,,129,41,41,73,,41,,,168,22,168,73,22,,,,7,94,22,,,,,166,166',
'166,166,,22,,,,,,22,,168,,,57,,,72,,,57,117,65,117,41,88,,,65,41,41',
'41,41,41,41,97,97,31,,31,,31,69,22,22,,,91,22,22,15,,22,57,97,97,166',
'72,117,140,,,64,21,31,21,22,60,8,61,91,22,22,64,73,,,73,70,31,,,,31',
'22,47,24,56,,168,49,,,,65,22,65,20,20,65,65,49,20,20,,,20,47,,,,,,,',
'31,,,,41,41,41,41,,31,41,41,41,41,41,41,17,,21,,,21,17,,,,,41,22,,129',
',129,22,22,,,,22,72,,,,21,,,,,22,69,,25,,69,,,25,,129,22,57,41,41,9',
'31,,9,,64,,41,,,,,,,,,,,,23,,25,25,25,,41,,22,,41,,,37,22,41,,,21,,26',
',65,21,21,26,,22,,,,20,,,,22,22,25,25,25,41,41,,,,,17,,,,,,,26,129,',
'41,41,22,,22,22,,9,22,,,,9,9,22,122,,64,,,41,,,,25,,,124,,64,,,26,25',
'25,,41,,,22,,,,,21,,,22,65,21,65,22,65,,127,,,97,122,21,97,,,,,97,,',
',,124,,,124,,26,31,,,,,,,,26,26,,,65,,,65,,,,,,,,170,,,41,127,9,127',
',21,22,,,,,,,,65,,33,25,21,,,57,,22,,,21,21,,22,,22,,73,65,,,,,,25,',
',,,31,,,22,,9,21,21,,41,21,41,,,,,21,,9,41,,,,26,57,,9,9,,,26,,,41,65',
',,,65,65,,,,,,,26,,,21,,9,9,,,9,25,,,,25,,,,25,25,,,,29,29,,,29,65,',
',65,,41,,25,41,,122,,,122,41,122,25,122,,,9,,124,65,41,124,,124,,124',
'41,,,26,,,21,26,,,65,,26,,,,,,29,29,29,29,,21,127,,127,,,21,,21,,,41',
'41,,26,,41,41,,,41,125,125,125,,,,127,9,127,,127,,,41,,,,,41,41,170',
'65,,170,9,170,,170,,,9,,,,,,52,,,,,52,,,,122,,122,,122,,122,,,,,,124',
',124,,124,,124,,,,,,52,52,52,29,29,29,29,,,,,,,,,127,,127,,127,122,41',
',,,,41,41,,,,41,124,127,,52,52,52,52,,65,41,,,,25,,25,,,,,41,25,,,,65',
',127,170,25,170,,170,,170,,,,,,,,,,,,52,125,125,125,125,,125,,,52,52',
'41,,,,,,,26,,26,,,,41,,170,,,,,,41,41,,,29,25,,,25,,,,,,,,,,,29,29,',
',41,,41,41,,,41,25,,,,,41,,25,,29,,125,125,125,125,,,,,,,,,,,29,26,',
',26,,,,52,125,,,,41,,,,,,,,,,,,,26,,,,,52,25,,,,,25,25,,,,,,,,,,,,25',
',,,,,,,,,,25,,,,,,,,26,,,,,41,,,,,,,,,26,,,,,26,26,53,41,52,,,53,52',
'41,,41,52,52,,,,,,,,,,,,,25,,41,52,,25,,,,53,53,53,52,,,25,,,,,,,,,',
',,,,,29,,,,,,,,,,,,,,53,53,53,53,,,,26,,,,,26,25,,,,,,25,,,26,,,,,,',
',,,25,,,,,,,,25,25,,53,,,,,,,,,53,53,,,,,,,,,,,,25,25,,,25,26,,,,,25',
',,,,,,,26,,,,,,,,26,26,,,,,,,,25,,,,,,,,25,,,,25,,,,26,26,,,26,,,,,',
'26,,,,,,,,,,,,53,,52,,52,,,,,,52,,,,,,,,52,,,26,,53,,26,,,,,,25,,,,',
',,,,,,,,,,,,25,,,,,29,25,,25,,,29,,,,,,,,,,,,,52,,,52,,,,,,,26,,53,',
',,53,,,,53,53,,,,52,,26,,,,,,26,,26,,53,,,,,,,,,53,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,52,,,,,52,52,,,,,29,,,,,,,52,,,,,,,,,,,52,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,52,,,,,52,,,,,,,,,,52,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,53,,53,52,,,,,53,52,,,,,,,53,,,',
',,52,,,,,,,,52,52,,,,,,,,,,,,,,,,,,,,,,,,52,52,,,52,,,,,,52,,,,,53,',
',53,,,,,,,,,,,,,,,,,52,,,,,53,,,52,,,,52,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,53,,,,,53,53,52,,,,,,,,,,,53,,,,,,52,,,,32,53',
'52,,52,,,,,32,32,32,,,,,,,,,,,32,32,32,,,,,,,,,,,,,,,32,32,,,,,,,,,',
',,,,53,,,,,53,,,,,,,,,,53,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,53',
',,,,,53,,,,,,,,,,,,,53,,,,,,,,53,53,,,,,,,,,,,,,,,,,,,,,,,,53,53,,,53',
',,,,,53,,,,,,,,,,,,,,32,32,32,,,32,32,,,,,53,,,,,,,,53,,32,,53,32,32',
'32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32',
',,,,,,,32,32,,,,,,,,32,,,,,,,32,,32,,,32,32,,53,,,,,,,,,,,,,,,,,53,',
',,,,53,,53,,,,,,,,,,,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,32,,32,32,32,,,,,,,,,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,,32,,32',
',32,,,,,,,,,,,,,,,,32,,,,,,,,,32,32,,,,,,,,,,32,,,32,,,,,,,,,,,,,,,',
',,,,,,,,,32,,,,,,,,,,,,,,,,,,,,32,32,,,,,,,,,,32,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,32,32,,,32,32,32,32,,,,32,32,,,32,32,,,,,,,,,,,,,,,,,',
',,,,,,32,,,32,,,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,32,,32,,,,,,,,,,,,,,32,32,32',
'32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,32,,,,,,,,,,,,,,,,,32,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,32' ]
        racc_goto_check = arr = ::Array.new(3100, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_goto_pointer = [
   nil,   119,    19,   nil,    50,    97,    67,    79,  -314,   198,
  -496,  -598,  -678,   nil,  -359,    20,   133,    -8,  -190,    75,
    58,   153,     4,  -202,  -333,   752,   803,  -171,   -63,   992,
    23,   -19,  2114,   -28,   nil,   nil,     3,  -190,    69,  -219,
  -343,   499,  -328,   nil,   102,    58,    59,  -159,   nil,    10,
    45,  -255,  1170,  1458,  -291,    59,   -65,    23,   nil,   nil,
    37,    39,  -249,    24,   -18,   401,  -105,  -706,    97,   -54,
   -18,  -228,   117,   -12,  -317,  -220,  -334,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    88,   100,   -39,   nil,
    99,  -252,  -647,  -482,  -335,    94,  -196,    51,  -530,    93,
   108,   108,  -356,   110,   104,  -539,   109,  -538,  -400,  -734,
  -404,  -550,  -179,  -188,  -406,  -666,  -732,  -350,  -476,  -735,
  -455,   nil,   208,  -455,   220,   355,  -453,   247,  -553,  -134,
   nil,  -561,  -679,  -767,  -757,  -140,  -490,   141,  -329,  -306,
    -4,   -52,   nil,   -61,   -59,  -698,  -456,  -579,   nil,   nil,
   153,    11,    23,   149,   155,  -247,   -98,   156,   156,   156,
  -353,  -351,  -336,  -287,   nil,   nil,  -368,  -440,  -296,  -552,
   292,  -434,  -606,   nil,  -618,  -764,   nil,   nil,  -422 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   344,   291,   nil,   521,
   nil,   836,   nil,   288,   289,   nil,   nil,   nil,    11,    12,
    18,   226,   319,   nil,   nil,   224,   225,   nil,   nil,    17,
   nil,   439,    21,    22,    23,    24,   nil,   675,   nil,   nil,
   nil,   308,   nil,    25,   410,    32,   nil,   nil,    34,    37,
    36,   nil,   221,   222,   356,   nil,   129,   418,   128,   131,
    75,    76,   nil,    90,    46,   280,   nil,   nil,   nil,   805,
   411,   nil,   412,   423,   629,   485,   278,   264,    47,    48,
    49,    50,    51,    52,    53,    54,    55,   nil,   265,    61,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   567,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   702,   549,   nil,   703,
   926,   776,   537,   nil,   538,   nil,   nil,   539,   nil,   541,
   645,   nil,   nil,   nil,   547,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   422,   nil,   nil,   nil,   nil,   nil,    74,    77,
    78,   nil,   nil,   nil,   nil,   nil,   596,   nil,   nil,   nil,
   nil,   nil,   nil,   820,   737,   536,   nil,   540,   828,   552,
   554,   555,   788,   558,   559,   789,   562,   565,   283 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 146, :_reduce_none,
  2, 147, :_reduce_2,
  0, 148, :_reduce_3,
  1, 148, :_reduce_4,
  3, 148, :_reduce_5,
  2, 148, :_reduce_6,
  1, 150, :_reduce_none,
  4, 150, :_reduce_8,
  4, 153, :_reduce_9,
  2, 154, :_reduce_10,
  0, 158, :_reduce_11,
  1, 158, :_reduce_12,
  3, 158, :_reduce_13,
  2, 158, :_reduce_14,
  1, 159, :_reduce_none,
  4, 159, :_reduce_16,
  0, 175, :_reduce_17,
  4, 152, :_reduce_18,
  3, 152, :_reduce_19,
  3, 152, :_reduce_20,
  3, 152, :_reduce_21,
  2, 152, :_reduce_22,
  3, 152, :_reduce_23,
  3, 152, :_reduce_24,
  3, 152, :_reduce_25,
  3, 152, :_reduce_26,
  3, 152, :_reduce_27,
  4, 152, :_reduce_28,
  1, 152, :_reduce_none,
  3, 152, :_reduce_30,
  3, 152, :_reduce_31,
  6, 152, :_reduce_32,
  5, 152, :_reduce_33,
  5, 152, :_reduce_34,
  5, 152, :_reduce_35,
  5, 152, :_reduce_36,
  3, 152, :_reduce_37,
  3, 152, :_reduce_38,
  3, 152, :_reduce_39,
  1, 152, :_reduce_none,
  3, 163, :_reduce_41,
  3, 163, :_reduce_42,
  1, 174, :_reduce_none,
  3, 174, :_reduce_44,
  3, 174, :_reduce_45,
  3, 174, :_reduce_46,
  2, 174, :_reduce_47,
  1, 174, :_reduce_none,
  1, 162, :_reduce_none,
  1, 165, :_reduce_none,
  1, 165, :_reduce_none,
  1, 179, :_reduce_none,
  4, 179, :_reduce_53,
  0, 187, :_reduce_54,
  5, 184, :_reduce_55,
  1, 186, :_reduce_none,
  2, 178, :_reduce_57,
  3, 178, :_reduce_58,
  4, 178, :_reduce_59,
  5, 178, :_reduce_60,
  4, 178, :_reduce_61,
  5, 178, :_reduce_62,
  2, 178, :_reduce_63,
  2, 178, :_reduce_64,
  2, 178, :_reduce_65,
  2, 178, :_reduce_66,
  2, 178, :_reduce_67,
  1, 164, :_reduce_68,
  3, 164, :_reduce_69,
  1, 191, :_reduce_70,
  3, 191, :_reduce_71,
  1, 190, :_reduce_none,
  2, 190, :_reduce_73,
  3, 190, :_reduce_74,
  5, 190, :_reduce_75,
  2, 190, :_reduce_76,
  4, 190, :_reduce_77,
  2, 190, :_reduce_78,
  4, 190, :_reduce_79,
  1, 190, :_reduce_80,
  3, 190, :_reduce_81,
  1, 194, :_reduce_none,
  3, 194, :_reduce_83,
  2, 193, :_reduce_84,
  3, 193, :_reduce_85,
  1, 196, :_reduce_86,
  3, 196, :_reduce_87,
  1, 195, :_reduce_88,
  1, 195, :_reduce_89,
  4, 195, :_reduce_90,
  3, 195, :_reduce_91,
  3, 195, :_reduce_92,
  3, 195, :_reduce_93,
  3, 195, :_reduce_94,
  2, 195, :_reduce_95,
  1, 195, :_reduce_96,
  1, 171, :_reduce_97,
  1, 171, :_reduce_98,
  4, 171, :_reduce_99,
  3, 171, :_reduce_100,
  3, 171, :_reduce_101,
  3, 171, :_reduce_102,
  3, 171, :_reduce_103,
  2, 171, :_reduce_104,
  1, 171, :_reduce_105,
  1, 199, :_reduce_106,
  1, 199, :_reduce_none,
  2, 200, :_reduce_108,
  1, 200, :_reduce_109,
  3, 200, :_reduce_110,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 201, :_reduce_none,
  1, 204, :_reduce_116,
  1, 204, :_reduce_none,
  1, 160, :_reduce_none,
  1, 160, :_reduce_none,
  1, 161, :_reduce_120,
  0, 207, :_reduce_121,
  4, 161, :_reduce_122,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 202, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  1, 203, :_reduce_none,
  3, 177, :_reduce_194,
  5, 177, :_reduce_195,
  3, 177, :_reduce_196,
  5, 177, :_reduce_197,
  6, 177, :_reduce_198,
  5, 177, :_reduce_199,
  5, 177, :_reduce_200,
  5, 177, :_reduce_201,
  5, 177, :_reduce_202,
  4, 177, :_reduce_203,
  3, 177, :_reduce_204,
  3, 177, :_reduce_205,
  3, 177, :_reduce_206,
  3, 177, :_reduce_207,
  3, 177, :_reduce_208,
  3, 177, :_reduce_209,
  3, 177, :_reduce_210,
  3, 177, :_reduce_211,
  3, 177, :_reduce_212,
  4, 177, :_reduce_213,
  2, 177, :_reduce_214,
  2, 177, :_reduce_215,
  3, 177, :_reduce_216,
  3, 177, :_reduce_217,
  3, 177, :_reduce_218,
  3, 177, :_reduce_219,
  3, 177, :_reduce_220,
  3, 177, :_reduce_221,
  3, 177, :_reduce_222,
  3, 177, :_reduce_223,
  3, 177, :_reduce_224,
  3, 177, :_reduce_225,
  3, 177, :_reduce_226,
  3, 177, :_reduce_227,
  3, 177, :_reduce_228,
  2, 177, :_reduce_229,
  2, 177, :_reduce_230,
  3, 177, :_reduce_231,
  3, 177, :_reduce_232,
  3, 177, :_reduce_233,
  3, 177, :_reduce_234,
  3, 177, :_reduce_235,
  0, 211, :_reduce_236,
  0, 212, :_reduce_237,
  8, 177, :_reduce_238,
  1, 177, :_reduce_none,
  1, 210, :_reduce_none,
  1, 213, :_reduce_none,
  2, 213, :_reduce_none,
  4, 213, :_reduce_243,
  2, 213, :_reduce_244,
  3, 218, :_reduce_245,
  0, 219, :_reduce_246,
  1, 219, :_reduce_none,
  0, 168, :_reduce_248,
  1, 168, :_reduce_none,
  2, 168, :_reduce_none,
  4, 168, :_reduce_251,
  2, 168, :_reduce_252,
  1, 189, :_reduce_253,
  2, 189, :_reduce_254,
  2, 189, :_reduce_255,
  4, 189, :_reduce_256,
  1, 189, :_reduce_257,
  0, 222, :_reduce_258,
  2, 183, :_reduce_259,
  2, 221, :_reduce_260,
  2, 220, :_reduce_261,
  0, 220, :_reduce_262,
  1, 215, :_reduce_263,
  2, 215, :_reduce_264,
  3, 215, :_reduce_265,
  4, 215, :_reduce_266,
  1, 173, :_reduce_267,
  1, 173, :_reduce_none,
  3, 172, :_reduce_269,
  4, 172, :_reduce_270,
  2, 172, :_reduce_271,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_none,
  1, 209, :_reduce_282,
  0, 245, :_reduce_283,
  4, 209, :_reduce_284,
  0, 246, :_reduce_285,
  0, 247, :_reduce_286,
  6, 209, :_reduce_287,
  0, 248, :_reduce_288,
  4, 209, :_reduce_289,
  3, 209, :_reduce_290,
  3, 209, :_reduce_291,
  2, 209, :_reduce_292,
  3, 209, :_reduce_293,
  3, 209, :_reduce_294,
  1, 209, :_reduce_295,
  4, 209, :_reduce_296,
  3, 209, :_reduce_297,
  1, 209, :_reduce_298,
  5, 209, :_reduce_299,
  4, 209, :_reduce_300,
  3, 209, :_reduce_301,
  2, 209, :_reduce_302,
  1, 209, :_reduce_none,
  2, 209, :_reduce_304,
  2, 209, :_reduce_305,
  6, 209, :_reduce_306,
  6, 209, :_reduce_307,
  0, 249, :_reduce_308,
  0, 250, :_reduce_309,
  7, 209, :_reduce_310,
  0, 251, :_reduce_311,
  0, 252, :_reduce_312,
  7, 209, :_reduce_313,
  5, 209, :_reduce_314,
  4, 209, :_reduce_315,
  0, 253, :_reduce_316,
  0, 254, :_reduce_317,
  9, 209, :_reduce_318,
  0, 255, :_reduce_319,
  6, 209, :_reduce_320,
  0, 256, :_reduce_321,
  7, 209, :_reduce_322,
  0, 257, :_reduce_323,
  5, 209, :_reduce_324,
  0, 258, :_reduce_325,
  6, 209, :_reduce_326,
  0, 259, :_reduce_327,
  0, 260, :_reduce_328,
  9, 209, :_reduce_329,
  1, 209, :_reduce_330,
  1, 209, :_reduce_331,
  1, 209, :_reduce_332,
  1, 209, :_reduce_333,
  1, 167, :_reduce_none,
  1, 236, :_reduce_none,
  1, 236, :_reduce_none,
  2, 236, :_reduce_337,
  1, 238, :_reduce_none,
  1, 238, :_reduce_none,
  1, 237, :_reduce_none,
  5, 237, :_reduce_341,
  1, 156, :_reduce_none,
  2, 156, :_reduce_343,
  1, 240, :_reduce_none,
  1, 240, :_reduce_none,
  1, 261, :_reduce_346,
  3, 261, :_reduce_347,
  1, 264, :_reduce_348,
  3, 264, :_reduce_349,
  1, 263, :_reduce_none,
  4, 263, :_reduce_351,
  6, 263, :_reduce_352,
  3, 263, :_reduce_353,
  5, 263, :_reduce_354,
  2, 263, :_reduce_355,
  4, 263, :_reduce_356,
  1, 263, :_reduce_357,
  3, 263, :_reduce_358,
  4, 265, :_reduce_359,
  2, 265, :_reduce_360,
  2, 265, :_reduce_361,
  1, 265, :_reduce_362,
  2, 270, :_reduce_363,
  0, 270, :_reduce_364,
  6, 271, :_reduce_365,
  8, 271, :_reduce_366,
  4, 271, :_reduce_367,
  6, 271, :_reduce_368,
  4, 271, :_reduce_369,
  2, 271, :_reduce_none,
  6, 271, :_reduce_371,
  2, 271, :_reduce_372,
  4, 271, :_reduce_373,
  6, 271, :_reduce_374,
  2, 271, :_reduce_375,
  4, 271, :_reduce_376,
  2, 271, :_reduce_377,
  4, 271, :_reduce_378,
  1, 271, :_reduce_none,
  0, 185, :_reduce_380,
  1, 185, :_reduce_381,
  3, 275, :_reduce_382,
  1, 275, :_reduce_383,
  4, 275, :_reduce_384,
  1, 276, :_reduce_385,
  4, 276, :_reduce_386,
  1, 277, :_reduce_387,
  3, 277, :_reduce_388,
  1, 278, :_reduce_389,
  1, 278, :_reduce_none,
  0, 282, :_reduce_391,
  0, 283, :_reduce_392,
  4, 235, :_reduce_393,
  4, 280, :_reduce_394,
  1, 280, :_reduce_395,
  3, 281, :_reduce_396,
  3, 281, :_reduce_397,
  0, 286, :_reduce_398,
  5, 285, :_reduce_399,
  2, 180, :_reduce_400,
  4, 180, :_reduce_401,
  5, 180, :_reduce_402,
  5, 180, :_reduce_403,
  2, 234, :_reduce_404,
  4, 234, :_reduce_405,
  4, 234, :_reduce_406,
  3, 234, :_reduce_407,
  3, 234, :_reduce_408,
  3, 234, :_reduce_409,
  2, 234, :_reduce_410,
  1, 234, :_reduce_411,
  4, 234, :_reduce_412,
  0, 288, :_reduce_413,
  5, 233, :_reduce_414,
  0, 289, :_reduce_415,
  5, 233, :_reduce_416,
  5, 239, :_reduce_417,
  1, 290, :_reduce_418,
  1, 290, :_reduce_none,
  6, 155, :_reduce_420,
  0, 155, :_reduce_421,
  1, 291, :_reduce_422,
  1, 291, :_reduce_none,
  1, 291, :_reduce_none,
  2, 292, :_reduce_425,
  1, 292, :_reduce_none,
  2, 157, :_reduce_427,
  1, 157, :_reduce_none,
  1, 223, :_reduce_none,
  1, 223, :_reduce_none,
  1, 223, :_reduce_none,
  1, 224, :_reduce_432,
  1, 294, :_reduce_433,
  2, 294, :_reduce_434,
  3, 295, :_reduce_435,
  1, 295, :_reduce_436,
  1, 295, :_reduce_437,
  3, 225, :_reduce_438,
  4, 226, :_reduce_439,
  3, 227, :_reduce_440,
  0, 299, :_reduce_441,
  3, 299, :_reduce_442,
  1, 300, :_reduce_443,
  2, 300, :_reduce_444,
  3, 229, :_reduce_445,
  0, 302, :_reduce_446,
  3, 302, :_reduce_447,
  3, 228, :_reduce_448,
  3, 230, :_reduce_449,
  0, 303, :_reduce_450,
  3, 303, :_reduce_451,
  0, 304, :_reduce_452,
  3, 304, :_reduce_453,
  0, 296, :_reduce_454,
  2, 296, :_reduce_455,
  0, 297, :_reduce_456,
  2, 297, :_reduce_457,
  0, 298, :_reduce_458,
  2, 298, :_reduce_459,
  1, 301, :_reduce_460,
  2, 301, :_reduce_461,
  0, 306, :_reduce_462,
  4, 301, :_reduce_463,
  1, 305, :_reduce_464,
  1, 305, :_reduce_465,
  1, 305, :_reduce_466,
  1, 305, :_reduce_none,
  1, 205, :_reduce_468,
  3, 206, :_reduce_469,
  1, 293, :_reduce_470,
  2, 293, :_reduce_471,
  1, 208, :_reduce_472,
  1, 208, :_reduce_473,
  1, 208, :_reduce_474,
  1, 208, :_reduce_475,
  1, 197, :_reduce_476,
  1, 197, :_reduce_477,
  1, 197, :_reduce_478,
  1, 197, :_reduce_479,
  1, 197, :_reduce_480,
  1, 198, :_reduce_481,
  1, 198, :_reduce_482,
  1, 198, :_reduce_483,
  1, 198, :_reduce_484,
  1, 198, :_reduce_485,
  1, 198, :_reduce_486,
  1, 198, :_reduce_487,
  1, 231, :_reduce_488,
  1, 231, :_reduce_489,
  1, 166, :_reduce_490,
  1, 166, :_reduce_491,
  1, 170, :_reduce_492,
  1, 170, :_reduce_493,
  1, 241, :_reduce_494,
  0, 307, :_reduce_495,
  4, 241, :_reduce_496,
  2, 241, :_reduce_497,
  3, 243, :_reduce_498,
  0, 309, :_reduce_499,
  3, 243, :_reduce_500,
  4, 308, :_reduce_501,
  2, 308, :_reduce_502,
  2, 308, :_reduce_503,
  1, 308, :_reduce_504,
  2, 311, :_reduce_505,
  0, 311, :_reduce_506,
  6, 284, :_reduce_507,
  8, 284, :_reduce_508,
  4, 284, :_reduce_509,
  6, 284, :_reduce_510,
  4, 284, :_reduce_511,
  6, 284, :_reduce_512,
  2, 284, :_reduce_513,
  4, 284, :_reduce_514,
  6, 284, :_reduce_515,
  2, 284, :_reduce_516,
  4, 284, :_reduce_517,
  2, 284, :_reduce_518,
  4, 284, :_reduce_519,
  1, 284, :_reduce_520,
  0, 284, :_reduce_521,
  1, 279, :_reduce_522,
  1, 279, :_reduce_523,
  1, 279, :_reduce_524,
  1, 279, :_reduce_525,
  1, 262, :_reduce_none,
  1, 262, :_reduce_527,
  1, 313, :_reduce_528,
  1, 314, :_reduce_529,
  3, 314, :_reduce_530,
  1, 272, :_reduce_531,
  3, 272, :_reduce_532,
  1, 315, :_reduce_533,
  2, 316, :_reduce_534,
  1, 316, :_reduce_535,
  2, 317, :_reduce_536,
  1, 317, :_reduce_537,
  1, 266, :_reduce_538,
  3, 266, :_reduce_539,
  1, 310, :_reduce_540,
  3, 310, :_reduce_541,
  1, 318, :_reduce_none,
  1, 318, :_reduce_none,
  2, 267, :_reduce_544,
  1, 267, :_reduce_545,
  3, 319, :_reduce_546,
  3, 320, :_reduce_547,
  1, 273, :_reduce_548,
  3, 273, :_reduce_549,
  1, 312, :_reduce_550,
  3, 312, :_reduce_551,
  1, 321, :_reduce_none,
  1, 321, :_reduce_none,
  2, 274, :_reduce_554,
  1, 274, :_reduce_555,
  1, 322, :_reduce_none,
  1, 322, :_reduce_none,
  2, 269, :_reduce_558,
  2, 268, :_reduce_559,
  0, 268, :_reduce_560,
  1, 244, :_reduce_none,
  3, 244, :_reduce_562,
  0, 232, :_reduce_563,
  2, 232, :_reduce_none,
  1, 217, :_reduce_565,
  3, 217, :_reduce_566,
  3, 323, :_reduce_567,
  2, 323, :_reduce_568,
  4, 323, :_reduce_569,
  2, 323, :_reduce_570,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 188, :_reduce_none,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 182, :_reduce_none,
  1, 287, :_reduce_none,
  1, 287, :_reduce_none,
  1, 287, :_reduce_none,
  1, 181, :_reduce_none,
  1, 181, :_reduce_none,
  0, 149, :_reduce_none,
  1, 149, :_reduce_none,
  0, 176, :_reduce_none,
  1, 176, :_reduce_none,
  2, 192, :_reduce_587,
  2, 169, :_reduce_588,
  0, 216, :_reduce_none,
  1, 216, :_reduce_none,
  1, 216, :_reduce_none,
  1, 242, :_reduce_592,
  1, 242, :_reduce_none,
  1, 151, :_reduce_none,
  2, 151, :_reduce_none,
  0, 214, :_reduce_596 ]

racc_reduce_n = 597

racc_shift_n = 1024

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tREGEXP_END => 63,
  :tUPLUS => 64,
  :tUMINUS => 65,
  :tUMINUS_NUM => 66,
  :tPOW => 67,
  :tCMP => 68,
  :tEQ => 69,
  :tEQQ => 70,
  :tNEQ => 71,
  :tGEQ => 72,
  :tLEQ => 73,
  :tANDOP => 74,
  :tOROP => 75,
  :tMATCH => 76,
  :tNMATCH => 77,
  :tDOT => 78,
  :tDOT2 => 79,
  :tDOT3 => 80,
  :tAREF => 81,
  :tASET => 82,
  :tLSHFT => 83,
  :tRSHFT => 84,
  :tCOLON2 => 85,
  :tCOLON3 => 86,
  :tOP_ASGN => 87,
  :tASSOC => 88,
  :tLPAREN => 89,
  :tLPAREN2 => 90,
  :tRPAREN => 91,
  :tLPAREN_ARG => 92,
  :tLBRACK => 93,
  :tLBRACK2 => 94,
  :tRBRACK => 95,
  :tLBRACE => 96,
  :tLBRACE_ARG => 97,
  :tSTAR => 98,
  :tSTAR2 => 99,
  :tAMPER => 100,
  :tAMPER2 => 101,
  :tTILDE => 102,
  :tPERCENT => 103,
  :tDIVIDE => 104,
  :tDSTAR => 105,
  :tPLUS => 106,
  :tMINUS => 107,
  :tLT => 108,
  :tGT => 109,
  :tPIPE => 110,
  :tBANG => 111,
  :tCARET => 112,
  :tLCURLY => 113,
  :tRCURLY => 114,
  :tBACK_REF2 => 115,
  :tSYMBEG => 116,
  :tSTRING_BEG => 117,
  :tXSTRING_BEG => 118,
  :tREGEXP_BEG => 119,
  :tREGEXP_OPT => 120,
  :tWORDS_BEG => 121,
  :tQWORDS_BEG => 122,
  :tSYMBOLS_BEG => 123,
  :tQSYMBOLS_BEG => 124,
  :tSTRING_DBEG => 125,
  :tSTRING_DVAR => 126,
  :tSTRING_END => 127,
  :tSTRING_DEND => 128,
  :tSTRING => 129,
  :tSYMBOL => 130,
  :tNL => 131,
  :tEH => 132,
  :tCOLON => 133,
  :tCOMMA => 134,
  :tSPACE => 135,
  :tSEMI => 136,
  :tLAMBDA => 137,
  :tLAMBEG => 138,
  :tCHARACTER => 139,
  :tRATIONAL => 140,
  :tIMAGINARY => 141,
  :tLABEL_END => 142,
  :tEQL => 143,
  :tLOWEST => 144 }

racc_nt_base = 145

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tREGEXP_END",
  "tUPLUS",
  "tUMINUS",
  "tUMINUS_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tDSTAR",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tREGEXP_OPT",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSYMBOLS_BEG",
  "tQSYMBOLS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING_DEND",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tCHARACTER",
  "tRATIONAL",
  "tIMAGINARY",
  "tLABEL_END",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "stmt_or_begin",
  "fitem",
  "undef_list",
  "expr_value",
  "command_asgn",
  "mlhs",
  "command_call",
  "var_lhs",
  "primary_value",
  "opt_call_args",
  "rbracket",
  "backref",
  "lhs",
  "mrhs",
  "mrhs_arg",
  "expr",
  "@1",
  "opt_nl",
  "arg",
  "command",
  "block_command",
  "block_call",
  "dot_or_colon",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_param",
  "fcall",
  "@2",
  "operation",
  "call_args",
  "mlhs_basic",
  "mlhs_inner",
  "rparen",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "user_variable",
  "keyword_variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "simple_numeric",
  "primary",
  "arg_value",
  "@4",
  "@5",
  "aref_args",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "@6",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "symbols",
  "qsymbols",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "superclass",
  "term",
  "f_arglist",
  "singleton",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "@20",
  "@21",
  "@22",
  "f_marg",
  "f_norm_arg",
  "f_margs",
  "f_marg_list",
  "block_args_tail",
  "f_block_kwarg",
  "f_kwrest",
  "opt_f_block_arg",
  "f_block_arg",
  "opt_block_args_tail",
  "block_param",
  "f_arg",
  "f_block_optarg",
  "f_rest_arg",
  "block_param_def",
  "opt_bv_decl",
  "bv_decls",
  "bvar",
  "f_bad_arg",
  "f_larglist",
  "lambda_body",
  "@23",
  "@24",
  "f_args",
  "do_block",
  "@25",
  "operation3",
  "@26",
  "@27",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "regexp_contents",
  "word_list",
  "word",
  "string_content",
  "symbol_list",
  "qword_list",
  "qsym_list",
  "string_dvar",
  "@28",
  "@29",
  "args_tail",
  "@30",
  "f_kwarg",
  "opt_args_tail",
  "f_optarg",
  "f_arg_asgn",
  "f_arg_item",
  "f_label",
  "f_kw",
  "f_block_kw",
  "kwrest_mark",
  "f_opt",
  "f_block_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

module_eval(<<'.,.,', 'ruby22.y', 78)
  def _reduce_2(val, _values, result)
                          result = @builder.compstmt(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 83)
  def _reduce_3(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 87)
  def _reduce_4(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 91)
  def _reduce_5(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 95)
  def _reduce_6(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

# reduce 7 omitted

module_eval(<<'.,.,', 'ruby22.y', 101)
  def _reduce_8(val, _values, result)
                          result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 106)
  def _reduce_9(val, _values, result)
                          rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 122)
  def _reduce_10(val, _values, result)
                          result = @builder.compstmt(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 127)
  def _reduce_11(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 131)
  def _reduce_12(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 135)
  def _reduce_13(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 139)
  def _reduce_14(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

# reduce 15 omitted

module_eval(<<'.,.,', 'ruby22.y', 145)
  def _reduce_16(val, _values, result)
                          diagnostic :error, :begin_in_method, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 150)
  def _reduce_17(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 154)
  def _reduce_18(val, _values, result)
                          result = @builder.alias(val[0], val[1], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 158)
  def _reduce_19(val, _values, result)
                          result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 164)
  def _reduce_20(val, _values, result)
                          result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 170)
  def _reduce_21(val, _values, result)
                          diagnostic :error, :nth_ref_alias, nil, val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 174)
  def _reduce_22(val, _values, result)
                          result = @builder.undef_method(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 178)
  def _reduce_23(val, _values, result)
                          result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 183)
  def _reduce_24(val, _values, result)
                          result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 188)
  def _reduce_25(val, _values, result)
                          result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 192)
  def _reduce_26(val, _values, result)
                          result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 196)
  def _reduce_27(val, _values, result)
                          rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 204)
  def _reduce_28(val, _values, result)
                          result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
  end
.,.,

# reduce 29 omitted

module_eval(<<'.,.,', 'ruby22.y', 209)
  def _reduce_30(val, _values, result)
                          result = @builder.multi_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 213)
  def _reduce_31(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 217)
  def _reduce_32(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 224)
  def _reduce_33(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 231)
  def _reduce_34(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 238)
  def _reduce_35(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 245)
  def _reduce_36(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 252)
  def _reduce_37(val, _values, result)
                          @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 256)
  def _reduce_38(val, _values, result)
                          result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 261)
  def _reduce_39(val, _values, result)
                          result = @builder.multi_assign(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 40 omitted

module_eval(<<'.,.,', 'ruby22.y', 267)
  def _reduce_41(val, _values, result)
                          result = @builder.assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 271)
  def _reduce_42(val, _values, result)
                          result = @builder.assign(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 43 omitted

module_eval(<<'.,.,', 'ruby22.y', 277)
  def _reduce_44(val, _values, result)
                          result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 281)
  def _reduce_45(val, _values, result)
                          result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 285)
  def _reduce_46(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[2], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 289)
  def _reduce_47(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[1], nil)

    result
  end
.,.,

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

module_eval(<<'.,.,', 'ruby22.y', 301)
  def _reduce_53(val, _values, result)
                          result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 307)
  def _reduce_54(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 311)
  def _reduce_55(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

# reduce 56 omitted

module_eval(<<'.,.,', 'ruby22.y', 320)
  def _reduce_57(val, _values, result)
                          result = @builder.call_method(nil, nil, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 325)
  def _reduce_58(val, _values, result)
                          method_call = @builder.call_method(nil, nil, val[0],
                                        nil, val[1], nil)

                      begin_t, args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 334)
  def _reduce_59(val, _values, result)
                          result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 339)
  def _reduce_60(val, _values, result)
                          method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 348)
  def _reduce_61(val, _values, result)
                          result = @builder.call_method(val[0], val[1], val[2],
                                  nil, val[3], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 353)
  def _reduce_62(val, _values, result)
                          method_call = @builder.call_method(val[0], val[1], val[2],
                                        nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 362)
  def _reduce_63(val, _values, result)
                          result = @builder.keyword_cmd(:super, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 367)
  def _reduce_64(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 372)
  def _reduce_65(val, _values, result)
                          result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 377)
  def _reduce_66(val, _values, result)
                          result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 382)
  def _reduce_67(val, _values, result)
                          result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 388)
  def _reduce_68(val, _values, result)
                          result = @builder.multi_lhs(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 392)
  def _reduce_69(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 397)
  def _reduce_70(val, _values, result)
                          result = @builder.multi_lhs(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 401)
  def _reduce_71(val, _values, result)
                          result = @builder.multi_lhs(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 72 omitted

module_eval(<<'.,.,', 'ruby22.y', 407)
  def _reduce_73(val, _values, result)
                          result = val[0].
                                  push(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 412)
  def _reduce_74(val, _values, result)
                          result = val[0].
                                  push(@builder.splat(val[1], val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 417)
  def _reduce_75(val, _values, result)
                          result = val[0].
                                  push(@builder.splat(val[1], val[2])).
                                  concat(val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 423)
  def _reduce_76(val, _values, result)
                          result = val[0].
                                  push(@builder.splat(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 428)
  def _reduce_77(val, _values, result)
                          result = val[0].
                                  push(@builder.splat(val[1])).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 434)
  def _reduce_78(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 438)
  def _reduce_79(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]),
                                 *val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 443)
  def _reduce_80(val, _values, result)
                          result = [ @builder.splat(val[0]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 447)
  def _reduce_81(val, _values, result)
                          result = [ @builder.splat(val[0]),
                                 *val[2] ]

    result
  end
.,.,

# reduce 82 omitted

module_eval(<<'.,.,', 'ruby22.y', 454)
  def _reduce_83(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 459)
  def _reduce_84(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 463)
  def _reduce_85(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 468)
  def _reduce_86(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 472)
  def _reduce_87(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 477)
  def _reduce_88(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 481)
  def _reduce_89(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 485)
  def _reduce_90(val, _values, result)
                          result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 489)
  def _reduce_91(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 493)
  def _reduce_92(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 497)
  def _reduce_93(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 501)
  def _reduce_94(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 506)
  def _reduce_95(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 511)
  def _reduce_96(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 516)
  def _reduce_97(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 520)
  def _reduce_98(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 524)
  def _reduce_99(val, _values, result)
                          result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 528)
  def _reduce_100(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 532)
  def _reduce_101(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 536)
  def _reduce_102(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 540)
  def _reduce_103(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 545)
  def _reduce_104(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 550)
  def _reduce_105(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 555)
  def _reduce_106(val, _values, result)
                          diagnostic :error, :module_name_const, nil, val[0]

    result
  end
.,.,

# reduce 107 omitted

module_eval(<<'.,.,', 'ruby22.y', 561)
  def _reduce_108(val, _values, result)
                          result = @builder.const_global(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 565)
  def _reduce_109(val, _values, result)
                          result = @builder.const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 569)
  def _reduce_110(val, _values, result)
                          result = @builder.const_fetch(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

module_eval(<<'.,.,', 'ruby22.y', 578)
  def _reduce_116(val, _values, result)
                          result = @builder.symbol(val[0])

    result
  end
.,.,

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

module_eval(<<'.,.,', 'ruby22.y', 587)
  def _reduce_120(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 591)
  def _reduce_121(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 595)
  def _reduce_122(val, _values, result)
                          result = val[0] << val[3]

    result
  end
.,.,

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

module_eval(<<'.,.,', 'ruby22.y', 616)
  def _reduce_194(val, _values, result)
                          result = @builder.assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 620)
  def _reduce_195(val, _values, result)
                          rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 630)
  def _reduce_196(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 634)
  def _reduce_197(val, _values, result)
                          rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result = @builder.op_assign(val[0], val[1], rescue_)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 644)
  def _reduce_198(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 651)
  def _reduce_199(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 658)
  def _reduce_200(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 665)
  def _reduce_201(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 672)
  def _reduce_202(val, _values, result)
                          const  = @builder.const_op_assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))
                      result = @builder.op_assign(const, val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 678)
  def _reduce_203(val, _values, result)
                          const  = @builder.const_op_assignable(
                                  @builder.const_global(val[0], val[1]))
                      result = @builder.op_assign(const, val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 684)
  def _reduce_204(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 688)
  def _reduce_205(val, _values, result)
                          result = @builder.range_inclusive(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 692)
  def _reduce_206(val, _values, result)
                          result = @builder.range_exclusive(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 696)
  def _reduce_207(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 700)
  def _reduce_208(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 704)
  def _reduce_209(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 708)
  def _reduce_210(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 712)
  def _reduce_211(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 716)
  def _reduce_212(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 720)
  def _reduce_213(val, _values, result)
                          result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    val[1], val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 726)
  def _reduce_214(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 730)
  def _reduce_215(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 734)
  def _reduce_216(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 738)
  def _reduce_217(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 742)
  def _reduce_218(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 746)
  def _reduce_219(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 750)
  def _reduce_220(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 754)
  def _reduce_221(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 758)
  def _reduce_222(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 762)
  def _reduce_223(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 766)
  def _reduce_224(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 770)
  def _reduce_225(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 774)
  def _reduce_226(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 778)
  def _reduce_227(val, _values, result)
                          result = @builder.match_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 782)
  def _reduce_228(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 786)
  def _reduce_229(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 790)
  def _reduce_230(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 794)
  def _reduce_231(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 798)
  def _reduce_232(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 802)
  def _reduce_233(val, _values, result)
                          result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 806)
  def _reduce_234(val, _values, result)
                          result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 810)
  def _reduce_235(val, _values, result)
                          result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 820)
  def _reduce_236(val, _values, result)
                          @lexer.push_cond
                      @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 825)
  def _reduce_237(val, _values, result)
                          @lexer.pop_cond

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 829)
  def _reduce_238(val, _values, result)
                          result = @builder.ternary(val[0], val[1],
                                                val[3], val[5], val[7])

    result
  end
.,.,

# reduce 239 omitted

# reduce 240 omitted

# reduce 241 omitted

# reduce 242 omitted

module_eval(<<'.,.,', 'ruby22.y', 840)
  def _reduce_243(val, _values, result)
                          result = val[0] << @builder.associate(nil, val[2], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 844)
  def _reduce_244(val, _values, result)
                          result = [ @builder.associate(nil, val[0], nil) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 849)
  def _reduce_245(val, _values, result)
                          result = val

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 854)
  def _reduce_246(val, _values, result)
                          result = [ nil, [], nil ]

    result
  end
.,.,

# reduce 247 omitted

module_eval(<<'.,.,', 'ruby22.y', 860)
  def _reduce_248(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 249 omitted

# reduce 250 omitted

module_eval(<<'.,.,', 'ruby22.y', 866)
  def _reduce_251(val, _values, result)
                          result = val[0] << @builder.associate(nil, val[2], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 870)
  def _reduce_252(val, _values, result)
                          result = [ @builder.associate(nil, val[0], nil) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 875)
  def _reduce_253(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 879)
  def _reduce_254(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 883)
  def _reduce_255(val, _values, result)
                          result = [ @builder.associate(nil, val[0], nil) ]
                      result.concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 888)
  def _reduce_256(val, _values, result)
                          assocs = @builder.associate(nil, val[2], nil)
                      result = val[0] << assocs
                      result.concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 894)
  def _reduce_257(val, _values, result)
                          result =  [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 898)
  def _reduce_258(val, _values, result)
                          result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 903)
  def _reduce_259(val, _values, result)
                          @lexer.cmdarg = val[0]

                      result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 910)
  def _reduce_260(val, _values, result)
                          result = @builder.block_pass(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 915)
  def _reduce_261(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 919)
  def _reduce_262(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 924)
  def _reduce_263(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 928)
  def _reduce_264(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 932)
  def _reduce_265(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 936)
  def _reduce_266(val, _values, result)
                          result = val[0] << @builder.splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 941)
  def _reduce_267(val, _values, result)
                          result = @builder.array(nil, val[0], nil)

    result
  end
.,.,

# reduce 268 omitted

module_eval(<<'.,.,', 'ruby22.y', 947)
  def _reduce_269(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 951)
  def _reduce_270(val, _values, result)
                          result = val[0] << @builder.splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 955)
  def _reduce_271(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

# reduce 279 omitted

# reduce 280 omitted

# reduce 281 omitted

module_eval(<<'.,.,', 'ruby22.y', 970)
  def _reduce_282(val, _values, result)
                          result = @builder.call_method(nil, nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 974)
  def _reduce_283(val, _values, result)
                          result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 979)
  def _reduce_284(val, _values, result)
                          @lexer.cmdarg = val[1]

                      result = @builder.begin_keyword(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 985)
  def _reduce_285(val, _values, result)
                          result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 990)
  def _reduce_286(val, _values, result)
                          @lexer.state = :expr_endarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 994)
  def _reduce_287(val, _values, result)
                          @lexer.cmdarg = val[1]

                      result = @builder.begin(val[0], val[2], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1000)
  def _reduce_288(val, _values, result)
                          @lexer.state = :expr_endarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1004)
  def _reduce_289(val, _values, result)
                          result = @builder.begin(val[0], nil, val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1008)
  def _reduce_290(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1012)
  def _reduce_291(val, _values, result)
                          result = @builder.const_fetch(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1016)
  def _reduce_292(val, _values, result)
                          result = @builder.const_global(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1020)
  def _reduce_293(val, _values, result)
                          result = @builder.array(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1024)
  def _reduce_294(val, _values, result)
                          result = @builder.associate(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1028)
  def _reduce_295(val, _values, result)
                          result = @builder.keyword_cmd(:return, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1032)
  def _reduce_296(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1036)
  def _reduce_297(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1040)
  def _reduce_298(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1044)
  def _reduce_299(val, _values, result)
                          result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1049)
  def _reduce_300(val, _values, result)
                          result = @builder.not_op(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1053)
  def _reduce_301(val, _values, result)
                          result = @builder.not_op(val[0], val[1], nil, val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1057)
  def _reduce_302(val, _values, result)
                          method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

# reduce 303 omitted

module_eval(<<'.,.,', 'ruby22.y', 1066)
  def _reduce_304(val, _values, result)
                          begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1072)
  def _reduce_305(val, _values, result)
                          lambda_call = @builder.call_lambda(val[0])

                      args, (begin_t, body, end_t) = val[1]
                      result      = @builder.block(lambda_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1080)
  def _reduce_306(val, _values, result)
                          else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1087)
  def _reduce_307(val, _values, result)
                          else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1094)
  def _reduce_308(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1098)
  def _reduce_309(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1102)
  def _reduce_310(val, _values, result)
                          result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1107)
  def _reduce_311(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1111)
  def _reduce_312(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1115)
  def _reduce_313(val, _values, result)
                          result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1120)
  def _reduce_314(val, _values, result)
                          *when_bodies, (else_t, else_body) = *val[3]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1128)
  def _reduce_315(val, _values, result)
                          *when_bodies, (else_t, else_body) = *val[2]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1136)
  def _reduce_316(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1140)
  def _reduce_317(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1144)
  def _reduce_318(val, _values, result)
                          result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1150)
  def _reduce_319(val, _values, result)
                          @static_env.extend_static
                      @lexer.push_cmdarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1155)
  def _reduce_320(val, _values, result)
                          if in_def?
                        diagnostic :error, :class_in_def, nil, val[0]
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(val[0], val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      @lexer.pop_cmdarg
                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1169)
  def _reduce_321(val, _values, result)
                          result = @def_level
                      @def_level = 0

                      @static_env.extend_static
                      @lexer.push_cmdarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1177)
  def _reduce_322(val, _values, result)
                          result = @builder.def_sclass(val[0], val[1], val[2],
                                                   val[5], val[6])

                      @lexer.pop_cmdarg
                      @static_env.unextend

                      @def_level = val[4]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1187)
  def _reduce_323(val, _values, result)
                          @static_env.extend_static
                      @lexer.push_cmdarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1192)
  def _reduce_324(val, _values, result)
                          if in_def?
                        diagnostic :error, :module_in_def, nil, val[0]
                      end

                      result = @builder.def_module(val[0], val[1],
                                                   val[3], val[4])

                      @lexer.pop_cmdarg
                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1204)
  def _reduce_325(val, _values, result)
                          @def_level += 1
                      @static_env.extend_static
                      @lexer.push_cmdarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1210)
  def _reduce_326(val, _values, result)
                          result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      @lexer.pop_cmdarg
                      @static_env.unextend
                      @def_level -= 1

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1219)
  def _reduce_327(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1223)
  def _reduce_328(val, _values, result)
                          @def_level += 1
                      @static_env.extend_static
                      @lexer.push_cmdarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1229)
  def _reduce_329(val, _values, result)
                          result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      @lexer.pop_cmdarg
                      @static_env.unextend
                      @def_level -= 1

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1238)
  def _reduce_330(val, _values, result)
                          result = @builder.keyword_cmd(:break, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1242)
  def _reduce_331(val, _values, result)
                          result = @builder.keyword_cmd(:next, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1246)
  def _reduce_332(val, _values, result)
                          result = @builder.keyword_cmd(:redo, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1250)
  def _reduce_333(val, _values, result)
                          result = @builder.keyword_cmd(:retry, val[0])

    result
  end
.,.,

# reduce 334 omitted

# reduce 335 omitted

# reduce 336 omitted

module_eval(<<'.,.,', 'ruby22.y', 1259)
  def _reduce_337(val, _values, result)
                          result = val[1]

    result
  end
.,.,

# reduce 338 omitted

# reduce 339 omitted

# reduce 340 omitted

module_eval(<<'.,.,', 'ruby22.y', 1268)
  def _reduce_341(val, _values, result)
                          else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
  end
.,.,

# reduce 342 omitted

module_eval(<<'.,.,', 'ruby22.y', 1279)
  def _reduce_343(val, _values, result)
                          result = val

    result
  end
.,.,

# reduce 344 omitted

# reduce 345 omitted

module_eval(<<'.,.,', 'ruby22.y', 1287)
  def _reduce_346(val, _values, result)
                          result = @builder.arg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1291)
  def _reduce_347(val, _values, result)
                          result = @builder.multi_lhs(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1296)
  def _reduce_348(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1300)
  def _reduce_349(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

# reduce 350 omitted

module_eval(<<'.,.,', 'ruby22.y', 1306)
  def _reduce_351(val, _values, result)
                          result = val[0].
                                  push(@builder.restarg(val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1311)
  def _reduce_352(val, _values, result)
                          result = val[0].
                                  push(@builder.restarg(val[2], val[3])).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1317)
  def _reduce_353(val, _values, result)
                          result = val[0].
                                  push(@builder.restarg(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1322)
  def _reduce_354(val, _values, result)
                          result = val[0].
                                  push(@builder.restarg(val[2])).
                                  concat(val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1328)
  def _reduce_355(val, _values, result)
                          result = [ @builder.restarg(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1332)
  def _reduce_356(val, _values, result)
                          result = [ @builder.restarg(val[0], val[1]),
                                 *val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1337)
  def _reduce_357(val, _values, result)
                          result = [ @builder.restarg(val[0]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1341)
  def _reduce_358(val, _values, result)
                          result = [ @builder.restarg(val[0]),
                                 *val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1347)
  def _reduce_359(val, _values, result)
                          result = val[0].concat(val[2]).concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1351)
  def _reduce_360(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1355)
  def _reduce_361(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1359)
  def _reduce_362(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1365)
  def _reduce_363(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1369)
  def _reduce_364(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1374)
  def _reduce_365(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1381)
  def _reduce_366(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1389)
  def _reduce_367(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1395)
  def _reduce_368(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1402)
  def _reduce_369(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

# reduce 370 omitted

module_eval(<<'.,.,', 'ruby22.y', 1409)
  def _reduce_371(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1416)
  def _reduce_372(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1420)
  def _reduce_373(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1426)
  def _reduce_374(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1433)
  def _reduce_375(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1438)
  def _reduce_376(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1444)
  def _reduce_377(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1449)
  def _reduce_378(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

# reduce 379 omitted

module_eval(<<'.,.,', 'ruby22.y', 1457)
  def _reduce_380(val, _values, result)
                          result = @builder.args(nil, [], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1461)
  def _reduce_381(val, _values, result)
                          @lexer.state = :expr_value

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1466)
  def _reduce_382(val, _values, result)
                          result = @builder.args(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1470)
  def _reduce_383(val, _values, result)
                          result = @builder.args(val[0], [], val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1474)
  def _reduce_384(val, _values, result)
                          result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1479)
  def _reduce_385(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1483)
  def _reduce_386(val, _values, result)
                          result = val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1488)
  def _reduce_387(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1492)
  def _reduce_388(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1497)
  def _reduce_389(val, _values, result)
                          result = @builder.shadowarg(val[0])

    result
  end
.,.,

# reduce 390 omitted

module_eval(<<'.,.,', 'ruby22.y', 1502)
  def _reduce_391(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1506)
  def _reduce_392(val, _values, result)
                          result = @lexer.cmdarg.dup
                      @lexer.cmdarg.clear

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1511)
  def _reduce_393(val, _values, result)
                          @lexer.cmdarg = val[2]
                      @lexer.cmdarg.lexpop

                      result = [ val[1], val[3] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1521)
  def _reduce_394(val, _values, result)
                          result = @builder.args(val[0], val[1].concat(val[2]), val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1525)
  def _reduce_395(val, _values, result)
                          result = @builder.args(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1530)
  def _reduce_396(val, _values, result)
                          result = [ val[0], val[1], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1534)
  def _reduce_397(val, _values, result)
                          result = [ val[0], val[1], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1539)
  def _reduce_398(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1543)
  def _reduce_399(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1550)
  def _reduce_400(val, _values, result)
                          begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1556)
  def _reduce_401(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1562)
  def _reduce_402(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1572)
  def _reduce_403(val, _values, result)
                          method_call = @builder.call_method(val[0], val[1], val[2],
                                      nil, val[3], nil)

                      begin_t, args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1582)
  def _reduce_404(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1588)
  def _reduce_405(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1594)
  def _reduce_406(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1600)
  def _reduce_407(val, _values, result)
                          result = @builder.call_method(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1604)
  def _reduce_408(val, _values, result)
                          lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1610)
  def _reduce_409(val, _values, result)
                          lparen_t, args, rparen_t = val[2]
                      result = @builder.call_method(val[0], val[1], nil,
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1616)
  def _reduce_410(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1622)
  def _reduce_411(val, _values, result)
                          result = @builder.keyword_cmd(:zsuper, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1626)
  def _reduce_412(val, _values, result)
                          result = @builder.index(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1631)
  def _reduce_413(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1635)
  def _reduce_414(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1641)
  def _reduce_415(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1645)
  def _reduce_416(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1652)
  def _reduce_417(val, _values, result)
                          result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1658)
  def _reduce_418(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

# reduce 419 omitted

module_eval(<<'.,.,', 'ruby22.y', 1664)
  def _reduce_420(val, _values, result)
                          assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1677)
  def _reduce_421(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1682)
  def _reduce_422(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

# reduce 423 omitted

# reduce 424 omitted

module_eval(<<'.,.,', 'ruby22.y', 1689)
  def _reduce_425(val, _values, result)
                          result = [ val[0], val[1] ]

    result
  end
.,.,

# reduce 426 omitted

module_eval(<<'.,.,', 'ruby22.y', 1695)
  def _reduce_427(val, _values, result)
                          result = [ val[0], val[1] ]

    result
  end
.,.,

# reduce 428 omitted

# reduce 429 omitted

# reduce 430 omitted

# reduce 431 omitted

module_eval(<<'.,.,', 'ruby22.y', 1705)
  def _reduce_432(val, _values, result)
                          result = @builder.string_compose(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1710)
  def _reduce_433(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1714)
  def _reduce_434(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1719)
  def _reduce_435(val, _values, result)
                          result = @builder.string_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1723)
  def _reduce_436(val, _values, result)
                          result = @builder.string(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1727)
  def _reduce_437(val, _values, result)
                          result = @builder.character(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1732)
  def _reduce_438(val, _values, result)
                          result = @builder.xstring_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1737)
  def _reduce_439(val, _values, result)
                          opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1743)
  def _reduce_440(val, _values, result)
                          result = @builder.words_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1748)
  def _reduce_441(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1752)
  def _reduce_442(val, _values, result)
                          result = val[0] << @builder.word(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1757)
  def _reduce_443(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1761)
  def _reduce_444(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1766)
  def _reduce_445(val, _values, result)
                          result = @builder.symbols_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1771)
  def _reduce_446(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1775)
  def _reduce_447(val, _values, result)
                          result = val[0] << @builder.word(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1780)
  def _reduce_448(val, _values, result)
                          result = @builder.words_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1785)
  def _reduce_449(val, _values, result)
                          result = @builder.symbols_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1790)
  def _reduce_450(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1794)
  def _reduce_451(val, _values, result)
                          result = val[0] << @builder.string_internal(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1799)
  def _reduce_452(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1803)
  def _reduce_453(val, _values, result)
                          result = val[0] << @builder.symbol_internal(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1808)
  def _reduce_454(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1812)
  def _reduce_455(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1817)
  def _reduce_456(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1821)
  def _reduce_457(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1826)
  def _reduce_458(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1830)
  def _reduce_459(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1835)
  def _reduce_460(val, _values, result)
                          result = @builder.string_internal(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1839)
  def _reduce_461(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1843)
  def _reduce_462(val, _values, result)
                          @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1848)
  def _reduce_463(val, _values, result)
                          @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1856)
  def _reduce_464(val, _values, result)
                          result = @builder.gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1860)
  def _reduce_465(val, _values, result)
                          result = @builder.ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1864)
  def _reduce_466(val, _values, result)
                          result = @builder.cvar(val[0])

    result
  end
.,.,

# reduce 467 omitted

module_eval(<<'.,.,', 'ruby22.y', 1871)
  def _reduce_468(val, _values, result)
                          result = @builder.symbol(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1876)
  def _reduce_469(val, _values, result)
                          result = @builder.symbol_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1881)
  def _reduce_470(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1885)
  def _reduce_471(val, _values, result)
                          result = @builder.negate(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1890)
  def _reduce_472(val, _values, result)
                          result = @builder.integer(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1894)
  def _reduce_473(val, _values, result)
                          result = @builder.float(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1898)
  def _reduce_474(val, _values, result)
                          result = @builder.rational(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1902)
  def _reduce_475(val, _values, result)
                          result = @builder.complex(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1907)
  def _reduce_476(val, _values, result)
                          result = @builder.ident(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1911)
  def _reduce_477(val, _values, result)
                          result = @builder.ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1915)
  def _reduce_478(val, _values, result)
                          result = @builder.gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1919)
  def _reduce_479(val, _values, result)
                          result = @builder.const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1923)
  def _reduce_480(val, _values, result)
                          result = @builder.cvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1928)
  def _reduce_481(val, _values, result)
                          result = @builder.nil(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1932)
  def _reduce_482(val, _values, result)
                          result = @builder.self(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1936)
  def _reduce_483(val, _values, result)
                          result = @builder.true(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1940)
  def _reduce_484(val, _values, result)
                          result = @builder.false(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1944)
  def _reduce_485(val, _values, result)
                          result = @builder.__FILE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1948)
  def _reduce_486(val, _values, result)
                          result = @builder.__LINE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1952)
  def _reduce_487(val, _values, result)
                          result = @builder.__ENCODING__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1957)
  def _reduce_488(val, _values, result)
                          result = @builder.accessible(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1961)
  def _reduce_489(val, _values, result)
                          result = @builder.accessible(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1966)
  def _reduce_490(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1970)
  def _reduce_491(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1975)
  def _reduce_492(val, _values, result)
                          result = @builder.nth_ref(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1979)
  def _reduce_493(val, _values, result)
                          result = @builder.back_ref(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1984)
  def _reduce_494(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1988)
  def _reduce_495(val, _values, result)
                          @lexer.state = :expr_value

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1992)
  def _reduce_496(val, _values, result)
                          result = [ val[0], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 1996)
  def _reduce_497(val, _values, result)
                          yyerrok
                      result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2002)
  def _reduce_498(val, _values, result)
                          result = @builder.args(val[0], val[1], val[2])

                      @lexer.state = :expr_value

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2007)
  def _reduce_499(val, _values, result)
                          result = @lexer.in_kwarg
                      @lexer.in_kwarg = true

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2012)
  def _reduce_500(val, _values, result)
                          @lexer.in_kwarg = val[0]
                      result = @builder.args(nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2018)
  def _reduce_501(val, _values, result)
                          result = val[0].concat(val[2]).concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2022)
  def _reduce_502(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2026)
  def _reduce_503(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2030)
  def _reduce_504(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2035)
  def _reduce_505(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2039)
  def _reduce_506(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2044)
  def _reduce_507(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2051)
  def _reduce_508(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[6]).
                                  concat(val[7])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2059)
  def _reduce_509(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2065)
  def _reduce_510(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2072)
  def _reduce_511(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2078)
  def _reduce_512(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2085)
  def _reduce_513(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2090)
  def _reduce_514(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2096)
  def _reduce_515(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2103)
  def _reduce_516(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2108)
  def _reduce_517(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2114)
  def _reduce_518(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2119)
  def _reduce_519(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2125)
  def _reduce_520(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2129)
  def _reduce_521(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2134)
  def _reduce_522(val, _values, result)
                          diagnostic :error, :argument_const, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2138)
  def _reduce_523(val, _values, result)
                          diagnostic :error, :argument_ivar, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2142)
  def _reduce_524(val, _values, result)
                          diagnostic :error, :argument_gvar, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2146)
  def _reduce_525(val, _values, result)
                          diagnostic :error, :argument_cvar, nil, val[0]

    result
  end
.,.,

# reduce 526 omitted

module_eval(<<'.,.,', 'ruby22.y', 2152)
  def _reduce_527(val, _values, result)
                          @static_env.declare val[0][0]

                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2159)
  def _reduce_528(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2164)
  def _reduce_529(val, _values, result)
                          result = @builder.arg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2168)
  def _reduce_530(val, _values, result)
                          result = @builder.multi_lhs(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2173)
  def _reduce_531(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2177)
  def _reduce_532(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2182)
  def _reduce_533(val, _values, result)
                          check_kwarg_name(val[0])

                      @static_env.declare val[0][0]

                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2191)
  def _reduce_534(val, _values, result)
                          result = @builder.kwoptarg(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2195)
  def _reduce_535(val, _values, result)
                          result = @builder.kwarg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2200)
  def _reduce_536(val, _values, result)
                          result = @builder.kwoptarg(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2204)
  def _reduce_537(val, _values, result)
                          result = @builder.kwarg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2209)
  def _reduce_538(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2213)
  def _reduce_539(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2218)
  def _reduce_540(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2222)
  def _reduce_541(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

# reduce 542 omitted

# reduce 543 omitted

module_eval(<<'.,.,', 'ruby22.y', 2229)
  def _reduce_544(val, _values, result)
                          @static_env.declare val[1][0]

                      result = [ @builder.kwrestarg(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2235)
  def _reduce_545(val, _values, result)
                          result = [ @builder.kwrestarg(val[0]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2240)
  def _reduce_546(val, _values, result)
                          result = @builder.optarg(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2245)
  def _reduce_547(val, _values, result)
                          result = @builder.optarg(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2250)
  def _reduce_548(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2254)
  def _reduce_549(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2259)
  def _reduce_550(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2263)
  def _reduce_551(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

# reduce 552 omitted

# reduce 553 omitted

module_eval(<<'.,.,', 'ruby22.y', 2270)
  def _reduce_554(val, _values, result)
                          @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2276)
  def _reduce_555(val, _values, result)
                          result = [ @builder.restarg(val[0]) ]

    result
  end
.,.,

# reduce 556 omitted

# reduce 557 omitted

module_eval(<<'.,.,', 'ruby22.y', 2283)
  def _reduce_558(val, _values, result)
                          @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2290)
  def _reduce_559(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2294)
  def _reduce_560(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 561 omitted

module_eval(<<'.,.,', 'ruby22.y', 2300)
  def _reduce_562(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2305)
  def _reduce_563(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 564 omitted

module_eval(<<'.,.,', 'ruby22.y', 2311)
  def _reduce_565(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2315)
  def _reduce_566(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2320)
  def _reduce_567(val, _values, result)
                          result = @builder.pair(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2324)
  def _reduce_568(val, _values, result)
                          result = @builder.pair_keyword(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2328)
  def _reduce_569(val, _values, result)
                          result = @builder.pair_quoted(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2332)
  def _reduce_570(val, _values, result)
                          result = @builder.kwsplat(val[0], val[1])

    result
  end
.,.,

# reduce 571 omitted

# reduce 572 omitted

# reduce 573 omitted

# reduce 574 omitted

# reduce 575 omitted

# reduce 576 omitted

# reduce 577 omitted

# reduce 578 omitted

# reduce 579 omitted

# reduce 580 omitted

# reduce 581 omitted

# reduce 582 omitted

# reduce 583 omitted

# reduce 584 omitted

# reduce 585 omitted

# reduce 586 omitted

module_eval(<<'.,.,', 'ruby22.y', 2343)
  def _reduce_587(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby22.y', 2347)
  def _reduce_588(val, _values, result)
                          result = val[1]

    result
  end
.,.,

# reduce 589 omitted

# reduce 590 omitted

# reduce 591 omitted

module_eval(<<'.,.,', 'ruby22.y', 2353)
  def _reduce_592(val, _values, result)
                        yyerrok

    result
  end
.,.,

# reduce 593 omitted

# reduce 594 omitted

# reduce 595 omitted

module_eval(<<'.,.,', 'ruby22.y', 2362)
  def _reduce_596(val, _values, result)
                        result = nil

    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby22
end   # module Parser

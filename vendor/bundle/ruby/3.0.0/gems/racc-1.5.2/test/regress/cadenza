#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'

# racc_parser.rb : generated by racc

module Cadenza
  class RaccParser < Racc::Parser

module_eval(<<'...end cadenza.y/module_eval...', 'cadenza.y', 171)

...end cadenza.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
    37,    89,    65,    66,    20,    21,    22,    23,    24,    17,
   106,    37,     3,    76,     4,     3,    75,     4,    50,    37,
    29,    68,    65,    66,    33,    67,    34,   110,     9,    74,
    35,     9,    37,    36,    71,    33,    38,    34,    77,    78,
    79,    35,    72,    33,    36,    34,    37,    38,     3,    35,
    39,    83,    36,    54,    55,    38,    33,    89,    34,    37,
    90,     3,    35,    43,     9,    36,    85,   103,    38,   108,
    33,   109,    34,    56,    57,   111,    35,     9,     3,    36,
    46,   122,    38,    33,   112,    34,   113,    78,    79,    35,
   114,     3,    36,     4,     9,    38,    20,    21,    22,    23,
    24,    20,    21,    22,    23,    24,   115,     9,    65,    66,
    65,    66,    29,    54,    55,   120,   107,    29,    20,    21,
    22,    23,    24,    20,    21,    22,    23,    24,    20,    21,
    22,    23,    24,     3,    29,    39,     3,   121,     4,    29,
     3,     3,    43,    46,    29,     3,   124,    87,   125,     9,
    54,    55,     9,    56,    57,   128,     9,     9,     3,   103,
   116,     9,    20,    21,    22,    23,    24,    20,    21,    22,
    23,    24,   131,     3,     9,   116,    56,    57,    29,     3,
    89,   116,   nil,    29,    20,    21,    22,    23,    24,     9,
    20,    21,    22,    23,    24,     9,    56,    57,    56,    57,
    29,    20,    21,    22,    23,    24,    29,    58,    59,    60,
    61,    62,    63,    56,    57,    56,    57,    29,    58,    59,
    60,    61,    62,    63,    20,    21,    22,    23,    24,    20,
    21,    22,    23,    24,    20,    21,    22,    23,    24,    20,
    21,    22,    23,    24,    20,    21,    22,    23,    24,    20,
    21,    22,    23,    24,    20,    21,    22,    23,    24,    20,
    21,    22,    23,    24,    20,    21,    22,    23,    24,    20,
    21,    22,    23,    24,    20,    21,    22,    23,    24,    65,
    66 ]

racc_action_check = [
     4,    73,    69,    69,    37,    37,    37,    37,    37,     1,
    69,    39,     0,    38,     0,     2,    38,     2,    17,    43,
    37,    32,    31,    31,     4,    31,     4,    73,     0,    37,
     4,     2,    46,     4,    35,    39,     4,    39,    39,    39,
    39,    39,    36,    43,    39,    43,    87,    39,     5,    43,
     5,    43,    43,    26,    26,    43,    46,    52,    46,   116,
    53,     6,    46,     6,     5,    46,    46,    67,    46,    71,
    87,    72,    87,    27,    27,    75,    87,     6,     7,    87,
     7,    87,    87,   116,    76,   116,    77,   116,   116,   116,
    78,     8,   116,     8,     7,   116,     3,     3,     3,     3,
     3,    20,    20,    20,    20,    20,    79,     8,    70,    70,
    51,    51,     3,    93,    93,    83,    70,    20,    24,    24,
    24,    24,    24,    33,    33,    33,    33,    33,    34,    34,
    34,    34,    34,    41,    24,    41,    42,    85,    42,    33,
    45,    48,    45,    48,    34,    49,   103,    49,   105,    41,
    94,    94,    42,    95,    95,   122,    45,    48,    81,   125,
    81,    49,    65,    65,    65,    65,    65,    66,    66,    66,
    66,    66,   126,    82,    81,    82,    96,    96,    65,   118,
   129,   118,   nil,    66,    89,    89,    89,    89,    89,    82,
   108,   108,   108,   108,   108,   118,    97,    97,    98,    98,
    89,   124,   124,   124,   124,   124,   108,    28,    28,    28,
    28,    28,    28,    99,    99,   100,   100,   124,    64,    64,
    64,    64,    64,    64,    29,    29,    29,    29,    29,    54,
    54,    54,    54,    54,    55,    55,    55,    55,    55,    56,
    56,    56,    56,    56,    57,    57,    57,    57,    57,    58,
    58,    58,    58,    58,    59,    59,    59,    59,    59,    60,
    60,    60,    60,    60,    61,    61,    61,    61,    61,    62,
    62,    62,    62,    62,    63,    63,    63,    63,    63,   123,
   123 ]

racc_action_pointer = [
   -12,     9,    -9,    93,    -3,    24,    37,    54,    67,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    18,   nil,   nil,
    98,   nil,   nil,   nil,   115,   nil,    44,    62,   194,   221,
   nil,     2,    -4,   120,   125,    31,    39,     1,    10,     8,
   nil,   109,   112,    16,   nil,   116,    29,   nil,   117,   121,
   nil,    90,    55,    52,   226,   231,   236,   241,   246,   251,
   256,   261,   266,   271,   205,   159,   164,    64,   nil,   -18,
    88,    35,    43,    -1,   nil,    47,    56,    58,    62,    78,
   nil,   134,   149,    87,   nil,   109,   nil,    43,   nil,   181,
   nil,   nil,   nil,   104,   141,   142,   165,   185,   187,   202,
   204,   nil,   nil,   124,   nil,   125,   nil,   nil,   187,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,    56,   nil,   155,   nil,
   nil,   nil,   127,   259,   198,   156,   144,   nil,   nil,   178,
   nil,   nil ]

racc_action_default = [
    -2,   -70,    -1,   -70,   -70,   -70,   -70,   -70,   -70,   -60,
   -61,   -62,   -63,   -64,   -65,   -66,   -68,   -70,   -67,   -69,
    -5,    -7,    -8,    -9,   -70,   -11,   -14,   -17,   -24,   -70,
   -26,   -33,   -70,   -70,   -70,   -70,   -70,   -70,   -70,   -70,
   -41,   -70,   -70,   -70,   -48,   -70,   -70,   -52,   -70,   -70,
   132,    -3,    -6,   -70,   -70,   -70,   -70,   -70,   -70,   -70,
   -70,   -70,   -70,   -70,   -25,   -70,   -70,   -70,   -35,   -70,
   -70,   -70,   -70,   -70,   -54,   -70,   -70,   -70,   -70,   -70,
   -42,   -70,   -70,   -70,   -49,   -70,   -53,   -70,   -57,   -70,
   -10,   -12,   -13,   -15,   -16,   -18,   -19,   -20,   -21,   -22,
   -23,   -27,   -28,   -29,   -31,   -34,   -36,   -37,   -70,   -50,
   -55,   -58,   -59,   -38,   -39,   -40,   -70,   -44,   -70,   -43,
   -47,   -51,   -70,    -4,   -70,   -70,   -70,   -45,   -56,   -30,
   -32,   -46 ]

racc_goto_table = [
    19,    40,    18,    32,   104,    52,    51,     1,     2,    64,
    47,    91,    92,    41,    45,    48,    49,    44,    42,    69,
    70,   105,    73,    51,    53,    95,    96,    97,    98,    99,
   100,    93,    94,   101,   102,    88,   nil,    80,   nil,    19,
   nil,    18,   nil,    19,   nil,    18,    19,    19,    18,    18,
    82,    86,   nil,   nil,    81,   nil,    84,   nil,   nil,   nil,
   nil,   nil,   130,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   123,   nil,   117,   119,   nil,
    19,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,   118,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   126,   129,
    51,   nil,   nil,   nil,   127,   nil,    19,   nil,    18 ]

racc_goto_check = [
    27,    16,    28,     6,    11,     3,     4,     1,     2,     9,
    22,     5,     5,     2,     2,     2,     2,    19,    15,     4,
     4,    12,     3,     4,     6,     8,     8,     8,     8,     8,
     8,     7,     7,    10,    10,    25,   nil,    16,   nil,    27,
   nil,    28,   nil,    27,   nil,    28,    27,    27,    28,    28,
     2,    22,   nil,   nil,    15,   nil,    19,   nil,   nil,   nil,
   nil,   nil,    11,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,     4,   nil,    16,    16,   nil,
    27,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,     2,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     6,     3,
     4,   nil,   nil,   nil,    16,   nil,    27,   nil,    28 ]

racc_goto_pointer = [
   nil,     7,     8,   -15,   -14,   -43,     0,   -25,   -33,   -20,
   -32,   -63,   -46,   nil,   nil,    13,    -4,   nil,   nil,    11,
   nil,   nil,     3,   nil,   nil,   -14,   nil,    -2,     0 ]

racc_goto_default = [
   nil,   nil,   nil,   nil,    31,    25,   nil,    26,    27,    28,
    30,   nil,   nil,    10,     5,   nil,   nil,    11,     6,   nil,
    12,     7,   nil,    14,     8,   nil,    13,    16,    15 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 42, :_reduce_none,
  0, 42, :_reduce_2,
  1, 44, :_reduce_3,
  3, 44, :_reduce_4,
  1, 46, :_reduce_5,
  2, 46, :_reduce_6,
  1, 46, :_reduce_7,
  1, 46, :_reduce_8,
  1, 46, :_reduce_9,
  3, 46, :_reduce_10,
  1, 48, :_reduce_none,
  3, 48, :_reduce_12,
  3, 48, :_reduce_13,
  1, 49, :_reduce_none,
  3, 49, :_reduce_15,
  3, 49, :_reduce_16,
  1, 50, :_reduce_none,
  3, 50, :_reduce_18,
  3, 50, :_reduce_19,
  3, 50, :_reduce_20,
  3, 50, :_reduce_21,
  3, 50, :_reduce_22,
  3, 50, :_reduce_23,
  1, 51, :_reduce_none,
  2, 51, :_reduce_25,
  1, 45, :_reduce_none,
  3, 45, :_reduce_27,
  3, 45, :_reduce_28,
  1, 52, :_reduce_29,
  3, 52, :_reduce_30,
  1, 53, :_reduce_31,
  3, 53, :_reduce_32,
  1, 47, :_reduce_none,
  3, 47, :_reduce_34,
  3, 54, :_reduce_35,
  4, 55, :_reduce_36,
  4, 55, :_reduce_37,
  3, 56, :_reduce_38,
  3, 57, :_reduce_39,
  3, 57, :_reduce_40,
  2, 58, :_reduce_41,
  3, 58, :_reduce_42,
  4, 58, :_reduce_43,
  4, 58, :_reduce_44,
  5, 58, :_reduce_45,
  6, 59, :_reduce_46,
  3, 60, :_reduce_47,
  2, 61, :_reduce_48,
  3, 61, :_reduce_49,
  4, 62, :_reduce_50,
  3, 63, :_reduce_51,
  2, 64, :_reduce_52,
  3, 64, :_reduce_53,
  3, 65, :_reduce_54,
  4, 65, :_reduce_55,
  3, 66, :_reduce_56,
  3, 67, :_reduce_57,
  4, 68, :_reduce_58,
  4, 68, :_reduce_59,
  1, 69, :_reduce_60,
  1, 69, :_reduce_none,
  1, 69, :_reduce_none,
  1, 69, :_reduce_none,
  1, 69, :_reduce_none,
  1, 69, :_reduce_none,
  1, 43, :_reduce_66,
  2, 43, :_reduce_67,
  1, 43, :_reduce_68,
  2, 43, :_reduce_69 ]

racc_reduce_n = 70

racc_shift_n = 132

racc_token_table = {
  false => 0,
  :error => 1,
  "," => 2,
  :IDENTIFIER => 3,
  :INTEGER => 4,
  :REAL => 5,
  :STRING => 6,
  "(" => 7,
  ")" => 8,
  "*" => 9,
  "/" => 10,
  "+" => 11,
  "-" => 12,
  :OP_EQ => 13,
  :OP_NEQ => 14,
  :OP_LEQ => 15,
  :OP_GEQ => 16,
  ">" => 17,
  "<" => 18,
  :NOT => 19,
  :AND => 20,
  :OR => 21,
  ":" => 22,
  "|" => 23,
  :VAR_OPEN => 24,
  :VAR_CLOSE => 25,
  :STMT_OPEN => 26,
  :IF => 27,
  :STMT_CLOSE => 28,
  :UNLESS => 29,
  :ELSE => 30,
  :ENDIF => 31,
  :ENDUNLESS => 32,
  :FOR => 33,
  :IN => 34,
  :ENDFOR => 35,
  :BLOCK => 36,
  :ENDBLOCK => 37,
  :END => 38,
  :EXTENDS => 39,
  :TEXT_BLOCK => 40 }

racc_nt_base = 41

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "\",\"",
  "IDENTIFIER",
  "INTEGER",
  "REAL",
  "STRING",
  "\"(\"",
  "\")\"",
  "\"*\"",
  "\"/\"",
  "\"+\"",
  "\"-\"",
  "OP_EQ",
  "OP_NEQ",
  "OP_LEQ",
  "OP_GEQ",
  "\">\"",
  "\"<\"",
  "NOT",
  "AND",
  "OR",
  "\":\"",
  "\"|\"",
  "VAR_OPEN",
  "VAR_CLOSE",
  "STMT_OPEN",
  "IF",
  "STMT_CLOSE",
  "UNLESS",
  "ELSE",
  "ENDIF",
  "ENDUNLESS",
  "FOR",
  "IN",
  "ENDFOR",
  "BLOCK",
  "ENDBLOCK",
  "END",
  "EXTENDS",
  "TEXT_BLOCK",
  "$start",
  "target",
  "document",
  "parameter_list",
  "logical_expression",
  "primary_expression",
  "filtered_expression",
  "multiplicative_expression",
  "additive_expression",
  "boolean_expression",
  "inverse_expression",
  "filter",
  "filter_list",
  "inject_statement",
  "if_tag",
  "else_tag",
  "end_if_tag",
  "if_block",
  "for_tag",
  "end_for_tag",
  "for_block",
  "block_tag",
  "end_block_tag",
  "block_block",
  "generic_block_tag",
  "end_generic_block_tag",
  "generic_block",
  "extends_statement",
  "document_component" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

module_eval(<<'.,.,', 'cadenza.y', 12)
  def _reduce_2(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 16)
  def _reduce_3(val, _values, result)
     result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 17)
  def _reduce_4(val, _values, result)
     result = val[0].push(val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 22)
  def _reduce_5(val, _values, result)
     result = VariableNode.new(val[0].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 23)
  def _reduce_6(val, _values, result)
     result = VariableNode.new(val[0].value, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 24)
  def _reduce_7(val, _values, result)
     result = ConstantNode.new(val[0].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 25)
  def _reduce_8(val, _values, result)
     result = ConstantNode.new(val[0].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 26)
  def _reduce_9(val, _values, result)
     result = ConstantNode.new(val[0].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 27)
  def _reduce_10(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 11 omitted

module_eval(<<'.,.,', 'cadenza.y', 32)
  def _reduce_12(val, _values, result)
     result = OperationNode.new(val[0], "*", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 33)
  def _reduce_13(val, _values, result)
     result = OperationNode.new(val[0], "/", val[2])
    result
  end
.,.,

# reduce 14 omitted

module_eval(<<'.,.,', 'cadenza.y', 38)
  def _reduce_15(val, _values, result)
     result = OperationNode.new(val[0], "+", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 39)
  def _reduce_16(val, _values, result)
     result = OperationNode.new(val[0], "-", val[2])
    result
  end
.,.,

# reduce 17 omitted

module_eval(<<'.,.,', 'cadenza.y', 44)
  def _reduce_18(val, _values, result)
     result = OperationNode.new(val[0], "==", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 45)
  def _reduce_19(val, _values, result)
     result = OperationNode.new(val[0], "!=", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 46)
  def _reduce_20(val, _values, result)
     result = OperationNode.new(val[0], "<=", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 47)
  def _reduce_21(val, _values, result)
     result = OperationNode.new(val[0], ">=", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 48)
  def _reduce_22(val, _values, result)
     result = OperationNode.new(val[0], ">", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 49)
  def _reduce_23(val, _values, result)
     result = OperationNode.new(val[0], "<", val[2])
    result
  end
.,.,

# reduce 24 omitted

module_eval(<<'.,.,', 'cadenza.y', 54)
  def _reduce_25(val, _values, result)
     result = BooleanInverseNode.new(val[1])
    result
  end
.,.,

# reduce 26 omitted

module_eval(<<'.,.,', 'cadenza.y', 59)
  def _reduce_27(val, _values, result)
     result = OperationNode.new(val[0], "and", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 60)
  def _reduce_28(val, _values, result)
     result = OperationNode.new(val[0], "or", val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 64)
  def _reduce_29(val, _values, result)
     result = FilterNode.new(val[0].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 65)
  def _reduce_30(val, _values, result)
     result = FilterNode.new(val[0].value, val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 69)
  def _reduce_31(val, _values, result)
     result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 70)
  def _reduce_32(val, _values, result)
     result = val[0].push(val[2])
    result
  end
.,.,

# reduce 33 omitted

module_eval(<<'.,.,', 'cadenza.y', 75)
  def _reduce_34(val, _values, result)
     result = FilteredValueNode.new(val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 79)
  def _reduce_35(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 83)
  def _reduce_36(val, _values, result)
     open_scope!; result = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 84)
  def _reduce_37(val, _values, result)
     open_scope!; result = BooleanInverseNode.new(val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 88)
  def _reduce_38(val, _values, result)
     result = close_scope!; open_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 92)
  def _reduce_39(val, _values, result)
     result = close_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 93)
  def _reduce_40(val, _values, result)
     result = close_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 97)
  def _reduce_41(val, _values, result)
     result = IfNode.new(val[0], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 98)
  def _reduce_42(val, _values, result)
     result = IfNode.new(val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 99)
  def _reduce_43(val, _values, result)
     result = IfNode.new(val[0], val[1], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 100)
  def _reduce_44(val, _values, result)
     result = IfNode.new(val[0], val[2], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 101)
  def _reduce_45(val, _values, result)
     result = IfNode.new(val[0], val[2], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 105)
  def _reduce_46(val, _values, result)
     open_scope!; result = [val[2].value, val[4]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 109)
  def _reduce_47(val, _values, result)
     result = close_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 114)
  def _reduce_48(val, _values, result)
     result = ForNode.new(VariableNode.new(val[0].first), val[0].last, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 115)
  def _reduce_49(val, _values, result)
     result = ForNode.new(VariableNode.new(val[0].first), val[0].last, val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 119)
  def _reduce_50(val, _values, result)
     result = open_block_scope!(val[2].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 123)
  def _reduce_51(val, _values, result)
     result = close_block_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 128)
  def _reduce_52(val, _values, result)
     result = BlockNode.new(val[0], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 129)
  def _reduce_53(val, _values, result)
     result = BlockNode.new(val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 133)
  def _reduce_54(val, _values, result)
     open_scope!; result = [val[1].value, []]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 134)
  def _reduce_55(val, _values, result)
     open_scope!; result = [val[1].value, val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 138)
  def _reduce_56(val, _values, result)
     result = close_scope!
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 142)
  def _reduce_57(val, _values, result)
     result = GenericBlockNode.new(val[0].first, val[2], val[0].last)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 146)
  def _reduce_58(val, _values, result)
     result = val[2].value
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 147)
  def _reduce_59(val, _values, result)
     result = VariableNode.new(val[2].value)
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 151)
  def _reduce_60(val, _values, result)
     result = TextNode.new(val[0].value)
    result
  end
.,.,

# reduce 61 omitted

# reduce 62 omitted

# reduce 63 omitted

# reduce 64 omitted

# reduce 65 omitted

module_eval(<<'.,.,', 'cadenza.y', 160)
  def _reduce_66(val, _values, result)
     push val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 161)
  def _reduce_67(val, _values, result)
     push val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 162)
  def _reduce_68(val, _values, result)
     document.extends = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cadenza.y', 163)
  def _reduce_69(val, _values, result)
     document.extends = val[1]
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class RaccParser
end   # module Cadenza

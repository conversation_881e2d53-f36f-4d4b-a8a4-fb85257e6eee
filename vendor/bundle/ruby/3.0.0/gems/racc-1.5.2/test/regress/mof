#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


# parser.rb - generated by racc

require 'strscan'
require 'rubygems'
require 'cim'
require File.join(File.dirname(__FILE__), 'result')
require File.join(File.dirname(__FILE__), 'scanner')
require File.join(File.dirname(__FILE__), 'helper')

module MOF
  class Parser < Racc::Parser

module_eval(<<'...end mof.y/module_eval...', 'mof.y', 571)

#
# Initialize MOF::Parser
#  MOF::Parser.new options = {}
#
#  options -> Hash of options
#    :debug -> boolean
#    :includes -> array of include dirs
#    :style -> :cim or :wmi
#
def initialize options = {}
  @yydebug = options[:debug]
  @includes = options[:includes] || []
  @quiet = options[:quiet]
  @style = options[:style] || :cim  # default to style CIM v2.2 syntax

  @lineno = 1
  @file = nil
  @iconv = nil
  @eol = "\n"
  @fname = nil
  @fstack = []
  @in_comment = false
  @seen_files = []
  @qualifiers = {}
end

#
# Make options hash from argv
#
# returns [ files, options ]
#

  def self.argv_handler name, argv
    files = []
    options = { :namespace => "" }
    while argv.size > 0
      case opt = argv.shift
      when "-h"
	$stderr.puts "Ruby MOF compiler"
	$stderr.puts "#{name} [-h] [-d] [-I <dir>] [<moffiles>]"
	$stderr.puts "Compiles <moffile>"
	$stderr.puts "\t-d  debug"
	$stderr.puts "\t-h  this help"
	$stderr.puts "\t-I <dir>  include dir"
	$stderr.puts "\t-f  force"
	$stderr.puts "\t-n <namespace>"
	$stderr.puts "\t-o <output>"
	$stderr.puts "\t-s <style>  syntax style (wmi,cim)"
	$stderr.puts "\t-q  quiet"
	$stderr.puts "\t<moffiles>  file(s) to read (else use $stdin)"
	exit 0
      when "-f" then options[:force] = true
      when "-s" then options[:style] = argv.shift.to_sym
      when "-d" then options[:debug] = true
      when "-q" then options[:quiet] = true
      when "-I"
	options[:includes] ||= []
	dirname = argv.shift
	unless File.directory?(dirname)
	  files << dirname
	  dirname = File.dirname(dirname)
	end
	options[:includes] << Pathname.new(dirname)
      when "-n" then options[:namespace] = argv.shift
      when "-o" then options[:output] = argv.shift
      when /^-.+/
	$stderr.puts "Undefined option #{opt}"
      else
	files << opt
      end
    end
    [ files, options ]
  end

include Helper
include Scanner

...end mof.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
    13,   172,   163,   197,   174,    27,    63,    17,   145,   146,
   147,    62,    28,   172,    11,   173,   174,   173,   148,    11,
   144,   149,   150,   151,   152,    18,   163,   173,    33,   153,
   106,   107,   108,   109,   110,   112,   111,    40,    15,    16,
    42,    55,    57,    68,    69,    71,    72,    52,    53,    54,
    56,   163,     7,   199,    35,    42,   174,     7,    10,    10,
   115,   102,   114,    36,    10,    55,    57,    68,    69,    71,
    72,    52,    53,    54,    56,    51,   190,    44,   -77,    42,
    33,   189,    10,    10,    33,   102,    51,   164,    60,    10,
    55,    57,    68,    69,    71,    72,    52,    53,    54,    56,
   170,    35,    10,    21,    42,    22,    23,    10,    29,    31,
   102,    35,    95,    96,    55,    57,    25,    65,    24,    78,
    52,    53,    54,    56,    97,    55,    57,    35,   180,   181,
   100,    52,    53,    54,    56,    93,    79,    80,    81,    82,
    83,    84,    85,    86,    87,    88,    89,    90,    91,    92,
   -25,    93,    79,    80,    81,    82,    83,    84,    85,    86,
    87,    88,    89,    90,    91,    92,   191,   192,   102,    60,
    55,    57,    68,    69,    71,    72,    52,    53,    54,    56,
   145,   146,   147,   172,   113,    97,   174,    10,    18,   118,
   148,   119,   144,   149,   150,   151,   152,   173,   121,   124,
    35,   153,    55,    57,    68,    69,    71,    72,    52,    53,
    54,    56,   106,   107,   108,   109,   110,   112,   111,    10,
    55,    57,    68,    69,    71,    72,    52,    53,    54,    56,
    21,   126,    22,    23,   127,   129,    21,    10,    22,    23,
   130,   131,   133,    25,   135,    24,    10,   141,   154,    25,
    35,    24,    93,    79,    80,    81,    82,    83,    84,    85,
    86,    87,    88,    89,    90,    91,    92,   106,   107,   108,
   109,   110,   112,   111,   184,   185,   194,   200,   121,   207,
   -59,   121,   209,   211,   135,   135,   218,   220,   221,   226,
   228,   229,   231,    10,   121,   135 ]

racc_action_check = [
     1,   140,   135,   186,   140,    12,    37,     8,   131,   131,
   131,    37,    13,   194,     0,   140,   194,   186,   131,     1,
   131,   131,   131,   131,   131,     8,   189,   194,    16,   131,
    65,    65,    65,    65,    65,    65,    65,    20,     7,     7,
    20,   135,   135,   135,   135,   135,   135,   135,   135,   135,
   135,   211,     0,   187,    17,   135,   187,     1,   135,     0,
    75,   135,    75,    18,     1,   189,   189,   189,   189,   189,
   189,   189,   189,   189,   189,    33,   177,    26,   207,   189,
    29,   177,   189,   207,    30,   189,    96,   137,    34,   137,
   211,   211,   211,   211,   211,   211,   211,   211,   211,   211,
   138,    36,   138,    10,   211,    10,    10,   211,    15,    15,
   211,   169,    47,    47,    33,    33,    10,    38,    10,    43,
    33,    33,    33,    33,    49,    96,    96,   216,   142,   142,
    58,    96,    96,    96,    96,   169,   169,   169,   169,   169,
   169,   169,   169,   169,   169,   169,   169,   169,   169,   169,
    42,   216,   216,   216,   216,   216,   216,   216,   216,   216,
   216,   216,   216,   216,   216,   216,   178,   178,    60,    61,
    42,    42,    42,    42,    42,    42,    42,    42,    42,    42,
   181,   181,   181,   218,    66,    70,   218,    42,    74,    77,
   181,    78,   181,   181,   181,   181,   181,   218,    94,    98,
   100,   181,    40,    40,    40,    40,    40,    40,    40,    40,
    40,    40,   141,   141,   141,   141,   141,   141,   141,    40,
   115,   115,   115,   115,   115,   115,   115,   115,   115,   115,
    11,   102,    11,    11,   103,   116,    63,   115,    63,    63,
   118,   119,   121,    11,   122,    11,   127,   130,   132,    63,
   161,    63,    44,    44,    44,    44,    44,    44,    44,    44,
    44,    44,    44,    44,    44,    44,    44,   192,   192,   192,
   192,   192,   192,   192,   162,   164,   183,   188,   195,   196,
   197,   198,   201,   205,   206,   208,   210,   212,   213,   217,
   221,   222,   223,   229,   230,   233 ]

racc_action_pointer = [
    -5,     0,   nil,   nil,   nil,   nil,   nil,    32,    -2,   nil,
    95,   222,   -58,    12,   nil,   101,   -30,    46,    32,   nil,
   -21,   nil,   nil,   nil,   nil,   nil,    11,   nil,   nil,    22,
    26,   nil,   nil,    67,    60,   nil,    93,   -54,    51,   nil,
   155,   nil,   123,    59,   220,   nil,   nil,    53,   nil,    76,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    64,   nil,
   101,   141,   nil,   228,   nil,    18,   125,   nil,   nil,   nil,
   137,   nil,   nil,   nil,   161,     0,   nil,   129,   171,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   134,   nil,    78,   nil,   138,   nil,
   192,   nil,   223,   173,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   173,   172,   nil,   214,   183,
   nil,   195,   176,   nil,   nil,   nil,   nil,   182,   nil,   nil,
   189,    -1,   183,   nil,   nil,    -6,   nil,    25,    38,   nil,
    -7,   200,    69,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   242,   208,   nil,   212,   nil,   nil,   nil,   nil,   103,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   107,   nil,
   nil,   171,   nil,   207,   nil,   nil,    -5,    45,   248,    18,
   nil,   nil,   255,   nil,     5,   214,   221,   222,   217,   nil,
   nil,   219,   nil,   nil,   nil,   215,   216,    19,   217,   nil,
   226,    43,   224,   229,   nil,   nil,   119,   226,   175,   nil,
   nil,   227,   231,   284,   nil,   nil,   nil,   nil,   nil,   229,
   230,   nil,   nil,   227,   nil,   nil ]

racc_action_default = [
    -1,   -25,    -2,    -4,    -5,    -6,    -7,  -161,  -161,   -26,
  -161,  -161,  -161,  -161,    -3,  -161,   -12,  -161,  -161,   -28,
   -33,  -133,  -134,  -135,  -136,  -137,  -161,  -155,   236,   -12,
   -12,   -11,   -10,  -161,   -44,   -48,  -161,  -161,   -31,   -34,
   -25,   -36,  -100,  -161,  -161,    -8,    -9,  -161,   -14,   -16,
   -17,   -18,  -110,  -111,  -112,  -113,  -114,  -115,   -46,   -45,
  -161,   -44,   -27,  -161,   -30,  -161,  -161,  -103,  -104,  -105,
  -106,  -107,  -108,  -109,  -161,  -161,  -101,  -131,  -161,   -60,
   -61,   -62,   -63,   -64,   -65,   -66,   -67,   -68,   -69,   -70,
   -71,   -72,   -73,   -74,   -86,   -13,  -161,  -116,  -161,   -47,
  -161,   -49,  -161,  -161,   -29,   -32,   -37,   -38,   -39,   -40,
   -41,   -42,   -43,   -35,   -99,   -25,  -161,  -132,  -161,  -161,
   -87,   -91,   -93,   -15,   -20,   -51,   -50,   -25,  -102,  -130,
  -161,  -161,  -161,   -92,   -94,   -25,  -138,   -25,   -25,  -157,
  -161,  -161,  -161,  -140,  -142,  -143,  -144,  -145,  -146,  -147,
  -148,  -149,  -150,  -151,   -90,   -95,   -96,   -97,   -98,  -117,
  -118,  -161,  -161,  -122,  -161,   -21,   -22,   -23,   -24,  -161,
  -156,  -158,   -55,   -56,   -58,  -128,  -129,  -161,  -161,  -153,
  -139,  -161,  -119,  -161,  -121,   -19,  -161,  -161,   -75,   -25,
  -160,  -152,  -161,  -141,  -161,   -86,  -161,   -55,   -86,   -57,
   -76,  -161,  -154,  -123,  -125,  -161,   -93,   -25,   -93,  -159,
  -124,   -25,  -161,  -161,   -78,   -80,  -161,  -161,  -161,  -127,
   -52,  -161,   -79,  -161,   -83,   -84,   -53,  -126,   -54,   -25,
   -86,   -85,   -81,   -88,   -82,   -89 ]

racc_goto_table = [
     8,     8,    34,    94,   122,   105,   136,   155,    48,    66,
    49,    76,    50,    19,   143,   187,    58,   204,   215,    32,
   139,    61,     2,    14,    12,    12,     1,    30,    47,    98,
   137,   171,    45,    46,   165,   166,   167,   168,    37,    26,
   232,   227,    38,   103,    64,    39,    41,    99,   101,   195,
   198,   196,   213,   214,   222,   223,   230,   234,   235,   132,
    75,   201,   225,   182,   193,   203,   104,   210,   177,    43,
    77,   123,   116,    49,   117,    50,   142,   178,   138,   nil,
   nil,   179,   nil,   219,   128,   125,   nil,   nil,   nil,   nil,
   212,   nil,   217,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,   208,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   140,   186,   nil,
   nil,   nil,   202,   nil,   nil,   nil,   nil,   169,   140,   nil,
   233,   nil,   nil,   nil,   nil,   nil,   183,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   224,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   216 ]

racc_goto_check = [
    13,    13,    14,    35,    37,    28,    38,    52,    10,    30,
    11,    30,    12,    23,    69,    39,    15,    61,    44,     7,
    72,    14,     2,     2,    55,    55,     1,     8,     9,    16,
    17,    72,     7,     7,    18,    19,    20,    21,    24,    25,
    44,    61,    26,    15,    27,    29,    31,    33,    34,    36,
    40,    41,    42,    43,    45,    46,    47,    48,    50,    51,
    54,    52,    39,    58,    69,    60,    23,    62,    63,    64,
    65,    10,    66,    11,    67,    12,    68,    70,    71,   nil,
   nil,    28,   nil,    52,    30,    14,   nil,   nil,   nil,   nil,
    38,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    37,   nil,   nil,    37,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,    35,   nil,
   nil,   nil,    28,   nil,   nil,   nil,   nil,    13,    13,   nil,
    37,   nil,   nil,   nil,   nil,   nil,    14,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    13 ]

racc_goto_pointer = [
   nil,    26,    22,   nil,   nil,   nil,   nil,     3,    12,    -5,
   -25,   -23,   -21,     0,   -15,   -18,   -29,   -94,  -103,  -102,
  -101,  -100,   nil,     3,    19,    28,    22,     6,   -60,    25,
   -31,    26,   nil,   -11,   -12,   -41,  -137,   -90,  -116,  -154,
  -137,  -135,  -155,  -154,  -189,  -161,  -161,  -167,  -176,   nil,
  -175,   -62,  -128,   nil,    18,    24,   nil,   nil,   -98,   nil,
  -129,  -177,  -137,   -72,    43,    27,    -5,    -3,   -55,  -117,
   -64,   -49,  -107 ]

racc_goto_default = [
   nil,   nil,   nil,     3,     4,     5,     6,   nil,   nil,   nil,
   nil,    70,    67,    74,   188,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     9,   nil,   nil,    20,   nil,   nil,   nil,   nil,
   156,   157,    59,   nil,   160,   nil,   175,   nil,   nil,   nil,
   176,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   120,
   134,   nil,   nil,   158,   nil,    73,   159,   161,   nil,   162,
   nil,   nil,   nil,   205,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil ]

racc_reduce_table = [
  0, 0, :racc_error,
  0, 71, :_reduce_1,
  1, 71, :_reduce_2,
  2, 71, :_reduce_3,
  1, 72, :_reduce_none,
  1, 72, :_reduce_5,
  1, 72, :_reduce_6,
  1, 72, :_reduce_7,
  4, 73, :_reduce_8,
  4, 73, :_reduce_none,
  3, 73, :_reduce_10,
  1, 78, :_reduce_none,
  0, 77, :_reduce_12,
  3, 77, :_reduce_13,
  1, 79, :_reduce_none,
  3, 79, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_17,
  1, 80, :_reduce_none,
  9, 74, :_reduce_19,
  0, 87, :_reduce_20,
  2, 87, :_reduce_21,
  1, 88, :_reduce_none,
  1, 88, :_reduce_none,
  1, 88, :_reduce_none,
  0, 83, :_reduce_none,
  1, 83, :_reduce_26,
  4, 92, :_reduce_27,
  0, 94, :_reduce_28,
  3, 94, :_reduce_29,
  3, 93, :_reduce_30,
  0, 97, :_reduce_none,
  2, 97, :_reduce_32,
  0, 96, :_reduce_none,
  1, 96, :_reduce_none,
  3, 99, :_reduce_35,
  1, 99, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_none,
  1, 98, :_reduce_43,
  0, 85, :_reduce_none,
  1, 85, :_reduce_none,
  0, 86, :_reduce_none,
  1, 86, :_reduce_none,
  1, 84, :_reduce_48,
  2, 102, :_reduce_49,
  2, 104, :_reduce_50,
  2, 103, :_reduce_51,
  6, 89, :_reduce_52,
  6, 91, :_reduce_53,
  7, 90, :_reduce_54,
  1, 106, :_reduce_none,
  1, 106, :_reduce_56,
  1, 110, :_reduce_none,
  1, 110, :_reduce_58,
  1, 111, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_none,
  1, 105, :_reduce_74,
  1, 109, :_reduce_75,
  2, 109, :_reduce_76,
  0, 112, :_reduce_none,
  1, 112, :_reduce_none,
  2, 113, :_reduce_79,
  0, 115, :_reduce_80,
  3, 115, :_reduce_81,
  5, 114, :_reduce_82,
  1, 116, :_reduce_none,
  1, 116, :_reduce_none,
  1, 117, :_reduce_none,
  0, 107, :_reduce_none,
  1, 107, :_reduce_none,
  0, 118, :_reduce_none,
  1, 118, :_reduce_89,
  3, 119, :_reduce_90,
  0, 121, :_reduce_91,
  1, 121, :_reduce_none,
  0, 108, :_reduce_none,
  1, 108, :_reduce_none,
  2, 120, :_reduce_95,
  1, 122, :_reduce_none,
  1, 122, :_reduce_none,
  1, 122, :_reduce_none,
  3, 101, :_reduce_99,
  0, 124, :_reduce_none,
  1, 124, :_reduce_101,
  3, 124, :_reduce_102,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  1, 100, :_reduce_109,
  1, 82, :_reduce_none,
  1, 82, :_reduce_none,
  1, 82, :_reduce_none,
  1, 82, :_reduce_none,
  1, 82, :_reduce_none,
  1, 81, :_reduce_none,
  2, 81, :_reduce_116,
  1, 123, :_reduce_none,
  1, 123, :_reduce_none,
  2, 126, :_reduce_none,
  0, 127, :_reduce_none,
  2, 127, :_reduce_none,
  1, 129, :_reduce_none,
  3, 128, :_reduce_none,
  2, 130, :_reduce_none,
  0, 132, :_reduce_none,
  3, 132, :_reduce_none,
  3, 131, :_reduce_none,
  1, 133, :_reduce_none,
  1, 133, :_reduce_none,
  6, 75, :_reduce_130,
  0, 136, :_reduce_none,
  1, 136, :_reduce_none,
  1, 95, :_reduce_none,
  1, 95, :_reduce_none,
  1, 95, :_reduce_none,
  1, 95, :_reduce_none,
  1, 95, :_reduce_none,
  4, 134, :_reduce_138,
  5, 135, :_reduce_139,
  1, 138, :_reduce_140,
  3, 138, :_reduce_141,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  1, 139, :_reduce_none,
  5, 137, :_reduce_152,
  1, 140, :_reduce_153,
  3, 140, :_reduce_154,
  2, 76, :_reduce_none,
  8, 125, :_reduce_none,
  1, 141, :_reduce_none,
  2, 141, :_reduce_none,
  5, 142, :_reduce_none,
  3, 142, :_reduce_160 ]

racc_reduce_n = 161

racc_shift_n = 236

racc_token_table = {
  false => 0,
  :error => 1,
  "*" => 2,
  "/" => 3,
  "+" => 4,
  "-" => 5,
  :PRAGMA => 6,
  :INCLUDE => 7,
  :IDENTIFIER => 8,
  :CLASS => 9,
  :ASSOCIATION => 10,
  :INDICATION => 11,
  :AMENDED => 12,
  :ENABLEOVERRIDE => 13,
  :DISABLEOVERRIDE => 14,
  :RESTRICTED => 15,
  :TOSUBCLASS => 16,
  :TOINSTANCE => 17,
  :TRANSLATABLE => 18,
  :QUALIFIER => 19,
  :SCOPE => 20,
  :SCHEMA => 21,
  :PROPERTY => 22,
  :REFERENCE => 23,
  :METHOD => 24,
  :PARAMETER => 25,
  :FLAVOR => 26,
  :INSTANCE => 27,
  :AS => 28,
  :REF => 29,
  :ANY => 30,
  :OF => 31,
  :DT_VOID => 32,
  :DT_UINT8 => 33,
  :DT_SINT8 => 34,
  :DT_UINT16 => 35,
  :DT_SINT16 => 36,
  :DT_UINT32 => 37,
  :DT_SINT32 => 38,
  :DT_UINT64 => 39,
  :DT_SINT64 => 40,
  :DT_REAL32 => 41,
  :DT_REAL64 => 42,
  :DT_CHAR16 => 43,
  :DT_STR => 44,
  :DT_BOOLEAN => 45,
  :DT_DATETIME => 46,
  :positiveDecimalValue => 47,
  :stringValue => 48,
  :realValue => 49,
  :charValue => 50,
  :booleanValue => 51,
  :nullValue => 52,
  :binaryValue => 53,
  :octalValue => 54,
  :decimalValue => 55,
  :hexValue => 56,
  "#" => 57,
  "(" => 58,
  ")" => 59,
  "," => 60,
  "{" => 61,
  "}" => 62,
  ";" => 63,
  "[" => 64,
  "]" => 65,
  ":" => 66,
  "$" => 67,
  "=" => 68,
  "." => 69 }

racc_nt_base = 70

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "\"*\"",
  "\"/\"",
  "\"+\"",
  "\"-\"",
  "PRAGMA",
  "INCLUDE",
  "IDENTIFIER",
  "CLASS",
  "ASSOCIATION",
  "INDICATION",
  "AMENDED",
  "ENABLEOVERRIDE",
  "DISABLEOVERRIDE",
  "RESTRICTED",
  "TOSUBCLASS",
  "TOINSTANCE",
  "TRANSLATABLE",
  "QUALIFIER",
  "SCOPE",
  "SCHEMA",
  "PROPERTY",
  "REFERENCE",
  "METHOD",
  "PARAMETER",
  "FLAVOR",
  "INSTANCE",
  "AS",
  "REF",
  "ANY",
  "OF",
  "DT_VOID",
  "DT_UINT8",
  "DT_SINT8",
  "DT_UINT16",
  "DT_SINT16",
  "DT_UINT32",
  "DT_SINT32",
  "DT_UINT64",
  "DT_SINT64",
  "DT_REAL32",
  "DT_REAL64",
  "DT_CHAR16",
  "DT_STR",
  "DT_BOOLEAN",
  "DT_DATETIME",
  "positiveDecimalValue",
  "stringValue",
  "realValue",
  "charValue",
  "booleanValue",
  "nullValue",
  "binaryValue",
  "octalValue",
  "decimalValue",
  "hexValue",
  "\"#\"",
  "\"(\"",
  "\")\"",
  "\",\"",
  "\"{\"",
  "\"}\"",
  "\";\"",
  "\"[\"",
  "\"]\"",
  "\":\"",
  "\"$\"",
  "\"=\"",
  "\".\"",
  "$start",
  "mofSpecification",
  "mofProduction",
  "compilerDirective",
  "classDeclaration",
  "qualifierDeclaration",
  "instanceDeclaration",
  "pragmaParameters_opt",
  "pragmaName",
  "pragmaParameterValues",
  "pragmaParameterValue",
  "string",
  "integerValue",
  "qualifierList_opt",
  "className",
  "alias_opt",
  "superClass_opt",
  "classFeatures",
  "classFeature",
  "propertyDeclaration",
  "methodDeclaration",
  "referenceDeclaration",
  "qualifierList",
  "qualifier",
  "qualifiers",
  "qualifierName",
  "qualifierParameter_opt",
  "flavor_opt",
  "flavor",
  "qualifierParameter",
  "constantValue",
  "arrayInitializer",
  "alias",
  "superClass",
  "aliasIdentifier",
  "dataType",
  "propertyName",
  "array_opt",
  "defaultValue_opt",
  "objectRef",
  "referenceName",
  "methodName",
  "parameterList_opt",
  "parameterList",
  "parameter",
  "parameters",
  "typespec",
  "parameterName",
  "parameterValue_opt",
  "array",
  "defaultValue",
  "positiveDecimalValue_opt",
  "initializer",
  "referenceInitializer",
  "constantValues",
  "instance",
  "objectHandle",
  "namespace_opt",
  "modelPath",
  "namespaceHandle",
  "keyValuePairList",
  "keyValuePair",
  "keyValuePairs",
  "keyname",
  "qualifierType",
  "scope",
  "defaultFlavor_opt",
  "defaultFlavor",
  "metaElements",
  "metaElement",
  "flavors",
  "valueInitializers",
  "valueInitializer" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'mof.y', 41)
  def _reduce_1(val, _values, result)
     result = Hash.new
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 43)
  def _reduce_2(val, _values, result)
     result = { @name => @result }
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 45)
  def _reduce_3(val, _values, result)
     result = val[0]
	    result[@name] = @result

    result
  end
.,.,

# reduce 4 omitted

module_eval(<<'.,.,', 'mof.y', 53)
  def _reduce_5(val, _values, result)
     #puts "Class '#{val[0].name}'"
	    @result.classes << val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 57)
  def _reduce_6(val, _values, result)
     @result.qualifiers << val[0]
	    @qualifiers[val[0].name.downcase] = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 61)
  def _reduce_7(val, _values, result)
     @result.instances << val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 71)
  def _reduce_8(val, _values, result)
     raise MOF::Helper::Error.new(@name,@lineno,@line,"Missing filename after '#pragma include'") unless val[3]
	    open val[3], :pragma

    result
  end
.,.,

# reduce 9 omitted

module_eval(<<'.,.,', 'mof.y', 76)
  def _reduce_10(val, _values, result)
     raise StyleError.new(@name,@lineno,@line,"Use '#pragma include' instead of '#include'") unless @style == :wmi
	    raise MOF::Helper::Error.new(@name,@lineno,@line,"Missing filename after '#include'") unless val[2]
	    open val[2], :pragma

    result
  end
.,.,

# reduce 11 omitted

module_eval(<<'.,.,', 'mof.y', 88)
  def _reduce_12(val, _values, result)
     raise StyleError.new(@name,@lineno,@line,"#pragma parameter missing") unless @style == :wmi
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 90)
  def _reduce_13(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 14 omitted

# reduce 15 omitted

# reduce 16 omitted

module_eval(<<'.,.,', 'mof.y', 101)
  def _reduce_17(val, _values, result)
     raise StyleError.new(@name,@lineno,@line,"#pragma parameter missing") unless @style == :wmi
    result
  end
.,.,

# reduce 18 omitted

module_eval(<<'.,.,', 'mof.y', 112)
  def _reduce_19(val, _values, result)
     qualifiers = val[0]
	    features = val[6]
	    # FIXME: features must not include references
	    result = CIM::Class.new(val[2],qualifiers,val[3],val[4],features)

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 121)
  def _reduce_20(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 123)
  def _reduce_21(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

# reduce 22 omitted

# reduce 23 omitted

# reduce 24 omitted

# reduce 25 omitted

module_eval(<<'.,.,', 'mof.y', 136)
  def _reduce_26(val, _values, result)
     result = CIM::QualifierSet.new val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 141)
  def _reduce_27(val, _values, result)
     result = val[2]
	    result.unshift val[1] if val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 147)
  def _reduce_28(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 149)
  def _reduce_29(val, _values, result)
     result = val[0]
	    result << val[2] if val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 156)
  def _reduce_30(val, _values, result)
     # Get qualifier decl
	    qualifier = case val[0]
	      when CIM::Qualifier then            val[0].definition
	      when CIM::QualifierDeclaration then val[0]
	      when String then                    @qualifiers[val[0].downcase]
	    else
	      nil
	    end
	    raise MOF::Helper::Error.new(@name,@lineno,@line,"'#{val[0]}' is not a valid qualifier") unless qualifier
	    value = val[1]
	    raise MOF::Helper::Error.new(@name,@lineno,@line,"#{value.inspect} does not match qualifier type '#{qualifier.type}'") unless qualifier.type.matches?(value)||@style == :wmi
	    # Don't propagate a boolean 'false'
	    if qualifier.type == :boolean && value == false
	      result = nil
	    else
	      result = CIM::Qualifier.new(qualifier,value,val[2])
	    end

    result
  end
.,.,

# reduce 31 omitted

module_eval(<<'.,.,', 'mof.y', 179)
  def _reduce_32(val, _values, result)
     result = CIM::QualifierFlavors.new val[1]
    result
  end
.,.,

# reduce 33 omitted

# reduce 34 omitted

module_eval(<<'.,.,', 'mof.y', 189)
  def _reduce_35(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 36 omitted

# reduce 37 omitted

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

# reduce 42 omitted

module_eval(<<'.,.,', 'mof.y', 196)
  def _reduce_43(val, _values, result)
     case val[0].to_sym
	      when :amended, :toinstance
	        raise StyleError.new(@name,@lineno,@line,"'#{val[0]}' is not a valid flavor") unless @style == :wmi
	    end

    result
  end
.,.,

# reduce 44 omitted

# reduce 45 omitted

# reduce 46 omitted

# reduce 47 omitted

module_eval(<<'.,.,', 'mof.y', 215)
  def _reduce_48(val, _values, result)
     raise ParseError.new("Class name must be prefixed by '<schema>_'") unless val[0].include?("_") || @style == :wmi
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 220)
  def _reduce_49(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 225)
  def _reduce_50(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 230)
  def _reduce_51(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 236)
  def _reduce_52(val, _values, result)
     if val[3]
	      type = CIM::Array.new val[3],val[1]
	    else
	      type = val[1]
	    end
	    result = CIM::Property.new(type,val[2],val[0],val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 247)
  def _reduce_53(val, _values, result)
     if val[4]
	      raise StyleError.new(@name,@lineno,@line,"Array not allowed in reference declaration") unless @style == :wmi
	    end
	    result = CIM::Reference.new(val[1],val[2],val[0],val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 255)
  def _reduce_54(val, _values, result)
     result = CIM::Method.new(val[1],val[2],val[0],val[4])
    result
  end
.,.,

# reduce 55 omitted

module_eval(<<'.,.,', 'mof.y', 261)
  def _reduce_56(val, _values, result)
     # tmplprov.mof has 'string Property;'
	    raise StyleError.new(@name,@lineno,@line,"Invalid keyword '#{val[0]}' used for property name") unless @style == :wmi

    result
  end
.,.,

# reduce 57 omitted

module_eval(<<'.,.,', 'mof.y', 269)
  def _reduce_58(val, _values, result)
     result = "Indication"
    result
  end
.,.,

# reduce 59 omitted

# reduce 60 omitted

# reduce 61 omitted

# reduce 62 omitted

# reduce 63 omitted

# reduce 64 omitted

# reduce 65 omitted

# reduce 66 omitted

# reduce 67 omitted

# reduce 68 omitted

# reduce 69 omitted

# reduce 70 omitted

# reduce 71 omitted

# reduce 72 omitted

# reduce 73 omitted

module_eval(<<'.,.,', 'mof.y', 292)
  def _reduce_74(val, _values, result)
     raise StyleError.new(@name,@lineno,@line,"'void' is not a valid datatype") unless @style == :wmi
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 297)
  def _reduce_75(val, _values, result)
     # WMI uses class names as data types (without REF ?!)
	    raise StyleError.new(@name,@lineno,@line,"Expected 'ref' keyword after classname '#{val[0]}'") unless @style == :wmi
	    result = CIM::ReferenceType.new val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 303)
  def _reduce_76(val, _values, result)
     result = CIM::ReferenceType.new val[0]
    result
  end
.,.,

# reduce 77 omitted

# reduce 78 omitted

module_eval(<<'.,.,', 'mof.y', 313)
  def _reduce_79(val, _values, result)
     result = val[1].unshift val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 318)
  def _reduce_80(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 320)
  def _reduce_81(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 325)
  def _reduce_82(val, _values, result)
     if val[3]
	      type = CIM::Array.new val[3], val[1]
	    else
	      type = val[1]
	    end
	    result = CIM::Property.new(type,val[2],val[0])

    result
  end
.,.,

# reduce 83 omitted

# reduce 84 omitted

# reduce 85 omitted

# reduce 86 omitted

# reduce 87 omitted

# reduce 88 omitted

module_eval(<<'.,.,', 'mof.y', 351)
  def _reduce_89(val, _values, result)
     raise "Default parameter value not allowed in syntax style '{@style}'" unless @style == :wmi
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 356)
  def _reduce_90(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 361)
  def _reduce_91(val, _values, result)
     result = -1
    result
  end
.,.,

# reduce 92 omitted

# reduce 93 omitted

# reduce 94 omitted

module_eval(<<'.,.,', 'mof.y', 372)
  def _reduce_95(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 96 omitted

# reduce 97 omitted

# reduce 98 omitted

module_eval(<<'.,.,', 'mof.y', 383)
  def _reduce_99(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 100 omitted

module_eval(<<'.,.,', 'mof.y', 389)
  def _reduce_101(val, _values, result)
     result = [ val[0] ]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 391)
  def _reduce_102(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

# reduce 103 omitted

# reduce 104 omitted

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

# reduce 108 omitted

module_eval(<<'.,.,', 'mof.y', 402)
  def _reduce_109(val, _values, result)
     raise "Instance as property value not allowed in syntax style '{@style}'" unless @style == :wmi
    result
  end
.,.,

# reduce 110 omitted

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

module_eval(<<'.,.,', 'mof.y', 416)
  def _reduce_116(val, _values, result)
     result = val[0] + val[1]
    result
  end
.,.,

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

module_eval(<<'.,.,', 'mof.y', 471)
  def _reduce_130(val, _values, result)
     result = CIM::QualifierDeclaration.new( val[1], val[2][0], val[2][1], val[3], val[4])
    result
  end
.,.,

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

module_eval(<<'.,.,', 'mof.y', 490)
  def _reduce_138(val, _values, result)
     type = val[2].nil? ? val[1] : CIM::Array.new(val[2],val[1])
	    result = [ type, val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 497)
  def _reduce_139(val, _values, result)
     result = CIM::QualifierScopes.new(val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 502)
  def _reduce_140(val, _values, result)
     result = [ val[0] ]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 504)
  def _reduce_141(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

module_eval(<<'.,.,', 'mof.y', 522)
  def _reduce_152(val, _values, result)
     result = CIM::QualifierFlavors.new val[3]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 527)
  def _reduce_153(val, _values, result)
     result = [ val[0] ]
    result
  end
.,.,

module_eval(<<'.,.,', 'mof.y', 529)
  def _reduce_154(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

module_eval(<<'.,.,', 'mof.y', 553)
  def _reduce_160(val, _values, result)
     raise "Instance property '#{val[1]} must have a value" unless @style == :wmi
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module MOF



#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'

  require File.expand_path("../lexer", __FILE__)
  require File.expand_path("../nodes", __FILE__)
  require File.expand_path("../errors", __FILE__)
  require File.expand_path("../ast_rewriter", __FILE__)
module Riml
  class Parser < Racc::Parser

module_eval(<<'...end riml.y/module_eval...', 'riml.y', 592)
  # This code will be put as-is in the parser class

  attr_accessor :ast_rewriter
  attr_writer :options

  # The Parser and AST_Rewriter share this same hash of options
  def options
    @options ||= {}
  end

  def self.ast_cache
    @ast_cache
  end
  @ast_cache = {}

  # parses tokens or code into output nodes
  def parse(object, ast_rewriter = Riml::AST_Rewriter.new, filename = nil, included = false)
    if (ast = self.class.ast_cache[filename])
    else
      if tokens?(object)
        @tokens = object
      elsif code?(object)
        @lexer = Riml::Lexer.new(object, filename, true)
      end

      begin
        ast = do_parse
      rescue Racc::ParseError => e
        raise unless @lexer
        if (invalid_token = @lexer.prev_token_is_keyword?)
          warning = "#{invalid_token.inspect} is a keyword, and cannot " \
            "be used as a variable name"
        end
        error_msg = e.message
        error_msg << "\nWARNING: #{warning}" if warning
        error = Riml::ParseError.new(error_msg, @lexer.filename, @lexer.lineno)
        raise error
      end
      self.class.ast_cache[filename] = ast if filename
    end
    @ast_rewriter ||= ast_rewriter
    return ast unless @ast_rewriter
    @ast_rewriter.ast = ast.dup
    @ast_rewriter.options ||= options
    @ast_rewriter.rewrite(filename, included)
    @ast_rewriter.ast
  end

  # get the next token from either the list of tokens provided, or
  # the lexer getting the next token
  def next_token
    return @tokens.shift unless @lexer
    token = @lexer.next_token
    if token && @lexer.parser_info
      @current_parser_info = token.pop
    end
    token
  end

  private

  def tokens?(object)
    Array === object
  end

  def code?(object)
    String === object
  end

  def make_node(racc_val)
    node = yield racc_val
    node.parser_info = @current_parser_info
    node
  end
...end riml.y/module_eval...
##### State transition tables begin ###

clist = [
'135,265,60,139,136,211,417,418,48,47,411,211,448,274,146,56,199,89,272',
'60,273,270,409,271,37,46,48,47,37,49,44,45,392,412,37,449,384,60,50',
'70,316,61,386,385,317,62,63,129,130,132,127,128,131,115,116,117,121',
'122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113',
'106,108,107,133,134,101,100,137,171,80,38,173,37,81,38,82,85,83,84,87',
'38,105,86,37,75,76,153,152,57,56,88,89,77,37,90,58,59,78,37,46,48,47',
'91,49,44,45,64,72,73,-116,161,60,50,70,79,61,272,37,92,62,63,271,48',
'47,-178,-178,-178,-178,211,147,211,211,38,60,-43,-43,153,152,329,409',
'99,409,409,38,331,153,152,-42,-42,161,60,50,70,38,139,313,29,313,38',
'52,80,54,164,277,81,277,82,85,83,84,87,279,279,86,149,75,76,38,182,57',
'56,88,89,77,149,90,58,59,78,37,46,48,47,91,49,44,45,64,72,73,-116,164',
'60,50,70,79,61,164,60,164,62,63,-204,277,149,164,279,164,-203,305,149',
'366,141,164,164,-44,-44,129,130,132,-85,-85,-85,-85,142,-48,-48,-116',
'258,60,50,70,-99,-98,60,50,70,29,149,38,52,80,54,371,149,81,52,82,85',
'83,84,87,149,139,86,164,75,76,164,139,57,56,88,89,77,105,90,58,59,78',
'37,46,48,47,91,49,44,45,64,72,73,-99,-98,60,50,70,79,61,-46,-46,370',
'62,63,-175,-175,-175,-175,139,129,130,132,-47,-47,-45,-45,433,195,432',
'129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124,125',
'126,164,354,29,211,38,52,80,54,153,152,81,60,82,85,83,84,87,149,60,86',
'105,75,76,417,437,57,56,88,89,77,105,90,58,59,78,215,46,48,47,91,49',
'44,45,64,72,73,-116,216,60,50,70,79,61,139,218,319,62,63,-175,-175,-175',
'-175,-178,-178,-178,-178,211,147,48,47,211,49,48,47,262,49,-176,-176',
'-176,-176,263,320,191,192,193,194,267,129,130,132,127,128,131,29,269',
'269,52,269,54,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,323,105,258,276,294,295,149,139,302,305,306,105,139',
'105,312,326,327,328,333,105,105,105,342,346,357,361,320,258,362,363',
'60,129,130,132,127,128,131,129,130,132,127,128,131,368,129,130,132,127',
'128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103',
'109,111,110,112,114,113,106,108,107,133,134,101,100,137,105,80,129,130',
'132,81,105,82,85,83,84,87,374,105,86,377,75,76,379,380,57,56,88,89,77',
'393,90,58,59,78,37,46,48,47,91,49,44,45,64,72,73,-116,302,60,50,70,79',
'61,394,397,105,62,63,269,258,129,130,132,127,128,131,115,116,117,121',
'122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113',
'106,108,107,133,134,101,29,400,38,52,80,54,401,402,81,-245,82,85,83',
'84,87,105,403,86,406,75,76,60,211,57,56,88,89,77,211,90,58,59,78,37',
'46,48,47,91,49,44,45,64,72,73,427,60,60,50,70,79,61,430,431,434,62,63',
'435,438,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120',
'124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134',
'440,29,441,38,52,80,54,442,443,81,211,82,85,83,84,87,105,445,86,446',
'75,76,371,371,57,56,88,89,77,211,90,58,59,78,37,46,48,47,91,49,44,45',
'64,72,73,458,435,60,50,70,79,61,462,463,371,62,63,465,468,129,130,132',
'127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,,,,,,,,',
',,,129,130,132,,29,,38,52,80,54,,,81,,82,85,83,84,87,105,,86,,75,76',
',,57,56,88,89,77,,90,58,59,78,37,46,48,47,91,49,44,45,64,72,73,105,',
'60,50,70,79,61,,,,62,63,,,129,130,132,127,128,131,115,116,117,121,122',
'123,118,119,120,124,125,126,,,,,,,,,,,,129,130,132,,29,,38,52,80,54',
',,81,,82,85,83,84,87,105,,86,,75,76,,,57,56,88,89,77,,90,58,59,78,37',
'46,48,47,91,49,44,45,64,72,73,105,,60,50,70,79,61,,,,62,63,414,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,29,,38,52,,54,,129,130,132,127,128,131',
'115,116,117,121,122,123,118,119,120,124,125,126,102,104,103,109,111',
'110,112,114,113,106,108,107,133,134,101,100,137,419,,,,,,,,,146,56,',
'89,105,,90,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,29,',
',52,,54,,261,146,56,,89,,105,90,,,,,46,48,47,,49,44,45,64,,,,,60,50',
'70,,61,,,,62,63,180,146,56,,89,,,90,,,,,46,48,47,,49,44,45,64,,,-116',
',60,50,70,,61,,,,62,63,,29,,,52,,54,,261,,129,130,132,127,128,131,115',
'116,117,121,122,123,118,119,120,124,125,126,,180,146,56,,89,,176,90',
',52,,54,46,48,47,,49,44,45,64,,,,,60,50,70,,61,105,,,62,63,180,146,56',
',89,,,90,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,,29,-119',
',52,,54,,,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120',
'124,125,126,,,,180,146,56,,89,29,-119,90,52,,54,,46,48,47,,49,44,45',
'64,,,,,60,50,70,105,61,,,,62,63,180,146,56,,89,,,90,,,,,46,48,47,,49',
'44,45,64,,,,,60,50,70,,61,,,,62,63,,29,-119,,52,,54,,,129,130,132,127',
'128,131,115,116,117,121,122,123,118,119,120,124,125,126,,,,180,146,56',
',89,29,-119,90,52,,54,,46,48,47,,49,44,45,64,,,,,60,50,70,105,61,,,',
'62,63,180,146,56,,89,,,90,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61',
',,,62,63,,29,-121,,52,,54,,,129,130,132,127,128,131,115,116,117,121',
'122,123,118,119,120,124,125,126,,,,,146,56,,89,29,-119,90,52,,54,,46',
'48,47,,49,44,45,64,,,,,60,50,70,105,61,,,,62,63,180,146,56,,89,,,90',
',,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,,29,,,52,,54,350',
',129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124,125',
'126,,,,180,146,56,,89,29,-119,90,52,,54,,46,48,47,,49,44,45,64,,,,,60',
'50,70,105,61,,,,62,63,365,146,56,,89,,,90,,,,,46,48,47,,49,44,45,64',
',,,,60,50,70,,61,,,,62,63,,29,-119,,52,,54,,129,130,132,127,128,131',
'115,116,117,121,122,123,118,119,120,124,125,126,,,,180,146,56,,89,,29',
'90,,52,,54,46,48,47,,49,44,45,64,,,,,60,50,70,105,61,,,,62,63,180,146',
'56,,89,,,90,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,,29',
'-119,,52,,54,,,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,,,,,146,56,,89,29,-119,90,52,,54,,46,48,47,,49,44,45',
'64,,,,,60,50,70,105,61,,,,62,63,146,56,,89,,,90,,,,,46,48,47,,49,44',
'45,64,,,-116,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48',
'47,,49,44,45,64,,,-116,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54',
',,167,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,',
'52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,165',
'89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,-116,,60,50,70,,61',
',,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70',
',61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60',
'50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,',
',,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45',
'64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49',
'44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47',
',49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46',
'48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54',
',,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52',
'90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89',
',52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56',
'29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,',
'62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,',
'61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50',
'70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,',
'60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64',
',,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44',
'45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,',
'49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48',
'47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,',
'46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90',
'54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,',
'52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29',
'89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,',
'62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,',
'61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50',
'70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,',
'60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64',
',,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44',
'45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,',
'49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48',
'47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,',
'46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90',
'54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,',
'52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29',
'89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,',
'62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,',
'61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50',
'70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,',
'60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64',
',,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44',
'45,64,,,-116,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,288,,,,46,48',
'47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,',
'46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90',
'54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,',
'52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29',
'89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,-116,,60,50,70,,61',
',,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70',
',61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60',
'50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,',
',,,60,50,70,,61,,,,62,63,,,29,,,52,348,54,129,130,132,127,128,131,115',
'116,117,121,122,123,118,119,120,124,125,126,,,,,146,56,,89,,29,90,,52',
',54,46,48,47,,49,44,45,64,,,-116,,60,50,70,105,61,,,,62,63,146,56,,89',
',,90,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89',
',52,90,288,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56',
'29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,',
'62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,',
'61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50',
'70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,',
'60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44,45,64',
',,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,,49,44',
'45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,,46,48,47,',
'49,44,45,64,,,-116,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90,54,,,',
'46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,,52,90',
'54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29,89,',
'52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146,56,29',
'89,,52,90,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,,54,,,,46,48,47,,49,44,45,,,,,,60,50,70,,61,,,,62,63,146',
'56,29,89,,52,,54,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62,63',
'146,56,188,89,,52,,,,,,46,48,47,,49,44,45,64,,,,,60,50,70,,61,,,,62',
'63,,,188,,,52,,,,,,,,,,,,,,,,211,,,,,,129,130,132,127,128,131,,,188',
',,52,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124',
'125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134,101',
'100,137,211,105,,,,,129,130,132,127,128,131,,105,,,,,129,130,132,127',
'128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103',
'109,111,110,112,114,113,106,108,107,133,134,101,100,137,211,105,,,,',
'129,130,132,127,128,131,,105,,,,,129,130,132,127,128,131,115,116,117',
'121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114',
'113,106,108,107,133,134,101,100,137,211,105,,,,,129,130,132,127,128',
'131,,105,,,,,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,211,105,,,,,129,130,132,127,128,131,,105,,,,,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,455',
'105,,,,,129,130,132,127,128,131,,105,,,,,129,130,132,127,128,131,115',
'116,117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110',
'112,114,113,106,108,107,133,134,101,100,137,460,105,,,,129,130,132,127',
'128,131,,,105,,,,,129,130,132,127,128,131,115,116,117,121,122,123,118',
'119,120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107',
'133,134,101,100,137,105,129,130,132,127,128,131,,,,,,,105,129,130,132',
'127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104',
'103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,105,,,,',
',266,129,130,132,127,128,131,105,129,130,132,127,128,131,115,116,117',
'121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114',
'113,106,108,107,133,134,101,100,137,,,,,,,105,,,,,,,105,129,130,132',
'127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104',
'103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,',
',,,,275,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117',
'121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114',
'113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127',
'128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103',
'109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,',
',105,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124',
'125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134,101',
'100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122',
'123,118,119,120,124,125,126,129,130,132,127,128,131,115,116,117,121',
'122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113',
'106,108,107,105,,,,,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116',
'117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112',
'114,113,106,108,107,,,,,,,,,,,,,,,,,,,105,129,130,132,127,128,131,115',
'116,117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110',
'112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,',
',,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,,,,,,,,,,,,,337,105,129,130,132,127,128,131,115,116',
'117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112',
'114,113,106,108,107,133,134,101,100,137,,,,,,,,,,340,,,341,105,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,',
',,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,,,,,,,266,,,,,,,105,129,130,132,127,128,131,115,116',
'117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112',
'114,113,106,108,107,133,134,101,100,137,,,,,,,360,,,,,,,105,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,',
',,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,,,,,,,360,,,,,,,105,129,130,132,127,128,131,115,116',
'117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112',
'114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130,132',
'127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104',
'103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,',
',,,,,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120',
'124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134',
'101,100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117,121',
'122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113',
'106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127,128',
'131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103,109',
'111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,396,,,341',
'105,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124',
'125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134,101',
'100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122',
'123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113,106',
'108,107,133,134,101,100,137,,,,,,,,,,399,,,,105,129,130,132,127,128',
'131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103,109',
'111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105',
'129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124,125',
'126,102,104,103,109,111,110,112,114,113,106,108,107,133,134,101,100',
'137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122,123',
'118,119,120,124,125,126,102,104,103,109,111,110,112,114,113,106,108',
'107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115',
'116,117,121,122,123,118,119,120,124,125,126,102,104,103,109,111,110',
'112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130',
'132,127,128,131,115,116,117,121,122,123,118,119,120,124,125,126,102',
'104,103,109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,',
',,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122,123,118,119',
'120,124,125,126,102,104,103,109,111,110,112,114,113,106,108,107,133',
'134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117',
'121,122,123,118,119,120,124,125,126,102,104,103,109,111,110,112,114',
'113,106,108,107,133,134,101,100,137,,,,,,,,,,,,,,105,129,130,132,127',
'128,131,115,116,117,121,122,123,118,119,120,124,125,126,102,104,103',
'109,111,110,112,114,113,106,108,107,133,134,101,100,137,,,,,,,,,,,,',
',105,129,130,132,127,128,131,115,116,117,121,122,123,118,119,120,124',
'125,126,102,104,103,109,111,110,112,114,113,106,108,107,133,134,101',
'100,137,,,,,,,,,,,,,,105,129,130,132,127,128,131,115,116,117,121,122',
'123,118,119,120,124,125,126,102,104,103,109,111,110,112,114,113,106',
'108,107,133,134,101,100,137,,,,,,,,,,,,,,105' ]
        racc_action_table = arr = ::Array.new(6668, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'22,143,74,143,22,333,376,376,182,182,370,369,433,157,57,57,74,57,155',
'182,157,155,369,155,3,57,57,57,93,57,57,57,333,370,376,433,330,57,57',
'57,202,57,330,330,202,57,57,22,22,22,22,22,22,22,22,22,22,22,22,22,22',
'22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,57,0',
'3,57,332,0,93,0,0,0,0,0,376,22,0,454,0,0,287,287,0,0,0,0,0,461,0,0,0',
'0,0,0,0,0,0,0,0,0,0,0,0,0,55,0,0,0,0,0,298,466,1,0,0,298,59,59,283,283',
'283,283,447,283,451,464,332,59,283,283,40,40,210,447,19,451,464,454',
'210,181,181,287,287,175,72,72,72,461,23,200,0,213,0,0,2,0,55,277,2,161',
'2,2,2,2,2,162,278,2,284,2,2,466,59,2,2,2,2,2,286,2,2,2,2,2,2,2,2,2,2',
'2,2,2,2,2,2,175,2,2,2,2,2,200,86,213,2,2,161,313,266,277,314,161,162',
'304,360,304,24,162,278,284,284,186,186,186,138,138,138,138,27,286,286',
'8,138,8,8,8,403,430,73,73,73,2,32,2,2,94,2,315,35,94,86,94,94,94,94',
'94,285,266,94,313,94,94,314,360,94,94,94,94,94,186,94,94,94,94,94,94',
'94,94,94,94,94,94,94,94,94,403,430,94,94,94,94,94,281,281,315,94,94',
'285,285,285,285,43,187,187,187,282,282,285,285,410,70,410,222,222,222',
'222,222,222,222,222,222,222,222,222,222,222,222,222,222,222,290,290',
'94,87,94,94,95,94,387,387,95,88,95,95,95,95,95,33,89,95,187,95,95,416',
'416,95,95,95,95,95,222,95,95,95,95,90,95,95,95,95,95,95,95,95,95,95',
'95,92,95,95,95,95,95,96,97,203,95,95,33,33,33,33,31,31,31,31,331,31',
'331,331,386,331,386,386,140,386,42,42,42,42,141,203,66,66,66,66,146',
'235,235,235,235,235,235,95,148,150,95,151,95,203,203,203,203,203,203',
'203,203,203,203,203,203,203,203,203,203,203,203,203,203,203,203,203',
'203,203,203,203,203,203,203,203,203,203,203,203,204,235,154,160,166',
'169,170,174,178,183,184,185,189,203,199,207,208,209,212,249,250,252',
'260,268,293,297,204,299,300,301,305,236,236,236,236,236,236,237,237',
'237,237,237,237,312,204,204,204,204,204,204,204,204,204,204,204,204',
'204,204,204,204,204,204,204,204,204,204,204,204,204,204,204,204,204',
'204,204,204,204,204,204,236,211,247,247,247,211,237,211,211,211,211',
'211,318,204,211,322,211,211,324,325,211,211,211,211,211,334,211,211',
'211,211,211,211,211,211,211,211,211,211,211,211,211,211,335,211,211',
'211,211,211,336,339,247,211,211,344,345,220,220,220,220,220,220,220',
'220,220,220,220,220,220,220,220,220,220,220,220,220,220,220,220,220',
'220,220,220,220,220,220,220,220,220,211,353,211,211,320,211,355,356',
'320,320,320,320,320,320,320,220,359,320,367,320,320,368,385,320,320',
'320,320,320,389,320,320,320,320,320,320,320,320,320,320,320,320,320',
'320,320,391,392,320,320,320,320,320,404,408,411,320,320,412,417,221',
'221,221,221,221,221,221,221,221,221,221,221,221,221,221,221,221,221',
'221,221,221,221,221,221,221,221,221,221,221,221,221,221,420,320,421',
'320,320,438,320,422,423,438,425,438,438,438,438,438,221,428,438,429',
'438,438,432,434,438,438,438,438,438,445,438,438,438,438,438,438,438',
'438,438,438,438,438,438,438,438,448,449,438,438,438,438,438,456,457',
'458,438,438,459,467,223,223,223,223,223,223,223,223,223,223,223,223',
'223,223,223,223,223,223,,,,,,,,,,,,248,248,248,,438,,438,438,455,438',
',,455,,455,455,455,455,455,223,,455,,455,455,,,455,455,455,455,455,',
'455,455,455,455,455,455,455,455,455,455,455,455,455,455,455,248,,455',
'455,455,455,455,,,,455,455,,,224,224,224,224,224,224,224,224,224,224',
'224,224,224,224,224,224,224,224,,,,,,,,,,,,251,251,251,,455,,455,455',
'460,455,,,460,,460,460,460,460,460,224,,460,,460,460,,,460,460,460,460',
'460,,460,460,460,460,460,460,460,460,460,460,460,460,460,460,460,251',
',460,460,460,460,460,,,,460,460,375,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,460,,460,460,,460,,375,375,375,375,375,375,375,375,375,375,375,375',
'375,375,375,375,375,375,375,375,375,375,375,375,375,375,375,375,375',
'375,375,375,375,375,375,378,,,,,,,,,139,139,,139,375,,139,,,,,139,139',
'139,,139,139,139,139,,,,,139,139,139,,139,,,,139,139,378,378,378,378',
'378,378,378,378,378,378,378,378,378,378,378,378,378,378,378,378,378',
'378,378,378,378,378,378,378,378,378,378,378,378,378,378,139,,,139,,139',
',139,258,258,,258,,378,258,,,,,258,258,258,,258,258,258,258,,,,,258',
'258,258,,258,,,,258,258,58,58,58,,58,,,58,,,,,58,58,58,,58,58,58,58',
',,58,,58,58,58,,58,,,,58,58,,258,,,258,,258,,258,,226,226,226,226,226',
'226,226,226,226,226,226,226,226,226,226,226,226,226,,147,147,147,,147',
',58,147,,58,,58,147,147,147,,147,147,147,147,,,,,147,147,147,,147,226',
',,147,147,165,165,165,,165,,,165,,,,,165,165,165,,165,165,165,165,,',
',,165,165,165,,165,,,,165,165,,147,147,,147,,147,,,227,227,227,227,227',
'227,227,227,227,227,227,227,227,227,227,227,227,227,,,,171,171,171,',
'171,165,165,171,165,,165,,171,171,171,,171,171,171,171,,,,,171,171,171',
'227,171,,,,171,171,176,176,176,,176,,,176,,,,,176,176,176,,176,176,176',
'176,,,,,176,176,176,,176,,,,176,176,,171,171,,171,,171,,,228,228,228',
'228,228,228,228,228,228,228,228,228,228,228,228,228,228,228,,,,215,215',
'215,,215,176,176,215,176,,176,,215,215,215,,215,215,215,215,,,,,215',
'215,215,228,215,,,,215,215,267,267,267,,267,,,267,,,,,267,267,267,,267',
'267,267,267,,,,,267,267,267,,267,,,,267,267,,215,215,,215,,215,,,229',
'229,229,229,229,229,229,229,229,229,229,229,229,229,229,229,229,229',
',,,,274,274,,274,267,267,274,267,,267,,274,274,274,,274,274,274,274',
',,,,274,274,274,229,274,,,,274,274,276,276,276,,276,,,276,,,,,276,276',
'276,,276,276,276,276,,,,,276,276,276,,276,,,,276,276,,274,,,274,,274',
'274,,230,230,230,230,230,230,230,230,230,230,230,230,230,230,230,230',
'230,230,,,,295,295,295,,295,276,276,295,276,,276,,295,295,295,,295,295',
'295,295,,,,,295,295,295,230,295,,,,295,295,302,302,302,,302,,,302,,',
',,302,302,302,,302,302,302,302,,,,,302,302,302,,302,,,,302,302,,295',
'295,,295,,295,,231,231,231,231,231,231,231,231,231,231,231,231,231,231',
'231,231,231,231,,,,362,362,362,,362,,302,362,,302,,302,362,362,362,',
'362,362,362,362,,,,,362,362,362,231,362,,,,362,362,393,393,393,,393',
',,393,,,,,393,393,393,,393,393,393,393,,,,,393,393,393,,393,,,,393,393',
',362,362,,362,,362,,,232,232,232,232,232,232,232,232,232,232,232,232',
'232,232,232,232,232,232,,,,,29,29,,29,393,393,29,393,,393,,29,29,29',
',29,29,29,29,,,,,29,29,29,232,29,,,,29,29,52,52,,52,,,52,,,,,52,52,52',
',52,52,52,52,,,52,,52,52,52,,52,,,,52,52,54,54,29,54,,29,54,29,,,,54',
'54,54,,54,54,54,54,,,54,,54,54,54,,54,,,,54,54,56,56,52,56,,52,56,52',
',,56,56,56,56,,56,56,56,56,,,,,56,56,56,,56,,,,56,56,61,61,54,61,,54',
'61,54,,,,61,61,61,,61,61,61,61,,,,,61,61,61,,61,,,,61,61,62,62,56,62',
',56,62,56,,,,62,62,62,,62,62,62,62,,,,,62,62,62,,62,,,,62,62,63,63,61',
'63,,61,63,61,,,,63,63,63,,63,63,63,63,,,,,63,63,63,,63,,,,63,63,78,78',
'62,78,,62,78,62,,,,78,78,78,,78,78,78,78,,,78,,78,78,78,,78,,,,78,78',
'80,80,63,80,,63,80,63,,,,80,80,80,,80,80,80,80,,,,,80,80,80,,80,,,,80',
'80,81,81,78,81,,78,81,78,,,,81,81,81,,81,81,81,81,,,,,81,81,81,,81,',
',,81,81,82,82,80,82,,80,82,80,,,,82,82,82,,82,82,82,82,,,,,82,82,82',
',82,,,,82,82,85,85,81,85,,81,85,81,,,,85,85,85,,85,85,85,85,,,,,85,85',
'85,,85,,,,85,85,100,100,82,100,,82,100,82,,,,100,100,100,,100,100,100',
'100,,,,,100,100,100,,100,,,,100,100,101,101,85,101,,85,101,85,,,,101',
'101,101,,101,101,101,101,,,,,101,101,101,,101,,,,101,101,102,102,100',
'102,,100,102,100,,,,102,102,102,,102,102,102,102,,,,,102,102,102,,102',
',,,102,102,103,103,101,103,,101,103,101,,,,103,103,103,,103,103,103',
'103,,,,,103,103,103,,103,,,,103,103,104,104,102,104,,102,104,102,,,',
'104,104,104,,104,104,104,104,,,,,104,104,104,,104,,,,104,104,105,105',
'103,105,,103,105,103,,,,105,105,105,,105,105,105,105,,,,,105,105,105',
',105,,,,105,105,106,106,104,106,,104,106,104,,,,106,106,106,,106,106',
'106,106,,,,,106,106,106,,106,,,,106,106,107,107,105,107,,105,107,105',
',,,107,107,107,,107,107,107,107,,,,,107,107,107,,107,,,,107,107,108',
'108,106,108,,106,108,106,,,,108,108,108,,108,108,108,108,,,,,108,108',
'108,,108,,,,108,108,109,109,107,109,,107,109,107,,,,109,109,109,,109',
'109,109,109,,,,,109,109,109,,109,,,,109,109,110,110,108,110,,108,110',
'108,,,,110,110,110,,110,110,110,110,,,,,110,110,110,,110,,,,110,110',
'111,111,109,111,,109,111,109,,,,111,111,111,,111,111,111,111,,,,,111',
'111,111,,111,,,,111,111,112,112,110,112,,110,112,110,,,,112,112,112',
',112,112,112,112,,,,,112,112,112,,112,,,,112,112,113,113,111,113,,111',
'113,111,,,,113,113,113,,113,113,113,113,,,,,113,113,113,,113,,,,113',
'113,114,114,112,114,,112,114,112,,,,114,114,114,,114,114,114,114,,,',
',114,114,114,,114,,,,114,114,115,115,113,115,,113,115,113,,,,115,115',
'115,,115,115,115,115,,,,,115,115,115,,115,,,,115,115,116,116,114,116',
',114,116,114,,,,116,116,116,,116,116,116,116,,,,,116,116,116,,116,,',
',116,116,117,117,115,117,,115,117,115,,,,117,117,117,,117,117,117,117',
',,,,117,117,117,,117,,,,117,117,118,118,116,118,,116,118,116,,,,118',
'118,118,,118,118,118,118,,,,,118,118,118,,118,,,,118,118,119,119,117',
'119,,117,119,117,,,,119,119,119,,119,119,119,119,,,,,119,119,119,,119',
',,,119,119,120,120,118,120,,118,120,118,,,,120,120,120,,120,120,120',
'120,,,,,120,120,120,,120,,,,120,120,121,121,119,121,,119,121,119,,,',
'121,121,121,,121,121,121,121,,,,,121,121,121,,121,,,,121,121,122,122',
'120,122,,120,122,120,,,,122,122,122,,122,122,122,122,,,,,122,122,122',
',122,,,,122,122,123,123,121,123,,121,123,121,,,,123,123,123,,123,123',
'123,123,,,,,123,123,123,,123,,,,123,123,124,124,122,124,,122,124,122',
',,,124,124,124,,124,124,124,124,,,,,124,124,124,,124,,,,124,124,125',
'125,123,125,,123,125,123,,,,125,125,125,,125,125,125,125,,,,,125,125',
'125,,125,,,,125,125,126,126,124,126,,124,126,124,,,,126,126,126,,126',
'126,126,126,,,,,126,126,126,,126,,,,126,126,127,127,125,127,,125,127',
'125,,,,127,127,127,,127,127,127,127,,,,,127,127,127,,127,,,,127,127',
'128,128,126,128,,126,128,126,,,,128,128,128,,128,128,128,128,,,,,128',
'128,128,,128,,,,128,128,129,129,127,129,,127,129,127,,,,129,129,129',
',129,129,129,129,,,,,129,129,129,,129,,,,129,129,130,130,128,130,,128',
'130,128,,,,130,130,130,,130,130,130,130,,,,,130,130,130,,130,,,,130',
'130,131,131,129,131,,129,131,129,,,,131,131,131,,131,131,131,131,,,',
',131,131,131,,131,,,,131,131,132,132,130,132,,130,132,130,,,,132,132',
'132,,132,132,132,132,,,,,132,132,132,,132,,,,132,132,133,133,131,133',
',131,133,131,,,,133,133,133,,133,133,133,133,,,,,133,133,133,,133,,',
',133,133,134,134,132,134,,132,134,132,,,,134,134,134,,134,134,134,134',
',,,,134,134,134,,134,,,,134,134,135,135,133,135,,133,135,133,,,,135',
'135,135,,135,135,135,135,,,,,135,135,135,,135,,,,135,135,136,136,134',
'136,,134,136,134,,,,136,136,136,,136,136,136,136,,,,,136,136,136,,136',
',,,136,136,137,137,135,137,,135,137,135,,,,137,137,137,,137,137,137',
'137,,,,,137,137,137,,137,,,,137,137,164,164,136,164,,136,164,136,,,',
'164,164,164,,164,164,164,164,,,,,164,164,164,,164,,,,164,164,173,173',
'137,173,,137,173,137,,,,173,173,173,,173,173,173,173,,,173,,173,173',
'173,,173,,,,173,173,180,180,164,180,,164,180,164,,,,180,180,180,,180',
'180,180,180,,,,,180,180,180,,180,,,,180,180,188,188,173,188,,173,188',
'173,,,,188,188,188,,188,188,188,188,,,,,188,188,188,,188,,,,188,188',
'191,191,180,191,,180,191,180,,,,191,191,191,,191,191,191,191,,,,,191',
'191,191,,191,,,,191,191,192,192,188,192,,188,192,188,,,,192,192,192',
',192,192,192,192,,,,,192,192,192,,192,,,,192,192,193,193,191,193,,191',
'193,191,,,,193,193,193,,193,193,193,193,,,,,193,193,193,,193,,,,193',
'193,194,194,192,194,,192,194,192,,,,194,194,194,,194,194,194,194,,,',
',194,194,194,,194,,,,194,194,261,261,193,261,,193,261,193,,,,261,261',
'261,,261,261,261,261,,,261,,261,261,261,,261,,,,261,261,270,270,194',
'270,,194,270,194,,,,270,270,270,,270,270,270,270,,,,,270,270,270,,270',
',,,270,270,272,272,261,272,,261,272,261,,,,272,272,272,,272,272,272',
'272,,,,,272,272,272,,272,,,,272,272,275,275,270,275,,270,275,270,,,',
'275,275,275,,275,275,275,275,,,,,275,275,275,,275,,,,275,275,,,272,',
',272,272,272,233,233,233,233,233,233,233,233,233,233,233,233,233,233',
'233,233,233,233,,,,,288,288,,288,,275,288,,275,,275,288,288,288,,288',
'288,288,288,,,288,,288,288,288,233,288,,,,288,288,291,291,,291,,,291',
',,,,291,291,291,,291,291,291,291,,,,,291,291,291,,291,,,,291,291,294',
'294,288,294,,288,294,288,,,,294,294,294,,294,294,294,294,,,,,294,294',
'294,,294,,,,294,294,316,316,291,316,,291,316,291,,,,316,316,316,,316',
'316,316,316,,,,,316,316,316,,316,,,,316,316,317,317,294,317,,294,317',
'294,,,,317,317,317,,317,317,317,317,,,,,317,317,317,,317,,,,317,317',
'319,319,316,319,,316,319,316,,,,319,319,319,,319,319,319,319,,,,,319',
'319,319,,319,,,,319,319,323,323,317,323,,317,323,317,,,,323,323,323',
',323,323,323,323,,,,,323,323,323,,323,,,,323,323,326,326,319,326,,319',
'326,319,,,,326,326,326,,326,326,326,326,,,,,326,326,326,,326,,,,326',
'326,327,327,323,327,,323,327,323,,,,327,327,327,,327,327,327,327,,,',
',327,327,327,,327,,,,327,327,328,328,326,328,,326,328,326,,,,328,328',
'328,,328,328,328,328,,,,,328,328,328,,328,,,,328,328,337,337,327,337',
',327,337,327,,,,337,337,337,,337,337,337,337,,,,,337,337,337,,337,,',
',337,337,341,341,328,341,,328,341,328,,,,341,341,341,,341,341,341,341',
',,341,,341,341,341,,341,,,,341,341,365,365,337,365,,337,365,337,,,,365',
'365,365,,365,365,365,365,,,,,365,365,365,,365,,,,365,365,418,418,341',
'418,,341,418,341,,,,418,418,418,,418,418,418,418,,,,,418,418,418,,418',
',,,418,418,435,435,365,435,,365,435,365,,,,435,435,435,,435,435,435',
'435,,,,,435,435,435,,435,,,,435,435,437,437,418,437,,418,437,418,,,',
'437,437,437,,437,437,437,437,,,,,437,437,437,,437,,,,437,437,64,64,435',
'64,,435,,435,,,,64,64,64,,64,64,64,,,,,,64,64,64,,64,,,,64,64,99,99',
'437,99,,437,,437,,,,99,99,99,,99,99,99,99,,,,,99,99,99,,99,,,,99,99',
'142,142,64,142,,64,,,,,,142,142,142,,142,142,142,142,,,,,142,142,142',
',142,,,,142,142,,,99,,,99,,,,,,,,,,,,,,,,205,,,,,,238,238,238,238,238',
'238,,,142,,,142,205,205,205,205,205,205,205,205,205,205,205,205,205',
'205,205,205,205,205,205,205,205,205,205,205,205,205,205,205,205,205',
'205,205,205,205,205,206,238,,,,,239,239,239,239,239,239,,205,,,,,206',
'206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206',
'206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206',
'381,239,,,,,240,240,240,240,240,240,,206,,,,,381,381,381,381,381,381',
'381,381,381,381,381,381,381,381,381,381,381,381,381,381,381,381,381',
'381,381,381,381,381,381,381,381,381,381,381,381,382,240,,,,,241,241',
'241,241,241,241,,381,,,,,382,382,382,382,382,382,382,382,382,382,382',
'382,382,382,382,382,382,382,382,382,382,382,382,382,382,382,382,382',
'382,382,382,382,382,382,382,383,241,,,,,242,242,242,242,242,242,,382',
',,,,383,383,383,383,383,383,383,383,383,383,383,383,383,383,383,383',
'383,383,383,383,383,383,383,383,383,383,383,383,383,383,383,383,383',
'383,383,439,242,,,,,243,243,243,243,243,243,,383,,,,,439,439,439,439',
'439,439,439,439,439,439,439,439,439,439,439,439,439,439,439,439,439',
'439,439,439,439,439,439,439,439,439,439,439,439,439,439,453,243,,,,244',
'244,244,244,244,244,,,439,,,,,453,453,453,453,453,453,453,453,453,453',
'453,453,453,453,453,453,453,453,453,453,453,453,453,453,453,453,453',
'453,453,453,453,453,453,453,453,244,245,245,245,245,245,245,,,,,,,453',
'145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145',
'145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145',
'145,245,,,,,,145,246,246,246,246,246,246,145,156,156,156,156,156,156',
'156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156',
'156,156,156,156,156,156,156,156,156,156,156,156,,,,,,,246,,,,,,,156',
'159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159',
'159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159',
'159,,,,,,,,,,,,,159,159,168,168,168,168,168,168,168,168,168,168,168',
'168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168',
'168,168,168,168,168,168,168,,,,,,,,,,,,,,168,179,179,179,179,179,179',
'179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179',
'179,179,179,179,179,179,179,179,179,179,179,179,,,,,,,,,,,,,,179,201',
'201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,201',
'201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,201',
',,,,,,,,,,,,,201,225,225,225,225,225,225,225,225,225,225,225,225,225',
'225,225,225,225,225,225,225,225,225,225,225,225,225,225,225,225,225',
'225,225,225,225,225,,,,,,,,,,,,,,225,234,234,234,234,234,234,234,234',
'234,234,234,234,234,234,234,234,234,234,253,253,253,253,253,253,253',
'253,253,253,253,253,253,253,253,253,253,253,253,253,253,253,253,253',
'253,253,253,253,253,253,234,,,,,,,,,,,,,,,,,,253,254,254,254,254,254',
'254,254,254,254,254,254,254,254,254,254,254,254,254,254,254,254,254',
'254,254,254,254,254,254,254,254,,,,,,,,,,,,,,,,,,,254,255,255,255,255',
'255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255',
'255,255,255,255,255,255,255,255,255,255,255,255,255,255,,,,,,,,,,,,',
',255,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256',
'256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256,256',
'256,256,,,,,,,,,,,,,,256,257,257,257,257,257,257,257,257,257,257,257',
'257,257,257,257,257,257,257,257,257,257,257,257,257,257,257,257,257',
'257,257,257,257,257,257,257,,,,,,,,,,,,,257,257,259,259,259,259,259',
'259,259,259,259,259,259,259,259,259,259,259,259,259,259,259,259,259',
'259,259,259,259,259,259,259,259,259,259,259,259,259,,,,,,,,,,259,,,259',
'259,289,289,289,289,289,289,289,289,289,289,289,289,289,289,289,289',
'289,289,289,289,289,289,289,289,289,289,289,289,289,289,289,289,289',
'289,289,,,,,,,,,,,,,,289,292,292,292,292,292,292,292,292,292,292,292',
'292,292,292,292,292,292,292,292,292,292,292,292,292,292,292,292,292',
'292,292,292,292,292,292,292,,,,,,,292,,,,,,,292,296,296,296,296,296',
'296,296,296,296,296,296,296,296,296,296,296,296,296,296,296,296,296',
'296,296,296,296,296,296,296,296,296,296,296,296,296,,,,,,,296,,,,,,',
'296,303,303,303,303,303,303,303,303,303,303,303,303,303,303,303,303',
'303,303,303,303,303,303,303,303,303,303,303,303,303,303,303,303,303',
'303,303,,,,,,,,,,,,,,303,307,307,307,307,307,307,307,307,307,307,307',
'307,307,307,307,307,307,307,307,307,307,307,307,307,307,307,307,307',
'307,307,307,307,307,307,307,,,,,,,307,,,,,,,307,308,308,308,308,308',
'308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,308,308',
'308,308,308,308,308,308,308,308,308,308,308,308,308,,,,,,,,,,,,,,308',
'309,309,309,309,309,309,309,309,309,309,309,309,309,309,309,309,309',
'309,309,309,309,309,309,309,309,309,309,309,309,309,309,309,309,309',
'309,,,,,,,,,,,,,,309,310,310,310,310,310,310,310,310,310,310,310,310',
'310,310,310,310,310,310,310,310,310,310,310,310,310,310,310,310,310',
'310,310,310,310,310,310,,,,,,,,,,,,,,310,311,311,311,311,311,311,311',
'311,311,311,311,311,311,311,311,311,311,311,311,311,311,311,311,311',
'311,311,311,311,311,311,311,311,311,311,311,,,,,,,,,,,,,,311,338,338',
'338,338,338,338,338,338,338,338,338,338,338,338,338,338,338,338,338',
'338,338,338,338,338,338,338,338,338,338,338,338,338,338,338,338,,,,',
',,,,,338,,,338,338,343,343,343,343,343,343,343,343,343,343,343,343,343',
'343,343,343,343,343,343,343,343,343,343,343,343,343,343,343,343,343',
'343,343,343,343,343,,,,,,,,,,,,,,343,347,347,347,347,347,347,347,347',
'347,347,347,347,347,347,347,347,347,347,347,347,347,347,347,347,347',
'347,347,347,347,347,347,347,347,347,347,,,,,,,,,,347,,,,347,349,349',
'349,349,349,349,349,349,349,349,349,349,349,349,349,349,349,349,349',
'349,349,349,349,349,349,349,349,349,349,349,349,349,349,349,349,,,,',
',,,,,,,,,349,352,352,352,352,352,352,352,352,352,352,352,352,352,352',
'352,352,352,352,352,352,352,352,352,352,352,352,352,352,352,352,352',
'352,352,352,352,,,,,,,,,,,,,,352,358,358,358,358,358,358,358,358,358',
'358,358,358,358,358,358,358,358,358,358,358,358,358,358,358,358,358',
'358,358,358,358,358,358,358,358,358,,,,,,,,,,,,,,358,364,364,364,364',
'364,364,364,364,364,364,364,364,364,364,364,364,364,364,364,364,364',
'364,364,364,364,364,364,364,364,364,364,364,364,364,364,,,,,,,,,,,,',
',364,372,372,372,372,372,372,372,372,372,372,372,372,372,372,372,372',
'372,372,372,372,372,372,372,372,372,372,372,372,372,372,372,372,372',
'372,372,,,,,,,,,,,,,,372,373,373,373,373,373,373,373,373,373,373,373',
'373,373,373,373,373,373,373,373,373,373,373,373,373,373,373,373,373',
'373,373,373,373,373,373,373,,,,,,,,,,,,,,373,395,395,395,395,395,395',
'395,395,395,395,395,395,395,395,395,395,395,395,395,395,395,395,395',
'395,395,395,395,395,395,395,395,395,395,395,395,,,,,,,,,,,,,,395,398',
'398,398,398,398,398,398,398,398,398,398,398,398,398,398,398,398,398',
'398,398,398,398,398,398,398,398,398,398,398,398,398,398,398,398,398',
',,,,,,,,,,,,,398,405,405,405,405,405,405,405,405,405,405,405,405,405',
'405,405,405,405,405,405,405,405,405,405,405,405,405,405,405,405,405',
'405,405,405,405,405,,,,,,,,,,,,,,405,452,452,452,452,452,452,452,452',
'452,452,452,452,452,452,452,452,452,452,452,452,452,452,452,452,452',
'452,452,452,452,452,452,452,452,452,452,,,,,,,,,,,,,,452' ]
        racc_action_check = arr = ::Array.new(6668, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
    81,   132,   172,    -7,   nil,   nil,   nil,   nil,   209,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    66,
   nil,   nil,    -2,    76,   182,   nil,   nil,   160,   nil,  1805,
   nil,   328,   219,   324,   nil,   225,   nil,   nil,   nil,   nil,
   117,   nil,   342,   229,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  1838,   nil,  1871,    82,  1904,    -7,  1155,   103,
   nil,  1937,  1970,  2003,  4409,   nil,   348,   nil,   nil,   nil,
   288,   nil,   120,   214,   -42,   nil,   nil,   nil,  2036,   nil,
  2069,  2102,  2135,   nil,   nil,  2168,   178,   322,   317,   324,
   296,   nil,   397,    -3,   263,   354,   311,   362,   nil,  4442,
  2201,  2234,  2267,  2300,  2333,  2366,  2399,  2432,  2465,  2498,
  2531,  2564,  2597,  2630,  2663,  2696,  2729,  2762,  2795,  2828,
  2861,  2894,  2927,  2960,  2993,  3026,  3059,  3092,  3125,  3158,
  3191,  3224,  3257,  3290,  3323,  3356,  3389,  3422,   160,  1045,
   381,   388,  4475,   -89,   nil,  4886,   347,  1218,   401,   nil,
   402,   404,   nil,   nil,   394,   -70,  4935,   -75,   nil,  4984,
   398,   137,   143,   nil,  3455,  1252,   400,   nil,  5033,   400,
   447,  1316,   nil,  3488,   399,   121,  1350,   nil,   404,  5082,
  3521,   126,   -25,   405,   452,   398,   192,   273,  3554,   404,
   nil,  3587,  3620,  3653,  3686,   nil,   nil,   nil,   nil,   456,
   127,  5131,    38,   400,   479,  4519,  4572,   483,   484,   485,
   145,   562,   460,   129,   nil,  1414,   nil,   nil,   nil,   nil,
   569,   660,   283,   751,   842,  5180,  1170,  1266,  1364,  1462,
  1560,  1657,  1755,  3831,  5229,   388,   466,   472,  4507,  4560,
  4613,  4666,  4719,  4772,  4824,  4873,  4928,   516,   780,   406,
   407,   871,   408,  5247,  5296,  5345,  5394,  5443,  1121,  5492,
   413,  3719,   nil,   nil,   nil,   nil,   185,  1448,   417,   nil,
  3752,   nil,  3785,   nil,  1512,  3818,  1546,   135,   144,   nil,
   nil,   218,   231,    54,   145,   233,   155,    67,  3881,  5541,
   256,  3914,  5590,   418,  3947,  1610,  5639,   419,    42,   419,
   423,   423,  1644,  5688,   145,   470,   nil,  5737,  5786,  5835,
  5884,  5933,   472,   185,   188,   225,  3980,  4013,   568,  4046,
   653,   nil,   571,  4079,   574,   575,  4112,  4145,  4178,   nil,
    29,   385,    55,   -26,   499,   517,   521,  4211,  5982,   519,
   nil,  4244,   nil,  6031,   573,   525,   nil,  6080,   nil,  6129,
   nil,   nil,  6178,   562,   nil,   562,   563,   nil,  6227,   577,
   191,   nil,  1707,   nil,  6276,  4277,   nil,   627,   628,   -20,
    -9,   nil,  6325,  6374,   nil,   973,     3,   nil,  1050,   nil,
   nil,  4625,  4678,  4731,   nil,   642,   389,   325,   nil,   648,
   nil,   688,   652,  1741,   nil,  6423,   nil,   nil,  6472,   nil,
   nil,   nil,   nil,   213,   612,  6521,   nil,   nil,   696,   nil,
   241,   614,   623,   nil,   nil,   nil,   370,   677,  4310,   nil,
   734,   736,   741,   742,   nil,   720,   nil,   nil,   716,   670,
   214,   nil,   721,    -7,   722,  4343,   nil,  4376,   744,  4784,
   nil,   nil,   nil,   nil,   nil,   739,   nil,   111,   696,   703,
   nil,   113,  6570,  4837,    66,   835,   786,   787,   753,   791,
   926,    76,   nil,   nil,   114,   nil,   100,   792,   nil ]

racc_action_default = [
    -1,  -259,    -2,    -3,    -4,    -8,    -9,   -10,   -11,   -12,
   -13,   -14,   -15,   -16,   -17,   -18,   -19,   -20,   -21,   -22,
   -23,   -24,   -25,   -26,   -27,   -29,   -30,   -31,   -32,  -116,
   -34,   -35,   -36,   -37,   -38,   -39,   -40,   -49,   -50,   -51,
   -52,   -53,   -54,   -55,   -56,   -57,   -58,   -59,   -60,   -63,
   -64,   -65,   -69,   -72,   -75,  -259,  -116,  -116,  -119,  -116,
  -115,  -116,  -116,  -116,  -116,  -168,  -259,  -177,  -179,  -180,
  -259,  -184,  -116,  -116,  -116,  -200,  -201,  -202,  -217,  -219,
  -116,  -116,  -116,  -228,  -229,  -116,  -116,  -259,  -116,  -116,
  -257,  -258,  -259,    -7,  -116,    -6,  -259,  -259,  -188,  -116,
  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,
  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,
  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,
  -116,  -116,  -116,  -116,  -116,  -116,  -116,  -116,   -83,  -116,
   -28,  -259,  -116,   -26,   -31,  -259,  -259,  -116,   -80,   -94,
   -79,   -81,   -61,   -62,  -182,  -259,   -70,  -259,   -76,  -259,
  -259,  -183,  -185,  -189,  -116,  -116,  -101,  -102,  -127,   -35,
   -37,  -116,   -54,   -69,  -259,  -259,  -116,  -107,  -120,  -123,
  -116,  -111,  -116,  -109,  -259,  -164,  -165,  -166,  -116,  -259,
  -167,  -116,  -116,  -116,  -116,  -181,  -186,  -187,  -117,  -259,
  -259,  -218,  -214,  -259,  -259,  -259,  -259,  -259,  -259,  -259,
  -237,  -245,  -259,  -259,  -255,  -116,   469,    -5,  -183,  -170,
  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,  -138,
  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,  -147,  -148,
  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,  -157,  -158,
  -159,  -160,  -161,  -162,  -163,  -222,  -225,  -259,  -116,  -259,
  -259,   -93,   -97,   -96,  -169,   -41,   -33,  -116,  -259,   -95,
  -116,   -67,  -116,   -73,  -116,  -116,  -116,  -259,  -190,  -191,
  -192,   -29,   -30,   -35,   -36,   -37,   -39,   -52,   -75,  -259,
  -259,  -116,  -123,  -259,  -116,  -116,  -123,  -259,  -259,   -83,
  -259,  -259,  -116,  -124,  -259,  -116,  -110,  -259,  -171,  -172,
  -173,  -174,  -259,  -204,  -203,  -207,  -116,  -116,  -259,  -116,
  -116,  -246,  -259,  -116,  -259,  -259,  -116,  -116,  -116,  -234,
  -259,  -259,  -244,  -259,  -105,  -122,  -259,  -116,  -259,  -259,
   -86,   -91,   -87,   -92,   -82,   -84,   -99,  -259,   -68,   -71,
   -74,   -77,   -78,  -259,  -193,  -259,  -259,  -100,  -128,  -259,
  -259,  -103,  -116,  -106,  -125,  -116,  -108,  -259,  -116,  -259,
  -208,  -205,  -215,  -216,  -220,  -259,  -244,  -223,  -259,  -227,
  -230,  -259,  -259,  -259,  -235,  -259,  -259,  -243,  -238,  -259,
  -242,  -259,  -116,  -116,  -256,  -226,   -88,   -89,   -90,   -66,
   -98,  -194,  -195,  -114,  -259,  -126,  -112,  -118,  -259,  -206,
  -259,  -259,  -209,  -210,  -221,  -247,  -248,  -259,  -116,  -224,
  -259,  -259,  -259,  -259,  -240,  -259,  -239,  -253,  -259,  -259,
  -113,  -196,  -207,  -259,  -207,  -116,  -249,  -116,  -116,  -259,
  -231,  -232,  -233,  -236,  -241,  -259,  -104,  -259,  -259,  -211,
  -212,  -259,  -213,  -259,  -250,  -116,  -259,  -259,  -207,  -259,
  -116,  -251,  -254,  -197,  -259,  -198,  -252,  -259,  -199 ]

racc_goto_table = [
     2,   154,     3,    95,    27,   169,    27,    96,   210,    98,
   390,   170,   148,   281,   155,   151,   183,   369,   413,   260,
   389,   415,   172,   318,   322,     1,   282,   217,   140,   356,
   284,   286,   209,   351,   166,   214,    97,   200,   335,   336,
   190,   207,   410,   202,   177,   330,   416,   nil,   nil,   nil,
   nil,   208,   nil,   nil,   154,   nil,   nil,   nil,   nil,   nil,
   nil,   436,   nil,   nil,   nil,   390,   nil,   nil,   nil,   nil,
   nil,    96,    96,   196,   197,   425,   nil,   nil,   nil,   nil,
   nil,   450,   nil,   nil,   nil,   175,   nil,   184,   nil,   nil,
   nil,   nil,   160,    95,   nil,   nil,   145,   nil,    27,    27,
    97,    97,   198,   219,   nil,   nil,   181,   nil,   nil,   nil,
   nil,   nil,   283,   nil,    97,   nil,   212,   213,   285,   156,
   nil,   159,   nil,   168,   nil,   179,   324,   325,   185,   186,
   187,   162,   299,   268,   447,   298,   451,   281,   339,   304,
   281,   280,   nil,   291,   nil,   201,   264,   203,   204,   205,
   282,   293,   206,   282,   284,   286,   nil,   284,   286,   nil,
   464,   nil,   301,   nil,   nil,   nil,   nil,   220,   221,   222,
   223,   224,   225,   226,   227,   228,   229,   230,   231,   232,
   233,   234,   235,   236,   237,   238,   239,   240,   241,   242,
   243,   244,   245,   246,   247,   248,   249,   250,   251,   252,
   253,   254,   255,   256,   257,   nil,   259,   nil,   nil,   nil,
   184,   287,   300,   332,   179,    27,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   345,   nil,   nil,   nil,   nil,   181,
   nil,   289,   292,   nil,   nil,   nil,   283,   315,   296,   283,
   156,   nil,   285,   292,   nil,   285,   344,   303,   nil,   nil,
   334,   162,   388,   nil,   391,   307,   nil,   280,   308,   309,
   310,   311,   353,   nil,   148,   nil,   151,   291,   nil,   355,
   nil,   nil,   nil,   nil,   nil,   nil,   314,   nil,   nil,   nil,
   nil,   359,   179,   nil,   nil,   nil,   nil,   nil,   nil,   314,
   408,   nil,   nil,   280,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   420,   421,   422,   nil,   423,   424,   nil,   nil,
   426,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   345,   nil,
   nil,   nil,   376,   nil,    27,   338,   nil,   nil,   343,   nil,
   nil,   nil,    95,   367,   179,   287,   143,   347,   287,   349,
   344,   159,   352,   179,   nil,   nil,   444,   nil,   404,   nil,
   nil,   nil,   nil,   nil,   nil,   159,   nil,   nil,   289,   nil,
   nil,   358,   179,   nil,   174,   nil,   456,   nil,   457,   364,
   nil,   189,   459,   nil,   nil,   nil,    95,   nil,   387,   429,
   nil,   nil,   nil,   372,   373,   467,   375,   nil,   nil,   nil,
   378,   nil,   nil,   381,   382,   383,   407,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   395,   nil,   189,   nil,   398,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   428,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   179,
   nil,   nil,   405,   387,   nil,   nil,   nil,   nil,   nil,   nil,
   454,   nil,    27,   nil,   nil,   nil,   nil,   nil,   nil,   189,
   nil,   nil,   nil,   nil,    95,   nil,   nil,   461,   nil,    27,
   179,    95,   466,   nil,    27,   nil,    95,   nil,   nil,   nil,
   nil,   nil,   143,   nil,   nil,   nil,   nil,   nil,   143,   nil,
   nil,   nil,   nil,   143,   nil,   439,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   143,   nil,   nil,   nil,   nil,
   nil,   nil,   452,   nil,   453 ]

racc_goto_check = [
     2,    50,     3,     2,    28,    31,    28,    42,    70,    64,
    40,    33,    49,    26,    45,    49,    58,    69,    72,    52,
    76,    77,    41,    74,    74,     1,    27,     4,    25,    37,
    32,    35,    44,    48,    56,    57,    53,    59,    60,    61,
    62,    65,    71,    73,    55,    75,    78,   nil,   nil,   nil,
   nil,    41,   nil,   nil,    50,   nil,   nil,   nil,   nil,   nil,
   nil,    77,   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,
   nil,    42,    42,    64,    64,    76,   nil,   nil,   nil,   nil,
   nil,    72,   nil,   nil,   nil,    53,   nil,    53,   nil,   nil,
   nil,   nil,    54,     2,   nil,   nil,    22,   nil,    28,    28,
    53,    53,    53,    28,   nil,   nil,    38,   nil,   nil,   nil,
   nil,   nil,    31,   nil,    53,   nil,    53,    53,    33,    22,
   nil,    22,   nil,    22,   nil,    22,    70,    70,    22,    22,
    22,    66,    50,    55,    69,    45,    69,    26,    52,    58,
    26,    67,   nil,    67,   nil,    22,    28,    22,    22,    22,
    27,    55,    22,    27,    32,    35,   nil,    32,    35,   nil,
    69,   nil,    55,   nil,   nil,   nil,   nil,    22,    22,    22,
    22,    22,    22,    22,    22,    22,    22,    22,    22,    22,
    22,    22,    22,    22,    22,    22,    22,    22,    22,    22,
    22,    22,    22,    22,    22,    22,    22,    22,    22,    22,
    22,    22,    22,    22,    22,   nil,    22,   nil,   nil,   nil,
    53,    38,    54,     3,    22,    28,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    50,   nil,   nil,   nil,   nil,    38,
   nil,    22,    22,   nil,   nil,   nil,    31,    54,    22,    31,
    22,   nil,    33,    22,   nil,    33,    49,    22,   nil,   nil,
    54,    66,    70,   nil,    70,    22,   nil,    67,    22,    22,
    22,    22,    55,   nil,    49,   nil,    49,    67,   nil,    67,
   nil,   nil,   nil,   nil,   nil,   nil,    66,   nil,   nil,   nil,
   nil,    55,    22,   nil,   nil,   nil,   nil,   nil,   nil,    66,
    70,   nil,   nil,    67,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    70,    70,    70,   nil,    70,    70,   nil,   nil,
    70,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    50,   nil,
   nil,   nil,     3,   nil,    28,    22,   nil,   nil,    22,   nil,
   nil,   nil,     2,    53,    22,    38,    23,    22,    38,    22,
    49,    22,    22,    22,   nil,   nil,    70,   nil,    55,   nil,
   nil,   nil,   nil,   nil,   nil,    22,   nil,   nil,    22,   nil,
   nil,    22,    22,   nil,    23,   nil,    70,   nil,    70,    22,
   nil,    23,    70,   nil,   nil,   nil,     2,   nil,    38,    55,
   nil,   nil,   nil,    22,    22,    70,    22,   nil,   nil,   nil,
    22,   nil,   nil,    22,    22,    22,    53,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    22,   nil,    23,   nil,    22,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    53,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    22,
   nil,   nil,    22,    38,   nil,   nil,   nil,   nil,   nil,   nil,
     3,   nil,    28,   nil,   nil,   nil,   nil,   nil,   nil,    23,
   nil,   nil,   nil,   nil,     2,   nil,   nil,     3,   nil,    28,
    22,     2,     3,   nil,    28,   nil,     2,   nil,   nil,   nil,
   nil,   nil,    23,   nil,   nil,   nil,   nil,   nil,    23,   nil,
   nil,   nil,   nil,    23,   nil,    22,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,   nil,   nil,
   nil,   nil,    22,   nil,    22 ]

racc_goto_pointer = [
   nil,    25,     0,     2,   -68,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    67,   307,   nil,     4,  -151,  -138,     4,   nil,
   nil,   -52,  -134,   -46,   nil,  -133,   nil,  -262,    47,   nil,
  -321,   -35,    -1,   nil,   -54,   -38,   nil,   nil,  -241,   -20,
   -42,   nil,  -120,    28,    37,   -14,   -22,   -54,   -43,   -37,
  -177,  -176,   -24,   nil,     1,   -45,    76,   -21,   nil,  -298,
   -79,  -328,  -352,   -35,  -180,  -165,  -311,  -355,  -330 ]

racc_goto_default = [
   nil,   nil,    94,    93,     4,     5,     6,     7,     8,     9,
    10,    11,    12,    13,    14,    15,    16,    17,    18,    19,
    20,    21,    22,    23,    24,   nil,    25,    26,   144,    28,
    30,    31,    32,    33,    34,    35,    36,   290,    40,    39,
    41,    42,    43,    51,    67,   nil,    53,   157,   158,   150,
   138,    68,   nil,    55,   nil,   297,   nil,   nil,   nil,   nil,
   178,   nil,    65,    66,    71,    69,   278,   163,    74,   nil,
   321,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil ]

racc_reduce_table = [
  0, 0, :racc_error,
  0, 99, :_reduce_1,
  1, 99, :_reduce_2,
  1, 99, :_reduce_3,
  1, 101, :_reduce_4,
  3, 101, :_reduce_5,
  2, 101, :_reduce_6,
  2, 101, :_reduce_7,
  1, 102, :_reduce_8,
  1, 102, :_reduce_9,
  1, 102, :_reduce_10,
  1, 102, :_reduce_11,
  1, 102, :_reduce_12,
  1, 102, :_reduce_13,
  1, 102, :_reduce_14,
  1, 102, :_reduce_15,
  1, 102, :_reduce_16,
  1, 102, :_reduce_17,
  1, 102, :_reduce_18,
  1, 102, :_reduce_19,
  1, 102, :_reduce_20,
  1, 102, :_reduce_21,
  1, 102, :_reduce_22,
  1, 102, :_reduce_23,
  1, 102, :_reduce_24,
  1, 102, :_reduce_25,
  1, 120, :_reduce_26,
  1, 120, :_reduce_27,
  2, 120, :_reduce_28,
  1, 120, :_reduce_29,
  1, 120, :_reduce_30,
  1, 120, :_reduce_31,
  1, 120, :_reduce_32,
  3, 120, :_reduce_33,
  1, 121, :_reduce_34,
  1, 121, :_reduce_35,
  1, 121, :_reduce_36,
  1, 121, :_reduce_37,
  1, 121, :_reduce_38,
  1, 121, :_reduce_39,
  1, 121, :_reduce_40,
  3, 121, :_reduce_41,
  1, 135, :_reduce_42,
  1, 135, :_reduce_43,
  1, 135, :_reduce_44,
  1, 135, :_reduce_45,
  1, 135, :_reduce_46,
  1, 135, :_reduce_47,
  1, 135, :_reduce_48,
  1, 100, :_reduce_49,
  1, 100, :_reduce_50,
  1, 132, :_reduce_51,
  1, 132, :_reduce_52,
  1, 132, :_reduce_53,
  1, 132, :_reduce_54,
  1, 132, :_reduce_55,
  1, 132, :_reduce_56,
  1, 132, :_reduce_57,
  1, 137, :_reduce_58,
  1, 136, :_reduce_59,
  1, 136, :_reduce_60,
  2, 136, :_reduce_61,
  2, 136, :_reduce_62,
  1, 138, :_reduce_63,
  1, 140, :_reduce_64,
  1, 139, :_reduce_65,
  5, 142, :_reduce_66,
  3, 141, :_reduce_67,
  4, 141, :_reduce_68,
  0, 143, :_reduce_69,
  1, 143, :_reduce_70,
  3, 143, :_reduce_71,
  1, 122, :_reduce_72,
  3, 144, :_reduce_73,
  4, 144, :_reduce_74,
  0, 145, :_reduce_75,
  1, 145, :_reduce_76,
  3, 145, :_reduce_77,
  3, 146, :_reduce_78,
  2, 129, :_reduce_79,
  2, 129, :_reduce_80,
  2, 129, :_reduce_81,
  4, 129, :_reduce_82,
  2, 130, :_reduce_83,
  4, 130, :_reduce_84,
  2, 149, :_reduce_85,
  3, 148, :_reduce_86,
  3, 148, :_reduce_87,
  4, 148, :_reduce_88,
  4, 148, :_reduce_89,
  3, 150, :_reduce_90,
  2, 150, :_reduce_91,
  2, 150, :_reduce_92,
  1, 150, :_reduce_93,
  1, 147, :_reduce_94,
  2, 147, :_reduce_95,
  2, 123, :_reduce_96,
  2, 123, :_reduce_97,
  5, 133, :_reduce_98,
  4, 133, :_reduce_99,
  4, 133, :_reduce_100,
  2, 133, :_reduce_101,
  2, 133, :_reduce_102,
  4, 133, :_reduce_103,
  5, 155, :_reduce_104,
  2, 155, :_reduce_105,
  4, 115, :_reduce_106,
  2, 115, :_reduce_107,
  4, 116, :_reduce_108,
  2, 116, :_reduce_109,
  2, 156, :_reduce_110,
  1, 156, :_reduce_111,
  4, 156, :_reduce_112,
  6, 103, :_reduce_113,
  5, 103, :_reduce_114,
  1, 151, :_reduce_115,
  0, 151, :_reduce_116,
  1, 157, :_reduce_117,
  4, 157, :_reduce_118,
  0, 153, :_reduce_119,
  1, 153, :_reduce_120,
  0, 159, :_reduce_121,
  1, 159, :_reduce_122,
  1, 158, :_reduce_123,
  2, 158, :_reduce_124,
  3, 158, :_reduce_125,
  4, 158, :_reduce_126,
  1, 154, :_reduce_127,
  3, 154, :_reduce_128,
  3, 124, :_reduce_129,
  3, 124, :_reduce_130,
  3, 124, :_reduce_131,
  3, 124, :_reduce_132,
  3, 124, :_reduce_133,
  3, 124, :_reduce_134,
  3, 124, :_reduce_135,
  3, 124, :_reduce_136,
  3, 124, :_reduce_137,
  3, 124, :_reduce_138,
  3, 124, :_reduce_139,
  3, 124, :_reduce_140,
  3, 124, :_reduce_141,
  3, 124, :_reduce_142,
  3, 124, :_reduce_143,
  3, 124, :_reduce_144,
  3, 124, :_reduce_145,
  3, 124, :_reduce_146,
  3, 124, :_reduce_147,
  3, 124, :_reduce_148,
  3, 124, :_reduce_149,
  3, 124, :_reduce_150,
  3, 124, :_reduce_151,
  3, 124, :_reduce_152,
  3, 124, :_reduce_153,
  3, 124, :_reduce_154,
  3, 124, :_reduce_155,
  3, 124, :_reduce_156,
  3, 124, :_reduce_157,
  3, 124, :_reduce_158,
  3, 124, :_reduce_159,
  3, 124, :_reduce_160,
  3, 124, :_reduce_161,
  3, 124, :_reduce_162,
  3, 124, :_reduce_163,
  2, 128, :_reduce_164,
  2, 128, :_reduce_165,
  2, 128, :_reduce_166,
  2, 126, :_reduce_167,
  1, 126, :_reduce_168,
  3, 117, :_reduce_169,
  3, 117, :_reduce_170,
  3, 160, :_reduce_171,
  3, 160, :_reduce_172,
  3, 160, :_reduce_173,
  3, 160, :_reduce_174,
  1, 161, :_reduce_175,
  1, 161, :_reduce_176,
  1, 161, :_reduce_177,
  1, 161, :_reduce_178,
  1, 161, :_reduce_179,
  1, 162, :_reduce_180,
  2, 162, :_reduce_181,
  2, 162, :_reduce_182,
  2, 163, :_reduce_183,
  1, 131, :_reduce_184,
  2, 131, :_reduce_185,
  2, 106, :_reduce_186,
  2, 106, :_reduce_187,
  2, 106, :_reduce_188,
  1, 164, :_reduce_189,
  2, 164, :_reduce_190,
  2, 164, :_reduce_191,
  2, 164, :_reduce_192,
  3, 165, :_reduce_193,
  4, 165, :_reduce_194,
  4, 165, :_reduce_195,
  6, 104, :_reduce_196,
  9, 104, :_reduce_197,
  9, 104, :_reduce_198,
  11, 104, :_reduce_199,
  1, 166, :_reduce_200,
  1, 166, :_reduce_201,
  1, 166, :_reduce_202,
  1, 152, :_reduce_203,
  1, 152, :_reduce_204,
  1, 167, :_reduce_205,
  2, 167, :_reduce_206,
  0, 167, :_reduce_207,
  0, 169, :_reduce_208,
  1, 169, :_reduce_209,
  1, 169, :_reduce_210,
  3, 169, :_reduce_211,
  3, 169, :_reduce_212,
  3, 170, :_reduce_213,
  2, 105, :_reduce_214,
  4, 105, :_reduce_215,
  4, 105, :_reduce_216,
  0, 171, :_reduce_217,
  1, 171, :_reduce_218,
  1, 114, :_reduce_219,
  4, 118, :_reduce_220,
  5, 118, :_reduce_221,
  3, 118, :_reduce_222,
  4, 119, :_reduce_223,
  5, 119, :_reduce_224,
  3, 119, :_reduce_225,
  5, 125, :_reduce_226,
  4, 109, :_reduce_227,
  1, 113, :_reduce_228,
  1, 113, :_reduce_229,
  4, 110, :_reduce_230,
  6, 108, :_reduce_231,
  6, 108, :_reduce_232,
  6, 108, :_reduce_233,
  3, 111, :_reduce_234,
  4, 111, :_reduce_235,
  6, 111, :_reduce_236,
  0, 173, :_reduce_237,
  2, 173, :_reduce_238,
  3, 173, :_reduce_239,
  3, 173, :_reduce_240,
  4, 173, :_reduce_241,
  1, 174, :_reduce_242,
  1, 174, :_reduce_243,
  2, 168, :_reduce_244,
  1, 168, :_reduce_245,
  1, 172, :_reduce_246,
  3, 172, :_reduce_247,
  3, 172, :_reduce_248,
  4, 172, :_reduce_249,
  3, 175, :_reduce_250,
  4, 176, :_reduce_251,
  5, 176, :_reduce_252,
  5, 112, :_reduce_253,
  8, 112, :_reduce_254,
  2, 134, :_reduce_255,
  4, 127, :_reduce_256,
  1, 127, :_reduce_257,
  1, 107, :_reduce_258 ]

racc_reduce_n = 259

racc_shift_n = 469

racc_token_table = {
  false => 0,
  :error => 1,
  :IF => 2,
  :ELSE => 3,
  :ELSEIF => 4,
  :THEN => 5,
  :UNLESS => 6,
  :END => 7,
  :WHILE => 8,
  :UNTIL => 9,
  :BREAK => 10,
  :CONTINUE => 11,
  :TRY => 12,
  :CATCH => 13,
  :FINALLY => 14,
  :FOR => 15,
  :IN => 16,
  :DEF => 17,
  :DEF_BANG => 18,
  :SPLAT_PARAM => 19,
  :SPLAT_ARG => 20,
  :CALL => 21,
  :BUILTIN_COMMAND => 22,
  :CLASS => 23,
  :NEW => 24,
  :DEFM => 25,
  :DEFM_BANG => 26,
  :SUPER => 27,
  :RIML_FILE_COMMAND => 28,
  :RIML_CLASS_COMMAND => 29,
  :RETURN => 30,
  :NEWLINE => 31,
  :NUMBER => 32,
  :STRING_D => 33,
  :STRING_S => 34,
  :EX_LITERAL => 35,
  :REGEXP => 36,
  :TRUE => 37,
  :FALSE => 38,
  :LET => 39,
  :UNLET => 40,
  :UNLET_BANG => 41,
  :IDENTIFIER => 42,
  :DICT_VAL => 43,
  :SCOPE_MODIFIER => 44,
  :SCOPE_MODIFIER_LITERAL => 45,
  :SPECIAL_VAR_PREFIX => 46,
  :FINISH => 47,
  "!" => 48,
  "*" => 49,
  "/" => 50,
  "%" => 51,
  "+" => 52,
  "-" => 53,
  "." => 54,
  ">" => 55,
  ">#" => 56,
  ">?" => 57,
  "<" => 58,
  "<#" => 59,
  "<?" => 60,
  ">=" => 61,
  ">=#" => 62,
  ">=?" => 63,
  "<=" => 64,
  "<=#" => 65,
  "<=?" => 66,
  "==" => 67,
  "==?" => 68,
  "==#" => 69,
  "=~" => 70,
  "=~?" => 71,
  "=~#" => 72,
  "!~" => 73,
  "!~?" => 74,
  "!~#" => 75,
  "!=" => 76,
  "!=?" => 77,
  "!=#" => 78,
  :IS => 79,
  :ISNOT => 80,
  "&&" => 81,
  "||" => 82,
  "?" => 83,
  "=" => 84,
  "+=" => 85,
  "-=" => 86,
  ".=" => 87,
  "," => 88,
  "(" => 89,
  ")" => 90,
  ";" => 91,
  "[" => 92,
  "]" => 93,
  "{" => 94,
  "}" => 95,
  ":" => 96,
  "===" => 97 }

racc_nt_base = 98

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "IF",
  "ELSE",
  "ELSEIF",
  "THEN",
  "UNLESS",
  "END",
  "WHILE",
  "UNTIL",
  "BREAK",
  "CONTINUE",
  "TRY",
  "CATCH",
  "FINALLY",
  "FOR",
  "IN",
  "DEF",
  "DEF_BANG",
  "SPLAT_PARAM",
  "SPLAT_ARG",
  "CALL",
  "BUILTIN_COMMAND",
  "CLASS",
  "NEW",
  "DEFM",
  "DEFM_BANG",
  "SUPER",
  "RIML_FILE_COMMAND",
  "RIML_CLASS_COMMAND",
  "RETURN",
  "NEWLINE",
  "NUMBER",
  "STRING_D",
  "STRING_S",
  "EX_LITERAL",
  "REGEXP",
  "TRUE",
  "FALSE",
  "LET",
  "UNLET",
  "UNLET_BANG",
  "IDENTIFIER",
  "DICT_VAL",
  "SCOPE_MODIFIER",
  "SCOPE_MODIFIER_LITERAL",
  "SPECIAL_VAR_PREFIX",
  "FINISH",
  "\"!\"",
  "\"*\"",
  "\"/\"",
  "\"%\"",
  "\"+\"",
  "\"-\"",
  "\".\"",
  "\">\"",
  "\">#\"",
  "\">?\"",
  "\"<\"",
  "\"<#\"",
  "\"<?\"",
  "\">=\"",
  "\">=#\"",
  "\">=?\"",
  "\"<=\"",
  "\"<=#\"",
  "\"<=?\"",
  "\"==\"",
  "\"==?\"",
  "\"==#\"",
  "\"=~\"",
  "\"=~?\"",
  "\"=~#\"",
  "\"!~\"",
  "\"!~?\"",
  "\"!~#\"",
  "\"!=\"",
  "\"!=?\"",
  "\"!=#\"",
  "IS",
  "ISNOT",
  "\"&&\"",
  "\"||\"",
  "\"?\"",
  "\"=\"",
  "\"+=\"",
  "\"-=\"",
  "\".=\"",
  "\",\"",
  "\"(\"",
  "\")\"",
  "\";\"",
  "\"[\"",
  "\"]\"",
  "\"{\"",
  "\"}\"",
  "\":\"",
  "\"===\"",
  "$start",
  "Root",
  "Terminator",
  "Statements",
  "Statement",
  "ExplicitCall",
  "Def",
  "Return",
  "UnletVariable",
  "ExLiteral",
  "For",
  "While",
  "Until",
  "Try",
  "ClassDefinition",
  "LoopKeyword",
  "EndScript",
  "RimlFileCommand",
  "RimlClassCommand",
  "MultiAssign",
  "If",
  "Unless",
  "Expression",
  "ExpressionWithoutDictLiteral",
  "Dictionary",
  "DictGetWithDotLiteral",
  "BinaryOperator",
  "Ternary",
  "Assign",
  "Super",
  "UnaryOperator",
  "DictGet",
  "ListOrDictGet",
  "AllVariableRetrieval",
  "LiteralWithoutDictLiteral",
  "Call",
  "ObjectInstantiation",
  "PossibleStringValue",
  "String",
  "Number",
  "Regexp",
  "List",
  "ScopeModifierLiteral",
  "ListLiteral",
  "ListUnpack",
  "ListItems",
  "DictionaryLiteral",
  "DictItems",
  "DictItem",
  "DictGetWithDot",
  "ListOrDictGetWithBrackets",
  "ListOrDictGetAssign",
  "SubList",
  "Scope",
  "DefCallIdentifier",
  "ArgList",
  "ArgListWithoutNothing",
  "ObjectInstantiationCall",
  "ClassArgList",
  "SIDAndScope",
  "ArgListWithoutNothingWithSplat",
  "ArgListWithSplat",
  "AssignExpression",
  "AssignLHS",
  "VariableRetrieval",
  "SimpleVariableRetrieval",
  "CurlyBraceName",
  "CurlyBraceVarPart",
  "FunctionType",
  "DefKeywords",
  "Block",
  "ParamList",
  "DefaultParam",
  "Returnable",
  "IfBlock",
  "Catch",
  "Catchable",
  "ElseBlock",
  "ElseifBlock" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'riml.y', 61)
  def _reduce_1(val, _values, result)
     result = make_node(val) { |_| Riml::Nodes.new([]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 62)
  def _reduce_2(val, _values, result)
     result = make_node(val) { |_| Riml::Nodes.new([]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 63)
  def _reduce_3(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 68)
  def _reduce_4(val, _values, result)
     result = make_node(val) { |v| Riml::Nodes.new([ v[0] ]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 69)
  def _reduce_5(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 70)
  def _reduce_6(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 71)
  def _reduce_7(val, _values, result)
     result = make_node(val) { |v| Riml::Nodes.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 76)
  def _reduce_8(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 77)
  def _reduce_9(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 78)
  def _reduce_10(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 79)
  def _reduce_11(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 80)
  def _reduce_12(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 81)
  def _reduce_13(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 82)
  def _reduce_14(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 83)
  def _reduce_15(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 84)
  def _reduce_16(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 85)
  def _reduce_17(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 86)
  def _reduce_18(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 87)
  def _reduce_19(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 88)
  def _reduce_20(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 89)
  def _reduce_21(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 90)
  def _reduce_22(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 91)
  def _reduce_23(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 92)
  def _reduce_24(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 93)
  def _reduce_25(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 97)
  def _reduce_26(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 98)
  def _reduce_27(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 99)
  def _reduce_28(val, _values, result)
     result = make_node(val) { |v| Riml::DictGetDotNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 100)
  def _reduce_29(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 101)
  def _reduce_30(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 102)
  def _reduce_31(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 103)
  def _reduce_32(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 104)
  def _reduce_33(val, _values, result)
     result = make_node(val) { |v| Riml::WrapInParensNode.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 108)
  def _reduce_34(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 109)
  def _reduce_35(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 110)
  def _reduce_36(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 111)
  def _reduce_37(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 112)
  def _reduce_38(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 113)
  def _reduce_39(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 114)
  def _reduce_40(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 115)
  def _reduce_41(val, _values, result)
     result = make_node(val) { |v| Riml::WrapInParensNode.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 120)
  def _reduce_42(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 121)
  def _reduce_43(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 122)
  def _reduce_44(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 123)
  def _reduce_45(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 124)
  def _reduce_46(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 125)
  def _reduce_47(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 126)
  def _reduce_48(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 130)
  def _reduce_49(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 131)
  def _reduce_50(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 135)
  def _reduce_51(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 136)
  def _reduce_52(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 137)
  def _reduce_53(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 138)
  def _reduce_54(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 139)
  def _reduce_55(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 140)
  def _reduce_56(val, _values, result)
     result = make_node(val) { |_| Riml::TrueNode.new }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 141)
  def _reduce_57(val, _values, result)
     result = make_node(val) { |_| Riml::FalseNode.new }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 145)
  def _reduce_58(val, _values, result)
     result = make_node(val) { |v| Riml::NumberNode.new(v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 149)
  def _reduce_59(val, _values, result)
     result = make_node(val) { |v| Riml::StringNode.new(v[0], :s) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 150)
  def _reduce_60(val, _values, result)
     result = make_node(val) { |v| Riml::StringNode.new(v[0], :d) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 151)
  def _reduce_61(val, _values, result)
     result = make_node(val) { |v| Riml::StringLiteralConcatNode.new(v[0], Riml::StringNode.new(v[1], :s)) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 152)
  def _reduce_62(val, _values, result)
     result = make_node(val) { |v| Riml::StringLiteralConcatNode.new(v[0], Riml::StringNode.new(v[1], :d)) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 156)
  def _reduce_63(val, _values, result)
     result = make_node(val) { |v| Riml::RegexpNode.new(v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 160)
  def _reduce_64(val, _values, result)
     result = make_node(val) { |v| Riml::ScopeModifierLiteralNode.new(v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 164)
  def _reduce_65(val, _values, result)
     result = make_node(val) { |v| Riml::ListNode.new(v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 168)
  def _reduce_66(val, _values, result)
     result = make_node(val) { |v| Riml::ListUnpackNode.new(v[1] << v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 172)
  def _reduce_67(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 173)
  def _reduce_68(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 177)
  def _reduce_69(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 178)
  def _reduce_70(val, _values, result)
     result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 179)
  def _reduce_71(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 183)
  def _reduce_72(val, _values, result)
     result = make_node(val) { |v| Riml::DictionaryNode.new(v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 190)
  def _reduce_73(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 191)
  def _reduce_74(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 196)
  def _reduce_75(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 197)
  def _reduce_76(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 198)
  def _reduce_77(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 203)
  def _reduce_78(val, _values, result)
     result = [val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 207)
  def _reduce_79(val, _values, result)
     result = make_node(val) { |v| Riml::DictGetDotNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 208)
  def _reduce_80(val, _values, result)
     result = make_node(val) { |v| Riml::DictGetDotNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 209)
  def _reduce_81(val, _values, result)
     result = make_node(val) { |v| Riml::DictGetDotNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 210)
  def _reduce_82(val, _values, result)
     result = make_node(val) { |v| Riml::DictGetDotNode.new(Riml::WrapInParensNode.new(v[1]), v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 214)
  def _reduce_83(val, _values, result)
     result = make_node(val) { |v| Riml::ListOrDictGetNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 215)
  def _reduce_84(val, _values, result)
     result = make_node(val) { |v| Riml::ListOrDictGetNode.new(Riml::WrapInParensNode.new(v[1]), v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 219)
  def _reduce_85(val, _values, result)
     result = make_node(val) { |v| Riml::ListOrDictGetNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 223)
  def _reduce_86(val, _values, result)
     result = [val[1]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 224)
  def _reduce_87(val, _values, result)
     result = [val[1]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 225)
  def _reduce_88(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 226)
  def _reduce_89(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 230)
  def _reduce_90(val, _values, result)
     result = make_node(val) { |v| Riml::SublistNode.new([v[0], Riml::LiteralNode.new(' : '), v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 231)
  def _reduce_91(val, _values, result)
     result = make_node(val) { |v| Riml::SublistNode.new([v[0], Riml::LiteralNode.new(' :')]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 232)
  def _reduce_92(val, _values, result)
     result = make_node(val) { |v| Riml::SublistNode.new([Riml::LiteralNode.new(': '), v[1]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 233)
  def _reduce_93(val, _values, result)
     result = make_node(val) { |_| Riml::SublistNode.new([Riml::LiteralNode.new(':')]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 237)
  def _reduce_94(val, _values, result)
     result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 238)
  def _reduce_95(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 242)
  def _reduce_96(val, _values, result)
     result = [val[1]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 243)
  def _reduce_97(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 247)
  def _reduce_98(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(v[0], v[1], v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 248)
  def _reduce_99(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(nil, v[0], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 249)
  def _reduce_100(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(nil, v[0], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 250)
  def _reduce_101(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(nil, v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 251)
  def _reduce_102(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(nil, v[0], []) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 252)
  def _reduce_103(val, _values, result)
     result = make_node(val) { |v| Riml::ExplicitCallNode.new(nil, nil, v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 256)
  def _reduce_104(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(v[0], v[1], v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 257)
  def _reduce_105(val, _values, result)
     result = make_node(val) { |v| Riml::CallNode.new(v[0], v[1], []) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 261)
  def _reduce_106(val, _values, result)
     result = make_node(val) { |v| Riml::RimlFileCommandNode.new(nil, v[0], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 262)
  def _reduce_107(val, _values, result)
     result = make_node(val) { |v| Riml::RimlFileCommandNode.new(nil, v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 266)
  def _reduce_108(val, _values, result)
     result = make_node(val) { |v| Riml::RimlClassCommandNode.new(nil, v[0], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 267)
  def _reduce_109(val, _values, result)
     result = make_node(val) { |v| Riml::RimlClassCommandNode.new(nil, v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 271)
  def _reduce_110(val, _values, result)
     result = ["#{val[0]}#{val[1]}"]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 272)
  def _reduce_111(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 273)
  def _reduce_112(val, _values, result)
     result = val[0].concat ["#{val[2]}#{val[3]}"]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 277)
  def _reduce_113(val, _values, result)
     result = make_node(val) { |v| Riml::ExplicitCallNode.new(v[1], v[2], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 278)
  def _reduce_114(val, _values, result)
     result = make_node(val) { |v| Riml::ExplicitCallNode.new(nil, v[1], v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 282)
  def _reduce_115(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 283)
  def _reduce_116(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 288)
  def _reduce_117(val, _values, result)
     result = [ nil, val[0] ]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 289)
  def _reduce_118(val, _values, result)
     result = [ make_node(val) { |v| Riml::SIDNode.new(v[1]) }, val[3] ]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 293)
  def _reduce_119(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 294)
  def _reduce_120(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 298)
  def _reduce_121(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 299)
  def _reduce_122(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 303)
  def _reduce_123(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 304)
  def _reduce_124(val, _values, result)
     result = [ make_node(val) { |v| Riml::SplatNode.new(v[1]) } ]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 305)
  def _reduce_125(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 306)
  def _reduce_126(val, _values, result)
     result = val[0] << make_node(val) { |v| Riml::SplatNode.new(v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 310)
  def _reduce_127(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 311)
  def _reduce_128(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 315)
  def _reduce_129(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 316)
  def _reduce_130(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 318)
  def _reduce_131(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 319)
  def _reduce_132(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 320)
  def _reduce_133(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 323)
  def _reduce_134(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 325)
  def _reduce_135(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 326)
  def _reduce_136(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 327)
  def _reduce_137(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 329)
  def _reduce_138(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 330)
  def _reduce_139(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 331)
  def _reduce_140(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 333)
  def _reduce_141(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 334)
  def _reduce_142(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 335)
  def _reduce_143(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 337)
  def _reduce_144(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 338)
  def _reduce_145(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 339)
  def _reduce_146(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 341)
  def _reduce_147(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 342)
  def _reduce_148(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 343)
  def _reduce_149(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 345)
  def _reduce_150(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 346)
  def _reduce_151(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 347)
  def _reduce_152(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 349)
  def _reduce_153(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 350)
  def _reduce_154(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 351)
  def _reduce_155(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 353)
  def _reduce_156(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 354)
  def _reduce_157(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 355)
  def _reduce_158(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 356)
  def _reduce_159(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 357)
  def _reduce_160(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 358)
  def _reduce_161(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 360)
  def _reduce_162(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 361)
  def _reduce_163(val, _values, result)
     result = make_node(val) { |v| Riml::BinaryOperatorNode.new(v[1], [v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 365)
  def _reduce_164(val, _values, result)
     result = make_node(val) { |v| Riml::UnaryOperatorNode.new(val[0], [val[1]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 366)
  def _reduce_165(val, _values, result)
     result = make_node(val) { |v| Riml::UnaryOperatorNode.new(val[0], [val[1]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 367)
  def _reduce_166(val, _values, result)
     result = make_node(val) { |v| Riml::UnaryOperatorNode.new(val[0], [val[1]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 372)
  def _reduce_167(val, _values, result)
     result = make_node(val) { |v| Riml::AssignNode.new(v[1][0], v[1][1], v[1][2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 373)
  def _reduce_168(val, _values, result)
     result = make_node(val) { |v| Riml::AssignNode.new(v[0][0], v[0][1], v[0][2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 377)
  def _reduce_169(val, _values, result)
     result = make_node(val) { |v| Riml::MultiAssignNode.new([v[0], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 378)
  def _reduce_170(val, _values, result)
     val[0].assigns << val[2]; result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 383)
  def _reduce_171(val, _values, result)
     result = [val[1], val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 384)
  def _reduce_172(val, _values, result)
     result = [val[1], val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 385)
  def _reduce_173(val, _values, result)
     result = [val[1], val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 386)
  def _reduce_174(val, _values, result)
     result = [val[1], val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 390)
  def _reduce_175(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 391)
  def _reduce_176(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 392)
  def _reduce_177(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 393)
  def _reduce_178(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 394)
  def _reduce_179(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 399)
  def _reduce_180(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 400)
  def _reduce_181(val, _values, result)
     result = make_node(val) { |v| Riml::GetSpecialVariableNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 401)
  def _reduce_182(val, _values, result)
     result = make_node(val) { |v| Riml::GetVariableByScopeAndDictNameNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 405)
  def _reduce_183(val, _values, result)
     result = make_node(val) { |v| Riml::GetVariableNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 409)
  def _reduce_184(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 410)
  def _reduce_185(val, _values, result)
     result = make_node(val) { |v| Riml::GetCurlyBraceNameNode.new(v[0], v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 414)
  def _reduce_186(val, _values, result)
     result = make_node(val) { |v| Riml::UnletVariableNode.new('!', [ v[1] ]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 415)
  def _reduce_187(val, _values, result)
     result = make_node(val) { |v| Riml::UnletVariableNode.new('!', [ v[1] ]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 416)
  def _reduce_188(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 420)
  def _reduce_189(val, _values, result)
     result = make_node(val) { |v| Riml::CurlyBraceVariable.new([ v[0] ]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 421)
  def _reduce_190(val, _values, result)
     result = make_node(val) { |v| Riml::CurlyBraceVariable.new([ Riml::CurlyBracePart.new(v[0]), v[1] ]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 422)
  def _reduce_191(val, _values, result)
     result = val[0] << make_node(val) { |v| Riml::CurlyBracePart.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 423)
  def _reduce_192(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 427)
  def _reduce_193(val, _values, result)
     result = make_node(val) { |v| Riml::CurlyBracePart.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 428)
  def _reduce_194(val, _values, result)
     result = make_node(val) { |v| Riml::CurlyBracePart.new([v[1], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 429)
  def _reduce_195(val, _values, result)
     result = make_node(val) { |v| Riml::CurlyBracePart.new([v[1], v[2]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 435)
  def _reduce_196(val, _values, result)
     result = make_node(val) { |v| Riml.const_get(val[0]).new('!', v[1][0], v[1][1], v[2], [], v[3], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 436)
  def _reduce_197(val, _values, result)
     result = make_node(val) { |v| Riml.const_get(val[0]).new('!', v[1][0], v[1][1], v[2], v[4], v[6], v[7]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 437)
  def _reduce_198(val, _values, result)
     result = make_node(val) { |v| Riml.const_get(val[0]).new('!', v[1][0], v[1][1], v[2], [v[4]], v[6], v[7]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 438)
  def _reduce_199(val, _values, result)
     result = make_node(val) { |v| Riml.const_get(val[0]).new('!', v[1][0], v[1][1], v[2], v[4] << v[6], v[8], v[9]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 442)
  def _reduce_200(val, _values, result)
     result = "DefNode"
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 443)
  def _reduce_201(val, _values, result)
     result = "DefNode"
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 444)
  def _reduce_202(val, _values, result)
     result = "DefMethodNode"
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 449)
  def _reduce_203(val, _values, result)
     result = make_node(val) { |v| Riml::GetCurlyBraceNameNode.new('', v[0]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 450)
  def _reduce_204(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 455)
  def _reduce_205(val, _values, result)
     result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 456)
  def _reduce_206(val, _values, result)
     result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 457)
  def _reduce_207(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 461)
  def _reduce_208(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 462)
  def _reduce_209(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 463)
  def _reduce_210(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 464)
  def _reduce_211(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 465)
  def _reduce_212(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 469)
  def _reduce_213(val, _values, result)
     result = make_node(val) { |v| Riml::DefaultParamNode.new(v[0], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 473)
  def _reduce_214(val, _values, result)
     result = make_node(val) { |v| Riml::ReturnNode.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 474)
  def _reduce_215(val, _values, result)
     result = make_node(val) { |v| Riml::IfNode.new(v[3], Nodes.new([ReturnNode.new(v[1])])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 475)
  def _reduce_216(val, _values, result)
     result = make_node(val) { |v| Riml::UnlessNode.new(v[3], Nodes.new([ReturnNode.new(v[1])])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 479)
  def _reduce_217(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 480)
  def _reduce_218(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 484)
  def _reduce_219(val, _values, result)
     result = make_node(val) { |_| Riml::FinishNode.new }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 489)
  def _reduce_220(val, _values, result)
     result = make_node(val) { |v| Riml::IfNode.new(v[1], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 490)
  def _reduce_221(val, _values, result)
     result = make_node(val) { |v| Riml::IfNode.new(v[1], Riml::Nodes.new([v[3]])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 491)
  def _reduce_222(val, _values, result)
     result = make_node(val) { |v| Riml::IfNode.new(v[2], Riml::Nodes.new([v[0]])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 495)
  def _reduce_223(val, _values, result)
     result = make_node(val) { |v| Riml::UnlessNode.new(v[1], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 496)
  def _reduce_224(val, _values, result)
     result = make_node(val) { |v| Riml::UnlessNode.new(v[1], Riml::Nodes.new([v[3]])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 497)
  def _reduce_225(val, _values, result)
     result = make_node(val) { |v| Riml::UnlessNode.new(v[2], Riml::Nodes.new([v[0]])) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 501)
  def _reduce_226(val, _values, result)
     result = make_node(val) { |v| Riml::TernaryOperatorNode.new([v[0], v[2], v[4]]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 505)
  def _reduce_227(val, _values, result)
     result = make_node(val) { |v| Riml::WhileNode.new(v[1], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 509)
  def _reduce_228(val, _values, result)
     result = make_node(val) { |_| Riml::BreakNode.new }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 510)
  def _reduce_229(val, _values, result)
     result = make_node(val) { |_| Riml::ContinueNode.new }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 514)
  def _reduce_230(val, _values, result)
     result = make_node(val) { |v| Riml::UntilNode.new(v[1], v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 518)
  def _reduce_231(val, _values, result)
     result = make_node(val) { |v| Riml::ForNode.new(v[1], v[3], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 519)
  def _reduce_232(val, _values, result)
     result = make_node(val) { |v| Riml::ForNode.new(v[1], v[3], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 520)
  def _reduce_233(val, _values, result)
     result = make_node(val) { |v| Riml::ForNode.new(v[1], v[3], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 524)
  def _reduce_234(val, _values, result)
     result = make_node(val) { |v| Riml::TryNode.new(v[1], nil, nil) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 525)
  def _reduce_235(val, _values, result)
     result = make_node(val) { |v| Riml::TryNode.new(v[1], v[2], nil) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 526)
  def _reduce_236(val, _values, result)
     result = make_node(val) { |v| Riml::TryNode.new(v[1], v[2], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 530)
  def _reduce_237(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 531)
  def _reduce_238(val, _values, result)
     result = [ make_node(val) { |v| Riml::CatchNode.new(nil, v[1]) } ]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 532)
  def _reduce_239(val, _values, result)
     result = [ make_node(val) { |v| Riml::CatchNode.new(v[1], v[2]) } ]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 533)
  def _reduce_240(val, _values, result)
     result = val[0] << make_node(val) { |v| Riml::CatchNode.new(nil, v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 534)
  def _reduce_241(val, _values, result)
     result = val[0] << make_node(val) { |v| Riml::CatchNode.new(v[2], v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 538)
  def _reduce_242(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 539)
  def _reduce_243(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 546)
  def _reduce_244(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 547)
  def _reduce_245(val, _values, result)
     result = make_node(val) { |_| Riml::Nodes.new([]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 551)
  def _reduce_246(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 552)
  def _reduce_247(val, _values, result)
     result = val[1] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 553)
  def _reduce_248(val, _values, result)
     result = val[1] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 554)
  def _reduce_249(val, _values, result)
     result = val[1] << val[2] << val[3]
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 558)
  def _reduce_250(val, _values, result)
     result = make_node(val) { |v| Riml::ElseNode.new(v[2]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 562)
  def _reduce_251(val, _values, result)
     result = make_node(val) { |v| Riml::Nodes.new([Riml::ElseifNode.new(v[1], v[3])]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 563)
  def _reduce_252(val, _values, result)
     result = val[0] << make_node(val) { |v| Riml::ElseifNode.new(v[2], v[4]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 567)
  def _reduce_253(val, _values, result)
     result = make_node(val) { |v| Riml::ClassDefinitionNode.new(v[1], v[2], nil, v[3]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 568)
  def _reduce_254(val, _values, result)
     result = make_node(val) { |v| Riml::ClassDefinitionNode.new(v[1], v[2], (v[4] || ClassDefinitionNode::DEFAULT_SCOPE_MODIFIER) + v[5], v[6]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 572)
  def _reduce_255(val, _values, result)
     result = make_node(val) { |v| Riml::ObjectInstantiationNode.new(v[1]) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 576)
  def _reduce_256(val, _values, result)
     result = make_node(val) { |v| Riml::SuperNode.new(v[2], true) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 577)
  def _reduce_257(val, _values, result)
     result = make_node(val) { |_| Riml::SuperNode.new([], false) }
    result
  end
.,.,

module_eval(<<'.,.,', 'riml.y', 581)
  def _reduce_258(val, _values, result)
     result = make_node(val) { |v| Riml::ExLiteralNode.new(v[0]) }
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module Riml

#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'
module Opal
  class Parser < Racc::Parser

module_eval(<<'...end opal.y/module_eval...', 'opal.y', 1808)

...end opal.y/module_eval...
##### State transition tables begin ###

clist = [
'63,64,65,8,51,575,552,-92,57,58,619,205,206,61,73,59,60,62,23,24,66',
'67,74,544,754,607,543,22,28,27,90,89,91,92,97,751,17,607,273,-458,612',
'653,7,41,6,9,94,93,575,84,50,86,85,87,273,88,95,96,653,81,82,-100,38',
'39,-99,-68,597,-446,344,343,-95,205,206,618,-446,652,-97,581,575,582',
'-94,205,206,-96,575,36,609,608,30,-92,575,52,652,108,54,770,32,-84,609',
'608,40,101,-79,-92,268,752,100,195,18,-100,-99,551,-84,79,73,75,76,77',
'78,101,-95,574,74,80,100,272,-98,63,64,65,56,51,-97,53,653,57,58,196',
'37,83,61,272,59,60,62,23,24,66,67,454,-534,205,206,197,22,28,27,90,89',
'91,92,308,101,17,574,-84,-91,100,652,-90,41,308,596,94,93,-86,84,50',
'86,85,87,-88,88,95,96,-85,81,82,-87,38,39,101,101,-535,574,900,100,100',
'101,-100,574,-449,-84,100,101,198,574,649,-449,100,-96,-84,210,246,-98',
'214,215,-92,52,-92,-91,54,-92,-100,-99,-100,-99,40,-100,-99,-94,-89',
'273,607,-95,18,-95,299,726,-95,79,73,75,76,77,78,-97,-90,-97,74,80,-97',
'-86,726,63,64,65,56,51,-88,53,544,57,58,546,37,83,61,531,59,60,62,23',
'24,66,67,205,206,653,205,206,22,28,27,90,89,91,92,-91,-534,219,777,300',
'-535,609,608,605,41,268,227,94,93,308,84,50,86,85,87,388,88,95,96,652',
'81,82,-90,38,39,-99,272,-86,726,301,302,-96,-91,-96,-88,-98,-96,-98',
'224,810,-98,-91,226,225,-87,101,210,725,-93,214,100,-94,52,-94,-85,54',
'-94,391,-89,-90,101,40,725,788,-86,100,402,101,-90,218,415,-88,100,-86',
'79,73,75,76,77,78,-88,453,101,74,80,455,204,100,63,64,65,56,51,607,53',
'544,57,58,546,37,83,61,789,59,60,62,258,259,66,67,876,-534,-87,-95,877',
'257,28,27,90,89,91,92,-85,456,219,-535,-89,-91,101,607,725,41,607,100',
'94,93,-100,84,50,86,85,87,261,88,95,96,-456,81,82,-87,38,39,-97,-456',
'609,608,610,101,-87,-85,-93,215,100,-89,-94,487,264,265,-85,754,-455',
'-454,-89,210,266,489,214,-455,-454,52,751,813,54,582,256,491,254,497',
'40,-67,609,608,614,609,608,620,218,400,401,203,201,79,73,75,76,77,78',
'202,514,544,74,80,546,344,343,63,64,65,56,51,101,53,-451,57,58,100,37',
'83,61,-451,59,60,62,258,259,66,67,515,-532,527,528,516,257,28,27,90',
'89,91,92,101,752,219,-331,-331,100,199,625,200,41,525,-331,94,93,277',
'84,50,86,85,87,261,88,95,96,308,81,82,268,38,39,532,227,231,236,237',
'238,233,235,243,244,239,240,-449,-449,220,221,-452,101,241,242,-449',
'210,100,-452,214,-532,533,52,227,-331,54,-331,256,224,254,230,40,226',
'225,222,223,234,232,228,218,229,-532,203,449,79,73,75,76,77,78,450,308',
'-458,74,80,547,245,548,63,64,65,56,51,-449,53,-449,57,58,491,37,83,61',
'555,59,60,62,258,259,66,67,103,104,105,106,107,257,28,27,90,89,91,92',
'-453,-84,219,524,521,531,452,-453,451,41,-92,522,94,93,558,84,50,86',
'85,87,261,88,95,96,559,81,82,561,38,39,570,227,231,236,237,238,233,235',
'243,244,239,240,524,534,220,221,-90,412,241,242,535,210,414,413,214',
'-99,571,52,404,520,54,523,256,224,254,230,40,226,225,222,223,234,232',
'228,218,229,587,524,584,79,73,75,76,77,78,585,588,630,74,80,589,245',
'650,-255,-255,-255,56,-255,452,53,451,-255,-255,613,37,83,-255,617,-255',
'-255,-255,-255,-255,-255,-255,103,104,105,106,107,-255,-255,-255,-255',
'-255,-255,-255,621,-86,-255,524,593,624,583,625,586,-255,-95,591,-255',
'-255,627,-255,-255,-255,-255,-255,-255,-255,-255,-255,-263,-255,-255',
'628,-255,-255,629,227,231,236,237,238,233,235,243,244,239,240,-284,-284',
'220,221,205,206,241,242,-284,-255,268,631,-255,268,227,-255,227,592',
'-255,523,-255,224,-255,230,-255,226,225,222,223,234,232,228,-255,229',
'344,343,602,-255,-255,-255,-255,-255,-255,603,832,813,-255,-255,776',
'245,227,-233,-88,227,-255,915,-284,-255,-284,308,754,-97,-255,-255,63',
'64,65,8,51,344,343,751,57,58,832,813,670,61,671,59,60,62,23,24,66,67',
'679,681,-79,-85,682,22,28,27,90,89,91,92,-94,684,17,537,341,340,344',
'343,7,41,227,9,94,93,694,84,50,86,85,87,700,88,95,96,701,81,82,702,38',
'39,706,227,231,236,237,238,233,235,243,244,239,240,224,752,220,221,226',
'225,241,242,716,36,718,721,281,582,729,52,772,-264,54,497,32,224,497',
'230,40,226,225,222,223,234,232,228,18,229,497,524,593,79,73,75,76,77',
'78,822,791,792,74,80,489,245,491,63,64,65,56,51,800,53,802,57,58,803',
'37,83,61,694,59,60,62,258,259,66,67,708,807,268,268,808,257,291,295',
'90,89,91,92,268,227,219,-286,-286,227,592,813,523,292,819,-286,94,93',
'820,84,50,86,85,87,558,88,95,96,561,81,82,823,327,824,336,334,333,561',
'335,341,340,344,343,827,866,867,-283,-283,868,95,96,813,836,837,-283',
'289,839,840,286,-535,-534,52,842,-286,54,-286,285,850,852,855,856,338',
'858,915,860,862,864,-265,754,341,340,344,343,79,73,75,76,77,78,751,794',
'878,74,80,879,880,881,63,64,65,56,51,-283,53,-283,57,58,883,296,83,61',
'884,59,60,62,258,259,66,67,708,341,340,344,343,257,291,295,90,89,91',
'92,524,946,219,103,104,105,106,107,947,292,227,694,94,93,886,84,50,86',
'85,87,-263,88,95,96,890,81,82,764,752,336,334,333,754,335,895,341,340',
'344,343,897,903,905,224,751,906,308,226,225,222,223,919,289,-266,921',
'214,-535,945,52,586,563,54,336,334,333,561,335,338,749,931,932,937,855',
'939,860,341,340,344,343,860,79,73,75,76,77,78,862,948,954,74,80,700',
'964,860,298,966,967,56,,,53,,,,,296,83,63,64,65,227,51,,,752,57,58,',
',,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,224,219,',
',226,225,222,223,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39',
',227,231,236,237,238,233,235,243,244,239,240,,,220,221,,,241,242,,210',
',,214,,,52,,,54,,,224,,230,40,226,225,222,223,234,232,228,218,229,,',
',79,73,75,76,77,78,,,,74,80,,245,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,563,219,336,334',
'333,,335,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,231',
'236,237,238,233,235,243,244,239,240,,,220,221,,,241,242,,210,,,214,',
',52,,,54,,,224,,230,40,226,225,222,223,234,232,228,218,229,,,,79,73',
'75,76,77,78,,,,74,80,,245,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,231,236,237,238,233,235',
'243,244,239,240,,,220,221,,,241,242,,210,,,214,,,52,,,54,,,224,,230',
'40,226,225,222,223,234,232,228,218,229,,,,79,73,75,76,77,78,,,,74,80',
',245,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,',
',22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96',
',81,82,,38,39,,227,231,236,237,238,233,235,243,244,239,240,,,220,221',
',,241,242,,210,,,214,,,52,,,54,,,224,,230,40,226,225,222,223,234,232',
'228,18,229,,,,79,73,75,76,77,78,,,,74,80,,245,,63,64,65,56,51,,53,,57',
'58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,227,231',
'236,237,238,233,235,243,244,239,240,,,220,221,,,241,242,,210,,,214,',
',52,,,54,,256,224,,230,40,226,225,222,223,234,232,228,218,229,,,,79',
'73,75,76,77,78,,,,74,80,,245,,63,64,65,56,51,,53,,57,58,,37,83,61,,59',
'60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94',
'93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,227,231,236,237,238,233',
'235,243,244,239,240,,,220,221,,,241,242,,210,,,214,,,52,,,54,,,224,',
'230,40,226,225,222,223,234,232,228,218,229,,,,79,73,75,76,77,78,,,,74',
'80,,245,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,',
',,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95',
'96,,81,82,,38,39,,227,231,236,237,238,233,235,243,244,239,240,,,220',
'221,,,241,242,,210,,,214,,,52,,,54,,,224,,230,40,226,225,222,223,234',
'232,228,18,229,,,,79,73,75,76,77,78,,,,74,80,,245,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17',
',,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,231,236',
'237,238,233,235,243,244,239,240,,,220,221,,,241,242,,210,,,214,,,52',
',,54,,,224,,230,40,226,225,222,223,234,232,228,18,229,,,,79,73,75,76',
'77,78,,,,74,80,,245,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,38,39,,227,231,236,237,238,233,235,243,244,239',
'240,,,220,221,,,241,242,,210,,,214,,,52,,,54,,,224,,230,40,226,225,222',
'223,234,232,228,18,229,,,,79,73,75,76,77,78,,,,74,80,101,245,,-233,',
'100,56,,,53,,,,,37,83,63,64,65,,51,,,,57,58,,,,61,,59,60,62,258,259',
'66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,292,,,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,227,231,236,237,238,233,235,243,244,239,240,227',
',220,221,,,241,242,,,,,,356,,,30,241,242,52,,224,54,230,32,226,225,222',
'223,234,232,228,224,229,230,,226,225,222,223,,79,73,75,76,77,78,,,245',
'74,80,,,,63,64,65,56,51,,53,,57,58,,296,83,61,,59,60,62,258,259,66,67',
',,,,,257,291,295,90,89,91,92,,,219,,,,,,,292,,,94,93,,84,50,86,85,361',
',88,95,96,,81,82,227,231,236,237,238,233,235,243,244,239,240,227,,220',
'221,,,241,242,,,367,,,362,,,214,241,242,52,,224,54,230,,226,225,222',
'223,234,232,228,224,229,230,,226,225,222,223,,79,73,75,76,77,78,,,245',
'74,80,,,,-531,-531,-531,56,-531,,53,,-531,-531,,296,83,-531,,-531,-531',
'-531,-531,-531,-531,-531,,-531,,,,-531,-531,-531,-531,-531,-531,-531',
',,-531,,,,,,,-531,,,-531,-531,,-531,-531,-531,-531,-531,-531,-531,-531',
'-531,,-531,-531,,-531,-531,,227,231,236,237,238,233,235,243,244,239',
'240,,,220,221,,,241,242,,-531,,,-531,-531,,-531,,,-531,,-531,224,-531',
'230,-531,226,225,222,223,234,232,228,-531,229,-531,,,-531,-531,-531',
'-531,-531,-531,,,,-531,-531,,245,,-532,-532,-532,-531,-532,,-531,,-532',
'-532,,-531,-531,-532,,-532,-532,-532,-532,-532,-532,-532,,-532,,,,-532',
'-532,-532,-532,-532,-532,-532,,,-532,,,,,,,-532,,,-532,-532,,-532,-532',
'-532,-532,-532,-532,-532,-532,-532,,-532,-532,,-532,-532,,227,231,236',
'237,238,233,235,243,244,239,240,,,220,221,,,241,242,,-532,,,-532,-532',
',-532,,,-532,,-532,224,-532,230,-532,226,225,222,223,234,232,228,-532',
'229,-532,,,-532,-532,-532,-532,-532,-532,,,,-532,-532,,245,,,,,-532',
',,-532,,,,,-532,-532,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66',
'67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,6,9,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,227,231,236,237,238,233,235,243,244,239,240',
',,220,221,,,241,242,,36,,,30,,,52,,,54,,32,224,,230,40,226,225,222,223',
'234,232,228,18,229,,,,79,73,75,76,77,78,,,,74,80,,245,,,,404,56,,,53',
',,,,37,83,63,64,65,,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22',
'28,27,90,89,91,92,,563,17,336,334,333,,335,,41,,,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,38,39,563,,336,334,333,,335,,,563,566,336,334,333',
',335,,,569,,,210,,,214,,,52,,,54,,,,,,40,,,566,,,,,18,,,569,566,79,73',
'75,76,77,78,,829,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,227,231,236,237,238,233,235,243,244',
'239,240,,,-555,-555,,,241,242,,210,,,214,,,52,,,54,,,224,,230,40,226',
'225,222,223,234,232,228,18,229,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90',
'89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,227,231,236,237,238,233,235,243,244,239,240,,,-555,-555,,,241,242',
',210,,,214,,,52,,,54,,,224,,230,40,226,225,222,223,234,232,228,18,229',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,-555,-555,-555,-555,233',
'235,,,-555,-555,,,,,,,241,242,,210,,,214,,,52,,,54,,,224,,230,40,226',
'225,222,223,234,232,228,18,229,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56',
',,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,',
',,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,227,,,,,,,,,,,,,,,,,241,242,,36,,,30,,,52,,,54',
',32,224,,230,40,226,225,222,223,,,228,18,229,,,,79,73,75,76,77,78,,',
',74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62',
'23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,6,9,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,227,,,,,,,,,,,,,,,,,241,242,,36,,',
'30,,,52,,,54,,32,224,,230,40,226,225,222,223,,,228,18,229,,,,79,73,75',
'76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,',
'61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9',
'94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,,,,,,,,,,,,,,,,,241',
'242,,36,,,30,,,52,,,54,,32,224,,230,40,226,225,222,223,,,228,18,229',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,219,,,,,,,41,,,94',
'93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,,,,,,,,,,,,,,,,,241',
'242,,210,,,214,,,52,,,54,,421,224,,230,40,226,225,222,223,,,228,218',
'229,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83',
'61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,219,,,,,,,41,,',
'94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,-555,-555,-555,-555',
'233,235,,,-555,-555,,,,,,,241,242,,210,,,214,,,52,,,54,,421,224,,230',
'40,226,225,222,223,234,232,228,218,229,,,,79,73,75,76,77,78,,,,74,80',
',,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22',
'28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,',
'81,82,,38,39,,227,-555,-555,-555,-555,233,235,,,-555,-555,,,,,,,241',
'242,,210,,,214,,,52,,,54,,,224,,230,40,226,225,222,223,234,232,228,218',
'229,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83',
'61,,59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,227,-555,-555,-555',
'-555,233,235,,,-555,-555,,,,,,,241,242,,210,,,214,,,52,,,54,,256,224',
',230,40,226,225,222,223,234,232,228,218,229,,,,79,73,75,76,77,78,,,',
'74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67',
',,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261',
'88,95,96,,81,82,,38,39,,227,-555,-555,-555,-555,233,235,,,-555,-555',
',,,,,,241,242,,210,,,214,,,52,,,54,,256,224,,230,40,226,225,222,223',
'234,232,228,218,229,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51',
',53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227',
'-555,-555,-555,-555,233,235,,,-555,-555,,,,,,,241,242,,210,,,214,,,52',
',,54,,,224,,230,40,226,225,222,223,234,232,228,218,229,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24',
'66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,227,231,236,237,238,233,235,,,239,240,,,,,',
',241,242,,210,,,214,,,52,,,54,,,224,,230,40,226,225,222,223,234,232',
'228,18,229,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,',
',41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,227,231,236,237',
'238,233,235,243,,239,240,,,,,,,241,242,,210,,,214,,,52,,,54,,,224,,230',
'40,226,225,222,223,234,232,228,18,229,,,,79,73,75,76,77,78,,,,74,80',
',,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24',
'66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,38,39,,227,,,,,,,,,,,,,,,,,241,242,,36,,,30,,,52',
',,54,,32,224,,,40,226,225,222,223,,,,18,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,',
',22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96',
',81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,458,52,,,54,,,,,,40,,',
',,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,',
',,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77',
'78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259',
'66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,',
'54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40',
',,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57',
'58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,',
'219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,',
',,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40',
',,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57',
'58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,',
'219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,',
',,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40',
',,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57',
'58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,',
'219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,',
',,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40',
',,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57',
'58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,',
'219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,',
',,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,28,27,90,89',
'91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,256,,254,,40,,,,,,,,218',
',,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,',
'94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,',
',,210,,,214,,,506,,,54,,256,,254,,40,,,,,,,,218,,,,,79,73,75,76,77,78',
',,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66',
'67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87',
'261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,510,52,,',
'54,,256,,254,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56',
',,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,',
',,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,281,,,52,,,54,,32,,,,40',
',,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,292,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,764,,336,334,333',
'754,335,,,,,,,,,,751,,,,,,,,289,,,214,,,52,,,54,,,,,,338,,,,,,,,341',
'340,344,343,,79,73,75,76,77,78,,,,74,80,,,,518,,,56,,,53,,,,,296,83',
'63,64,65,8,51,,,752,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27',
'90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,36,,,281,,,52,,,54,,32,,,,40,,,,,,,,18,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,292',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,327,,336,334,333,,335,,,,,',
',,,,,,,,,,,,289,,,286,,,52,,,54,,,,,,338,322,,,,,,,341,340,344,343,',
'79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,296,83,61,',
'59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,',
',94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,',
'210,,,214,537,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54',
',32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51',
',53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92',
',,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,',
',,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,18,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24',
'66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,',
',,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,292,,,94,93,,84,50,86,85,361,,88,95,96,,81,82,327,,336,334',
'333,,335,,,,,,,,,,,,,,,,,,362,,,214,,,52,,,54,,,,,,338,,554,,,,,,341',
'340,344,343,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',296,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,',
',,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,',
',,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,',
',74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67',
',,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,',
',,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,-255,-255,-255,56',
'-255,,53,,-255,-255,,37,83,-255,,-255,-255,-255,-255,-255,-255,-255',
',,,,,-255,-255,-255,-255,-255,-255,-255,,,-255,,,,,,,-255,,,-255,-255',
',-255,-255,-255,-255,-255,-255,-255,-255,-255,,-255,-255,,-255,-255',
',,,,,,,,,,,,,,,,,,,,,-255,,,-255,268,,-255,,,-255,,-255,,-255,,-255',
',,,,,,,-255,,,,,-255,-255,-255,-255,-255,-255,,,,-255,-255,,,,-536,-536',
'-536,-255,-536,,-255,,-536,-536,,-255,-255,-536,,-536,-536,-536,-536',
'-536,-536,-536,,,,,,-536,-536,-536,-536,-536,-536,-536,,,-536,,,,,,',
'-536,,,-536,-536,,-536,-536,-536,-536,-536,-536,-536,-536,-536,,-536',
'-536,,-536,-536,,,,,,,,,,,,,,,,,,,,,,-536,,,-536,-536,,-536,,,-536,',
'-536,,-536,,-536,,,,,,,,-536,,,,,-536,-536,-536,-536,-536,-536,,,,-536',
'-536,,,,-537,-537,-537,-536,-537,,-536,,-537,-537,,-536,-536,-537,,-537',
'-537,-537,-537,-537,-537,-537,,,,,,-537,-537,-537,-537,-537,-537,-537',
',,-537,,,,,,,-537,,,-537,-537,,-537,-537,-537,-537,-537,-537,-537,-537',
'-537,,-537,-537,,-537,-537,,,,,,,,,,,,,,,,,,,,,,-537,,,-537,-537,,-537',
',,-537,,-537,,-537,,-537,,,,,,,,-537,,,,,-537,-537,-537,-537,-537,-537',
',,,-537,-537,,,,-255,-255,-255,-537,-255,,-537,,-255,-255,,-537,-537',
'-255,,-255,-255,-255,-255,-255,-255,-255,,,,,,-255,-255,-255,-255,-255',
'-255,-255,,,-255,,,,,,,-255,,,-255,-255,,-255,-255,-255,-255,-255,-255',
'-255,-255,-255,,-255,-255,,-255,-255,,,,,,,,,,,,,,,,,,,,,,-255,,,-255',
'268,,-255,,,-255,,-255,,-255,,-255,,,,,,,,-255,,,,,-255,-255,-255,-255',
'-255,-255,,,,-255,-255,,,,63,64,65,-255,51,,-255,,57,58,,-255,-255,61',
',59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,',
'94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,',
',,210,,,214,,,52,,,54,,256,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,',
',74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67',
',,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261',
'88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,256',
',,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,',
',,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75',
'76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210',
',,214,,,52,,,54,,658,,254,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261',
'88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,254',
',40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,',
',,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75',
'76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,675,52,,',
'54,,,,254,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53',
',,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22',
'28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96',
',81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,',
',,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,404,56,,,53,,,,,37,83,63,64',
'65,,51,,,,57,58,,,,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89',
'91,92,,,219,,,,,,,292,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,327,',
'336,334,333,,335,,,,,,,,,,,,,,,,,,289,,,286,,,52,,,54,,,,,,338,,,,,',
',,341,340,344,343,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,296,83,61,,59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,',
',,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,256,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,',
'84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,',
',214,,,52,,,54,,256,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80',
',,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,',
'257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95',
'96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,',
',,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,',
',,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77',
'78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259',
'66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,',
'54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91',
'92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,',
',,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,18,,,,,79,73,75',
'76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,',
',52,,,54,,658,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63',
'64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291',
'295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96',
',81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,',
',,218,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65',
'8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92',
',,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,',
',,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75',
'76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,',
'61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9',
'94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36',
',,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,',
',63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257',
'291,295,90,89,91,92,,,219,,,,,,,292,,,94,93,,84,50,86,85,87,,88,95,96',
',81,82,764,,336,334,333,754,335,,,,,,,,,,751,,,,,,,,712,,,214,,,52,',
',54,,,,,,338,,,,,,,,341,340,344,343,,79,73,75,76,77,78,,,,74,80,,,,',
',,56,,,53,,,,,296,83,63,64,65,8,51,,,752,57,58,,,,61,,59,60,62,23,24',
'66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32',
',,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37',
'83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27',
'90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,',
',,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,',
',,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,',
',,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,',
',,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77',
'78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259',
'66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85',
'87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,',
'54,,256,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65',
'56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,28,27,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,256,,,,40,,,,,,,,218',
',,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,',
'94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,',
',,210,,,214,,,52,,,54,,256,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,',
',74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67',
',,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40',
',,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,',
',41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,',
',,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,',
'74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67',
',,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,',
',,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,',
',,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,775,,,,40,,,,,,,,218,,,,,79,73',
'75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62',
'23,24,66,67,,,,,,22,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91',
'92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,',
',,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73',
'75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62',
'23,24,66,67,,,,,,22,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56',
',,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,',
',,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40',
',,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,',
',,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77',
'78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59',
'60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30',
',,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81',
'82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,658,,254,,40,,,',
',,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,',
',,,,,,,,,,,,,210,,,214,,,52,,,54,,,,254,,40,,,,,,,,218,,,,,79,73,75',
'76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,',
'61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9',
'94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36',
',,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,',
',,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66',
'67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87',
',88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,',
',,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53',
',57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,292,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,,,,,,,,,,',
',,,,,,,,,,,,,289,,,286,,,52,,,54,,,,,,,,,,,,,,,,,,,79,73,75,76,77,78',
',,,74,80,,,,63,64,65,56,51,,53,,57,58,,296,83,61,,59,60,62,258,259,66',
'67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,292,,,94,93,,84,50,86,85',
'87,,88,95,96,,81,82,,,,,,,,,,,,,,,,,,,,,,,,,289,,,286,,,52,,,54,,,,',
',,,,,,,,,,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58',
',296,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219',
',,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,',
',,,,,,,,,,210,,,214,,,52,,,54,,421,,,,40,,,,,,,,218,,,,,79,73,75,76',
'77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,256,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65',
'56,51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89',
'91,92,,,17,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39',
',,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,18,,,,,79,73',
'75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58',
',,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41',
',9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,',
',,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74',
'80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54',
',32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,',
',37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28',
'27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81',
'82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18',
',,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54',
',32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51',
',53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89',
'91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39',
',,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73',
'75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62',
'23,24,66,67,,,,,,22,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91',
'92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,',
',,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73',
'75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62',
'258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84',
'50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,',
',52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82',
',38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,',
',,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61',
',59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41',
',,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,',
',210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74',
'80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,',
',,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,256,,',
',40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83',
'63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90',
'89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53',
',,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22',
'28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96',
',81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,',
',,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65',
'8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92',
',,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,',
',,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75',
'76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52',
',,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56',
'51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90',
'89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38',
'39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,,,40,,,,,,,,218,,,,,79',
'73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60',
'62,258,259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,41,,,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,,,,,40,,,,,,,,218,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64',
'65,56,51,,53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295',
'90,89,91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,261,88,95,96,,81',
'82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,,,254,,40,,,,,,',
',218,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65',
'8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92',
',,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,',
',,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75',
'76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62,258',
'259,66,67,,,,,,257,291,295,90,89,91,92,,,219,,,,,,,292,,,94,93,,84,50',
'86,85,87,,88,95,96,,81,82,,,,,,,,,,,,,,,,,,,,,,,,,926,,,214,,,52,,,54',
',,,,,,,,,,,,,,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,',
'57,58,,296,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89,91,92',
',,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,',
',,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,658,,,,40,,,,,,,,218,,,,,79,73',
'75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58',
',,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41',
',9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,',
',,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74',
'80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23',
'24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86',
'85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54',
',32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,63,64,65,56,51',
',53,,57,58,,37,83,61,,59,60,62,258,259,66,67,,,,,,257,291,295,90,89',
'91,92,,,219,,,,,,,41,,,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39',
',,,,,,,,,,,,,,,,,,,,,210,,,214,,,52,,,54,,658,,254,,40,,,,,,,,218,,',
',,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,',
',,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,',
',,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,',
',,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77',
'78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59',
'60,62,23,24,66,67,,,,,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93',
',84,50,86,85,87,,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30',
',,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56',
',,53,,,,,37,83,63,64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,',
',,,22,28,27,90,89,91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88',
'95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40',
',,,,,,,18,,,,,79,73,75,76,77,78,,,,74,80,,,,,,,56,,,53,,,,,37,83,63',
'64,65,8,51,,,,57,58,,,,61,,59,60,62,23,24,66,67,,,,,,22,28,27,90,89',
'91,92,,,17,,,,,,7,41,,9,94,93,,84,50,86,85,87,,88,95,96,,81,82,,38,39',
',,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,,,54,,32,,,,40,,,,,,,,18,,,,,79,73',
'75,76,77,78,,,,74,80,,,,63,64,65,56,51,,53,,57,58,,37,83,61,,59,60,62',
'258,259,66,67,,,,,,257,28,27,90,89,91,92,,,219,,,,,,,41,,,94,93,,84',
'50,86,85,87,261,88,95,96,,81,82,,38,39,,,,,,,,,,,,,,,,,,,,,,210,,,214',
',,52,,,54,,256,,,,40,,,,,,,,218,,,,-538,79,73,75,76,77,78,-538,-538',
'-538,74,80,,-538,-538,,-538,,56,,,53,,,,-538,37,83,,,,,,,,,-538,-538',
',-538,-538,-538,-538,-538,,,,,,,,,,,,,,,,,,,,,,,,-538,-538,-538,-538',
'-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-283,,-538,-538',
'-538,,636,-283,-283,-283,-538,,,-283,-283,,-283,-538,,-538,,-538,-538',
'-538,-538,-538,-538,-538,,-538,-538,-538,,,,,-283,-283,,-283,-283,-283',
'-283,-283,-538,-538,,-87,,-538,,,-538,,-538,,-96,,,,,,,,,,,-283,-283',
'-283,-283,-283,-283,-283,-283,-283,-283,-283,-283,-283,-283,-283,,,-283',
'-283,-283,,639,,,,-283,,,,,,,-283,,-283,,-283,-283,-283,-283,-283,-283',
'-283,,-283,,-283,,,,,,,,,,,,,-283,-283,,-89,,-283,-538,,-283,,-283,',
'-98,-538,-538,-538,,,-538,-538,-538,,-538,,,,,,,,,-538,-538,-538,,,',
',,,,,-538,-538,,-538,-538,-538,-538,-538,,,,,,,,,,,,,,,,,,,,,,,,-538',
'-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-538,-538',
',,-538,-538,-538,,790,-538,,,-538,,,-538,,-538,,-538,,-538,,-538,-538',
'-538,-538,-538,-538,-538,,-538,-538,-538,,,,,,,,,,,,,-538,-538,-538',
'-538,,-538,-283,,-538,,-538,,-96,-283,-283,-283,,,-283,-283,-283,,-283',
',,,,,,,,,-283,-283,,,,,,,,,-283,-283,,-283,-283,-283,-283,-283,,,,,',
',,,,,,,,,,,,,,,,,,-283,-283,-283,-283,-283,-283,-283,-283,-283,-283',
'-283,-283,-283,-283,-283,,,-283,-283,-283,,639,-283,,,-283,,,-283,,-283',
',-283,,-283,,-283,-283,-283,-283,-283,-283,-283,,-283,,-283,,,,,,,,',
',,,,-283,-283,-283,-283,,-283,-292,,-283,,-283,,-98,-292,-292,-292,',
',-292,-292,-292,,-292,,,,,,,,,,-292,-292,,,,,,,,,-292,-292,,-292,-292',
'-292,-292,-292,,,,,,,,,,,,,,,,,,,,,,,,-292,-292,-292,-292,-292,-292',
'-292,-292,-292,-292,-292,-292,-292,-292,-292,,,-292,-292,-292,,,-292',
',277,-292,,,-292,,-292,,-292,,-292,,-292,-292,-292,-292,-292,-292,-292',
',-292,,-292,,,,,,,,,,,,,-292,-292,-292,-292,-278,-292,,,-292,,-292,-278',
'-278,-278,,,-278,-278,-278,,-278,,,,,,,,,,-278,-278,-278,,,,,,,,-278',
'-278,,-278,-278,-278,-278,-278,,,,,,,,,,,,,,,,,,,,,,,,-278,-278,-278',
'-278,-278,-278,-278,-278,-278,-278,-278,-278,-278,-278,-278,,,-278,-278',
'-278,,,-278,,,-278,,,-278,,-278,,-278,,-278,,-278,-278,-278,-278,-278',
'-278,-278,,-278,,-278,,,,,,,,,,,,,-278,-278,-278,-278,-554,-278,,-278',
'-278,,-278,-554,-554,-554,,,-554,-554,-554,,-554,,,,,,,,,,-554,,,,,',
',,,,-554,-554,,-554,-554,-554,-554,-554,,,,,,,,,,,,,,-554,,,,,,,-554',
'-554,-554,,,-554,-554,-554,,-554,,,,,-554,-554,,,,-554,,,-554,,,,,268',
'-554,-554,-554,,-554,-554,-554,-554,-554,,,,,764,,336,334,333,754,335',
',-554,,,,,,,,751,,,,-554,-554,,-554,,,-554,-554,-554,-554,-554,-554',
'-554,-554,-554,,-554,,-554,338,749,,,268,-554,,-554,341,340,344,343',
',,,,,-554,-554,,-554,-554,-554,-554,-554,,-554,,,,,,436,440,,,438,,',
'-554,,-554,,,-554,142,143,752,139,121,122,123,130,127,129,,,124,125',
'-554,-554,,,144,145,131,132,-554,,,,,268,-554,,,,,,136,135,,120,141',
'138,137,133,134,128,126,118,140,119,,-554,146,,,,,,,,,,,,-554,,-554',
',,-554,156,167,157,180,153,173,163,162,188,191,178,161,160,155,181,189',
'190,165,154,168,172,174,166,159,,,,175,182,177,176,169,179,164,152,171',
'170,183,184,185,186,187,151,158,149,150,147,148,,111,113,110,,112,,',
',,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131',
'132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119',
',,146,192,,,,,,,,,,80,156,167,157,180,153,173,163,162,188,191,178,161',
'160,155,181,189,190,165,154,168,172,174,166,159,,,,175,182,177,176,169',
'179,164,152,171,170,183,184,185,186,187,151,158,149,150,147,148,,111',
'113,,,112,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,',
',144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126',
'118,140,119,,,146,192,,,,,,,,,,80,156,167,157,180,153,173,163,162,188',
'191,178,161,160,155,181,189,190,165,154,168,172,174,166,159,,,,175,182',
'177,176,169,179,164,152,171,170,183,184,185,186,187,151,158,149,150',
'147,148,,111,113,,,112,,,,,,,,,142,143,,139,121,122,123,130,127,129',
',,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137,133',
'134,128,126,118,140,119,,,146,192,,,,,,,,,,80,156,167,157,180,153,173',
'163,162,188,191,178,161,160,155,181,189,190,165,154,168,172,174,166',
'159,,,,175,182,177,176,169,179,164,152,171,170,183,184,185,186,187,151',
'158,149,150,147,148,,111,113,,,112,,,,,,,,,142,143,,139,121,122,123',
'130,127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141',
'138,137,133,134,128,126,118,140,119,,,146,192,,,,,,,,,,80,156,167,157',
'180,153,173,163,162,188,191,178,161,160,155,181,189,190,165,154,168',
'172,174,166,159,,,,175,182,177,376,375,377,374,152,171,170,183,184,185',
'186,187,151,158,149,150,372,373,,370,113,86,85,371,,88,,,,,,,142,143',
',139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,,381',
',,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119,,,146,156',
'167,157,180,153,173,163,162,188,191,178,161,160,155,181,189,190,165',
'154,168,172,174,166,159,,,,175,182,177,176,169,179,164,152,171,170,183',
'184,185,186,187,151,158,149,150,147,148,,111,113,398,397,112,,399,,',
',,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132',
',,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119,,,146',
'156,167,157,180,153,173,163,162,188,191,178,161,160,155,181,189,190',
'165,154,168,172,174,166,159,,,,175,182,177,176,169,179,164,152,171,170',
'183,184,185,186,187,151,158,149,150,147,148,,111,113,398,397,112,,399',
',,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131',
'132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119',
',,146,156,167,157,180,153,173,163,162,188,191,178,161,160,155,181,189',
'190,165,154,168,172,174,166,159,,,,175,182,177,176,169,179,164,152,171',
'170,183,184,185,186,187,151,158,149,150,147,148,,111,113,,,112,,,,,',
',,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132',
',,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119,430',
'434,146,,431,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125',
',,,,144,145,131,132,,,,,,268,,,,,,,136,135,,120,141,138,137,133,134',
'128,126,118,140,119,443,434,146,,444,,,,,,,,,142,143,,139,121,122,123',
'130,127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141',
'138,137,133,134,128,126,118,140,119,443,434,146,,444,,,,,,,,,142,143',
',139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,',
',,,136,135,,120,141,138,137,133,134,128,126,118,140,119,443,434,146',
',444,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144',
'145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118',
'140,119,443,434,146,,444,,,,,,,,,142,143,,139,121,122,123,130,127,129',
',,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137,133',
'134,128,126,118,140,119,642,434,146,,643,,,,,,,,,142,143,,139,121,122',
'123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,,268,,,,,,,136,135',
',120,141,138,137,133,134,128,126,118,140,119,644,440,146,,645,,,,,,',
',,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132',
',,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119,686',
'434,146,,687,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125',
',,,,144,145,131,132,,,,,,268,,,,,,,136,135,,120,141,138,137,133,134',
'128,126,118,140,119,689,440,146,,690,,,,,,,,,142,143,,139,121,122,123',
'130,127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141',
'138,137,133,134,128,126,118,140,119,443,434,146,,444,,,,,,,,,142,143',
',139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,',
',,,136,135,,120,141,138,137,133,134,128,126,118,140,119,642,434,146',
',643,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144',
'145,131,132,,,,,,268,,,,,,,136,135,,120,141,138,137,133,134,128,126',
'118,140,119,644,440,146,,645,,,,,,,,,142,143,,139,121,122,123,130,127',
'129,,,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137',
'133,134,128,126,118,140,119,733,434,146,,734,,,,,,,,,142,143,,139,121',
'122,123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,,268,,,,,,,136',
'135,,120,141,138,137,133,134,128,126,118,140,119,735,440,146,,736,,',
',,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131',
'132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119',
'741,440,146,,739,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124',
'125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138,137,133,134',
'128,126,118,140,119,443,434,146,,444,,,,,,,,,142,143,,139,121,122,123',
'130,127,129,,,124,125,,,,,144,145,131,132,,,,,,268,,,,,,,136,135,,120',
'141,138,137,133,134,128,126,118,140,119,741,440,146,,892,,,,,,,,,142',
'143,,139,121,122,123,130,127,129,,,124,125,,,,,144,145,131,132,,,,,',
',,,,,,,136,135,,120,141,138,137,133,134,128,126,118,140,119,959,434',
'146,,960,,,,,,,,,142,143,,139,121,122,123,130,127,129,,,124,125,,,,',
'144,145,131,132,,,,,,268,,,,,,,136,135,,120,141,138,137,133,134,128',
'126,118,140,119,961,440,146,,962,,,,,,,,,142,143,,139,121,122,123,130',
'127,129,,,124,125,,,,,144,145,131,132,,,,,,,,,,,,,136,135,,120,141,138',
'137,133,134,128,126,118,140,119,,,146' ]
        racc_action_table = arr = ::Array.new(25031, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'0,0,0,0,0,347,321,354,0,0,392,746,746,0,71,0,0,0,0,0,0,0,71,313,852',
'615,313,0,0,0,0,0,0,0,1,852,0,387,26,209,387,485,0,0,0,0,0,0,348,0,0',
'0,0,0,55,0,0,0,497,0,0,357,0,0,587,681,366,370,852,852,733,310,310,392',
'370,485,734,352,838,352,839,604,604,735,889,0,615,615,0,209,896,0,497',
'6,0,615,0,287,387,387,0,746,681,924,26,852,746,9,0,927,948,321,354,0',
'0,0,0,0,0,347,959,347,0,0,347,26,736,17,17,17,0,17,960,0,507,17,17,10',
'0,0,17,55,17,17,17,17,17,17,17,211,735,457,457,11,17,17,17,17,17,17',
'17,310,348,17,348,287,357,348,507,587,17,604,366,17,17,733,17,17,17',
'17,17,734,17,17,17,839,17,17,735,17,17,366,838,736,838,838,366,838,889',
'211,889,371,287,889,896,12,896,457,371,896,961,287,17,20,962,17,17,924',
'17,924,290,17,924,927,948,927,948,17,927,948,967,736,293,383,959,17',
'959,34,577,959,17,17,17,17,17,17,960,525,960,17,17,960,686,578,18,18',
'18,17,18,687,17,314,18,18,314,17,17,18,453,18,18,18,18,18,18,18,698',
'698,508,15,15,18,18,18,18,18,18,18,290,961,18,629,36,962,383,383,383',
'18,293,461,18,18,41,18,18,18,18,18,77,18,18,18,508,18,18,525,18,18,453',
'293,686,841,37,37,961,290,961,687,962,961,962,461,698,962,290,461,461',
'689,577,18,577,629,18,577,967,18,967,807,18,967,78,690,525,578,18,578',
'642,686,578,97,3,525,18,193,687,3,686,18,18,18,18,18,18,687,210,278',
'18,18,212,14,278,22,22,22,18,22,385,18,317,22,22,317,18,18,22,643,22',
'22,22,22,22,22,22,791,689,689,642,792,22,22,22,22,22,22,22,807,213,22',
'690,690,14,841,389,841,22,394,841,22,22,14,22,22,22,22,22,22,22,22,22',
'372,22,22,689,22,22,643,372,385,385,385,351,689,807,791,219,351,690',
'792,251,25,25,807,860,373,374,690,22,25,252,22,373,374,22,860,934,22',
'934,22,255,22,267,22,280,389,389,389,394,394,394,22,83,83,13,13,22,22',
'22,22,22,22,13,282,703,22,22,703,860,860,23,23,23,22,23,597,22,375,23',
'23,597,22,22,23,375,23,23,23,23,23,23,23,283,361,296,296,284,23,23,23',
'23,23,23,23,744,860,23,42,42,744,13,648,13,23,289,42,23,23,291,23,23',
'23,23,23,23,23,23,23,292,23,23,295,23,23,301,648,648,648,648,648,648',
'648,648,648,648,648,361,361,648,648,376,750,648,648,361,23,750,376,23',
'361,302,23,305,42,23,42,23,648,23,648,23,648,648,648,648,648,648,648',
'23,648,361,208,208,23,23,23,23,23,23,208,312,35,23,23,315,648,316,24',
'24,24,23,24,361,23,361,24,24,318,23,23,24,326,24,24,24,24,24,24,24,5',
'5,5,5,5,24,24,24,24,24,24,24,377,35,24,288,288,300,208,377,208,24,35',
'288,24,24,327,24,24,24,24,24,24,24,24,24,329,24,24,330,24,24,342,484',
'484,484,484,484,484,484,484,484,484,484,303,303,484,484,300,110,484',
'484,303,24,110,110,24,300,345,24,353,288,24,288,24,484,24,484,24,484',
'484,484,484,484,484,484,24,484,356,355,355,24,24,24,24,24,24,355,358',
'430,24,24,362,484,484,27,27,27,24,27,303,24,303,27,27,388,24,24,27,391',
'27,27,27,27,27,27,27,279,279,279,279,279,27,27,27,27,27,27,27,410,430',
'27,364,364,416,355,419,355,27,430,364,27,27,422,27,27,27,27,27,27,27',
'27,27,424,27,27,428,27,27,429,419,419,419,419,419,419,419,419,419,419',
'419,589,589,419,419,346,346,419,419,589,27,437,431,27,27,463,27,464',
'364,27,364,27,419,27,419,27,419,419,419,419,419,419,419,27,419,561,561',
'379,27,27,27,27,27,27,379,720,720,27,27,628,419,465,419,431,466,27,858',
'589,27,589,499,858,431,27,27,30,30,30,30,30,855,855,858,30,30,949,949',
'502,30,503,30,30,30,30,30,30,30,509,513,514,628,517,30,30,30,30,30,30',
'30,628,519,30,526,858,858,858,858,30,30,462,30,30,30,529,30,30,30,30',
'30,538,30,30,30,539,30,30,540,30,30,553,19,19,19,19,19,19,19,19,19,19',
'19,462,858,19,19,462,462,19,19,564,30,568,573,30,579,580,30,619,626',
'30,632,30,19,637,19,30,19,19,19,19,19,19,19,30,19,640,711,711,30,30',
'30,30,30,30,711,646,647,30,30,655,19,657,31,31,31,30,31,669,30,673,31',
'31,676,30,30,31,683,31,31,31,31,31,31,31,555,685,688,691,692,31,31,31',
'31,31,31,31,693,696,31,840,840,697,711,699,711,31,704,840,31,31,705',
'31,31,31,31,31,708,31,31,31,710,31,31,712,652,713,652,652,652,714,652',
'555,555,555,555,717,768,768,739,739,768,768,768,722,728,730,739,31,732',
'737,31,739,741,31,742,840,31,840,31,753,757,758,760,652,761,939,762',
'764,767,774,939,652,652,652,652,31,31,31,31,31,31,939,652,795,31,31',
'796,799,801,32,32,32,31,32,739,31,739,32,32,804,31,31,32,805,32,32,32',
'32,32,32,32,824,939,939,939,939,32,32,32,32,32,32,32,925,925,32,680',
'680,680,680,680,925,32,480,806,32,32,811,32,32,32,32,32,814,32,32,32',
'815,32,32,600,939,600,600,600,600,600,830,824,824,824,824,833,843,846',
'480,600,847,848,480,480,480,480,870,32,871,882,32,892,925,32,925,569',
'32,569,569,569,893,569,600,600,898,899,907,909,912,913,600,600,600,600',
'914,32,32,32,32,32,32,915,926,936,32,32,944,953,955,32,956,958,32,,',
'32,,,,,32,32,38,38,38,481,38,,,600,38,38,,,,38,,38,38,38,38,38,38,38',
',,,,,38,38,38,38,38,38,38,,481,38,,,481,481,481,481,38,,,38,38,,38,38',
'38,38,38,,38,38,38,,38,38,,38,38,,249,249,249,249,249,249,249,249,249',
'249,249,,,249,249,,,249,249,,38,,,38,,,38,,,38,,,249,,249,38,249,249',
'249,249,249,249,249,38,249,,,,38,38,38,38,38,38,,,,38,38,,249,,39,39',
'39,38,39,,38,,39,39,,38,38,39,,39,39,39,39,39,39,39,,,,,,39,39,39,39',
'39,39,39,,829,39,829,829,829,,829,,39,,,39,39,,39,39,39,39,39,,39,39',
'39,,39,39,,39,39,,427,427,427,427,427,427,427,427,427,427,427,,,427',
'427,,,427,427,,39,,,39,,,39,,,39,,,427,,427,39,427,427,427,427,427,427',
'427,39,427,,,,39,39,39,39,39,39,,,,39,39,,427,,40,40,40,39,40,,39,,40',
'40,,39,39,40,,40,40,40,40,40,40,40,,,,,,40,40,40,40,40,40,40,,,40,,',
',,,,40,,,40,40,,40,40,40,40,40,,40,40,40,,40,40,,40,40,,446,446,446',
'446,446,446,446,446,446,446,446,,,446,446,,,446,446,,40,,,40,,,40,,',
'40,,,446,,446,40,446,446,446,446,446,446,446,40,446,,,,40,40,40,40,40',
'40,,,,40,40,,446,,52,52,52,40,52,,40,,52,52,,40,40,52,,52,52,52,52,52',
'52,52,,,,,,52,52,52,52,52,52,52,,,52,,,,,,,52,,,52,52,,52,52,52,52,52',
',52,52,52,,52,52,,52,52,,536,536,536,536,536,536,536,536,536,536,536',
',,536,536,,,536,536,,52,,,52,,,52,,,52,,,536,,536,52,536,536,536,536',
'536,536,536,52,536,,,,52,52,52,52,52,52,,,,52,52,,536,,53,53,53,52,53',
',52,,53,53,,52,52,53,,53,53,53,53,53,53,53,,,,,,53,53,53,53,53,53,53',
',,53,,,,,,,53,,,53,53,,53,53,53,53,53,53,53,53,53,,53,53,,53,53,,695',
'695,695,695,695,695,695,695,695,695,695,,,695,695,,,695,695,,53,,,53',
',,53,,,53,,53,695,,695,53,695,695,695,695,695,695,695,53,695,,,,53,53',
'53,53,53,53,,,,53,53,,695,,54,54,54,53,54,,53,,54,54,,53,53,54,,54,54',
'54,54,54,54,54,,,,,,54,54,54,54,54,54,54,,,54,,,,,,,54,,,54,54,,54,54',
'54,54,54,54,54,54,54,,54,54,,54,54,,773,773,773,773,773,773,773,773',
'773,773,773,,,773,773,,,773,773,,54,,,54,,,54,,,54,,,773,,773,54,773',
'773,773,773,773,773,773,54,773,,,,54,54,54,54,54,54,,,,54,54,,773,,57',
'57,57,54,57,,54,,57,57,,54,54,57,,57,57,57,57,57,57,57,,,,,,57,57,57',
'57,57,57,57,,,57,,,,,,,57,,,57,57,,57,57,57,57,57,,57,57,57,,57,57,',
'57,57,,779,779,779,779,779,779,779,779,779,779,779,,,779,779,,,779,779',
',57,,,57,,,57,,,57,,,779,,779,57,779,779,779,779,779,779,779,57,779',
',,,57,57,57,57,57,57,,,,57,57,,779,,58,58,58,57,58,,57,,58,58,,57,57',
'58,,58,58,58,58,58,58,58,,,,,,58,58,58,58,58,58,58,,,58,,,,,,,58,,,58',
'58,,58,58,58,58,58,,58,58,58,,58,58,,58,58,,781,781,781,781,781,781',
'781,781,781,781,781,,,781,781,,,781,781,,58,,,58,,,58,,,58,,,781,,781',
'58,781,781,781,781,781,781,781,58,781,,,,58,58,58,58,58,58,,,,58,58',
',781,,61,61,61,58,61,,58,,61,61,,58,58,61,,61,61,61,61,61,61,61,,,,',
',61,61,61,61,61,61,61,,,61,,,,,,,61,,,61,61,,61,61,61,61,61,,61,61,61',
',61,61,,61,61,,672,672,672,672,672,672,672,672,672,672,672,,,672,672',
',,672,672,,61,,,61,,,61,,,61,,,672,,672,61,672,672,672,672,672,672,672',
'61,672,,,,61,61,61,61,61,61,,,,61,61,61,672,,672,,61,61,,,61,,,,,61',
'61,62,62,62,,62,,,,62,62,,,,62,,62,62,62,62,62,62,62,,,,,,62,62,62,62',
'62,62,62,,,62,,,,,,,62,,,62,62,,62,62,62,62,62,,62,62,62,,62,62,784',
'784,784,784,784,784,784,784,784,784,784,467,,784,784,,,784,784,,,,,',
'62,,,62,467,467,62,,784,62,784,62,784,784,784,784,784,784,784,467,784',
'467,,467,467,467,467,,62,62,62,62,62,62,,,784,62,62,,,,63,63,63,62,63',
',62,,63,63,,62,62,63,,63,63,63,63,63,63,63,,,,,,63,63,63,63,63,63,63',
',,63,,,,,,,63,,,63,63,,63,63,63,63,63,,63,63,63,,63,63,786,786,786,786',
'786,786,786,786,786,786,786,468,,786,786,,,786,786,,,63,,,63,,,63,468',
'468,63,,786,63,786,,786,786,786,786,786,786,786,468,786,468,,468,468',
'468,468,,63,63,63,63,63,63,,,786,63,63,,,,84,84,84,63,84,,63,,84,84',
',63,63,84,,84,84,84,84,84,84,84,,84,,,,84,84,84,84,84,84,84,,,84,,,',
',,,84,,,84,84,,84,84,84,84,84,84,84,84,84,,84,84,,84,84,,793,793,793',
'793,793,793,793,793,793,793,793,,,793,793,,,793,793,,84,,,84,84,,84',
',,84,,84,793,84,793,84,793,793,793,793,793,793,793,84,793,84,,,84,84',
'84,84,84,84,,,,84,84,,793,,87,87,87,84,87,,84,,87,87,,84,84,87,,87,87',
'87,87,87,87,87,,87,,,,87,87,87,87,87,87,87,,,87,,,,,,,87,,,87,87,,87',
'87,87,87,87,87,87,87,87,,87,87,,87,87,,873,873,873,873,873,873,873,873',
'873,873,873,,,873,873,,,873,873,,87,,,87,87,,87,,,87,,87,873,87,873',
'87,873,873,873,873,873,873,873,87,873,87,,,87,87,87,87,87,87,,,,87,87',
',873,,,,,87,,,87,,,,,87,87,99,99,99,99,99,,,,99,99,,,,99,,99,99,99,99',
'99,99,99,,,,,,99,99,99,99,99,99,99,,,99,,,,,,99,99,99,99,99,99,,99,99',
'99,99,99,,99,99,99,,99,99,,99,99,,875,875,875,875,875,875,875,875,875',
'875,875,,,875,875,,,875,875,,99,,,99,,,99,,,99,,99,875,,875,99,875,875',
'875,875,875,875,875,99,875,,,,99,99,99,99,99,99,,,,99,99,,875,,,,99',
'99,,,99,,,,,99,99,103,103,103,,103,,,,103,103,,,,103,,103,103,103,103',
'103,103,103,,,,,,103,103,103,103,103,103,103,,338,103,338,338,338,,338',
',103,,,103,103,,103,103,103,103,103,,103,103,103,,103,103,,103,103,566',
',566,566,566,,566,,,718,338,718,718,718,,718,,,338,,,103,,,103,,,103',
',,103,,,,,,103,,,566,,,,,103,,,566,718,103,103,103,103,103,103,,718',
',103,103,,,,104,104,104,103,104,,103,,104,104,,103,103,104,,104,104',
'104,104,104,104,104,,,,,,104,104,104,104,104,104,104,,,104,,,,,,,104',
',,104,104,,104,104,104,104,104,,104,104,104,,104,104,,104,104,,459,459',
'459,459,459,459,459,459,459,459,459,,,459,459,,,459,459,,104,,,104,',
',104,,,104,,,459,,459,104,459,459,459,459,459,459,459,104,459,,,,104',
'104,104,104,104,104,,,,104,104,,,,105,105,105,104,105,,104,,105,105',
',104,104,105,,105,105,105,105,105,105,105,,,,,,105,105,105,105,105,105',
'105,,,105,,,,,,,105,,,105,105,,105,105,105,105,105,,105,105,105,,105',
'105,,105,105,,460,460,460,460,460,460,460,460,460,460,460,,,460,460',
',,460,460,,105,,,105,,,105,,,105,,,460,,460,105,460,460,460,460,460',
'460,460,105,460,,,,105,105,105,105,105,105,,,,105,105,,,,106,106,106',
'105,106,,105,,106,106,,105,105,106,,106,106,106,106,106,106,106,,,,',
',106,106,106,106,106,106,106,,,106,,,,,,,106,,,106,106,,106,106,106',
'106,106,,106,106,106,,106,106,,106,106,,470,470,470,470,470,470,470',
',,470,470,,,,,,,470,470,,106,,,106,,,106,,,106,,,470,,470,106,470,470',
'470,470,470,470,470,106,470,,,,106,106,106,106,106,106,,,,106,106,,',
',,,,106,,,106,,,,,106,106,107,107,107,107,107,,,,107,107,,,,107,,107',
'107,107,107,107,107,107,,,,,,107,107,107,107,107,107,107,,,107,,,,,',
'107,107,,107,107,107,,107,107,107,107,107,,107,107,107,,107,107,,107',
'107,,471,,,,,,,,,,,,,,,,,471,471,,107,,,107,,,107,,,107,,107,471,,471',
'107,471,471,471,471,,,471,107,471,,,,107,107,107,107,107,107,,,,107',
'107,,,,,,,107,,,107,,,,,107,107,108,108,108,108,108,,,,108,108,,,,108',
',108,108,108,108,108,108,108,,,,,,108,108,108,108,108,108,108,,,108',
',,,,,108,108,108,108,108,108,,108,108,108,108,108,,108,108,108,,108',
'108,,108,108,,472,,,,,,,,,,,,,,,,,472,472,,108,,,108,,,108,,,108,,108',
'472,,472,108,472,472,472,472,,,472,108,472,,,,108,108,108,108,108,108',
',,,108,108,,,,,,,108,,,108,,,,,108,108,195,195,195,195,195,,,,195,195',
',,,195,,195,195,195,195,195,195,195,,,,,,195,195,195,195,195,195,195',
',,195,,,,,,195,195,,195,195,195,,195,195,195,195,195,,195,195,195,,195',
'195,,195,195,,473,,,,,,,,,,,,,,,,,473,473,,195,,,195,,,195,,,195,,195',
'473,,473,195,473,473,473,473,,,473,195,473,,,,195,195,195,195,195,195',
',,,195,195,,,,196,196,196,195,196,,195,,196,196,,195,195,196,,196,196',
'196,196,196,196,196,,,,,,196,196,196,196,196,196,196,,,196,,,,,,,196',
',,196,196,,196,196,196,196,196,,196,196,196,,196,196,,196,196,,474,',
',,,,,,,,,,,,,,,474,474,,196,,,196,,,196,,,196,,196,474,,474,196,474',
'474,474,474,,,474,196,474,,,,196,196,196,196,196,196,,,,196,196,,,,197',
'197,197,196,197,,196,,197,197,,196,196,197,,197,197,197,197,197,197',
'197,,,,,,197,197,197,197,197,197,197,,,197,,,,,,,197,,,197,197,,197',
'197,197,197,197,,197,197,197,,197,197,,197,197,,475,475,475,475,475',
'475,475,,,475,475,,,,,,,475,475,,197,,,197,,,197,,,197,,197,475,,475',
'197,475,475,475,475,475,475,475,197,475,,,,197,197,197,197,197,197,',
',,197,197,,,,198,198,198,197,198,,197,,198,198,,197,197,198,,198,198',
'198,198,198,198,198,,,,,,198,198,198,198,198,198,198,,,198,,,,,,,198',
',,198,198,,198,198,198,198,198,,198,198,198,,198,198,,198,198,,476,476',
'476,476,476,476,476,,,476,476,,,,,,,476,476,,198,,,198,,,198,,,198,',
',476,,476,198,476,476,476,476,476,476,476,198,476,,,,198,198,198,198',
'198,198,,,,198,198,,,,199,199,199,198,199,,198,,199,199,,198,198,199',
',199,199,199,199,199,199,199,,,,,,199,199,199,199,199,199,199,,,199',
',,,,,,199,,,199,199,,199,199,199,199,199,199,199,199,199,,199,199,,199',
'199,,477,477,477,477,477,477,477,,,477,477,,,,,,,477,477,,199,,,199',
',,199,,,199,,199,477,,477,199,477,477,477,477,477,477,477,199,477,,',
',199,199,199,199,199,199,,,,199,199,,,,200,200,200,199,200,,199,,200',
'200,,199,199,200,,200,200,200,200,200,200,200,,,,,,200,200,200,200,200',
'200,200,,,200,,,,,,,200,,,200,200,,200,200,200,200,200,200,200,200,200',
',200,200,,200,200,,478,478,478,478,478,478,478,,,478,478,,,,,,,478,478',
',200,,,200,,,200,,,200,,200,478,,478,200,478,478,478,478,478,478,478',
'200,478,,,,200,200,200,200,200,200,,,,200,200,,,,204,204,204,200,204',
',200,,204,204,,200,200,204,,204,204,204,204,204,204,204,,,,,,204,204',
'204,204,204,204,204,,,204,,,,,,,204,,,204,204,,204,204,204,204,204,',
'204,204,204,,204,204,,204,204,,479,479,479,479,479,479,479,,,479,479',
',,,,,,479,479,,204,,,204,,,204,,,204,,,479,,479,204,479,479,479,479',
'479,479,479,204,479,,,,204,204,204,204,204,204,,,,204,204,,,,205,205',
'205,204,205,,204,,205,205,,204,204,205,,205,205,205,205,205,205,205',
',,,,,205,205,205,205,205,205,205,,,205,,,,,,,205,,,205,205,,205,205',
'205,205,205,,205,205,205,,205,205,,205,205,,482,482,482,482,482,482',
'482,,,482,482,,,,,,,482,482,,205,,,205,,,205,,,205,,,482,,482,205,482',
'482,482,482,482,482,482,205,482,,,,205,205,205,205,205,205,,,,205,205',
',,,206,206,206,205,206,,205,,206,206,,205,205,206,,206,206,206,206,206',
'206,206,,,,,,206,206,206,206,206,206,206,,,206,,,,,,,206,,,206,206,',
'206,206,206,206,206,,206,206,206,,206,206,,206,206,,483,483,483,483',
'483,483,483,483,,483,483,,,,,,,483,483,,206,,,206,,,206,,,206,,,483',
',483,206,483,483,483,483,483,483,483,206,483,,,,206,206,206,206,206',
'206,,,,206,206,,,,,,,206,,,206,,,,,206,206,214,214,214,214,214,,,,214',
'214,,,,214,,214,214,214,214,214,214,214,,,,,,214,214,214,214,214,214',
'214,,,214,,,,,,214,214,,214,214,214,,214,214,214,214,214,,214,214,214',
',214,214,,214,214,,469,,,,,,,,,,,,,,,,,469,469,,214,,,214,,,214,,,214',
',214,469,,,214,469,469,469,469,,,,214,,,,,214,214,214,214,214,214,,',
',214,214,,,,215,215,215,214,215,,214,,215,215,,214,214,215,,215,215',
'215,215,215,215,215,,,,,,215,215,215,215,215,215,215,,,215,,,,,,,215',
',,215,215,,215,215,215,215,215,,215,215,215,,215,215,,215,215,,,,,,',
',,,,,,,,,,,,,,,215,,,215,,215,215,,,215,,,,,,215,,,,,,,,215,,,,,215',
'215,215,215,215,215,,,,215,215,,,,218,218,218,215,218,,215,,218,218',
',215,215,218,,218,218,218,218,218,218,218,,,,,,218,218,218,218,218,218',
'218,,,218,,,,,,,218,,,218,218,,218,218,218,218,218,,218,218,218,,218',
'218,,218,218,,,,,,,,,,,,,,,,,,,,,,218,,,218,,,218,,,218,,,,,,218,,,',
',,,,218,,,,,218,218,218,218,218,218,,,,218,218,,,,220,220,220,218,220',
',218,,220,220,,218,218,220,,220,220,220,220,220,220,220,,,,,,220,220',
'220,220,220,220,220,,,220,,,,,,,220,,,220,220,,220,220,220,220,220,',
'220,220,220,,220,220,,220,220,,,,,,,,,,,,,,,,,,,,,,220,,,220,,,220,',
',220,,,,,,220,,,,,,,,220,,,,,220,220,220,220,220,220,,,,220,220,,,,221',
'221,221,220,221,,220,,221,221,,220,220,221,,221,221,221,221,221,221',
'221,,,,,,221,221,221,221,221,221,221,,,221,,,,,,,221,,,221,221,,221',
'221,221,221,221,,221,221,221,,221,221,,221,221,,,,,,,,,,,,,,,,,,,,,',
'221,,,221,,,221,,,221,,,,,,221,,,,,,,,221,,,,,221,221,221,221,221,221',
',,,221,221,,,,222,222,222,221,222,,221,,222,222,,221,221,222,,222,222',
'222,222,222,222,222,,,,,,222,222,222,222,222,222,222,,,222,,,,,,,222',
',,222,222,,222,222,222,222,222,,222,222,222,,222,222,,222,222,,,,,,',
',,,,,,,,,,,,,,,222,,,222,,,222,,,222,,,,,,222,,,,,,,,222,,,,,222,222',
'222,222,222,222,,,,222,222,,,,223,223,223,222,223,,222,,223,223,,222',
'222,223,,223,223,223,223,223,223,223,,,,,,223,223,223,223,223,223,223',
',,223,,,,,,,223,,,223,223,,223,223,223,223,223,,223,223,223,,223,223',
',223,223,,,,,,,,,,,,,,,,,,,,,,223,,,223,,,223,,,223,,,,,,223,,,,,,,',
'223,,,,,223,223,223,223,223,223,,,,223,223,,,,224,224,224,223,224,,223',
',224,224,,223,223,224,,224,224,224,224,224,224,224,,,,,,224,224,224',
'224,224,224,224,,,224,,,,,,,224,,,224,224,,224,224,224,224,224,,224',
'224,224,,224,224,,224,224,,,,,,,,,,,,,,,,,,,,,,224,,,224,,,224,,,224',
',,,,,224,,,,,,,,224,,,,,224,224,224,224,224,224,,,,224,224,,,,225,225',
'225,224,225,,224,,225,225,,224,224,225,,225,225,225,225,225,225,225',
',,,,,225,225,225,225,225,225,225,,,225,,,,,,,225,,,225,225,,225,225',
'225,225,225,,225,225,225,,225,225,,225,225,,,,,,,,,,,,,,,,,,,,,,225',
',,225,,,225,,,225,,,,,,225,,,,,,,,225,,,,,225,225,225,225,225,225,,',
',225,225,,,,226,226,226,225,226,,225,,226,226,,225,225,226,,226,226',
'226,226,226,226,226,,,,,,226,226,226,226,226,226,226,,,226,,,,,,,226',
',,226,226,,226,226,226,226,226,,226,226,226,,226,226,,226,226,,,,,,',
',,,,,,,,,,,,,,,226,,,226,,,226,,,226,,,,,,226,,,,,,,,226,,,,,226,226',
'226,226,226,226,,,,226,226,,,,227,227,227,226,227,,226,,227,227,,226',
'226,227,,227,227,227,227,227,227,227,,,,,,227,227,227,227,227,227,227',
',,227,,,,,,,227,,,227,227,,227,227,227,227,227,,227,227,227,,227,227',
',227,227,,,,,,,,,,,,,,,,,,,,,,227,,,227,,,227,,,227,,,,,,227,,,,,,,',
'227,,,,,227,227,227,227,227,227,,,,227,227,,,,228,228,228,227,228,,227',
',228,228,,227,227,228,,228,228,228,228,228,228,228,,,,,,228,228,228',
'228,228,228,228,,,228,,,,,,,228,,,228,228,,228,228,228,228,228,,228',
'228,228,,228,228,,228,228,,,,,,,,,,,,,,,,,,,,,,228,,,228,,,228,,,228',
',,,,,228,,,,,,,,228,,,,,228,228,228,228,228,228,,,,228,228,,,,229,229',
'229,228,229,,228,,229,229,,228,228,229,,229,229,229,229,229,229,229',
',,,,,229,229,229,229,229,229,229,,,229,,,,,,,229,,,229,229,,229,229',
'229,229,229,,229,229,229,,229,229,,229,229,,,,,,,,,,,,,,,,,,,,,,229',
',,229,,,229,,,229,,,,,,229,,,,,,,,229,,,,,229,229,229,229,229,229,,',
',229,229,,,,230,230,230,229,230,,229,,230,230,,229,229,230,,230,230',
'230,230,230,230,230,,,,,,230,230,230,230,230,230,230,,,230,,,,,,,230',
',,230,230,,230,230,230,230,230,,230,230,230,,230,230,,230,230,,,,,,',
',,,,,,,,,,,,,,,230,,,230,,,230,,,230,,,,,,230,,,,,,,,230,,,,,230,230',
'230,230,230,230,,,,230,230,,,,231,231,231,230,231,,230,,231,231,,230',
'230,231,,231,231,231,231,231,231,231,,,,,,231,231,231,231,231,231,231',
',,231,,,,,,,231,,,231,231,,231,231,231,231,231,,231,231,231,,231,231',
',231,231,,,,,,,,,,,,,,,,,,,,,,231,,,231,,,231,,,231,,,,,,231,,,,,,,',
'231,,,,,231,231,231,231,231,231,,,,231,231,,,,232,232,232,231,232,,231',
',232,232,,231,231,232,,232,232,232,232,232,232,232,,,,,,232,232,232',
'232,232,232,232,,,232,,,,,,,232,,,232,232,,232,232,232,232,232,,232',
'232,232,,232,232,,232,232,,,,,,,,,,,,,,,,,,,,,,232,,,232,,,232,,,232',
',,,,,232,,,,,,,,232,,,,,232,232,232,232,232,232,,,,232,232,,,,233,233',
'233,232,233,,232,,233,233,,232,232,233,,233,233,233,233,233,233,233',
',,,,,233,233,233,233,233,233,233,,,233,,,,,,,233,,,233,233,,233,233',
'233,233,233,,233,233,233,,233,233,,233,233,,,,,,,,,,,,,,,,,,,,,,233',
',,233,,,233,,,233,,,,,,233,,,,,,,,233,,,,,233,233,233,233,233,233,,',
',233,233,,,,234,234,234,233,234,,233,,234,234,,233,233,234,,234,234',
'234,234,234,234,234,,,,,,234,234,234,234,234,234,234,,,234,,,,,,,234',
',,234,234,,234,234,234,234,234,,234,234,234,,234,234,,234,234,,,,,,',
',,,,,,,,,,,,,,,234,,,234,,,234,,,234,,,,,,234,,,,,,,,234,,,,,234,234',
'234,234,234,234,,,,234,234,,,,235,235,235,234,235,,234,,235,235,,234',
'234,235,,235,235,235,235,235,235,235,,,,,,235,235,235,235,235,235,235',
',,235,,,,,,,235,,,235,235,,235,235,235,235,235,,235,235,235,,235,235',
',235,235,,,,,,,,,,,,,,,,,,,,,,235,,,235,,,235,,,235,,,,,,235,,,,,,,',
'235,,,,,235,235,235,235,235,235,,,,235,235,,,,236,236,236,235,236,,235',
',236,236,,235,235,236,,236,236,236,236,236,236,236,,,,,,236,236,236',
'236,236,236,236,,,236,,,,,,,236,,,236,236,,236,236,236,236,236,,236',
'236,236,,236,236,,236,236,,,,,,,,,,,,,,,,,,,,,,236,,,236,,,236,,,236',
',,,,,236,,,,,,,,236,,,,,236,236,236,236,236,236,,,,236,236,,,,237,237',
'237,236,237,,236,,237,237,,236,236,237,,237,237,237,237,237,237,237',
',,,,,237,237,237,237,237,237,237,,,237,,,,,,,237,,,237,237,,237,237',
'237,237,237,,237,237,237,,237,237,,237,237,,,,,,,,,,,,,,,,,,,,,,237',
',,237,,,237,,,237,,,,,,237,,,,,,,,237,,,,,237,237,237,237,237,237,,',
',237,237,,,,238,238,238,237,238,,237,,238,238,,237,237,238,,238,238',
'238,238,238,238,238,,,,,,238,238,238,238,238,238,238,,,238,,,,,,,238',
',,238,238,,238,238,238,238,238,,238,238,238,,238,238,,238,238,,,,,,',
',,,,,,,,,,,,,,,238,,,238,,,238,,,238,,,,,,238,,,,,,,,238,,,,,238,238',
'238,238,238,238,,,,238,238,,,,239,239,239,238,239,,238,,239,239,,238',
'238,239,,239,239,239,239,239,239,239,,,,,,239,239,239,239,239,239,239',
',,239,,,,,,,239,,,239,239,,239,239,239,239,239,,239,239,239,,239,239',
',239,239,,,,,,,,,,,,,,,,,,,,,,239,,,239,,,239,,,239,,,,,,239,,,,,,,',
'239,,,,,239,239,239,239,239,239,,,,239,239,,,,240,240,240,239,240,,239',
',240,240,,239,239,240,,240,240,240,240,240,240,240,,,,,,240,240,240',
'240,240,240,240,,,240,,,,,,,240,,,240,240,,240,240,240,240,240,,240',
'240,240,,240,240,,240,240,,,,,,,,,,,,,,,,,,,,,,240,,,240,,,240,,,240',
',,,,,240,,,,,,,,240,,,,,240,240,240,240,240,240,,,,240,240,,,,241,241',
'241,240,241,,240,,241,241,,240,240,241,,241,241,241,241,241,241,241',
',,,,,241,241,241,241,241,241,241,,,241,,,,,,,241,,,241,241,,241,241',
'241,241,241,,241,241,241,,241,241,,241,241,,,,,,,,,,,,,,,,,,,,,,241',
',,241,,,241,,,241,,,,,,241,,,,,,,,241,,,,,241,241,241,241,241,241,,',
',241,241,,,,242,242,242,241,242,,241,,242,242,,241,241,242,,242,242',
'242,242,242,242,242,,,,,,242,242,242,242,242,242,242,,,242,,,,,,,242',
',,242,242,,242,242,242,242,242,,242,242,242,,242,242,,242,242,,,,,,',
',,,,,,,,,,,,,,,242,,,242,,,242,,,242,,,,,,242,,,,,,,,242,,,,,242,242',
'242,242,242,242,,,,242,242,,,,243,243,243,242,243,,242,,243,243,,242',
'242,243,,243,243,243,243,243,243,243,,,,,,243,243,243,243,243,243,243',
',,243,,,,,,,243,,,243,243,,243,243,243,243,243,,243,243,243,,243,243',
',243,243,,,,,,,,,,,,,,,,,,,,,,243,,,243,,,243,,,243,,,,,,243,,,,,,,',
'243,,,,,243,243,243,243,243,243,,,,243,243,,,,244,244,244,243,244,,243',
',244,244,,243,243,244,,244,244,244,244,244,244,244,,,,,,244,244,244',
'244,244,244,244,,,244,,,,,,,244,,,244,244,,244,244,244,244,244,,244',
'244,244,,244,244,,244,244,,,,,,,,,,,,,,,,,,,,,,244,,,244,,,244,,,244',
',,,,,244,,,,,,,,244,,,,,244,244,244,244,244,244,,,,244,244,,,,245,245',
'245,244,245,,244,,245,245,,244,244,245,,245,245,245,245,245,245,245',
',,,,,245,245,245,245,245,245,245,,,245,,,,,,,245,,,245,245,,245,245',
'245,245,245,,245,245,245,,245,245,,245,245,,,,,,,,,,,,,,,,,,,,,,245',
',,245,,,245,,,245,,,,,,245,,,,,,,,245,,,,,245,245,245,245,245,245,,',
',245,245,,,,254,254,254,245,254,,245,,254,254,,245,245,254,,254,254',
'254,254,254,254,254,,,,,,254,254,254,254,254,254,254,,,254,,,,,,,254',
',,254,254,,254,254,254,254,254,,254,254,254,,254,254,,254,254,,,,,,',
',,,,,,,,,,,,,,,254,,,254,,,254,,,254,,,,,,254,,,,,,,,254,,,,,254,254',
'254,254,254,254,,,,254,254,,,,256,256,256,254,256,,254,,256,256,,254',
'254,256,,256,256,256,256,256,256,256,,,,,,256,256,256,256,256,256,256',
',,256,,,,,,,256,,,256,256,,256,256,256,256,256,,256,256,256,,256,256',
',256,256,,,,,,,,,,,,,,,,,,,,,,256,,,256,,,256,,,256,,,,,,256,,,,,,,',
'256,,,,,256,256,256,256,256,256,,,,256,256,,,,261,261,261,256,261,,256',
',261,261,,256,256,261,,261,261,261,261,261,261,261,,,,,,261,261,261',
'261,261,261,261,,,261,,,,,,,261,,,261,261,,261,261,261,261,261,,261',
'261,261,,261,261,,261,261,,,,,,,,,,,,,,,,,,,,,,261,,,261,,,261,,,261',
',,,,,261,,,,,,,,261,,,,,261,261,261,261,261,261,,,,261,261,,,,268,268',
'268,261,268,,261,,268,268,,261,261,268,,268,268,268,268,268,268,268',
',,,,,268,268,268,268,268,268,268,,,268,,,,,,,268,,,268,268,,268,268',
'268,268,268,268,268,268,268,,268,268,,268,268,,,,,,,,,,,,,,,,,,,,,,268',
',,268,,,268,,,268,,268,,268,,268,,,,,,,,268,,,,,268,268,268,268,268',
'268,,,,268,268,,,,269,269,269,268,269,,268,,269,269,,268,268,269,,269',
'269,269,269,269,269,269,,,,,,269,269,269,269,269,269,269,,,269,,,,,',
',269,,,269,269,,269,269,269,269,269,269,269,269,269,,269,269,,269,269',
',,,,,,,,,,,,,,,,,,,,,269,,,269,,,269,,,269,,269,,269,,269,,,,,,,,269',
',,,,269,269,269,269,269,269,,,,269,269,,,,277,277,277,269,277,,269,',
'277,277,,269,269,277,,277,277,277,277,277,277,277,,,,,,277,277,277,277',
'277,277,277,,,277,,,,,,,277,,,277,277,,277,277,277,277,277,277,277,277',
'277,,277,277,,277,277,,,,,,,,,,,,,,,,,,,,,,277,,,277,,277,277,,,277',
',277,,277,,277,,,,,,,,277,,,,,277,277,277,277,277,277,,,,277,277,,,',
',,,277,,,277,,,,,277,277,281,281,281,281,281,,,,281,281,,,,281,,281',
'281,281,281,281,281,281,,,,,,281,281,281,281,281,281,281,,,281,,,,,',
'281,281,,281,281,281,,281,281,281,281,281,,281,281,281,,281,281,,281',
'281,,,,,,,,,,,,,,,,,,,,,,281,,,281,,,281,,,281,,281,,,,281,,,,,,,,281',
',,,,281,281,281,281,281,281,,,,281,281,,,,285,285,285,281,285,,281,',
'285,285,,281,281,285,,285,285,285,285,285,285,285,,,,,,285,285,285,285',
'285,285,285,,,285,,,,,,,285,,,285,285,,285,285,285,285,285,,285,285',
'285,,285,285,749,,749,749,749,749,749,,,,,,,,,,749,,,,,,,,285,,,285',
',,285,,,285,,,,,,749,,,,,,,,749,749,749,749,,285,285,285,285,285,285',
',,,285,285,,,,285,,,285,,,285,,,,,285,285,286,286,286,286,286,,,749',
'286,286,,,,286,,286,286,286,286,286,286,286,,,,,,286,286,286,286,286',
'286,286,,,286,,,,,,286,286,,286,286,286,,286,286,286,286,286,,286,286',
'286,,286,286,,286,286,,,,,,,,,,,,,,,,,,,,,,286,,,286,,,286,,,286,,286',
',,,286,,,,,,,,286,,,,,286,286,286,286,286,286,,,,286,286,,,,298,298',
'298,286,298,,286,,298,298,,286,286,298,,298,298,298,298,298,298,298',
',,,,,298,298,298,298,298,298,298,,,298,,,,,,,298,,,298,298,,298,298',
'298,298,298,,298,298,298,,298,298,56,,56,56,56,,56,,,,,,,,,,,,,,,,,',
'298,,,298,,,298,,,298,,,,,,56,56,,,,,,,56,56,56,56,,298,298,298,298',
'298,298,,,,298,298,,,,307,307,307,298,307,,298,,307,307,,298,298,307',
',307,307,307,307,307,307,307,,,,,,307,307,307,307,307,307,307,,,307',
',,,,,,307,,,307,307,,307,307,307,307,307,,307,307,307,,307,307,,307',
'307,,,,,,,,,,,,,,,,,,,,,,307,,,307,307,,307,,,307,,,,,,307,,,,,,,,307',
',,,,307,307,307,307,307,307,,,,307,307,,,,,,,307,,,307,,,,,307,307,309',
'309,309,309,309,,,,309,309,,,,309,,309,309,309,309,309,309,309,,,,,',
'309,309,309,309,309,309,309,,,309,,,,,,309,309,,309,309,309,,309,309',
'309,309,309,,309,309,309,,309,309,,309,309,,,,,,,,,,,,,,,,,,,,,,309',
',,309,,,309,,,309,,309,,,,309,,,,,,,,309,,,,,309,309,309,309,309,309',
',,,309,309,,,,349,349,349,309,349,,309,,349,349,,309,309,349,,349,349',
'349,349,349,349,349,,,,,,349,349,349,349,349,349,349,,,349,,,,,,,349',
',,349,349,,349,349,349,349,349,,349,349,349,,349,349,,349,349,,,,,,',
',,,,,,,,,,,,,,,349,,,349,,,349,,,349,,,,,,349,,,,,,,,349,,,,,349,349',
'349,349,349,349,,,,349,349,,,,350,350,350,349,350,,349,,350,350,,349',
'349,350,,350,350,350,350,350,350,350,,,,,,350,350,350,350,350,350,350',
',,350,,,,,,,350,,,350,350,,350,350,350,350,350,,350,350,350,,350,350',
',350,350,,,,,,,,,,,,,,,,,,,,,,350,,,350,,,350,,,350,,,,,,350,,,,,,,',
'350,,,,,350,350,350,350,350,350,,,,350,350,,,,369,369,369,350,369,,350',
',369,369,,350,350,369,,369,369,369,369,369,369,369,,,,,,369,369,369',
'369,369,369,369,,,369,,,,,,,369,,,369,369,,369,369,369,369,369,,369',
'369,369,,369,369,322,,322,322,322,,322,,,,,,,,,,,,,,,,,,369,,,369,,',
'369,,,369,,,,,,322,,322,,,,,,322,322,322,322,,369,369,369,369,369,369',
',,,369,369,,,,381,381,381,369,381,,369,,381,381,,369,369,381,,381,381',
'381,381,381,381,381,,,,,,381,381,381,381,381,381,381,,,381,,,,,,,381',
',,381,381,,381,381,381,381,381,,381,381,381,,381,381,,381,381,,,,,,',
',,,,,,,,,,,,,,,381,,,381,,,381,,,381,,,,,,381,,,,,,,,381,,,,,381,381',
'381,381,381,381,,,,381,381,,,,421,421,421,381,421,,381,,421,421,,381',
'381,421,,421,421,421,421,421,421,421,,,,,,421,421,421,421,421,421,421',
',,421,,,,,,,421,,,421,421,,421,421,421,421,421,,421,421,421,,421,421',
',421,421,,,,,,,,,,,,,,,,,,,,,,421,,,421,,,421,,,421,,,,,,421,,,,,,,',
'421,,,,,421,421,421,421,421,421,,,,421,421,,,,432,432,432,421,432,,421',
',432,432,,421,421,432,,432,432,432,432,432,432,432,,,,,,432,432,432',
'432,432,432,432,,,432,,,,,,,432,,,432,432,,432,432,432,432,432,432,432',
'432,432,,432,432,,432,432,,,,,,,,,,,,,,,,,,,,,,432,,,432,432,,432,,',
'432,,432,,432,,432,,,,,,,,432,,,,,432,432,432,432,432,432,,,,432,432',
',,,440,440,440,432,440,,432,,440,440,,432,432,440,,440,440,440,440,440',
'440,440,,,,,,440,440,440,440,440,440,440,,,440,,,,,,,440,,,440,440,',
'440,440,440,440,440,440,440,440,440,,440,440,,440,440,,,,,,,,,,,,,,',
',,,,,,,440,,,440,440,,440,,,440,,440,,440,,440,,,,,,,,440,,,,,440,440',
'440,440,440,440,,,,440,440,,,,441,441,441,440,441,,440,,441,441,,440',
'440,441,,441,441,441,441,441,441,441,,,,,,441,441,441,441,441,441,441',
',,441,,,,,,,441,,,441,441,,441,441,441,441,441,441,441,441,441,,441',
'441,,441,441,,,,,,,,,,,,,,,,,,,,,,441,,,441,441,,441,,,441,,441,,441',
',441,,,,,,,,441,,,,,441,441,441,441,441,441,,,,441,441,,,,442,442,442',
'441,442,,441,,442,442,,441,441,442,,442,442,442,442,442,442,442,,,,',
',442,442,442,442,442,442,442,,,442,,,,,,,442,,,442,442,,442,442,442',
'442,442,442,442,442,442,,442,442,,442,442,,,,,,,,,,,,,,,,,,,,,,442,',
',442,442,,442,,,442,,442,,442,,442,,,,,,,,442,,,,,442,442,442,442,442',
'442,,,,442,442,,,,451,451,451,442,451,,442,,451,451,,442,442,451,,451',
'451,451,451,451,451,451,,,,,,451,451,451,451,451,451,451,,,451,,,,,',
',451,,,451,451,,451,451,451,451,451,451,451,451,451,,451,451,,451,451',
',,,,,,,,,,,,,,,,,,,,,451,,,451,,,451,,,451,,451,,,,451,,,,,,,,451,,',
',,451,451,451,451,451,451,,,,451,451,,,,452,452,452,451,452,,451,,452',
'452,,451,451,452,,452,452,452,452,452,452,452,,,,,,452,452,452,452,452',
'452,452,,,452,,,,,,,452,,,452,452,,452,452,452,452,452,452,452,452,452',
',452,452,,452,452,,,,,,,,,,,,,,,,,,,,,,452,,,452,,,452,,,452,,452,,',
',452,,,,,,,,452,,,,,452,452,452,452,452,452,,,,452,452,,,,454,454,454',
'452,454,,452,,454,454,,452,452,454,,454,454,454,454,454,454,454,,,,',
',454,454,454,454,454,454,454,,,454,,,,,,,454,,,454,454,,454,454,454',
'454,454,,454,454,454,,454,454,,454,454,,,,,,,,,,,,,,,,,,,,,,454,,,454',
',,454,,,454,,,,,,454,,,,,,,,454,,,,,454,454,454,454,454,454,,,,454,454',
',,,455,455,455,454,455,,454,,455,455,,454,454,455,,455,455,455,455,455',
'455,455,,,,,,455,455,455,455,455,455,455,,,455,,,,,,,455,,,455,455,',
'455,455,455,455,455,,455,455,455,,455,455,,455,455,,,,,,,,,,,,,,,,,',
',,,,455,,,455,,,455,,,455,,,,,,455,,,,,,,,455,,,,,455,455,455,455,455',
'455,,,,455,455,,,,456,456,456,455,456,,455,,456,456,,455,455,456,,456',
'456,456,456,456,456,456,,,,,,456,456,456,456,456,456,456,,,456,,,,,',
',456,,,456,456,,456,456,456,456,456,,456,456,456,,456,456,,456,456,',
',,,,,,,,,,,,,,,,,,,,456,,,456,,,456,,,456,,,,,,456,,,,,,,,456,,,,,456',
'456,456,456,456,456,,,,456,456,,,,487,487,487,456,487,,456,,487,487',
',456,456,487,,487,487,487,487,487,487,487,,,,,,487,487,487,487,487,487',
'487,,,487,,,,,,,487,,,487,487,,487,487,487,487,487,487,487,487,487,',
'487,487,,487,487,,,,,,,,,,,,,,,,,,,,,,487,,,487,,,487,,,487,,487,,487',
',487,,,,,,,,487,,,,,487,487,487,487,487,487,,,,487,487,,,,489,489,489',
'487,489,,487,,489,489,,487,487,489,,489,489,489,489,489,489,489,,,,',
',489,489,489,489,489,489,489,,,489,,,,,,,489,,,489,489,,489,489,489',
'489,489,489,489,489,489,,489,489,,489,489,,,,,,,,,,,,,,,,,,,,,,489,',
',489,,,489,,,489,,,,489,,489,,,,,,,,489,,,,,489,489,489,489,489,489',
',,,489,489,,,,491,491,491,489,491,,489,,491,491,,489,489,491,,491,491',
'491,491,491,491,491,,,,,,491,491,491,491,491,491,491,,,491,,,,,,,491',
',,491,491,,491,491,491,491,491,,491,491,491,,491,491,,491,491,,,,,,',
',,,,,,,,,,,,,,,491,,,491,,,491,,,491,,,,,,491,,,,,,,,491,,,,,491,491',
'491,491,491,491,,,,491,491,,,,506,506,506,491,506,,491,,506,506,,491',
'491,506,,506,506,506,506,506,506,506,,,,,,506,506,506,506,506,506,506',
',,506,,,,,,,506,,,506,506,,506,506,506,506,506,,506,506,506,,506,506',
',506,506,,,,,,,,,,,,,,,,,,,,,,506,,,506,,506,506,,,506,,,,506,,506,',
',,,,,,506,,,,,506,506,506,506,506,506,,,,506,506,,,,,,,506,,,506,,,',
',506,506,512,512,512,512,512,,,,512,512,,,,512,,512,512,512,512,512',
'512,512,,,,,,512,512,512,512,512,512,512,,,512,,,,,,512,512,,512,512',
'512,,512,512,512,512,512,,512,512,512,,512,512,,512,512,,,,,,,,,,,,',
',,,,,,,,,512,,,512,,,512,,,512,,512,,,,512,,,,,,,,512,,,,,512,512,512',
'512,512,512,,,,512,512,,,,,,512,512,,,512,,,,,512,512,518,518,518,,518',
',,,518,518,,,,518,,518,518,518,518,518,518,518,,,,,,518,518,518,518',
'518,518,518,,,518,,,,,,,518,,,518,518,,518,518,518,518,518,,518,518',
'518,,518,518,559,,559,559,559,,559,,,,,,,,,,,,,,,,,,518,,,518,,,518',
',,518,,,,,,559,,,,,,,,559,559,559,559,,518,518,518,518,518,518,,,,518',
'518,,,,520,520,520,518,520,,518,,520,520,,518,518,520,,520,520,520,520',
'520,520,520,,,,,,520,520,520,520,520,520,520,,,520,,,,,,,520,,,520,520',
',520,520,520,520,520,520,520,520,520,,520,520,,520,520,,,,,,,,,,,,,',
',,,,,,,,520,,,520,,,520,,,520,,520,,,,520,,,,,,,,520,,,,,520,520,520',
'520,520,520,,,,520,520,,,,523,523,523,520,523,,520,,523,523,,520,520',
'523,,523,523,523,523,523,523,523,,,,,,523,523,523,523,523,523,523,,',
'523,,,,,,,523,,,523,523,,523,523,523,523,523,523,523,523,523,,523,523',
',523,523,,,,,,,,,,,,,,,,,,,,,,523,,,523,,,523,,,523,,523,,,,523,,,,',
',,,523,,,,,523,523,523,523,523,523,,,,523,523,,,,531,531,531,523,531',
',523,,531,531,,523,523,531,,531,531,531,531,531,531,531,,,,,,531,531',
'531,531,531,531,531,,,531,,,,,,,531,,,531,531,,531,531,531,531,531,',
'531,531,531,,531,531,,531,531,,,,,,,,,,,,,,,,,,,,,,531,,,531,,,531,',
',531,,,,,,531,,,,,,,,531,,,,,531,531,531,531,531,531,,,,531,531,,,,532',
'532,532,531,532,,531,,532,532,,531,531,532,,532,532,532,532,532,532',
'532,,,,,,532,532,532,532,532,532,532,,,532,,,,,,,532,,,532,532,,532',
'532,532,532,532,,532,532,532,,532,532,,532,532,,,,,,,,,,,,,,,,,,,,,',
'532,,,532,,,532,,,532,,,,,,532,,,,,,,,532,,,,,532,532,532,532,532,532',
',,,532,532,,,,533,533,533,532,533,,532,,533,533,,532,532,533,,533,533',
'533,533,533,533,533,,,,,,533,533,533,533,533,533,533,,,533,,,,,,,533',
',,533,533,,533,533,533,533,533,,533,533,533,,533,533,,533,533,,,,,,',
',,,,,,,,,,,,,,,533,,,533,,,533,,,533,,,,,,533,,,,,,,,533,,,,,533,533',
'533,533,533,533,,,,533,533,,,,537,537,537,533,537,,533,,537,537,,533',
'533,537,,537,537,537,537,537,537,537,,,,,,537,537,537,537,537,537,537',
',,537,,,,,,,537,,,537,537,,537,537,537,537,537,,537,537,537,,537,537',
',537,537,,,,,,,,,,,,,,,,,,,,,,537,,,537,,,537,,,537,,,,,,537,,,,,,,',
'537,,,,,537,537,537,537,537,537,,,,537,537,,,,543,543,543,537,543,,537',
',543,543,,537,537,543,,543,543,543,543,543,543,543,,,,,,543,543,543',
'543,543,543,543,,,543,,,,,,,543,,,543,543,,543,543,543,543,543,543,543',
'543,543,,543,543,,543,543,,,,,,,,,,,,,,,,,,,,,,543,,,543,,,543,,,543',
',543,,,,543,,,,,,,,543,,,,,543,543,543,543,543,543,,,,543,543,,,,546',
'546,546,543,546,,543,,546,546,,543,543,546,,546,546,546,546,546,546',
'546,,,,,,546,546,546,546,546,546,546,,,546,,,,,,,546,,,546,546,,546',
'546,546,546,546,546,546,546,546,,546,546,,546,546,,,,,,,,,,,,,,,,,,',
',,,546,,,546,,,546,,,546,,,,,,546,,,,,,,,546,,,,,546,546,546,546,546',
'546,,,,546,546,,,,,,,546,,,546,,,,,546,546,551,551,551,551,551,,,,551',
'551,,,,551,,551,551,551,551,551,551,551,,,,,,551,551,551,551,551,551',
'551,,,551,,,,,,551,551,,551,551,551,,551,551,551,551,551,,551,551,551',
',551,551,,551,551,,,,,,,,,,,,,,,,,,,,,,551,,,551,,,551,,,551,,551,,',
',551,,,,,,,,551,,,,,551,551,551,551,551,551,,,,551,551,,,,,,,551,,,551',
',,,,551,551,552,552,552,552,552,,,,552,552,,,,552,,552,552,552,552,552',
'552,552,,,,,,552,552,552,552,552,552,552,,,552,,,,,,552,552,,552,552',
'552,,552,552,552,552,552,,552,552,552,,552,552,,552,552,,,,,,,,,,,,',
',,,,,,,,,552,,,552,,,552,,,552,,552,,,,552,,,,,,,,552,,,,,552,552,552',
'552,552,552,,,,552,552,,,,558,558,558,552,558,,552,,558,558,,552,552',
'558,,558,558,558,558,558,558,558,,,,,,558,558,558,558,558,558,558,,',
'558,,,,,,,558,,,558,558,,558,558,558,558,558,,558,558,558,,558,558,856',
',856,856,856,856,856,,,,,,,,,,856,,,,,,,,558,,,558,,,558,,,558,,,,,',
'856,,,,,,,,856,856,856,856,,558,558,558,558,558,558,,,,558,558,,,,,',
',558,,,558,,,,,558,558,572,572,572,572,572,,,856,572,572,,,,572,,572',
'572,572,572,572,572,572,,,,,,572,572,572,572,572,572,572,,,572,,,,,',
'572,572,,572,572,572,,572,572,572,572,572,,572,572,572,,572,572,,572',
'572,,,,,,,,,,,,,,,,,,,,,,572,,,572,,,572,,,572,,572,,,,572,,,,,,,,572',
',,,,572,572,572,572,572,572,,,,572,572,,,,,,,572,,,572,,,,,572,572,576',
'576,576,576,576,,,,576,576,,,,576,,576,576,576,576,576,576,576,,,,,',
'576,576,576,576,576,576,576,,,576,,,,,,576,576,,576,576,576,,576,576',
'576,576,576,,576,576,576,,576,576,,576,576,,,,,,,,,,,,,,,,,,,,,,576',
',,576,,,576,,,576,,576,,,,576,,,,,,,,576,,,,,576,576,576,576,576,576',
',,,576,576,,,,,,,576,,,576,,,,,576,576,581,581,581,581,581,,,,581,581',
',,,581,,581,581,581,581,581,581,581,,,,,,581,581,581,581,581,581,581',
',,581,,,,,,581,581,,581,581,581,,581,581,581,581,581,,581,581,581,,581',
'581,,581,581,,,,,,,,,,,,,,,,,,,,,,581,,,581,,,581,,,581,,581,,,,581',
',,,,,,,581,,,,,581,581,581,581,581,581,,,,581,581,,,,583,583,583,581',
'583,,581,,583,583,,581,581,583,,583,583,583,583,583,583,583,,,,,,583',
'583,583,583,583,583,583,,,583,,,,,,,583,,,583,583,,583,583,583,583,583',
'583,583,583,583,,583,583,,583,583,,,,,,,,,,,,,,,,,,,,,,583,,,583,,,583',
',,583,,583,,,,583,,,,,,,,583,,,,,583,583,583,583,583,583,,,,583,583',
',,,586,586,586,583,586,,583,,586,586,,583,583,586,,586,586,586,586,586',
'586,586,,,,,,586,586,586,586,586,586,586,,,586,,,,,,,586,,,586,586,',
'586,586,586,586,586,586,586,586,586,,586,586,,586,586,,,,,,,,,,,,,,',
',,,,,,,586,,,586,,,586,,,586,,586,,,,586,,,,,,,,586,,,,,586,586,586',
'586,586,586,,,,586,586,,,,592,592,592,586,592,,586,,592,592,,586,586',
'592,,592,592,592,592,592,592,592,,,,,,592,592,592,592,592,592,592,,',
'592,,,,,,,592,,,592,592,,592,592,592,592,592,592,592,592,592,,592,592',
',592,592,,,,,,,,,,,,,,,,,,,,,,592,,,592,,,592,,,592,,592,,,,592,,,,',
',,,592,,,,,592,592,592,592,592,592,,,,592,592,,,,596,596,596,592,596',
',592,,596,596,,592,592,596,,596,596,596,596,596,596,596,,,,,,596,596',
'596,596,596,596,596,,,596,,,,,,,596,,,596,596,,596,596,596,596,596,',
'596,596,596,,596,596,,596,596,,,,,,,,,,,,,,,,,,,,,,596,,,596,,,596,',
',596,,,,,,596,,,,,,,,596,,,,,596,596,596,596,596,596,,,,596,596,,,,598',
'598,598,596,598,,596,,598,598,,596,596,598,,598,598,598,598,598,598',
'598,,,,,,598,598,598,598,598,598,598,,,598,,,,,,,598,,,598,598,,598',
'598,598,598,598,,598,598,598,,598,598,,598,598,,,,,,,,,,,,,,,,,,,,,',
'598,,,598,,,598,,,598,,,,,,598,,,,,,,,598,,,,,598,598,598,598,598,598',
',,,598,598,,,,625,625,625,598,625,,598,,625,625,,598,598,625,,625,625',
'625,625,625,625,625,,,,,,625,625,625,625,625,625,625,,,625,,,,,,,625',
',,625,625,,625,625,625,625,625,,625,625,625,,625,625,,625,625,,,,,,',
',,,,,,,,,,,,,,,625,,,625,,,625,,,625,,,,,,625,,,,,,,,625,,,,,625,625',
'625,625,625,625,,,,625,625,,,,627,627,627,625,627,,625,,627,627,,625',
'625,627,,627,627,627,627,627,627,627,,,,,,627,627,627,627,627,627,627',
',,627,,,,,,,627,,,627,627,,627,627,627,627,627,,627,627,627,,627,627',
',627,627,,,,,,,,,,,,,,,,,,,,,,627,,,627,,,627,,,627,,627,,,,627,,,,',
',,,627,,,,,627,627,627,627,627,627,,,,627,627,,,,630,630,630,627,630',
',627,,630,630,,627,627,630,,630,630,630,630,630,630,630,,,,,,630,630',
'630,630,630,630,630,,,630,,,,,,,630,,,630,630,,630,630,630,630,630,',
'630,630,630,,630,630,,630,630,,,,,,,,,,,,,,,,,,,,,,630,,,630,,,630,',
',630,,,,,,630,,,,,,,,630,,,,,630,630,630,630,630,630,,,,630,630,,,,631',
'631,631,630,631,,630,,631,631,,630,630,631,,631,631,631,631,631,631',
'631,,,,,,631,631,631,631,631,631,631,,,631,,,,,,,631,,,631,631,,631',
'631,631,631,631,,631,631,631,,631,631,,631,631,,,,,,,,,,,,,,,,,,,,,',
'631,,,631,,,631,,,631,,,,,,631,,,,,,,,631,,,,,631,631,631,631,631,631',
',,,631,631,,,,636,636,636,631,636,,631,,636,636,,631,631,636,,636,636',
'636,636,636,636,636,,,,,,636,636,636,636,636,636,636,,,636,,,,,,,636',
',,636,636,,636,636,636,636,636,,636,636,636,,636,636,,636,636,,,,,,',
',,,,,,,,,,,,,,,636,,,636,,,636,,,636,,,,,,636,,,,,,,,636,,,,,636,636',
'636,636,636,636,,,,636,636,,,,639,639,639,636,639,,636,,639,639,,636',
'636,639,,639,639,639,639,639,639,639,,,,,,639,639,639,639,639,639,639',
',,639,,,,,,,639,,,639,639,,639,639,639,639,639,,639,639,639,,639,639',
',639,639,,,,,,,,,,,,,,,,,,,,,,639,,,639,,,639,,,639,,,,,,639,,,,,,,',
'639,,,,,639,639,639,639,639,639,,,,639,639,,,,650,650,650,639,650,,639',
',650,650,,639,639,650,,650,650,650,650,650,650,650,,,,,,650,650,650',
'650,650,650,650,,,650,,,,,,,650,,,650,650,,650,650,650,650,650,,650',
'650,650,,650,650,,650,650,,,,,,,,,,,,,,,,,,,,,,650,,,650,,,650,,,650',
',,,,,650,,,,,,,,650,,,,,650,650,650,650,650,650,,,,650,650,,,,,,,650',
',,650,,,,,650,650,654,654,654,654,654,,,,654,654,,,,654,,654,654,654',
'654,654,654,654,,,,,,654,654,654,654,654,654,654,,,654,,,,,,654,654',
',654,654,654,,654,654,654,654,654,,654,654,654,,654,654,,654,654,,,',
',,,,,,,,,,,,,,,,,,654,,,654,,,654,,,654,,654,,,,654,,,,,,,,654,,,,,654',
'654,654,654,654,654,,,,654,654,,,,658,658,658,654,658,,654,,658,658',
',654,654,658,,658,658,658,658,658,658,658,,,,,,658,658,658,658,658,658',
'658,,,658,,,,,,,658,,,658,658,,658,658,658,658,658,,658,658,658,,658',
'658,,658,658,,,,,,,,,,,,,,,,,,,,,,658,,,658,,,658,,,658,,,,,,658,,,',
',,,,658,,,,,658,658,658,658,658,658,,,,658,658,,,,,,,658,,,658,,,,,658',
'658,667,667,667,667,667,,,,667,667,,,,667,,667,667,667,667,667,667,667',
',,,,,667,667,667,667,667,667,667,,,667,,,,,,667,667,,667,667,667,,667',
'667,667,667,667,,667,667,667,,667,667,,667,667,,,,,,,,,,,,,,,,,,,,,',
'667,,,667,,,667,,,667,,667,,,,667,,,,,,,,667,,,,,667,667,667,667,667',
'667,,,,667,667,,,,670,670,670,667,670,,667,,670,670,,667,667,670,,670',
'670,670,670,670,670,670,,,,,,670,670,670,670,670,670,670,,,670,,,,,',
',670,,,670,670,,670,670,670,670,670,670,670,670,670,,670,670,,670,670',
',,,,,,,,,,,,,,,,,,,,,670,,,670,,,670,,,670,,670,,670,,670,,,,,,,,670',
',,,,670,670,670,670,670,670,,,,670,670,,,,671,671,671,670,671,,670,',
'671,671,,670,670,671,,671,671,671,671,671,671,671,,,,,,671,671,671,671',
'671,671,671,,,671,,,,,,,671,,,671,671,,671,671,671,671,671,671,671,671',
'671,,671,671,,671,671,,,,,,,,,,,,,,,,,,,,,,671,,,671,,,671,,,671,,,',
'671,,671,,,,,,,,671,,,,,671,671,671,671,671,671,,,,671,671,,,,,,,671',
',,671,,,,,671,671,677,677,677,677,677,,,,677,677,,,,677,,677,677,677',
'677,677,677,677,,,,,,677,677,677,677,677,677,677,,,677,,,,,,677,677',
',677,677,677,,677,677,677,677,677,,677,677,677,,677,677,,677,677,,,',
',,,,,,,,,,,,,,,,,,677,,,677,,,677,,,677,,677,,,,677,,,,,,,,677,,,,,677',
'677,677,677,677,677,,,,677,677,,,,,,,677,,,677,,,,,677,677,678,678,678',
'678,678,,,,678,678,,,,678,,678,678,678,678,678,678,678,,,,,,678,678',
'678,678,678,678,678,,,678,,,,,,678,678,,678,678,678,,678,678,678,678',
'678,,678,678,678,,678,678,,678,678,,,,,,,,,,,,,,,,,,,,,,678,,,678,,',
'678,,,678,,678,,,,678,,,,,,,,678,,,,,678,678,678,678,678,678,,,,678',
'678,,,,682,682,682,678,682,,678,,682,682,,678,678,682,,682,682,682,682',
'682,682,682,,,,,,682,682,682,682,682,682,682,,,682,,,,,,,682,,,682,682',
',682,682,682,682,682,,682,682,682,,682,682,,,,,,,,,,,,,,,,,,,,,,,,,682',
',,682,,,682,,,682,,,,,,,,,,,,,,,,,,,682,682,682,682,682,682,,,,682,682',
',,,694,694,694,682,694,,682,,694,694,,682,682,694,,694,694,694,694,694',
'694,694,,,,,,694,694,694,694,694,694,694,,,694,,,,,,,694,,,694,694,',
'694,694,694,694,694,,694,694,694,,694,694,,,,,,,,,,,,,,,,,,,,,,,,,694',
',,694,,,694,,,694,,,,,,,,,,,,,,,,,,,694,694,694,694,694,694,,,,694,694',
',,,700,700,700,694,700,,694,,700,700,,694,694,700,,700,700,700,700,700',
'700,700,,,,,,700,700,700,700,700,700,700,,,700,,,,,,,700,,,700,700,',
'700,700,700,700,700,,700,700,700,,700,700,,700,700,,,,,,,,,,,,,,,,,',
',,,,700,,,700,,,700,,,700,,700,,,,700,,,,,,,,700,,,,,700,700,700,700',
'700,700,,,,700,700,,,,731,731,731,700,731,,700,,731,731,,700,700,731',
',731,731,731,731,731,731,731,,,,,,731,731,731,731,731,731,731,,,731',
',,,,,,731,,,731,731,,731,731,731,731,731,,731,731,731,,731,731,,731',
'731,,,,,,,,,,,,,,,,,,,,,,731,,,731,,,731,,,731,,731,,,,731,,,,,,,,731',
',,,,731,731,731,731,731,731,,,,731,731,,,,738,738,738,731,738,,731,',
'738,738,,731,731,738,,738,738,738,738,738,738,738,,,,,,738,738,738,738',
'738,738,738,,,738,,,,,,,738,,,738,738,,738,738,738,738,738,,738,738',
'738,,738,738,,738,738,,,,,,,,,,,,,,,,,,,,,,738,,,738,,,738,,,738,,,',
',,738,,,,,,,,738,,,,,738,738,738,738,738,738,,,,738,738,,,,,,,738,,',
'738,,,,,738,738,743,743,743,743,743,,,,743,743,,,,743,,743,743,743,743',
'743,743,743,,,,,,743,743,743,743,743,743,743,,,743,,,,,,743,743,,743',
'743,743,,743,743,743,743,743,,743,743,743,,743,743,,743,743,,,,,,,,',
',,,,,,,,,,,,,743,,,743,,,743,,,743,,743,,,,743,,,,,,,,743,,,,,743,743',
'743,743,743,743,,,,743,743,,,,,,,743,,,743,,,,,743,743,747,747,747,747',
'747,,,,747,747,,,,747,,747,747,747,747,747,747,747,,,,,,747,747,747',
'747,747,747,747,,,747,,,,,,747,747,,747,747,747,,747,747,747,747,747',
',747,747,747,,747,747,,747,747,,,,,,,,,,,,,,,,,,,,,,747,,,747,,,747',
',,747,,747,,,,747,,,,,,,,747,,,,,747,747,747,747,747,747,,,,747,747',
',,,,,,747,,,747,,,,,747,747,748,748,748,748,748,,,,748,748,,,,748,,748',
'748,748,748,748,748,748,,,,,,748,748,748,748,748,748,748,,,748,,,,,',
'748,748,,748,748,748,,748,748,748,748,748,,748,748,748,,748,748,,748',
'748,,,,,,,,,,,,,,,,,,,,,,748,,,748,,,748,,,748,,748,,,,748,,,,,,,,748',
',,,,748,748,748,748,748,748,,,,748,748,,,,755,755,755,748,755,,748,',
'755,755,,748,748,755,,755,755,755,755,755,755,755,,,,,,755,755,755,755',
'755,755,755,,,755,,,,,,,755,,,755,755,,755,755,755,755,755,,755,755',
'755,,755,755,,755,755,,,,,,,,,,,,,,,,,,,,,,755,,,755,,,755,,,755,,,',
',,755,,,,,,,,755,,,,,755,755,755,755,755,755,,,,755,755,,,,,,,755,,',
'755,,,,,755,755,769,769,769,769,769,,,,769,769,,,,769,,769,769,769,769',
'769,769,769,,,,,,769,769,769,769,769,769,769,,,769,,,,,,769,769,,769',
'769,769,,769,769,769,769,769,,769,769,769,,769,769,,769,769,,,,,,,,',
',,,,,,,,,,,,,769,,,769,,,769,,,769,,769,,,,769,,,,,,,,769,,,,,769,769',
'769,769,769,769,,,,769,769,,,,775,775,775,769,775,,769,,775,775,,769',
'769,775,,775,775,775,775,775,775,775,,,,,,775,775,775,775,775,775,775',
',,775,,,,,,,775,,,775,775,,775,775,775,775,775,,775,775,775,,775,775',
',775,775,,,,,,,,,,,,,,,,,,,,,,775,,,775,,,775,,,775,,,,,,775,,,,,,,',
'775,,,,,775,775,775,775,775,775,,,,775,775,,,,776,776,776,775,776,,775',
',776,776,,775,775,776,,776,776,776,776,776,776,776,,,,,,776,776,776',
'776,776,776,776,,,776,,,,,,,776,,,776,776,,776,776,776,776,776,,776',
'776,776,,776,776,,776,776,,,,,,,,,,,,,,,,,,,,,,776,,,776,,,776,,,776',
',,,,,776,,,,,,,,776,,,,,776,776,776,776,776,776,,,,776,776,,,,777,777',
'777,776,777,,776,,777,777,,776,776,777,,777,777,777,777,777,777,777',
',,,,,777,777,777,777,777,777,777,,,777,,,,,,,777,,,777,777,,777,777',
'777,777,777,,777,777,777,,777,777,,777,777,,,,,,,,,,,,,,,,,,,,,,777',
',,777,,,777,,,777,,,,,,777,,,,,,,,777,,,,,777,777,777,777,777,777,,',
',777,777,,,,788,788,788,777,788,,777,,788,788,,777,777,788,,788,788',
'788,788,788,788,788,,,,,,788,788,788,788,788,788,788,,,788,,,,,,,788',
',,788,788,,788,788,788,788,788,,788,788,788,,788,788,,788,788,,,,,,',
',,,,,,,,,,,,,,,788,,,788,,,788,,,788,,,,,,788,,,,,,,,788,,,,,788,788',
'788,788,788,788,,,,788,788,,,,789,789,789,788,789,,788,,789,789,,788',
'788,789,,789,789,789,789,789,789,789,,,,,,789,789,789,789,789,789,789',
',,789,,,,,,,789,,,789,789,,789,789,789,789,789,,789,789,789,,789,789',
',789,789,,,,,,,,,,,,,,,,,,,,,,789,,,789,,,789,,,789,,,,,,789,,,,,,,',
'789,,,,,789,789,789,789,789,789,,,,789,789,,,,790,790,790,789,790,,789',
',790,790,,789,789,790,,790,790,790,790,790,790,790,,,,,,790,790,790',
'790,790,790,790,,,790,,,,,,,790,,,790,790,,790,790,790,790,790,,790',
'790,790,,790,790,,790,790,,,,,,,,,,,,,,,,,,,,,,790,,,790,,,790,,,790',
',,,,,790,,,,,,,,790,,,,,790,790,790,790,790,790,,,,790,790,,,,802,802',
'802,790,802,,790,,802,802,,790,790,802,,802,802,802,802,802,802,802',
',,,,,802,802,802,802,802,802,802,,,802,,,,,,,802,,,802,802,,802,802',
'802,802,802,,802,802,802,,802,802,,802,802,,,,,,,,,,,,,,,,,,,,,,802',
',,802,,,802,,,802,,802,,,,802,,,,,,,,802,,,,,802,802,802,802,802,802',
',,,802,802,,,,,,,802,,,802,,,,,802,802,813,813,813,813,813,,,,813,813',
',,,813,,813,813,813,813,813,813,813,,,,,,813,813,813,813,813,813,813',
',,813,,,,,,813,813,,813,813,813,,813,813,813,813,813,,813,813,813,,813',
'813,,813,813,,,,,,,,,,,,,,,,,,,,,,813,,,813,,,813,,,813,,813,,,,813',
',,,,,,,813,,,,,813,813,813,813,813,813,,,,813,813,,,,832,832,832,813',
'832,,813,,832,832,,813,813,832,,832,832,832,832,832,832,832,,,,,,832',
'832,832,832,832,832,832,,,832,,,,,,,832,,,832,832,,832,832,832,832,832',
',832,832,832,,832,832,,832,832,,,,,,,,,,,,,,,,,,,,,,832,,,832,,,832',
',,832,,,,,,832,,,,,,,,832,,,,,832,832,832,832,832,832,,,,832,832,,,',
',,,832,,,832,,,,,832,832,834,834,834,834,834,,,,834,834,,,,834,,834',
'834,834,834,834,834,834,,,,,,834,834,834,834,834,834,834,,,834,,,,,',
'834,834,,834,834,834,,834,834,834,834,834,,834,834,834,,834,834,,834',
'834,,,,,,,,,,,,,,,,,,,,,,834,,,834,,,834,,,834,,834,,,,834,,,,,,,,834',
',,,,834,834,834,834,834,834,,,,834,834,,,,,,,834,,,834,,,,,834,834,835',
'835,835,835,835,,,,835,835,,,,835,,835,835,835,835,835,835,835,,,,,',
'835,835,835,835,835,835,835,,,835,,,,,,835,835,,835,835,835,,835,835',
'835,835,835,,835,835,835,,835,835,,835,835,,,,,,,,,,,,,,,,,,,,,,835',
',,835,,,835,,,835,,835,,,,835,,,,,,,,835,,,,,835,835,835,835,835,835',
',,,835,835,,,,862,862,862,835,862,,835,,862,862,,835,835,862,,862,862',
'862,862,862,862,862,,,,,,862,862,862,862,862,862,862,,,862,,,,,,,862',
',,862,862,,862,862,862,862,862,,862,862,862,,862,862,,862,862,,,,,,',
',,,,,,,,,,,,,,,862,,,862,,,862,,,862,,,,,,862,,,,,,,,862,,,,,862,862',
'862,862,862,862,,,,862,862,,,,876,876,876,862,876,,862,,876,876,,862',
'862,876,,876,876,876,876,876,876,876,,,,,,876,876,876,876,876,876,876',
',,876,,,,,,,876,,,876,876,,876,876,876,876,876,,876,876,876,,876,876',
',876,876,,,,,,,,,,,,,,,,,,,,,,876,,,876,,,876,,,876,,,,,,876,,,,,,,',
'876,,,,,876,876,876,876,876,876,,,,876,876,,,,877,877,877,876,877,,876',
',877,877,,876,876,877,,877,877,877,877,877,877,877,,,,,,877,877,877',
'877,877,877,877,,,877,,,,,,,877,,,877,877,,877,877,877,877,877,,877',
'877,877,,877,877,,877,877,,,,,,,,,,,,,,,,,,,,,,877,,,877,,,877,,,877',
',,,,,877,,,,,,,,877,,,,,877,877,877,877,877,877,,,,877,877,,,,881,881',
'881,877,881,,877,,881,881,,877,877,881,,881,881,881,881,881,881,881',
',,,,,881,881,881,881,881,881,881,,,881,,,,,,,881,,,881,881,,881,881',
'881,881,881,881,881,881,881,,881,881,,881,881,,,,,,,,,,,,,,,,,,,,,,881',
',,881,,,881,,,881,,,,881,,881,,,,,,,,881,,,,,881,881,881,881,881,881',
',,,881,881,,,,,,,881,,,881,,,,,881,881,886,886,886,886,886,,,,886,886',
',,,886,,886,886,886,886,886,886,886,,,,,,886,886,886,886,886,886,886',
',,886,,,,,,886,886,,886,886,886,,886,886,886,886,886,,886,886,886,,886',
'886,,886,886,,,,,,,,,,,,,,,,,,,,,,886,,,886,,,886,,,886,,886,,,,886',
',,,,,,,886,,,,,886,886,886,886,886,886,,,,886,886,,,,890,890,890,886',
'890,,886,,890,890,,886,886,890,,890,890,890,890,890,890,890,,,,,,890',
'890,890,890,890,890,890,,,890,,,,,,,890,,,890,890,,890,890,890,890,890',
',890,890,890,,890,890,,,,,,,,,,,,,,,,,,,,,,,,,890,,,890,,,890,,,890',
',,,,,,,,,,,,,,,,,,890,890,890,890,890,890,,,,890,890,,,,900,900,900',
'890,900,,890,,900,900,,890,890,900,,900,900,900,900,900,900,900,,,,',
',900,900,900,900,900,900,900,,,900,,,,,,,900,,,900,900,,900,900,900',
'900,900,,900,900,900,,900,900,,900,900,,,,,,,,,,,,,,,,,,,,,,900,,,900',
',,900,,,900,,900,,,,900,,,,,,,,900,,,,,900,900,900,900,900,900,,,,900',
'900,,,,,,,900,,,900,,,,,900,900,901,901,901,901,901,,,,901,901,,,,901',
',901,901,901,901,901,901,901,,,,,,901,901,901,901,901,901,901,,,901',
',,,,,901,901,,901,901,901,,901,901,901,901,901,,901,901,901,,901,901',
',901,901,,,,,,,,,,,,,,,,,,,,,,901,,,901,,,901,,,901,,901,,,,901,,,,',
',,,901,,,,,901,901,901,901,901,901,,,,901,901,,,,,,,901,,,901,,,,,901',
'901,904,904,904,904,904,,,,904,904,,,,904,,904,904,904,904,904,904,904',
',,,,,904,904,904,904,904,904,904,,,904,,,,,,904,904,,904,904,904,,904',
'904,904,904,904,,904,904,904,,904,904,,904,904,,,,,,,,,,,,,,,,,,,,,',
'904,,,904,,,904,,,904,,904,,,,904,,,,,,,,904,,,,,904,904,904,904,904',
'904,,,,904,904,,,,921,921,921,904,921,,904,,921,921,,904,904,921,,921',
'921,921,921,921,921,921,,,,,,921,921,921,921,921,921,921,,,921,,,,,',
',921,,,921,921,,921,921,921,921,921,,921,921,921,,921,921,,921,921,',
',,,,,,,,,,,,,,,,,,,,921,,,921,,,921,,,921,,921,,921,,921,,,,,,,,921',
',,,,921,921,921,921,921,921,,,,921,921,,,,,,,921,,,921,,,,,921,921,923',
'923,923,923,923,,,,923,923,,,,923,,923,923,923,923,923,923,923,,,,,',
'923,923,923,923,923,923,923,,,923,,,,,,923,923,,923,923,923,,923,923',
'923,923,923,,923,923,923,,923,923,,923,923,,,,,,,,,,,,,,,,,,,,,,923',
',,923,,,923,,,923,,923,,,,923,,,,,,,,923,,,,,923,923,923,923,923,923',
',,,923,923,,,,,,,923,,,923,,,,,923,923,930,930,930,930,930,,,,930,930',
',,,930,,930,930,930,930,930,930,930,,,,,,930,930,930,930,930,930,930',
',,930,,,,,,930,930,,930,930,930,,930,930,930,930,930,,930,930,930,,930',
'930,,930,930,,,,,,,,,,,,,,,,,,,,,,930,,,930,,,930,,,930,,930,,,,930',
',,,,,,,930,,,,,930,930,930,930,930,930,,,,930,930,,,,,,,930,,,930,,',
',,930,930,935,935,935,935,935,,,,935,935,,,,935,,935,935,935,935,935',
'935,935,,,,,,935,935,935,935,935,935,935,,,935,,,,,,935,935,,935,935',
'935,,935,935,935,935,935,,935,935,935,,935,935,,935,935,,,,,,,,,,,,',
',,,,,,,,,935,,,935,,,935,,,935,,935,,,,935,,,,,,,,935,,,,,935,935,935',
'935,935,935,,,,935,935,,,,,,,935,,,935,,,,,935,935,943,943,943,943,943',
',,,943,943,,,,943,,943,943,943,943,943,943,943,,,,,,943,943,943,943',
'943,943,943,,,943,,,,,,943,943,,943,943,943,,943,943,943,943,943,,943',
'943,943,,943,943,,943,943,,,,,,,,,,,,,,,,,,,,,,943,,,943,,,943,,,943',
',943,,,,943,,,,,,,,943,,,,,943,943,943,943,943,943,,,,943,943,,,,945',
'945,945,943,945,,943,,945,945,,943,943,945,,945,945,945,945,945,945',
'945,,,,,,945,945,945,945,945,945,945,,,945,,,,,,,945,,,945,945,,945',
'945,945,945,945,945,945,945,945,,945,945,,945,945,,,,,,,,,,,,,,,,,,',
',,,945,,,945,,,945,,,945,,945,,,,945,,,,,,,,945,,,,436,945,945,945,945',
'945,945,436,436,436,945,945,,436,436,,436,,945,,,945,,,,436,945,945',
',,,,,,,,436,436,,436,436,436,436,436,,,,,,,,,,,,,,,,,,,,,,,,436,436',
'436,436,436,436,436,436,436,436,436,436,436,436,436,438,,436,436,436',
',436,438,438,438,436,,,438,438,,438,436,,436,,436,436,436,436,436,436',
'436,,436,436,436,,,,,438,438,,438,438,438,438,438,436,436,,436,,436',
',,436,,436,,436,,,,,,,,,,,438,438,438,438,438,438,438,438,438,438,438',
'438,438,438,438,,,438,438,438,,438,,,,438,,,,,,,438,,438,,438,438,438',
'438,438,438,438,,438,,438,,,,,,,,,,,,,438,438,,438,,438,644,,438,,438',
',438,644,644,644,,,644,644,644,,644,,,,,,,,,644,644,644,,,,,,,,,644',
'644,,644,644,644,644,644,,,,,,,,,,,,,,,,,,,,,,,,644,644,644,644,644',
'644,644,644,644,644,644,644,644,644,644,,,644,644,644,,644,644,,,644',
',,644,,644,,644,,644,,644,644,644,644,644,644,644,,644,644,644,,,,,',
',,,,,,,644,644,644,644,,644,645,,644,,644,,644,645,645,645,,,645,645',
'645,,645,,,,,,,,,,645,645,,,,,,,,,645,645,,645,645,645,645,645,,,,,',
',,,,,,,,,,,,,,,,,,645,645,645,645,645,645,645,645,645,645,645,645,645',
'645,645,,,645,645,645,,645,645,,,645,,,645,,645,,645,,645,,645,645,645',
'645,645,645,645,,645,,645,,,,,,,,,,,,,645,645,645,645,,645,28,,645,',
'645,,645,28,28,28,,,28,28,28,,28,,,,,,,,,,28,28,,,,,,,,,28,28,,28,28',
'28,28,28,,,,,,,,,,,,,,,,,,,,,,,,28,28,28,28,28,28,28,28,28,28,28,28',
'28,28,28,,,28,28,28,,,28,,28,28,,,28,,28,,28,,28,,28,28,28,28,28,28',
'28,,28,,28,,,,,,,,,,,,,28,28,28,28,50,28,,,28,,28,50,50,50,,,50,50,50',
',50,,,,,,,,,,50,50,50,,,,,,,,50,50,,50,50,50,50,50,,,,,,,,,,,,,,,,,',
',,,,,,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,,,50,50,50,,,50,',
',50,,,50,,50,,50,,50,,50,50,50,50,50,50,50,,50,,50,,,,,,,,,,,,,50,50',
'50,50,494,50,,50,50,,50,494,494,494,,,494,494,494,,494,,,,,,,,,,494',
',,,,,,,,,494,494,,494,494,494,494,494,,,,,,,,,,,,,,495,,,,,,,495,495',
'495,,,495,495,495,,495,,,,,494,494,,,,495,,,494,,,,,494,494,495,495',
',495,495,495,495,495,,,,,918,,918,918,918,918,918,,494,,,,,,,,918,,',
',496,494,,494,,,494,496,496,496,495,495,496,496,496,,496,,495,918,918',
',,495,495,,496,918,918,918,918,,,,,,496,496,,496,496,496,496,496,,495',
',,,,,202,202,,,202,,,495,,495,,,495,202,202,918,202,202,202,202,202',
'202,202,,,202,202,496,496,,,202,202,202,202,496,,,,,496,496,,,,,,202',
'202,,202,202,202,202,202,202,202,202,202,202,202,,496,202,,,,,,,,,,',
',496,,496,,,496,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,,,,7',
'7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,,7,7,7,,7,,,,,,,,,7,7,,7,7,7',
'7,7,7,7,,,7,7,,,,,7,7,7,7,,,,,,,,,,,,,7,7,,7,7,7,7,7,7,7,7,7,7,7,,,7',
'7,,,,,,,,,,7,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,,,,8,8',
'8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,,8,8,,,8,,,,,,,,,8,8,,8,8,8,8',
'8,8,8,,,8,8,,,,,8,8,8,8,,,,,,,,,,,,,8,8,,8,8,8,8,8,8,8,8,8,8,8,,,8,8',
',,,,,,,,,8,411,411,411,411,411,411,411,411,411,411,411,411,411,411,411',
'411,411,411,411,411,411,411,411,411,,,,411,411,411,411,411,411,411,411',
'411,411,411,411,411,411,411,411,411,411,411,411,411,,411,411,,,411,',
',,,,,,,411,411,,411,411,411,411,411,411,411,,,411,411,,,,,411,411,411',
'411,,,,,,,,,,,,,411,411,,411,411,411,411,411,411,411,411,411,411,411',
',,411,411,,,,,,,,,,411,415,415,415,415,415,415,415,415,415,415,415,415',
'415,415,415,415,415,415,415,415,415,415,415,415,,,,415,415,415,415,415',
'415,415,415,415,415,415,415,415,415,415,415,415,415,415,415,415,,415',
'415,,,415,,,,,,,,,415,415,,415,415,415,415,415,415,415,,,415,415,,,',
',415,415,415,415,,,,,,,,,,,,,415,415,,415,415,415,415,415,415,415,415',
'415,415,415,,,415,415,,,,,,,,,,415,65,65,65,65,65,65,65,65,65,65,65',
'65,65,65,65,65,65,65,65,65,65,65,65,65,,,,65,65,65,65,65,65,65,65,65',
'65,65,65,65,65,65,65,65,65,65,65,65,,65,65,65,65,65,,65,,,,,,,65,65',
',65,65,65,65,65,65,65,,,65,65,,,,,65,65,65,65,,,,,,65,,,,,,,65,65,,65',
'65,65,65,65,65,65,65,65,65,65,,,65,79,79,79,79,79,79,79,79,79,79,79',
'79,79,79,79,79,79,79,79,79,79,79,79,79,,,,79,79,79,79,79,79,79,79,79',
'79,79,79,79,79,79,79,79,79,79,79,79,,79,79,79,79,79,,79,,,,,,,79,79',
',79,79,79,79,79,79,79,,,79,79,,,,,79,79,79,79,,,,,,,,,,,,,79,79,,79',
'79,79,79,79,79,79,79,79,79,79,,,79,192,192,192,192,192,192,192,192,192',
'192,192,192,192,192,192,192,192,192,192,192,192,192,192,192,,,,192,192',
'192,192,192,192,192,192,192,192,192,192,192,192,192,192,192,192,192',
'192,192,,192,192,192,192,192,,192,,,,,,,192,192,,192,192,192,192,192',
'192,192,,,192,192,,,,,192,192,192,192,,,,,,,,,,,,,192,192,,192,192,192',
'192,192,192,192,192,192,192,192,,,192,766,766,766,766,766,766,766,766',
'766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,,,,766',
'766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766,766',
'766,766,766,,766,766,,,766,,,,,,,,,766,766,,766,766,766,766,766,766',
'766,,,766,766,,,,,766,766,766,766,,,,,,,,,,,,,766,766,,766,766,766,766',
'766,766,766,766,766,766,766,201,201,766,,201,,,,,,,,,201,201,,201,201',
'201,201,201,201,201,,,201,201,,,,,201,201,201,201,,,,,,201,,,,,,,201',
'201,,201,201,201,201,201,201,201,201,201,201,201,203,203,201,,203,,',
',,,,,,203,203,,203,203,203,203,203,203,203,,,203,203,,,,,203,203,203',
'203,,,,,,,,,,,,,203,203,,203,203,203,203,203,203,203,203,203,203,203',
'264,264,203,,264,,,,,,,,,264,264,,264,264,264,264,264,264,264,,,264',
'264,,,,,264,264,264,264,,,,,,,,,,,,,264,264,,264,264,264,264,264,264',
'264,264,264,264,264,265,265,264,,265,,,,,,,,,265,265,,265,265,265,265',
'265,265,265,,,265,265,,,,,265,265,265,265,,,,,,,,,,,,,265,265,,265,265',
'265,265,265,265,265,265,265,265,265,266,266,265,,266,,,,,,,,,266,266',
',266,266,266,266,266,266,266,,,266,266,,,,,266,266,266,266,,,,,,,,,',
',,,266,266,,266,266,266,266,266,266,266,266,266,266,266,449,449,266',
',449,,,,,,,,,449,449,,449,449,449,449,449,449,449,,,449,449,,,,,449',
'449,449,449,,,,,,449,,,,,,,449,449,,449,449,449,449,449,449,449,449',
'449,449,449,450,450,449,,450,,,,,,,,,450,450,,450,450,450,450,450,450',
'450,,,450,450,,,,,450,450,450,450,,,,,,,,,,,,,450,450,,450,450,450,450',
'450,450,450,450,450,450,450,521,521,450,,521,,,,,,,,,521,521,,521,521',
'521,521,521,521,521,,,521,521,,,,,521,521,521,521,,,,,,521,,,,,,,521',
'521,,521,521,521,521,521,521,521,521,521,521,521,522,522,521,,522,,',
',,,,,,522,522,,522,522,522,522,522,522,522,,,522,522,,,,,522,522,522',
'522,,,,,,,,,,,,,522,522,,522,522,522,522,522,522,522,522,522,522,522',
'524,524,522,,524,,,,,,,,,524,524,,524,524,524,524,524,524,524,,,524',
'524,,,,,524,524,524,524,,,,,,,,,,,,,524,524,,524,524,524,524,524,524',
'524,524,524,524,524,534,534,524,,534,,,,,,,,,534,534,,534,534,534,534',
'534,534,534,,,534,534,,,,,534,534,534,534,,,,,,534,,,,,,,534,534,,534',
'534,534,534,534,534,534,534,534,534,534,535,535,534,,535,,,,,,,,,535',
'535,,535,535,535,535,535,535,535,,,535,535,,,,,535,535,535,535,,,,,',
',,,,,,,535,535,,535,535,535,535,535,535,535,535,535,535,535,584,584',
'535,,584,,,,,,,,,584,584,,584,584,584,584,584,584,584,,,584,584,,,,',
'584,584,584,584,,,,,,584,,,,,,,584,584,,584,584,584,584,584,584,584',
'584,584,584,584,585,585,584,,585,,,,,,,,,585,585,,585,585,585,585,585',
'585,585,,,585,585,,,,,585,585,585,585,,,,,,,,,,,,,585,585,,585,585,585',
'585,585,585,585,585,585,585,585,591,591,585,,591,,,,,,,,,591,591,,591',
'591,591,591,591,591,591,,,591,591,,,,,591,591,591,591,,,,,,,,,,,,,591',
'591,,591,591,591,591,591,591,591,591,591,591,591,593,593,591,,593,,',
',,,,,,593,593,,593,593,593,593,593,593,593,,,593,593,,,,,593,593,593',
'593,,,,,,593,,,,,,,593,593,,593,593,593,593,593,593,593,593,593,593',
'593,822,822,593,,822,,,,,,,,,822,822,,822,822,822,822,822,822,822,,',
'822,822,,,,,822,822,822,822,,,,,,,,,,,,,822,822,,822,822,822,822,822',
'822,822,822,822,822,822,946,946,822,,946,,,,,,,,,946,946,,946,946,946',
'946,946,946,946,,,946,946,,,,,946,946,946,946,,,,,,946,,,,,,,946,946',
',946,946,946,946,946,946,946,946,946,946,946,947,947,946,,947,,,,,,',
',,947,947,,947,947,947,947,947,947,947,,,947,947,,,,,947,947,947,947',
',,,,,,,,,,,,947,947,,947,947,947,947,947,947,947,947,947,947,947,,,947' ]
        racc_action_check = arr = ::Array.new(25031, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
    -2,    34,   nil,   234,   nil,   618,   -19, 22922, 23046,    -5,
    -1,    15,   117,   414,   291,   243,   nil,   125,   252,   900,
   186,   nil,   379,   506,   633,   381,    13,   760, 22407,   nil,
   900,  1027,  1154,   nil,   108,   541,   237,   261,  1294,  1421,
  1548,   176,   467,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
 22537,   nil,  1675,  1802,  1929,    29,  9932,  2056,  2183,   nil,
   nil,  2310,  2450,  2577,   nil, 23418,   nil,   nil,   nil,   nil,
   nil,  -102,   nil,   nil,   nil,   nil,   nil,   178,   220, 23531,
   nil,   nil,   nil,   429,  2704,   nil,   nil,  2831,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   359,   nil,  2971,
   nil,   nil,   nil,  3111,  3238,  3365,  3492,  3632,  3772,   nil,
   663,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil, 23644,   234,   nil,  3912,  4039,  4166,  4293,  4420,
  4547, 23818, 22792, 23879,  4674,  4801,  4928,   nil,   541,   -49,
   319,    61,   240,   328,  5068,  5195,   nil,   nil,  5322,   363,
  5449,  5576,  5703,  5830,  5957,  6084,  6211,  6338,  6465,  6592,
  6719,  6846,  6973,  7100,  7227,  7354,  7481,  7608,  7735,  7862,
  7989,  8116,  8243,  8370,  8497,  8624,   nil,   nil,   nil,  1294,
   nil,   329,   339,   nil,  8751,   389,  8878,   nil,   nil,   nil,
   nil,  9005,   nil,   nil, 23940, 24001, 24062,   383,  9132,  9259,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,  9386,   249,   745,
   390,  9526,   409,   438,   405,  9653,  9793,    73,   594,   497,
   196,   465,   440,   207,   nil,   478,   471,   nil,  9920,   nil,
   586,   505,   531,   633,   nil,   533,   nil, 10047,   nil, 10187,
    35,   nil,   502,  -103,   135,   537,   521,   262,   556,   nil,
   nil,   -22, 10580,   nil,   nil,   nil,   520,   545,   nil,   564,
   567,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  3097,   nil,
   nil,   nil,   648,   nil,   nil,   674,   806,    -7,    36, 10314,
 10441,   324,    63,   596,   -17,   668,   690,    37,   731,   nil,
   nil,   506,   704,   nil,   721,   nil,    65,   nil,   nil, 10568,
   -12,   122,   360,   384,   385,   436,   509,   590,   nil,   795,
   nil, 10695,   nil,   173,   nil,   326,   nil,   -23,   649,   362,
   nil,   653,   -50,   nil,   365,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   683, 23170,   nil,   nil,   nil, 23294,   688,   nil,   nil,   760,
   nil, 10822,   681,   nil,   691,   nil,   nil,  1421,   728,   731,
   668,   761, 10949,   nil,   nil,   nil, 21929,   757, 22011,   nil,
 11076, 11203, 11330,   nil,   nil,   nil,  1548,   nil,   nil, 24123,
 24184, 11457, 11584,   180, 11711, 11838, 11965,   115,   nil,  3238,
  3365,   232,   879,   785,   787,   821,   824,  2457,  2584,  5068,
  3492,  3632,  3772,  3912,  4039,  4166,  4293,  4420,  4547,  4674,
  1133,  1232,  4801,  4928,   633,   -34,   nil, 12092,   nil, 12219,
   nil, 12346,   nil,   nil, 22667, 22724, 22792,   -17,   nil,   771,
   nil,   nil,   785,   787,   nil,   nil, 12473,    59,   203,   832,
   nil,   nil, 12613,   833,   797,   nil,   nil,   799, 12753,   845,
 12880, 24245, 24306, 13007, 24367,   223,   848,   nil,   nil,   821,
   nil, 13134, 13261, 13388, 24428, 24489,  1675, 13515,   949,   951,
   871,   nil,   nil, 13642,   nil,   nil, 13769,   nil,   nil,   nil,
   nil, 13909, 14049,   874,   nil,  1000,   nil,   nil, 14176, 12765,
   nil,   772,   nil,   nil,   894,   nil,  3126,   nil,   859,  1198,
   nil,   nil, 14316,   977,   nil,   nil, 14456,   212,   227,   975,
   983, 14596,   nil, 14723, 24550, 24611, 14850,    40,   nil,   760,
   nil, 24672, 14977, 24733,   nil,   nil, 15104,   387, 15231,   nil,
  1166,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   -35,   nil,   nil,   nil,   864,
   nil,   nil,   nil,   nil,   nil, 15358,   866, 15485,   798,   203,
 15612, 15739,   900,   nil,   nil,   nil, 15866,   903,   nil, 15993,
   915,   nil,   268,   307, 22143, 22275,   927,   928,   506,   nil,
 16120,   nil,  1040,   nil, 16260,   897,   nil,   939, 16387,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil, 16527,   nil,   942,
 16654, 16781,  2310,   907,   nil,   nil,   947, 16921, 17061,   nil,
  1154,   -27, 17188,   914,   nil,   957,   228,   235,   962,   313,
   327,   963,   960,   972, 17315,  1802,   997,  1001,   240,  1056,
 17442,   nil,   nil,   376,   960,  1068,   nil,   nil,   945,   nil,
   958,   935,  1035,   963,   967,   nil,   nil,  1010,  3135,   nil,
   869,   nil,  1096,   nil,   nil,   nil,   nil,   nil,  1102,   nil,
  1103, 17569,  1020,    46,    52,    59,   102,  1021, 17696,  1027,
   nil,  1028,  1026, 17836,   416,   nil,   -25, 17976, 18116,  9665,
   463,   nil,   nil,  1075,   nil, 18243,   nil,   998,   999,   nil,
  1000,  1002,  1004,   nil,   996,   nil, 23757,  1043,  1050, 18383,
   nil,   nil,   nil,  1929,  1007, 18510, 18637, 18764,   nil,  2056,
   nil,  2183,   nil,   nil,  2446,   nil,  2573,   nil, 18891, 19018,
 19145,   315,   319,  2704,   nil,  1041,  1144,   nil,   nil,  1041,
   nil,  1026, 19272,   nil,  1053,  1161,  1072,   323,   nil,   nil,
   nil,  1196,   nil, 19412,  1081,  1125,   nil,   nil,   nil,   nil,
   nil,   nil, 24794,   nil,  1127,   nil,   nil,   nil,   nil,  1407,
  1215,   nil, 19539,  1220, 19679, 19819,   nil,   nil,    66,    56,
   988,   295,   nil,  1221,   nil,   nil,  1222,  1225,  1109,   nil,
   nil,   nil,   -32,   nil,   nil,   807, 14188,   nil,   842,   nil,
   406,   nil, 19946,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
  1127,  1113,   nil,  2831,   nil,  2971, 20073, 20200,   nil,   nil,
   nil, 20327,  1114,   nil,   nil,   nil, 20467,   nil,   nil,    72,
 20594,   nil,  1154,  1125,   nil,   nil,    78,   nil,  1249,  1250,
 20721, 20861,   nil,   nil, 21001,   nil,   nil,  1168,   nil,  1132,
   nil,   nil,  1133,  1134,  1139,  1137,   nil,   nil, 22721,   nil,
   nil, 21128,   nil, 21268,    91,  1112,  1221,    97,   nil,   nil,
 21408,   nil,   nil,   nil,   460, 21548,  1268,   nil,   nil,  1081,
   nil,   nil,   nil, 21688,  1273, 21815, 24855, 24916,    98,   899,
   nil,   nil,   nil,  1272,   nil,  1153,  1275,   nil,  1190,   108,
   120,   198,   202,   nil,   nil,   nil,   nil,   218 ]

racc_action_default = [
    -3,  -555,    -1,  -543,    -4,    -6,  -555,  -555,  -555,  -555,
  -555,  -555,  -555,  -555,  -277,   -37,   -38,  -555,  -555,   -43,
   -45,   -46,  -289,  -327,  -328,   -50,  -255,  -382,  -255,   -65,
   -10,   -69,   -76,   -78,  -555,  -457,  -555,  -555,  -555,  -555,
  -555,  -545,  -232,  -270,  -271,  -272,  -273,  -274,  -275,  -276,
  -533,  -279,  -555,  -554,  -525,  -297,  -554,  -555,  -555,  -302,
  -305,  -543,  -555,  -555,  -319,  -555,  -329,  -330,  -400,  -401,
  -402,  -403,  -404,  -554,  -407,  -554,  -554,  -554,  -554,  -554,
  -434,  -440,  -441,  -555,  -446,  -447,  -448,  -449,  -450,  -451,
  -452,  -453,  -454,  -455,  -456,  -459,  -460,  -555,    -2,  -544,
  -550,  -551,  -552,  -555,  -555,  -555,  -555,  -555,    -3,   -13,
  -555,  -105,  -106,  -107,  -108,  -109,  -110,  -111,  -114,  -115,
  -116,  -117,  -118,  -119,  -120,  -121,  -122,  -123,  -124,  -125,
  -126,  -127,  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,
  -136,  -137,  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,
  -146,  -147,  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,
  -156,  -157,  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,
  -166,  -167,  -168,  -169,  -170,  -171,  -172,  -173,  -174,  -175,
  -176,  -177,  -178,  -179,  -180,  -181,  -182,  -183,  -184,  -185,
  -186,  -187,  -555,   -18,  -112,   -10,  -555,  -555,  -555,  -554,
  -554,  -555,  -555,  -555,  -555,  -555,  -555,   -41,  -555,  -457,
  -555,  -277,  -555,  -555,   -10,  -555,   -42,  -224,  -555,  -555,
  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,
  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,  -555,
  -555,  -555,  -555,  -555,  -555,  -555,  -369,  -371,   -47,  -233,
  -248,  -262,  -262,  -252,  -555,  -263,  -555,  -289,  -327,  -328,
  -527,  -555,   -48,   -49,  -555,  -555,  -555,   -55,  -554,  -555,
  -296,  -375,  -383,  -385,   -63,  -381,   -64,  -555,  -543,   -11,
   -65,   -10,  -555,  -555,   -70,   -73,   -10,  -457,  -555,  -555,
  -277,  -292,  -545,  -555,  -331,  -382,  -555,   -75,  -555,   -80,
  -284,  -442,  -443,  -555,  -209,  -210,  -225,  -555,  -546,   -10,
  -545,  -234,  -545,  -547,  -547,  -555,  -555,  -547,  -555,  -298,
  -299,  -555,  -555,  -342,  -343,  -350,  -554,  -491,  -357,  -554,
  -554,  -368,  -490,  -492,  -493,  -494,  -495,  -496,  -555,  -509,
  -514,  -515,  -517,  -518,  -519,  -555,   -44,  -555,  -555,  -555,
  -555,  -543,  -555,  -544,  -457,  -555,  -555,  -277,  -555,  -498,
  -499,  -101,  -555,  -103,  -555,  -277,  -555,  -316,  -457,  -555,
  -105,  -106,  -143,  -144,  -160,  -165,  -172,  -175,  -322,  -555,
  -523,  -555,  -405,  -555,  -420,  -555,  -422,  -555,  -555,  -555,
  -412,  -555,  -555,  -418,  -555,  -433,  -435,  -436,  -437,  -438,
  -444,  -445,   968,    -5,  -553,   -19,   -20,   -21,   -22,   -23,
  -555,  -555,   -15,   -16,   -17,  -555,  -555,   -25,   -34,  -188,
  -263,  -555,  -555,   -26,   -35,   -36,   -27,  -190,  -555,  -555,
  -534,  -535,  -554,  -378,  -536,  -537,  -534,  -255,  -535,  -380,
  -539,  -540,  -554,  -534,  -535,   -33,  -198,   -39,   -40,  -555,
  -555,  -554,  -554,  -284,  -555,  -555,  -555,  -555,  -295,  -199,
  -200,  -201,  -202,  -203,  -204,  -205,  -206,  -211,  -212,  -213,
  -214,  -215,  -216,  -217,  -218,  -219,  -220,  -221,  -222,  -223,
  -226,  -227,  -228,  -229,  -555,  -554,  -249,  -555,  -250,  -555,
  -260,  -555,  -264,  -530,  -255,  -255,  -255,  -554,   -56,  -545,
  -243,  -244,  -262,  -262,  -256,  -257,  -555,  -554,  -554,  -555,
  -291,    -9,  -544,  -555,   -66,  -282,   -81,   -71,  -555,  -555,
  -554,  -555,  -555,  -554,  -555,  -284,  -555,  -442,  -443,   -77,
   -82,  -555,  -555,  -555,  -555,  -555,  -230,  -555,  -392,  -555,
  -555,  -235,  -236,  -549,  -548,  -238,  -549,  -287,  -288,  -526,
  -339,   -10,   -10,  -555,  -341,  -555,  -359,  -366,  -555,  -363,
  -364,  -555,  -367,  -491,  -555,  -500,  -555,  -502,  -504,  -508,
  -516,  -520,   -10,  -332,  -333,  -334,   -10,  -555,  -555,  -555,
  -555,   -10,  -387,  -554,  -555,  -555,  -554,  -284,  -311,  -101,
  -102,  -555,  -554,  -555,  -314,  -461,  -555,  -555,  -555,  -320,
  -489,  -324,  -541,  -542,  -545,  -406,  -421,  -424,  -425,  -427,
  -408,  -423,  -409,  -410,  -411,  -555,  -414,  -416,  -417,  -555,
  -439,    -7,   -14,  -113,   -24,  -555,  -269,  -555,  -285,  -286,
  -555,  -555,   -59,  -241,  -242,  -376,  -555,   -61,  -379,  -555,
   -57,  -377,  -534,  -535,  -534,  -535,  -555,  -555,  -188,  -294,
  -555,  -353,  -555,  -355,   -10,  -262,  -261,  -265,  -555,  -528,
  -529,   -51,  -372,   -52,  -373,   -53,  -374,   -10,  -239,  -555,
  -245,  -247,   -43,  -555,  -254,  -258,  -555,   -10,   -10,  -290,
   -12,   -66,  -555,   -74,   -79,  -555,  -534,  -535,  -554,  -538,
  -283,  -555,  -555,  -554,  -555,  -197,  -207,  -208,  -555,  -554,
  -554,  -280,  -281,  -547,  -555,  -555,  -340,  -351,  -555,  -358,
  -554,  -352,  -555,  -554,  -554,  -510,  -497,  -555,  -555,  -507,
  -554,  -335,  -554,  -303,  -336,  -337,  -338,  -306,  -555,  -309,
  -555,  -555,  -555,  -534,  -535,  -538,  -283,  -555,  -555,  -101,
  -104,  -538,  -555,   -10,  -555,  -463,  -555,   -10,   -10,  -489,
  -555,  -466,  -467,  -469,  -470,  -472,  -473,  -522,  -522,  -478,
  -480,  -480,  -480,  -488,  -491,  -512,  -555,  -555,  -555,   -10,
  -413,  -415,  -419,  -189,  -267,  -555,  -555,  -555,   -30,  -193,
   -31,  -194,   -60,   -32,  -195,   -62,  -196,   -58,  -555,  -555,
  -555,  -286,  -285,  -231,  -354,  -555,  -555,  -251,  -266,  -555,
  -240,  -262,  -555,  -259,  -555,  -555,   -72,  -285,  -286,   -83,
  -293,  -554,  -348,   -10,  -393,  -554,  -394,  -395,  -237,  -344,
  -345,  -365,  -555,  -284,  -555,  -361,  -362,  -501,  -503,  -506,
  -555,  -346,  -555,  -555,   -10,   -10,  -308,  -310,  -555,  -285,
   -93,  -555,  -285,  -555,  -462,  -317,  -555,  -555,  -545,  -465,
  -468,  -471,  -555,  -476,  -477,  -555,  -555,  -484,  -555,  -486,
  -555,  -487,  -555,  -325,  -524,  -426,  -429,  -430,  -431,  -432,
  -555,  -268,   -28,  -191,   -29,  -192,  -555,  -555,  -356,  -370,
   -54,  -246,  -262,  -384,  -386,    -8,   -10,  -399,  -349,  -555,
  -555,  -397,  -283,  -554,  -505,  -300,  -555,  -301,  -555,  -555,
  -555,   -10,  -312,  -315,   -10,  -321,  -323,  -555,  -474,  -522,
  -521,  -479,  -480,  -480,  -480,  -555,  -513,  -511,  -489,  -428,
  -253,  -555,  -398,   -10,  -457,  -555,  -555,  -277,  -396,  -360,
   -10,  -304,  -307,  -265,  -554,   -10,  -555,  -464,  -475,  -555,
  -482,  -483,  -485,   -10,  -392,  -554,  -555,  -555,  -284,  -554,
  -388,  -389,  -390,  -555,  -318,  -480,  -555,  -391,  -555,  -534,
  -535,  -538,  -283,  -347,  -313,  -481,  -326,  -285 ]

clist = [
'13,315,307,699,323,378,498,114,114,539,250,250,250,432,437,442,5,208',
'208,396,284,488,208,208,208,659,331,102,294,294,13,288,288,572,576,529',
'10,98,12,557,748,312,560,562,251,251,251,366,565,109,194,580,208,208',
'117,117,216,208,208,294,294,208,355,364,99,114,10,715,12,418,425,760',
'707,267,274,276,494,495,496,114,542,545,659,2,549,102,280,297,252,252',
'252,606,763,723,727,352,759,616,359,13,1,916,590,208,208,208,208,13',
'13,347,348,403,282,351,641,5,564,710,385,387,317,714,394,409,5,885,830',
'248,262,263,193,360,396,411,499,10,668,12,676,504,207,654,811,10,10',
'12,12,428,429,853,854,380,316,667,319,405,406,407,408,320,358,594,762',
'677,678,833,662,664,666,379,601,309,349,834,350,310,835,738,935,743',
'346,346,916,598,346,904,369,312,312,747,600,410,114,766,918,13,208,208',
'208,208,208,321,441,550,208,208,208,659,713,247,485,656,507,656,13,208',
'508,950,422,422,760,731,815,889,382,383,346,346,346,346,674,10,389,12',
'417,423,426,615,392,865,768,763,445,769,848,759,909,908,912,828,10,',
'12,526,,683,250,250,,,,432,437,,,250,,,208,208,553,540,488,541,,,565',
'208,728,719,,13,294,,,288,13,530,,,502,251,331,,,294,,,288,251,,938',
'102,,,,,,,13,,762,511,,,,,10,,12,,,10,771,12,,271,275,447,448,14,740',
',688,503,252,693,280,457,512,517,707,280,252,717,10,688,12,910,208,208',
'910,759,599,759,963,759,902,943,,14,290,290,513,715,641,294,,519,364',
',782,,501,505,102,785,951,,787,,208,509,,,579,893,,,760,595,,357,365',
'656,656,688,,,646,647,577,578,,,688,,,,957,763,114,,,759,114,,659,913',
'806,914,,,821,797,,825,826,565,14,,,,,,759,,14,14,312,312,,,843,,,,846',
'847,,441,208,208,622,,,,623,117,,669,,117,,,,,,685,,818,692,346,346',
',,,,,632,762,,,,637,,,,,640,,,,,,,,,,,,955,,,604,,208,530,312,,,312',
'13,,,294,,,288,,208,,441,208,901,14,,,680,,,732,,,737,441,,208,,894',
'742,661,663,665,14,,,,10,,12,13,13,655,,294,,,711,,,,,,,767,,,,,797',
'312,13,816,312,923,13,857,859,861,312,13,930,208,,441,208,10,10,12,12',
'441,208,271,35,275,208,,208,,795,724,724,,936,656,929,,10,703,12,14',
'10,,12,290,14,10,,12,,745,331,,35,287,287,,290,310,208,208,,952,,,208',
',,14,,,,956,,656,,,744,,,920,,,13,,354,368,,368,698,,283,,,,,13,778',
'780,,530,,,783,,,13,13,294,,,288,809,,,,,,,10,294,12,35,288,,,,,365',
',35,35,10,,12,863,,,,,,,10,10,12,12,,,346,,746,422,,,,,,,940,941,942',
'634,,,,801,638,208,,,,634,13,,,,13,13,,,,,838,,,,,688,,,,,,,,114,,844',
'13,845,,965,,849,,208,208,,10,,12,,10,10,12,12,,,35,841,,,,634,634,634',
',,,,,,,,10,,12,35,907,,,,13,872,874,,,,,,,441,,882,,,416,,,,,208,318',
'13,13,,,,,,,14,,,,283,,290,10,,12,,,,,,,,,,346,,,,724,,,,,10,10,12,12',
',35,,,,287,35,14,14,,896,,13,294,365,,925,,287,958,,,,,,,,13,14,35,13',
',14,,,,,14,,283,,,,,283,,,,10,13,12,,928,,,,13,,,312,,13,,10,,12,10',
',12,13,,208,,441,,,,,,,,346,,,,10,,12,,368,,,10,,12,,,10,,12,,420,424',
',,10,,12,,,14,,,,634,,,638,,634,,,,14,,,,,,,,,,14,14,,,,290,,,,,,,,',
',,,290,,,,,,,,,490,,492,,,,,493,,,,,,,,,,,,,,,,,,,,,,324,,,,,,,,,,,14',
',,,14,14,384,,386,386,390,393,386,,,,,,,,,,,,,869,14,,,,,,,35,,,,,,287',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,14,,35,35,,,,,,368,,,,,,,,,,,,14,14,35',
'217,,,35,249,249,249,,35,,,,,,,,704,705,,,304,305,306,,,,,,,,,,,626',
',249,249,720,,,,722,,,,,730,,,14,,,,927,,,,,,,,,,,14,,,14,,,,,,,,,,',
',,,35,,,,,14,,,,,,,14,35,,,,14,657,,318,,660,35,35,14,500,,287,,,,,',
',,,673,796,,287,,,,,,,,,,,799,,,,,,,,,,804,805,,,,,,,,,,,,657,,,318',
',,,,,,556,,,556,556,35,,,,35,35,,,419,249,427,249,249,,,,446,,,,,,,',
',,35,,,,217,,459,460,461,462,463,464,465,466,467,468,469,470,471,472',
'473,474,475,476,477,478,479,480,481,482,483,484,,,,,,,870,,249,,249',
'774,35,,,249,,,,,,,249,249,,,,,,,,249,,35,35,,,,,,,,798,633,,,,,,,888',
',,633,657,318,,,,,,536,,,,,,,,,,898,899,,,,,,,,,,,,814,35,,,,924,,,',
',,,651,,,,35,,,35,,633,633,633,651,,,,,,,420,,,651,651,,,35,922,,,,',
',35,,,,,35,,,,934,851,,,35,,,,,,,,,,,,,,,,,871,944,,,,,,,949,,,,,953',
',,249,,,,,,,,,,,420,,,,,,,,,,,,,,,,,,,249,249,,446,648,427,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,249,,249,,249,917,,,,,,,,,,,,,,672,,,,,318,,,,',
',,,,249,,,249,,,,,,,933,695,696,697,,,,,,,,,,249,,633,249,,,,633,933',
',,,,812,817,,,,,,,,,,556,,,556,556,,,,,,812,,812,,,,249,,,249,,,,,,249',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,773,,249,,,779,781,,,293,293,784,,,786',
',293,293,293,,,,,,,793,,,,,,,293,249,,,,,,,293,293,,887,,249,249,891',
',,,,,,,,,,,,,,,,,,,,,,,,,,,249,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,249,,,',
',,,,,,,,,,,,,,,556,,,,,249,,,,,,,,,,,,,,,,,,,,249,873,875,,,,,,,,,,',
'779,781,784,812,,,,,,,,,,,249,,,,812,,,,,,,,,,,,,,,293,,293,293,293',
'293,293,293,293,293,293,293,293,293,293,293,293,293,293,293,293,293',
'293,293,293,293,293,293,,,,,,,,,293,,293,,,249,,293,,,,,,,,,,,,875,873',
',,,249,,,,,,,293,,,,,,,,,,,,249,293,,,,,,,,,293,,,,,,,,,,,249,,,,,,',
',,,,,,,,,,,,,,,,,249,,,,,,,,,,,,,,,,,,,,,,,,,,,293,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,293,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,293,293,293,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,293,,293,,293,,,,,,,,,,',
',,,,,,,,,,,,,,,,293,,,,,,,,,,,,,293,293,293,,,,,,,,,,293,,,293,,,,,',
',,,,,,293,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,293,,293,,,,,,,,,,,,293,,,,,,,,,,,293,,,,,,,,293,,,,,,,,,,,',
'293,293,,,,,,,,,,,293,,,,,,,,,,,,293,,,,,,293,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,293,,,,,,,,,,,,,,,,,,,,,,,,293,,,,,,,,,,,,,,,,,,,,293,,,,,,',
',,,,,,293,293,293,,,,,,,,,,,,293,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,293,,,,,,,,,,,,,,293,293,,,,293,,,,,,,,,293',
',,,,,,,,,293,,,,,,,,,,,,,,,,,,,,,293' ]
        racc_goto_table = arr = ::Array.new(2674, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'21,22,51,10,104,47,35,48,48,8,29,29,29,33,33,33,7,21,21,47,41,61,21',
'21,21,154,107,83,52,52,21,21,21,77,77,43,17,4,20,109,84,29,109,109,54',
'54,54,46,145,14,14,80,21,21,50,50,18,21,21,52,52,21,21,21,6,48,17,147',
'20,24,24,110,106,34,34,34,33,33,33,48,55,55,154,2,55,83,38,42,56,56',
'56,129,141,79,79,4,108,129,17,21,1,151,45,21,21,21,21,21,21,16,16,5',
'39,16,60,7,148,111,126,126,56,111,126,7,7,12,78,31,31,31,15,19,47,27',
'58,17,59,20,63,64,26,36,11,17,17,20,20,22,22,142,142,72,73,36,74,16',
'16,16,16,76,81,82,111,36,36,11,60,60,60,85,86,87,88,89,90,26,91,92,93',
'94,26,26,151,95,26,96,97,29,29,98,99,2,48,100,101,21,21,21,21,21,21',
'102,48,103,21,21,21,154,105,112,113,62,115,62,21,21,116,117,54,54,110',
'118,119,120,124,125,26,26,26,26,62,17,127,20,18,18,18,128,130,131,132',
'141,18,133,135,108,137,139,144,149,17,,20,51,,43,29,29,,,,33,33,,,29',
',,21,21,104,51,61,51,,,145,21,80,145,,21,52,,,21,21,41,,,54,54,107,',
',52,,,21,54,,142,83,,,,,,,21,,111,4,,,,,17,,20,,,17,129,20,,57,57,26',
'26,23,45,,33,56,56,33,38,26,6,42,106,38,56,148,17,33,20,108,21,21,108',
'108,46,108,78,108,79,84,,23,23,23,39,147,60,52,,39,21,,35,,31,31,83',
'35,11,,35,,21,31,,,4,111,,,110,83,,23,23,62,62,33,,,22,22,16,16,,,33',
',,,10,141,48,,,108,48,,154,111,43,111,,,109,61,,109,109,145,23,,,,,',
'108,,23,23,29,29,,,8,,,,8,8,,48,21,21,14,,,,14,50,,51,,50,,,,,,22,,55',
'22,26,26,,,,,,34,111,,,,34,,,,,34,,,,,,,,,,,,111,,,26,,21,41,29,,,29',
'21,,,52,,,21,,21,,48,21,77,23,,,7,,,22,,,22,48,,21,,145,22,34,34,34',
'23,,,,17,,20,21,21,56,,52,,,21,,,,,,,51,,,,,61,29,21,24,29,77,21,143',
'143,143,29,21,77,21,,48,21,17,17,20,20,48,21,57,44,57,21,,21,,104,83',
'83,,8,62,109,,17,56,20,23,17,,20,23,23,17,,20,,83,107,,44,44,44,,23',
'26,21,21,,80,,,21,,,23,,,,8,,62,,,16,,,61,,,21,,44,44,,44,26,,9,,,,',
'21,18,18,,41,,,18,,,21,21,52,,,21,41,,,,,,,17,52,20,44,21,,,,,23,,44',
'44,17,,20,47,,,,,,,17,17,20,20,,,26,,26,54,,,,,,,143,143,143,57,,,,56',
'57,21,,,,57,21,,,,21,21,,,,,54,,,,,33,,,,,,,,48,,83,21,83,,143,,83,',
'21,21,,17,,20,,17,17,20,20,,,44,16,,,,57,57,57,,,,,,,,,17,,20,44,51',
',,,21,18,18,,,,,,,48,,54,,,9,,,,,21,25,21,21,,,,,,,23,,,,9,,23,17,,20',
',,,,,,,,,26,,,,83,,,,,17,17,20,20,,44,,,,44,44,23,23,,16,,21,52,23,',
'21,,44,22,,,,,,,,21,23,44,21,,23,,,,,23,,9,,,,,9,,,,17,21,20,,17,,,',
'21,,,29,,21,,17,,20,17,,20,21,,21,,48,,,,,,,,26,,,,17,,20,,44,,,17,',
'20,,,17,,20,,25,25,,,17,,20,,,23,,,,57,,,57,,57,,,,23,,,,,,,,,,23,23',
',,,23,,,,,,,,,,,,23,,,,,,,,,25,,25,,,,,25,,,,,,,,,,,,,,,,,,,,,,53,,',
',,,,,,,,23,,,,23,23,53,,53,53,53,53,53,,,,,,,,,,,,,23,23,,,,,,,44,,',
',,,44,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,23,,44,44,,,,,,44,,,,,,,,,,,,23',
'23,44,28,,,44,28,28,28,,44,,,,,,,,9,9,,,28,28,28,,,,,,,,,,,25,,28,28',
'9,,,,9,,,,,9,,,23,,,,23,,,,,,,,,,,23,,,23,,,,,,,,,,,,,,44,,,,,23,,,',
',,,23,44,,,,23,25,,25,,25,44,44,23,53,,44,,,,,,,,,25,9,,44,,,,,,,,,',
',9,,,,,,,,,,9,9,,,,,,,,,,,,25,,,25,,,,,,,53,,,53,53,44,,,,44,44,,,28',
'28,28,28,28,,,,28,,,,,,,,,,44,,,,28,,28,28,28,28,28,28,28,28,28,28,28',
'28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,,,,,,,9,,28,,28,25,44,',
',28,,,,,,,28,28,,,,,,,,28,,44,44,,,,,,,,25,53,,,,,,,9,,,53,25,25,,,',
',,28,,,,,,,,,,9,9,,,,,,,,,,,,25,44,,,,44,,,,,,,53,,,,44,,,44,,53,53',
'53,53,,,,,,,25,,,53,53,,,44,9,,,,,,44,,,,,44,,,,9,25,,,44,,,,,,,,,,',
',,,,,,25,9,,,,,,,9,,,,,9,,,28,,,,,,,,,,,25,,,,,,,,,,,,,,,,,,,28,28,',
'28,28,28,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,28,,28,,28,25,,,,,,,,,,,,,,28',
',,,,25,,,,,,,,,28,,,28,,,,,,,25,28,28,28,,,,,,,,,,28,,53,28,,,,53,25',
',,,,53,53,,,,,,,,,,53,,,53,53,,,,,,53,,53,,,,28,,,28,,,,,,28,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,28,,28,,,28,28,,,37,37,28,,,28,,37,37,37,,',
',,,,28,,,,,,,37,28,,,,,,,37,37,,53,,28,28,53,,,,,,,,,,,,,,,,,,,,,,,',
',,,,28,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,28,,,,,,,,,,,,,,,,,,,53,,,,,28',
',,,,,,,,,,,,,,,,,,,28,28,28,,,,,,,,,,,28,28,28,53,,,,,,,,,,,28,,,,53',
',,,,,,,,,,,,,,37,,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37',
'37,37,37,37,37,37,37,37,37,,,,,,,,,37,,37,,,28,,37,,,,,,,,,,,,28,28',
',,,28,,,,,,,37,,,,,,,,,,,,28,37,,,,,,,,,37,,,,,,,,,,,28,,,,,,,,,,,,',
',,,,,,,,,,,28,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,37,37',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,37,,37,,,,,,,,,,,,,,,,,,,,,,,,,,,37',
',,,,,,,,,,,,37,37,37,,,,,,,,,,37,,,37,,,,,,,,,,,,37,,,,,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,37,,,,,,,,,,,',
'37,,,,,,,,,,,37,,,,,,,,37,,,,,,,,,,,,37,37,,,,,,,,,,,37,,,,,,,,,,,,37',
',,,,,37,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,,,,,,,,,,,37,',
',,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,37,37,37,,,,,,,,,,,,37,,,,,,,,,,,,',
',,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,37,,,,,,,,,,,,,,37,37',
',,,37,,,,,,,,,37,,,,,,,,,,37,,,,,,,,,,,,,,,,,,,,,37' ]
        racc_goto_check = arr = ::Array.new(2674, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_goto_pointer = [
   nil,   100,    83,   nil,    34,    12,    61,    16,  -300,   632,
  -535,  -557,  -686,   nil,    42,   122,    52,    36,    38,    69,
    38,     0,   -52,   330,  -127,   779,   123,    24,  1149,   -12,
   nil,   105,   nil,  -188,    47,  -261,  -344,  1752,    56,    82,
   nil,   -11,    55,  -263,   594,  -260,   -16,   -60,     0,   nil,
    47,   -39,    -3,  1006,    22,  -233,    66,   300,  -134,  -363,
  -328,  -231,  -276,  -368,  -130,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    86,    98,    99,   nil,   103,  -314,  -594,  -484,
  -301,    98,  -205,    24,  -560,   104,  -209,   120,   113,  -550,
   114,  -551,  -411,  -724,  -415,  -184,  -660,   122,  -410,  -188,
  -408,  -669,   145,  -118,   -52,  -351,  -483,   -30,  -504,  -287,
  -529,  -438,   189,   -36,   nil,   -60,   -57,  -717,  -361,  -478,
  -592,   nil,   nil,   nil,   153,   152,    43,   155,  -152,  -292,
   160,  -529,  -368,  -366,   nil,  -505,   nil,  -606,   nil,  -605,
   nil,  -508,  -608,  -183,  -608,  -290,   nil,  -492,  -222,  -469,
   nil,  -757,   nil,   nil,  -464 ]

racc_goto_default = [
   nil,   nil,   nil,     3,   nil,     4,   353,   279,   nil,   538,
   nil,   831,   nil,   278,   nil,   nil,   nil,   212,    16,    11,
   213,   303,   nil,   211,   nil,   255,    15,   nil,    19,    20,
    21,   nil,    25,   691,   nil,   nil,   nil,    26,    29,   nil,
    31,    34,    33,   nil,   209,   363,   nil,   116,   435,   115,
    69,   nil,    42,   311,   313,   nil,   314,   433,   nil,   nil,
   635,   486,   253,   nil,   nil,   269,    43,    44,    45,    46,
    47,    48,    49,   nil,   270,    55,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   573,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   326,   325,   709,   328,   nil,
   329,   330,   nil,   nil,   439,   nil,   nil,   nil,   nil,   nil,
   nil,    68,    70,    71,    72,   nil,   nil,   nil,   nil,   611,
   nil,   nil,   nil,   nil,   395,   750,   753,   758,   755,   756,
   757,   911,   nil,   nil,   761,   337,   332,   339,   nil,   567,
   568,   765,   342,   345,   260 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 143, :_reduce_none,
  2, 144, :_reduce_2,
  0, 145, :_reduce_3,
  1, 145, :_reduce_4,
  3, 145, :_reduce_5,
  1, 147, :_reduce_none,
  4, 147, :_reduce_7,
  4, 150, :_reduce_8,
  2, 151, :_reduce_9,
  0, 155, :_reduce_10,
  1, 155, :_reduce_11,
  3, 155, :_reduce_12,
  0, 169, :_reduce_13,
  4, 149, :_reduce_14,
  3, 149, :_reduce_15,
  3, 149, :_reduce_none,
  3, 149, :_reduce_17,
  2, 149, :_reduce_18,
  3, 149, :_reduce_19,
  3, 149, :_reduce_20,
  3, 149, :_reduce_21,
  3, 149, :_reduce_22,
  3, 149, :_reduce_23,
  4, 149, :_reduce_none,
  3, 149, :_reduce_25,
  3, 149, :_reduce_26,
  3, 149, :_reduce_27,
  6, 149, :_reduce_none,
  6, 149, :_reduce_none,
  5, 149, :_reduce_30,
  5, 149, :_reduce_none,
  5, 149, :_reduce_none,
  3, 149, :_reduce_none,
  3, 149, :_reduce_34,
  3, 149, :_reduce_35,
  3, 149, :_reduce_36,
  1, 149, :_reduce_none,
  1, 168, :_reduce_none,
  3, 168, :_reduce_39,
  3, 168, :_reduce_40,
  2, 168, :_reduce_41,
  2, 168, :_reduce_42,
  1, 168, :_reduce_none,
  1, 158, :_reduce_none,
  1, 160, :_reduce_none,
  1, 160, :_reduce_none,
  2, 160, :_reduce_47,
  2, 160, :_reduce_48,
  2, 160, :_reduce_49,
  1, 172, :_reduce_none,
  4, 172, :_reduce_none,
  4, 172, :_reduce_none,
  4, 172, :_reduce_none,
  4, 177, :_reduce_none,
  2, 171, :_reduce_55,
  3, 171, :_reduce_none,
  4, 171, :_reduce_57,
  5, 171, :_reduce_none,
  4, 171, :_reduce_59,
  5, 171, :_reduce_none,
  4, 171, :_reduce_61,
  5, 171, :_reduce_none,
  2, 171, :_reduce_63,
  2, 171, :_reduce_64,
  1, 161, :_reduce_65,
  3, 161, :_reduce_66,
  1, 181, :_reduce_67,
  3, 181, :_reduce_68,
  1, 180, :_reduce_69,
  2, 180, :_reduce_70,
  3, 180, :_reduce_71,
  5, 180, :_reduce_none,
  2, 180, :_reduce_73,
  4, 180, :_reduce_none,
  2, 180, :_reduce_75,
  1, 180, :_reduce_76,
  3, 180, :_reduce_none,
  1, 183, :_reduce_78,
  3, 183, :_reduce_79,
  2, 182, :_reduce_80,
  3, 182, :_reduce_81,
  1, 185, :_reduce_none,
  3, 185, :_reduce_none,
  1, 184, :_reduce_84,
  4, 184, :_reduce_85,
  3, 184, :_reduce_86,
  3, 184, :_reduce_none,
  3, 184, :_reduce_none,
  3, 184, :_reduce_none,
  2, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 159, :_reduce_92,
  4, 159, :_reduce_93,
  4, 159, :_reduce_94,
  3, 159, :_reduce_95,
  3, 159, :_reduce_96,
  3, 159, :_reduce_97,
  3, 159, :_reduce_98,
  2, 159, :_reduce_99,
  1, 159, :_reduce_none,
  1, 187, :_reduce_none,
  2, 188, :_reduce_102,
  1, 188, :_reduce_103,
  3, 188, :_reduce_104,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_none,
  1, 189, :_reduce_108,
  1, 189, :_reduce_109,
  1, 156, :_reduce_110,
  1, 156, :_reduce_none,
  1, 157, :_reduce_112,
  3, 157, :_reduce_113,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 190, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  1, 191, :_reduce_none,
  3, 170, :_reduce_188,
  5, 170, :_reduce_189,
  3, 170, :_reduce_190,
  6, 170, :_reduce_191,
  6, 170, :_reduce_192,
  5, 170, :_reduce_193,
  5, 170, :_reduce_none,
  5, 170, :_reduce_none,
  5, 170, :_reduce_none,
  4, 170, :_reduce_none,
  3, 170, :_reduce_none,
  3, 170, :_reduce_199,
  3, 170, :_reduce_200,
  3, 170, :_reduce_201,
  3, 170, :_reduce_202,
  3, 170, :_reduce_203,
  3, 170, :_reduce_204,
  3, 170, :_reduce_205,
  3, 170, :_reduce_206,
  4, 170, :_reduce_207,
  4, 170, :_reduce_208,
  2, 170, :_reduce_209,
  2, 170, :_reduce_210,
  3, 170, :_reduce_211,
  3, 170, :_reduce_212,
  3, 170, :_reduce_213,
  3, 170, :_reduce_214,
  3, 170, :_reduce_215,
  3, 170, :_reduce_216,
  3, 170, :_reduce_217,
  3, 170, :_reduce_218,
  3, 170, :_reduce_219,
  3, 170, :_reduce_220,
  3, 170, :_reduce_221,
  3, 170, :_reduce_222,
  3, 170, :_reduce_223,
  2, 170, :_reduce_224,
  2, 170, :_reduce_225,
  3, 170, :_reduce_226,
  3, 170, :_reduce_227,
  3, 170, :_reduce_228,
  3, 170, :_reduce_229,
  3, 170, :_reduce_230,
  5, 170, :_reduce_231,
  1, 170, :_reduce_none,
  1, 167, :_reduce_none,
  1, 164, :_reduce_234,
  2, 164, :_reduce_235,
  2, 164, :_reduce_236,
  4, 164, :_reduce_237,
  2, 164, :_reduce_238,
  3, 199, :_reduce_239,
  2, 201, :_reduce_none,
  1, 202, :_reduce_241,
  1, 202, :_reduce_none,
  1, 200, :_reduce_243,
  1, 200, :_reduce_none,
  2, 200, :_reduce_245,
  4, 200, :_reduce_246,
  2, 200, :_reduce_247,
  1, 173, :_reduce_248,
  2, 173, :_reduce_249,
  2, 173, :_reduce_250,
  4, 173, :_reduce_251,
  1, 173, :_reduce_252,
  4, 205, :_reduce_none,
  1, 205, :_reduce_none,
  0, 207, :_reduce_255,
  2, 176, :_reduce_256,
  1, 206, :_reduce_none,
  2, 206, :_reduce_258,
  3, 206, :_reduce_259,
  2, 204, :_reduce_260,
  2, 203, :_reduce_261,
  0, 203, :_reduce_262,
  1, 196, :_reduce_263,
  2, 196, :_reduce_264,
  3, 196, :_reduce_265,
  4, 196, :_reduce_266,
  3, 166, :_reduce_267,
  4, 166, :_reduce_268,
  2, 166, :_reduce_269,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  1, 194, :_reduce_none,
  0, 229, :_reduce_279,
  4, 194, :_reduce_280,
  4, 194, :_reduce_281,
  3, 194, :_reduce_282,
  3, 194, :_reduce_283,
  2, 194, :_reduce_284,
  4, 194, :_reduce_285,
  4, 194, :_reduce_286,
  3, 194, :_reduce_287,
  3, 194, :_reduce_288,
  1, 194, :_reduce_289,
  4, 194, :_reduce_290,
  3, 194, :_reduce_291,
  1, 194, :_reduce_292,
  5, 194, :_reduce_293,
  4, 194, :_reduce_294,
  3, 194, :_reduce_295,
  2, 194, :_reduce_296,
  1, 194, :_reduce_none,
  2, 194, :_reduce_298,
  2, 194, :_reduce_299,
  6, 194, :_reduce_300,
  6, 194, :_reduce_301,
  0, 230, :_reduce_302,
  0, 231, :_reduce_303,
  7, 194, :_reduce_304,
  0, 232, :_reduce_305,
  0, 233, :_reduce_306,
  7, 194, :_reduce_307,
  5, 194, :_reduce_308,
  4, 194, :_reduce_309,
  5, 194, :_reduce_310,
  0, 234, :_reduce_311,
  0, 235, :_reduce_312,
  9, 194, :_reduce_313,
  0, 236, :_reduce_314,
  6, 194, :_reduce_315,
  0, 237, :_reduce_316,
  0, 238, :_reduce_317,
  8, 194, :_reduce_318,
  0, 239, :_reduce_319,
  0, 240, :_reduce_320,
  6, 194, :_reduce_321,
  0, 241, :_reduce_322,
  6, 194, :_reduce_323,
  0, 242, :_reduce_324,
  0, 243, :_reduce_325,
  9, 194, :_reduce_326,
  1, 194, :_reduce_327,
  1, 194, :_reduce_328,
  1, 194, :_reduce_329,
  1, 194, :_reduce_none,
  1, 163, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  1, 219, :_reduce_none,
  2, 219, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  1, 221, :_reduce_none,
  2, 218, :_reduce_339,
  3, 244, :_reduce_340,
  2, 244, :_reduce_341,
  1, 244, :_reduce_none,
  1, 244, :_reduce_none,
  3, 245, :_reduce_344,
  3, 245, :_reduce_345,
  1, 220, :_reduce_346,
  5, 220, :_reduce_347,
  1, 153, :_reduce_none,
  2, 153, :_reduce_349,
  1, 247, :_reduce_350,
  3, 247, :_reduce_351,
  3, 248, :_reduce_352,
  1, 178, :_reduce_none,
  2, 178, :_reduce_354,
  1, 178, :_reduce_355,
  3, 178, :_reduce_356,
  1, 249, :_reduce_357,
  2, 251, :_reduce_358,
  1, 251, :_reduce_359,
  6, 246, :_reduce_360,
  4, 246, :_reduce_361,
  4, 246, :_reduce_362,
  2, 246, :_reduce_363,
  2, 246, :_reduce_364,
  4, 246, :_reduce_365,
  2, 246, :_reduce_366,
  2, 246, :_reduce_367,
  1, 246, :_reduce_368,
  0, 255, :_reduce_369,
  5, 254, :_reduce_370,
  2, 174, :_reduce_371,
  4, 174, :_reduce_none,
  4, 174, :_reduce_none,
  4, 174, :_reduce_none,
  2, 217, :_reduce_375,
  4, 217, :_reduce_376,
  4, 217, :_reduce_377,
  3, 217, :_reduce_378,
  4, 217, :_reduce_379,
  3, 217, :_reduce_380,
  2, 217, :_reduce_381,
  1, 217, :_reduce_382,
  0, 257, :_reduce_383,
  5, 216, :_reduce_384,
  0, 258, :_reduce_385,
  5, 216, :_reduce_386,
  0, 260, :_reduce_387,
  6, 222, :_reduce_388,
  1, 259, :_reduce_389,
  1, 259, :_reduce_none,
  6, 152, :_reduce_391,
  0, 152, :_reduce_392,
  1, 261, :_reduce_393,
  1, 261, :_reduce_none,
  1, 261, :_reduce_none,
  2, 262, :_reduce_396,
  1, 262, :_reduce_397,
  2, 154, :_reduce_398,
  1, 154, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 209, :_reduce_403,
  1, 265, :_reduce_none,
  2, 265, :_reduce_405,
  3, 266, :_reduce_406,
  1, 266, :_reduce_407,
  3, 210, :_reduce_408,
  3, 211, :_reduce_409,
  3, 212, :_reduce_410,
  3, 212, :_reduce_411,
  1, 269, :_reduce_412,
  3, 269, :_reduce_413,
  1, 270, :_reduce_414,
  2, 270, :_reduce_415,
  3, 213, :_reduce_416,
  3, 213, :_reduce_417,
  1, 272, :_reduce_418,
  3, 272, :_reduce_419,
  1, 267, :_reduce_420,
  2, 267, :_reduce_421,
  1, 268, :_reduce_422,
  2, 268, :_reduce_423,
  1, 271, :_reduce_424,
  0, 274, :_reduce_425,
  3, 271, :_reduce_426,
  0, 275, :_reduce_427,
  4, 271, :_reduce_428,
  1, 273, :_reduce_429,
  1, 273, :_reduce_430,
  1, 273, :_reduce_431,
  1, 273, :_reduce_none,
  2, 192, :_reduce_433,
  1, 192, :_reduce_434,
  1, 276, :_reduce_none,
  1, 276, :_reduce_none,
  1, 276, :_reduce_none,
  1, 276, :_reduce_none,
  3, 264, :_reduce_439,
  1, 263, :_reduce_440,
  1, 263, :_reduce_441,
  2, 263, :_reduce_442,
  2, 263, :_reduce_443,
  2, 263, :_reduce_444,
  2, 263, :_reduce_445,
  1, 186, :_reduce_446,
  1, 186, :_reduce_447,
  1, 186, :_reduce_448,
  1, 186, :_reduce_449,
  1, 186, :_reduce_450,
  1, 186, :_reduce_451,
  1, 186, :_reduce_452,
  1, 186, :_reduce_453,
  1, 186, :_reduce_454,
  1, 186, :_reduce_455,
  1, 186, :_reduce_456,
  1, 214, :_reduce_457,
  1, 162, :_reduce_458,
  1, 165, :_reduce_459,
  1, 165, :_reduce_none,
  1, 224, :_reduce_461,
  3, 224, :_reduce_462,
  2, 224, :_reduce_463,
  4, 226, :_reduce_464,
  2, 226, :_reduce_465,
  1, 278, :_reduce_none,
  1, 278, :_reduce_none,
  2, 279, :_reduce_468,
  1, 279, :_reduce_469,
  1, 280, :_reduce_470,
  2, 281, :_reduce_471,
  1, 281, :_reduce_472,
  1, 282, :_reduce_473,
  3, 282, :_reduce_474,
  4, 283, :_reduce_475,
  2, 283, :_reduce_476,
  2, 283, :_reduce_477,
  1, 283, :_reduce_478,
  2, 285, :_reduce_479,
  0, 285, :_reduce_480,
  6, 277, :_reduce_481,
  4, 277, :_reduce_482,
  4, 277, :_reduce_483,
  2, 277, :_reduce_484,
  4, 277, :_reduce_485,
  2, 277, :_reduce_486,
  2, 277, :_reduce_487,
  1, 277, :_reduce_488,
  0, 277, :_reduce_489,
  1, 287, :_reduce_none,
  1, 287, :_reduce_491,
  1, 288, :_reduce_492,
  1, 288, :_reduce_493,
  1, 288, :_reduce_494,
  1, 288, :_reduce_495,
  1, 289, :_reduce_496,
  3, 289, :_reduce_497,
  1, 223, :_reduce_none,
  1, 223, :_reduce_none,
  1, 291, :_reduce_500,
  3, 291, :_reduce_none,
  1, 292, :_reduce_502,
  3, 292, :_reduce_503,
  1, 290, :_reduce_none,
  4, 290, :_reduce_none,
  3, 290, :_reduce_none,
  2, 290, :_reduce_none,
  1, 290, :_reduce_none,
  1, 252, :_reduce_509,
  3, 252, :_reduce_510,
  3, 293, :_reduce_511,
  1, 286, :_reduce_512,
  3, 286, :_reduce_513,
  1, 294, :_reduce_none,
  1, 294, :_reduce_none,
  2, 253, :_reduce_516,
  1, 253, :_reduce_517,
  1, 295, :_reduce_none,
  1, 295, :_reduce_none,
  2, 250, :_reduce_520,
  2, 284, :_reduce_521,
  0, 284, :_reduce_522,
  1, 227, :_reduce_523,
  4, 227, :_reduce_524,
  0, 215, :_reduce_525,
  2, 215, :_reduce_526,
  1, 198, :_reduce_527,
  3, 198, :_reduce_528,
  3, 296, :_reduce_529,
  2, 296, :_reduce_530,
  1, 179, :_reduce_none,
  1, 179, :_reduce_none,
  1, 179, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 175, :_reduce_none,
  1, 256, :_reduce_none,
  1, 256, :_reduce_none,
  1, 256, :_reduce_none,
  1, 228, :_reduce_none,
  1, 228, :_reduce_none,
  0, 146, :_reduce_none,
  1, 146, :_reduce_none,
  0, 193, :_reduce_none,
  1, 193, :_reduce_none,
  0, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 197, :_reduce_none,
  1, 225, :_reduce_none,
  1, 225, :_reduce_none,
  1, 148, :_reduce_none,
  2, 148, :_reduce_none,
  0, 195, :_reduce_554 ]

racc_reduce_n = 555

racc_shift_n = 968

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kDO_LAMBDA => 28,
  :kRETURN => 29,
  :kYIELD => 30,
  :kSUPER => 31,
  :kSELF => 32,
  :kNIL => 33,
  :kTRUE => 34,
  :kFALSE => 35,
  :kAND => 36,
  :kOR => 37,
  :kNOT => 38,
  :kIF_MOD => 39,
  :kUNLESS_MOD => 40,
  :kWHILE_MOD => 41,
  :kUNTIL_MOD => 42,
  :kRESCUE_MOD => 43,
  :kALIAS => 44,
  :kDEFINED => 45,
  :klBEGIN => 46,
  :klEND => 47,
  :k__LINE__ => 48,
  :k__FILE__ => 49,
  :k__ENCODING__ => 50,
  :tIDENTIFIER => 51,
  :tFID => 52,
  :tGVAR => 53,
  :tIVAR => 54,
  :tCONSTANT => 55,
  :tLABEL => 56,
  :tCVAR => 57,
  :tNTH_REF => 58,
  :tBACK_REF => 59,
  :tSTRING_CONTENT => 60,
  :tINTEGER => 61,
  :tFLOAT => 62,
  :tREGEXP_END => 63,
  :tUPLUS => 64,
  :tUMINUS => 65,
  :tUMINUS_NUM => 66,
  :tPOW => 67,
  :tCMP => 68,
  :tEQ => 69,
  :tEQQ => 70,
  :tNEQ => 71,
  :tGEQ => 72,
  :tLEQ => 73,
  :tANDOP => 74,
  :tOROP => 75,
  :tMATCH => 76,
  :tNMATCH => 77,
  :tJSDOT => 78,
  :tDOT => 79,
  :tDOT2 => 80,
  :tDOT3 => 81,
  :tAREF => 82,
  :tASET => 83,
  :tLSHFT => 84,
  :tRSHFT => 85,
  :tCOLON2 => 86,
  :tCOLON3 => 87,
  :tOP_ASGN => 88,
  :tASSOC => 89,
  :tLPAREN => 90,
  :tLPAREN2 => 91,
  :tRPAREN => 92,
  :tLPAREN_ARG => 93,
  :ARRAY_BEG => 94,
  :tRBRACK => 95,
  :tLBRACE => 96,
  :tLBRACE_ARG => 97,
  :tSTAR => 98,
  :tSTAR2 => 99,
  :tAMPER => 100,
  :tAMPER2 => 101,
  :tTILDE => 102,
  :tPERCENT => 103,
  :tDIVIDE => 104,
  :tPLUS => 105,
  :tMINUS => 106,
  :tLT => 107,
  :tGT => 108,
  :tPIPE => 109,
  :tBANG => 110,
  :tCARET => 111,
  :tLCURLY => 112,
  :tRCURLY => 113,
  :tBACK_REF2 => 114,
  :tSYMBEG => 115,
  :tSTRING_BEG => 116,
  :tXSTRING_BEG => 117,
  :tREGEXP_BEG => 118,
  :tWORDS_BEG => 119,
  :tAWORDS_BEG => 120,
  :tSTRING_DBEG => 121,
  :tSTRING_DVAR => 122,
  :tSTRING_END => 123,
  :tSTRING => 124,
  :tSYMBOL => 125,
  :tNL => 126,
  :tEH => 127,
  :tCOLON => 128,
  :tCOMMA => 129,
  :tSPACE => 130,
  :tSEMI => 131,
  :tLAMBDA => 132,
  :tLAMBEG => 133,
  :tLBRACK2 => 134,
  :tLBRACK => 135,
  :tJSLBRACK => 136,
  :tDSTAR => 137,
  :tEQL => 138,
  :tLOWEST => 139,
  "-@NUM" => 140,
  "+@NUM" => 141 }

racc_nt_base = 142

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kDO_LAMBDA",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "k__ENCODING__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tLABEL",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tREGEXP_END",
  "tUPLUS",
  "tUMINUS",
  "tUMINUS_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tJSDOT",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "ARRAY_BEG",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tWORDS_BEG",
  "tAWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tLAMBDA",
  "tLAMBEG",
  "tLBRACK2",
  "tLBRACK",
  "tJSLBRACK",
  "tDSTAR",
  "tEQL",
  "tLOWEST",
  "\"-@NUM\"",
  "\"+@NUM\"",
  "$start",
  "program",
  "top_compstmt",
  "top_stmts",
  "opt_terms",
  "top_stmt",
  "terms",
  "stmt",
  "bodystmt",
  "compstmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "fitem",
  "undef_list",
  "expr_value",
  "lhs",
  "command_call",
  "mlhs",
  "var_lhs",
  "primary_value",
  "aref_args",
  "backref",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "arg",
  "command",
  "block_command",
  "call_args",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_var",
  "operation",
  "mlhs_basic",
  "mlhs_entry",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "mlhs_post",
  "variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "symbol",
  "opt_nl",
  "primary",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_call_args",
  "rparen",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "call_args2",
  "open_args",
  "@2",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "awords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "lambda",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "superclass",
  "term",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@3",
  "@4",
  "@5",
  "@6",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "f_larglist",
  "lambda_body",
  "block_param",
  "f_block_optarg",
  "f_block_opt",
  "block_args_tail",
  "f_block_arg",
  "opt_block_args_tail",
  "f_arg",
  "f_rest_arg",
  "do_block",
  "@18",
  "operation3",
  "@19",
  "@20",
  "cases",
  "@21",
  "exc_list",
  "exc_var",
  "numeric",
  "dsym",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@22",
  "@23",
  "sym",
  "f_args",
  "kwrest_mark",
  "f_kwrest",
  "f_label",
  "f_kw",
  "f_kwarg",
  "args_tail",
  "opt_f_block_arg",
  "opt_args_tail",
  "f_optarg",
  "f_norm_arg",
  "f_bad_arg",
  "f_arg_item",
  "f_margs",
  "f_marg",
  "f_marg_list",
  "f_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

module_eval(<<'.,.,', 'opal.y', 70)
  def _reduce_2(val, _values, result)
                          result = new_compstmt val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 75)
  def _reduce_3(val, _values, result)
                          result = new_block

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 79)
  def _reduce_4(val, _values, result)
                          result = new_block val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 83)
  def _reduce_5(val, _values, result)
                          val[0] << val[2]
                      result = val[0]

    result
  end
.,.,

# reduce 6 omitted

module_eval(<<'.,.,', 'opal.y', 90)
  def _reduce_7(val, _values, result)
                          result = val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 95)
  def _reduce_8(val, _values, result)
                          result = new_body(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 100)
  def _reduce_9(val, _values, result)
                          result = new_compstmt val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 105)
  def _reduce_10(val, _values, result)
                          result = new_block

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 109)
  def _reduce_11(val, _values, result)
                          result = new_block val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 113)
  def _reduce_12(val, _values, result)
                          val[0] << val[2]
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 119)
  def _reduce_13(val, _values, result)
                          lexer.lex_state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 123)
  def _reduce_14(val, _values, result)
                          result = new_alias(val[0], val[1], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 127)
  def _reduce_15(val, _values, result)
                          result = s(:valias, value(val[1]).to_sym, value(val[2]).to_sym)

    result
  end
.,.,

# reduce 16 omitted

module_eval(<<'.,.,', 'opal.y', 132)
  def _reduce_17(val, _values, result)
                          result = s(:valias, value(val[1]).to_sym, value(val[2]).to_sym)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 136)
  def _reduce_18(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 140)
  def _reduce_19(val, _values, result)
                          result = new_if(val[1], val[2], val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 144)
  def _reduce_20(val, _values, result)
                          result = new_if(val[1], val[2], nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 148)
  def _reduce_21(val, _values, result)
                          result = new_while(val[1], val[2], val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 152)
  def _reduce_22(val, _values, result)
                          result = new_until(val[1], val[2], val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 156)
  def _reduce_23(val, _values, result)
                          result = new_rescue_mod(val[1], val[0], val[2])

    result
  end
.,.,

# reduce 24 omitted

module_eval(<<'.,.,', 'opal.y', 161)
  def _reduce_25(val, _values, result)
                          result = new_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 165)
  def _reduce_26(val, _values, result)
                          result = s(:masgn, val[0], s(:to_ary, val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 169)
  def _reduce_27(val, _values, result)
                          result = new_op_asgn val[1], val[0], val[2]

    result
  end
.,.,

# reduce 28 omitted

# reduce 29 omitted

module_eval(<<'.,.,', 'opal.y', 175)
  def _reduce_30(val, _values, result)
                          result = s(:op_asgn2, val[0], op_to_setter(val[2]), value(val[3]).to_sym, val[4])

    result
  end
.,.,

# reduce 31 omitted

# reduce 32 omitted

# reduce 33 omitted

module_eval(<<'.,.,', 'opal.y', 182)
  def _reduce_34(val, _values, result)
                          result = new_assign val[0], val[1], s(:svalue, val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 186)
  def _reduce_35(val, _values, result)
                          result = s(:masgn, val[0], s(:to_ary, val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 190)
  def _reduce_36(val, _values, result)
                          result = s(:masgn, val[0], val[2])

    result
  end
.,.,

# reduce 37 omitted

# reduce 38 omitted

module_eval(<<'.,.,', 'opal.y', 197)
  def _reduce_39(val, _values, result)
                          result = s(:and, val[0], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 201)
  def _reduce_40(val, _values, result)
                          result = s(:or, val[0], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 205)
  def _reduce_41(val, _values, result)
                          result = new_unary_call(['!', []], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 209)
  def _reduce_42(val, _values, result)
                          result = new_unary_call(val[0], val[1])

    result
  end
.,.,

# reduce 43 omitted

# reduce 44 omitted

# reduce 45 omitted

# reduce 46 omitted

module_eval(<<'.,.,', 'opal.y', 219)
  def _reduce_47(val, _values, result)
                          result = new_return(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 223)
  def _reduce_48(val, _values, result)
                          result = new_break(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 227)
  def _reduce_49(val, _values, result)
                          result = new_next(val[0], val[1])

    result
  end
.,.,

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

# reduce 53 omitted

# reduce 54 omitted

module_eval(<<'.,.,', 'opal.y', 239)
  def _reduce_55(val, _values, result)
                          result = new_call(nil, val[0], val[1])

    result
  end
.,.,

# reduce 56 omitted

module_eval(<<'.,.,', 'opal.y', 244)
  def _reduce_57(val, _values, result)
                          result = new_js_call(val[0], val[2], val[3])

    result
  end
.,.,

# reduce 58 omitted

module_eval(<<'.,.,', 'opal.y', 249)
  def _reduce_59(val, _values, result)
                          result = new_call(val[0], val[2], val[3])

    result
  end
.,.,

# reduce 60 omitted

module_eval(<<'.,.,', 'opal.y', 254)
  def _reduce_61(val, _values, result)
                        result = new_call(val[0], val[2], val[3])

    result
  end
.,.,

# reduce 62 omitted

module_eval(<<'.,.,', 'opal.y', 259)
  def _reduce_63(val, _values, result)
                          result = new_super(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 263)
  def _reduce_64(val, _values, result)
                          result = new_yield val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 268)
  def _reduce_65(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 272)
  def _reduce_66(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 277)
  def _reduce_67(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 281)
  def _reduce_68(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 286)
  def _reduce_69(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 290)
  def _reduce_70(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 294)
  def _reduce_71(val, _values, result)
                          result = val[0] << s(:splat, val[2])

    result
  end
.,.,

# reduce 72 omitted

module_eval(<<'.,.,', 'opal.y', 299)
  def _reduce_73(val, _values, result)
                          result = val[0] << s(:splat)

    result
  end
.,.,

# reduce 74 omitted

module_eval(<<'.,.,', 'opal.y', 304)
  def _reduce_75(val, _values, result)
                          result = s(:array, s(:splat, val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 308)
  def _reduce_76(val, _values, result)
                          result = s(:array, s(:splat))

    result
  end
.,.,

# reduce 77 omitted

module_eval(<<'.,.,', 'opal.y', 314)
  def _reduce_78(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 318)
  def _reduce_79(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 323)
  def _reduce_80(val, _values, result)
                          result = s(:array, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 327)
  def _reduce_81(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

# reduce 82 omitted

# reduce 83 omitted

module_eval(<<'.,.,', 'opal.y', 335)
  def _reduce_84(val, _values, result)
                          result = new_assignable val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 339)
  def _reduce_85(val, _values, result)
                          args = val[2] ? val[2] : []
                      result = s(:attrasgn, val[0], :[]=, s(:arglist, *args))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 344)
  def _reduce_86(val, _values, result)
                          result = new_call val[0], val[2], []

    result
  end
.,.,

# reduce 87 omitted

# reduce 88 omitted

# reduce 89 omitted

# reduce 90 omitted

# reduce 91 omitted

module_eval(<<'.,.,', 'opal.y', 354)
  def _reduce_92(val, _values, result)
                          result = new_assignable val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 358)
  def _reduce_93(val, _values, result)
                          result = new_js_attrasgn(val[0], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 362)
  def _reduce_94(val, _values, result)
                          result = new_attrasgn(val[0], :[]=, val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 366)
  def _reduce_95(val, _values, result)
                          result = new_attrasgn(val[0], op_to_setter(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 370)
  def _reduce_96(val, _values, result)
                          result = new_attrasgn(val[0], op_to_setter(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 374)
  def _reduce_97(val, _values, result)
                          result = new_attrasgn(val[0], op_to_setter(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 378)
  def _reduce_98(val, _values, result)
                          result = new_colon2(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 382)
  def _reduce_99(val, _values, result)
                          result = new_colon3(val[0], val[1])

    result
  end
.,.,

# reduce 100 omitted

# reduce 101 omitted

module_eval(<<'.,.,', 'opal.y', 390)
  def _reduce_102(val, _values, result)
                          result = new_colon3(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 394)
  def _reduce_103(val, _values, result)
                          result = new_const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 398)
  def _reduce_104(val, _values, result)
                          result = new_colon2(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

module_eval(<<'.,.,', 'opal.y', 406)
  def _reduce_108(val, _values, result)
                          lexer.lex_state = :expr_end
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 411)
  def _reduce_109(val, _values, result)
                          lexer.lex_state = :expr_end
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 417)
  def _reduce_110(val, _values, result)
                          result = new_sym(val[0])

    result
  end
.,.,

# reduce 111 omitted

module_eval(<<'.,.,', 'opal.y', 423)
  def _reduce_112(val, _values, result)
                          result = s(:undef, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 427)
  def _reduce_113(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

# reduce 114 omitted

# reduce 115 omitted

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

# reduce 171 omitted

# reduce 172 omitted

# reduce 173 omitted

# reduce 174 omitted

# reduce 175 omitted

# reduce 176 omitted

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

# reduce 180 omitted

# reduce 181 omitted

# reduce 182 omitted

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

module_eval(<<'.,.,', 'opal.y', 447)
  def _reduce_188(val, _values, result)
                          result = new_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 451)
  def _reduce_189(val, _values, result)
                          result = new_assign val[0], val[1], s(:rescue_mod, val[2], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 455)
  def _reduce_190(val, _values, result)
                          result = new_op_asgn val[1], val[0], val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 459)
  def _reduce_191(val, _values, result)
                          result = new_op_asgn1(val[0], val[2], val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 463)
  def _reduce_192(val, _values, result)
                          raise ".JS[...] #{val[4]} is not supported"

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 467)
  def _reduce_193(val, _values, result)
                          result = s(:op_asgn2, val[0], op_to_setter(val[2]), value(val[3]).to_sym, val[4])

    result
  end
.,.,

# reduce 194 omitted

# reduce 195 omitted

# reduce 196 omitted

# reduce 197 omitted

# reduce 198 omitted

module_eval(<<'.,.,', 'opal.y', 476)
  def _reduce_199(val, _values, result)
                          result = new_irange(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 480)
  def _reduce_200(val, _values, result)
                          result = new_erange(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 484)
  def _reduce_201(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 488)
  def _reduce_202(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 492)
  def _reduce_203(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 496)
  def _reduce_204(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 500)
  def _reduce_205(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 504)
  def _reduce_206(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 508)
  def _reduce_207(val, _values, result)
                          result = new_call new_binary_call(new_int(val[1]), val[2], val[3]), [:"-@", []], []

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 512)
  def _reduce_208(val, _values, result)
                          result = new_call new_binary_call(new_float(val[1]), val[2], val[3]), [:"-@", []], []

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 516)
  def _reduce_209(val, _values, result)
                          result = new_call val[1], [:"+@", []], []
                      if [:int, :float].include? val[1].type
                        result = val[1]
                      end

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 523)
  def _reduce_210(val, _values, result)
                          result = new_call val[1], [:"-@", []], []
                      if val[1].type == :int
                        val[1][1] = -val[1][1]
                        result = val[1]
                      elsif val[1].type == :float
                        val[1][1] = -val[1][1].to_f
                        result = val[1]
                      end

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 534)
  def _reduce_211(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 538)
  def _reduce_212(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 542)
  def _reduce_213(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 546)
  def _reduce_214(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 550)
  def _reduce_215(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 554)
  def _reduce_216(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 558)
  def _reduce_217(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 562)
  def _reduce_218(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 566)
  def _reduce_219(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 570)
  def _reduce_220(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 574)
  def _reduce_221(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 578)
  def _reduce_222(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 582)
  def _reduce_223(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 586)
  def _reduce_224(val, _values, result)
                          result = new_unary_call(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 590)
  def _reduce_225(val, _values, result)
                          result = new_unary_call(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 594)
  def _reduce_226(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 598)
  def _reduce_227(val, _values, result)
                          result = new_binary_call(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 602)
  def _reduce_228(val, _values, result)
                          result = new_and(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 606)
  def _reduce_229(val, _values, result)
                          result = new_or(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 610)
  def _reduce_230(val, _values, result)
                          result = s(:defined, val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 614)
  def _reduce_231(val, _values, result)
                          result = new_if(val[1], val[0], val[2], val[4])

    result
  end
.,.,

# reduce 232 omitted

# reduce 233 omitted

module_eval(<<'.,.,', 'opal.y', 622)
  def _reduce_234(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 626)
  def _reduce_235(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 630)
  def _reduce_236(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 634)
  def _reduce_237(val, _values, result)
                          val[0] << s(:hash, *val[2])
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 639)
  def _reduce_238(val, _values, result)
                          result = [s(:hash, *val[0])]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 644)
  def _reduce_239(val, _values, result)
                          result = val[1]

    result
  end
.,.,

# reduce 240 omitted

module_eval(<<'.,.,', 'opal.y', 651)
  def _reduce_241(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 242 omitted

module_eval(<<'.,.,', 'opal.y', 657)
  def _reduce_243(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 244 omitted

module_eval(<<'.,.,', 'opal.y', 662)
  def _reduce_245(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 666)
  def _reduce_246(val, _values, result)
                          result = val[0]
                      result << new_hash(nil, val[2], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 671)
  def _reduce_247(val, _values, result)
                          result = [new_hash(nil, val[0], nil)]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 676)
  def _reduce_248(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 680)
  def _reduce_249(val, _values, result)
                          result = val[0]
                      add_block_pass val[0], val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 685)
  def _reduce_250(val, _values, result)
                          result = [new_hash(nil, val[0], nil)]
                      add_block_pass result, val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 690)
  def _reduce_251(val, _values, result)
                          result = val[0]
                      result << new_hash(nil, val[2], nil)
                      result << val[3] if val[3]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 696)
  def _reduce_252(val, _values, result)
                          result = []
                      add_block_pass result, val[0]

    result
  end
.,.,

# reduce 253 omitted

# reduce 254 omitted

module_eval(<<'.,.,', 'opal.y', 704)
  def _reduce_255(val, _values, result)
                          lexer.cmdarg_push 1

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 708)
  def _reduce_256(val, _values, result)
                          lexer.cmdarg_pop
                      result = val[1]

    result
  end
.,.,

# reduce 257 omitted

module_eval(<<'.,.,', 'opal.y', 715)
  def _reduce_258(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 719)
  def _reduce_259(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 724)
  def _reduce_260(val, _values, result)
                          result = new_block_pass(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 729)
  def _reduce_261(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 733)
  def _reduce_262(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 738)
  def _reduce_263(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 742)
  def _reduce_264(val, _values, result)
                          result = [new_splat(val[0], val[1])]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 746)
  def _reduce_265(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 750)
  def _reduce_266(val, _values, result)
                          result  = val[0] << new_splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 755)
  def _reduce_267(val, _values, result)
                          val[0] << val[2]
                      result = s(:array, *val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 760)
  def _reduce_268(val, _values, result)
                          val[0] << s(:splat, val[3])
                      result = s(:array, *val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 765)
  def _reduce_269(val, _values, result)
                          result = s(:splat, val[1])

    result
  end
.,.,

# reduce 270 omitted

# reduce 271 omitted

# reduce 272 omitted

# reduce 273 omitted

# reduce 274 omitted

# reduce 275 omitted

# reduce 276 omitted

# reduce 277 omitted

# reduce 278 omitted

module_eval(<<'.,.,', 'opal.y', 779)
  def _reduce_279(val, _values, result)
                          result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 783)
  def _reduce_280(val, _values, result)
                          result = s(:begin, val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 787)
  def _reduce_281(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 791)
  def _reduce_282(val, _values, result)
                          result = new_paren(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 795)
  def _reduce_283(val, _values, result)
                          result = new_colon2(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 799)
  def _reduce_284(val, _values, result)
                          result = new_colon3(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 803)
  def _reduce_285(val, _values, result)
                          result = new_call val[0], [:[], []], val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 807)
  def _reduce_286(val, _values, result)
                          result = new_js_call val[0], [:[], []], val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 811)
  def _reduce_287(val, _values, result)
                          result = new_array(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 815)
  def _reduce_288(val, _values, result)
                          result = new_hash(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 819)
  def _reduce_289(val, _values, result)
                          result = new_return(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 823)
  def _reduce_290(val, _values, result)
                          result = new_yield val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 827)
  def _reduce_291(val, _values, result)
                          result = s(:yield)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 831)
  def _reduce_292(val, _values, result)
                          result = s(:yield)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 835)
  def _reduce_293(val, _values, result)
                          result = s(:defined, val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 839)
  def _reduce_294(val, _values, result)
                          result = new_unary_call(['!', []], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 843)
  def _reduce_295(val, _values, result)
                          result = new_unary_call(['!', []], new_nil(val[0]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 847)
  def _reduce_296(val, _values, result)
                          result = new_call(nil, val[0], [])
                      result << val[1]

    result
  end
.,.,

# reduce 297 omitted

module_eval(<<'.,.,', 'opal.y', 853)
  def _reduce_298(val, _values, result)
                          val[0] << val[1]
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 858)
  def _reduce_299(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 862)
  def _reduce_300(val, _values, result)
                          result = new_if(val[0], val[1], val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 866)
  def _reduce_301(val, _values, result)
                          result = new_if(val[0], val[1], val[4], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 870)
  def _reduce_302(val, _values, result)
                          lexer.cond_push 1
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 875)
  def _reduce_303(val, _values, result)
                          lexer.cond_pop

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 879)
  def _reduce_304(val, _values, result)
                          result = s(:while, val[2], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 883)
  def _reduce_305(val, _values, result)
                          lexer.cond_push 1
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 888)
  def _reduce_306(val, _values, result)
                          lexer.cond_pop

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 892)
  def _reduce_307(val, _values, result)
                          result = s(:until, val[2], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 896)
  def _reduce_308(val, _values, result)
                          result = s(:case, val[1], *val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 900)
  def _reduce_309(val, _values, result)
                          result = s(:case, nil, *val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 904)
  def _reduce_310(val, _values, result)
                          result = s(:case, nil, val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 908)
  def _reduce_311(val, _values, result)
                          lexer.cond_push 1
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 913)
  def _reduce_312(val, _values, result)
                          lexer.cond_pop

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 917)
  def _reduce_313(val, _values, result)
                          result = s(:for, val[4], val[1], val[7])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 921)
  def _reduce_314(val, _values, result)
                          # ...

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 925)
  def _reduce_315(val, _values, result)
                          result = new_class val[0], val[1], val[2], val[4], val[5]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 929)
  def _reduce_316(val, _values, result)
                          result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 933)
  def _reduce_317(val, _values, result)
                          # ...

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 937)
  def _reduce_318(val, _values, result)
                          result = new_sclass(val[0], val[3], val[6], val[7])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 941)
  def _reduce_319(val, _values, result)
                          result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 945)
  def _reduce_320(val, _values, result)
                          # ...

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 949)
  def _reduce_321(val, _values, result)
                          result = new_module(val[0], val[2], val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 953)
  def _reduce_322(val, _values, result)
                          push_scope
                      lexer.lex_state = :expr_endfn

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 958)
  def _reduce_323(val, _values, result)
                          result = new_def(val[0], nil, val[1], val[3], val[4], val[5])
                      pop_scope

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 963)
  def _reduce_324(val, _values, result)
                           lexer.lex_state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 967)
  def _reduce_325(val, _values, result)
                          push_scope
                      lexer.lex_state = :expr_endfn

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 972)
  def _reduce_326(val, _values, result)
                          result = new_def(val[0], val[1], val[4], val[6], val[7], val[8])
                      pop_scope

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 977)
  def _reduce_327(val, _values, result)
                          result = new_break(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 981)
  def _reduce_328(val, _values, result)
                          result = s(:next)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 985)
  def _reduce_329(val, _values, result)
                          result = s(:redo)

    result
  end
.,.,

# reduce 330 omitted

# reduce 331 omitted

# reduce 332 omitted

# reduce 333 omitted

# reduce 334 omitted

# reduce 335 omitted

# reduce 336 omitted

# reduce 337 omitted

# reduce 338 omitted

module_eval(<<'.,.,', 'opal.y', 1002)
  def _reduce_339(val, _values, result)
                          result = new_call nil, [:lambda, []], []
                      result << new_iter(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1008)
  def _reduce_340(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1012)
  def _reduce_341(val, _values, result)
                          result = nil

    result
  end
.,.,

# reduce 342 omitted

# reduce 343 omitted

module_eval(<<'.,.,', 'opal.y', 1019)
  def _reduce_344(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1023)
  def _reduce_345(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1028)
  def _reduce_346(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1032)
  def _reduce_347(val, _values, result)
                          result = new_if(val[0], val[1], val[3], val[4])

    result
  end
.,.,

# reduce 348 omitted

module_eval(<<'.,.,', 'opal.y', 1038)
  def _reduce_349(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1043)
  def _reduce_350(val, _values, result)
                          result = s(:block, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1047)
  def _reduce_351(val, _values, result)
                          val[0] << val[2]
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1053)
  def _reduce_352(val, _values, result)
                          result = new_assign(new_assignable(new_ident(
                                  val[0])), val[1], val[2])

    result
  end
.,.,

# reduce 353 omitted

module_eval(<<'.,.,', 'opal.y', 1060)
  def _reduce_354(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1064)
  def _reduce_355(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1068)
  def _reduce_356(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1073)
  def _reduce_357(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1078)
  def _reduce_358(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1082)
  def _reduce_359(val, _values, result)
                          nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1087)
  def _reduce_360(val, _values, result)
                          result = new_block_args(val[0], val[2], val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1091)
  def _reduce_361(val, _values, result)
                          result = new_block_args(val[0], val[2], nil, val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1095)
  def _reduce_362(val, _values, result)
                          result = new_block_args(val[0], nil, val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1099)
  def _reduce_363(val, _values, result)
                          result = new_block_args(val[0], nil, nil, nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1103)
  def _reduce_364(val, _values, result)
                          result = new_block_args(val[0], nil, nil, val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1107)
  def _reduce_365(val, _values, result)
                          result = new_block_args(nil, val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1111)
  def _reduce_366(val, _values, result)
                          result = new_block_args(nil, val[0], nil, val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1115)
  def _reduce_367(val, _values, result)
                          result = new_block_args(nil, nil, val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1119)
  def _reduce_368(val, _values, result)
                          result = new_block_args(nil, nil, nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1124)
  def _reduce_369(val, _values, result)
                          push_scope :block
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1129)
  def _reduce_370(val, _values, result)
                          result = new_iter val[2], val[3]
                      pop_scope

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1135)
  def _reduce_371(val, _values, result)
                          val[0] << val[1]
                      result = val[0]

    result
  end
.,.,

# reduce 372 omitted

# reduce 373 omitted

# reduce 374 omitted

module_eval(<<'.,.,', 'opal.y', 1144)
  def _reduce_375(val, _values, result)
                          result = new_call(nil, val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1148)
  def _reduce_376(val, _values, result)
                          result = new_call(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1152)
  def _reduce_377(val, _values, result)
                          result = new_js_call(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1156)
  def _reduce_378(val, _values, result)
                          result = new_call(val[0], [:call, []], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1160)
  def _reduce_379(val, _values, result)
                          result = new_call(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1164)
  def _reduce_380(val, _values, result)
                          result = new_call(val[0], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1168)
  def _reduce_381(val, _values, result)
                          result = new_super(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1172)
  def _reduce_382(val, _values, result)
                          result = new_super(val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1177)
  def _reduce_383(val, _values, result)
                          push_scope :block
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1182)
  def _reduce_384(val, _values, result)
                          result = new_iter val[2], val[3]
                      pop_scope

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1187)
  def _reduce_385(val, _values, result)
                          push_scope :block
                      result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1192)
  def _reduce_386(val, _values, result)
                          result = new_iter val[2], val[3]
                      pop_scope

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1198)
  def _reduce_387(val, _values, result)
                          result = lexer.line

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1202)
  def _reduce_388(val, _values, result)
                          part = s(:when, s(:array, *val[2]), val[4])
                      result = [part]
                      result.push(*val[5]) if val[5]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1209)
  def _reduce_389(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

# reduce 390 omitted

module_eval(<<'.,.,', 'opal.y', 1215)
  def _reduce_391(val, _values, result)
                          exc = val[1] || s(:array)
                      exc << new_assign(val[2], val[2], s(:gvar, '$!'.intern)) if val[2]
                      result = [s(:resbody, exc, val[4])]
                      result.push val[5].first if val[5]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1222)
  def _reduce_392(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1227)
  def _reduce_393(val, _values, result)
                          result = s(:array, val[0])

    result
  end
.,.,

# reduce 394 omitted

# reduce 395 omitted

module_eval(<<'.,.,', 'opal.y', 1234)
  def _reduce_396(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1238)
  def _reduce_397(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1243)
  def _reduce_398(val, _values, result)
                          result = val[1].nil? ? s(:nil) : val[1]

    result
  end
.,.,

# reduce 399 omitted

# reduce 400 omitted

# reduce 401 omitted

# reduce 402 omitted

module_eval(<<'.,.,', 'opal.y', 1253)
  def _reduce_403(val, _values, result)
                          result = new_str val[0]

    result
  end
.,.,

# reduce 404 omitted

module_eval(<<'.,.,', 'opal.y', 1259)
  def _reduce_405(val, _values, result)
                        result = str_append val[0], val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1264)
  def _reduce_406(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1268)
  def _reduce_407(val, _values, result)
                          result = s(:str, value(val[0]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1273)
  def _reduce_408(val, _values, result)
                          result = new_xstr(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1278)
  def _reduce_409(val, _values, result)
                          result = new_regexp val[1], val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1283)
  def _reduce_410(val, _values, result)
                          result = s(:array)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1287)
  def _reduce_411(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1292)
  def _reduce_412(val, _values, result)
                          result = s(:array)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1296)
  def _reduce_413(val, _values, result)
                          part = val[1]
                      part = s(:dstr, "", val[1]) if part.type == :evstr
                      result = val[0] << part

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1303)
  def _reduce_414(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1307)
  def _reduce_415(val, _values, result)
                          result = val[0].concat([val[1]])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1312)
  def _reduce_416(val, _values, result)
                          result = s(:array)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1316)
  def _reduce_417(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1321)
  def _reduce_418(val, _values, result)
                          result = s(:array)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1325)
  def _reduce_419(val, _values, result)
                          result = val[0] << s(:str, value(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1330)
  def _reduce_420(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1334)
  def _reduce_421(val, _values, result)
                          result = str_append val[0], val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1339)
  def _reduce_422(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1343)
  def _reduce_423(val, _values, result)
                          result = str_append val[0], val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1348)
  def _reduce_424(val, _values, result)
                          result = new_str_content(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1352)
  def _reduce_425(val, _values, result)
                          result = lexer.strterm
                      lexer.strterm = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1357)
  def _reduce_426(val, _values, result)
                          lexer.strterm = val[1]
                      result = new_evstr(val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1362)
  def _reduce_427(val, _values, result)
                          lexer.cond_push 0
                      lexer.cmdarg_push 0
                      result = lexer.strterm
                      lexer.strterm = nil
                      lexer.lex_state = :expr_beg

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1370)
  def _reduce_428(val, _values, result)
                          lexer.strterm = val[1]
                      lexer.cond_lexpop
                      lexer.cmdarg_lexpop
                      result = new_evstr(val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1378)
  def _reduce_429(val, _values, result)
                          result = new_gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1382)
  def _reduce_430(val, _values, result)
                          result = new_ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1386)
  def _reduce_431(val, _values, result)
                          result = new_cvar(val[0])

    result
  end
.,.,

# reduce 432 omitted

module_eval(<<'.,.,', 'opal.y', 1393)
  def _reduce_433(val, _values, result)
                          result = new_sym(val[1])
                      lexer.lex_state = :expr_end

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1398)
  def _reduce_434(val, _values, result)
                          result = new_sym(val[0])

    result
  end
.,.,

# reduce 435 omitted

# reduce 436 omitted

# reduce 437 omitted

# reduce 438 omitted

module_eval(<<'.,.,', 'opal.y', 1408)
  def _reduce_439(val, _values, result)
                          result = new_dsym val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1413)
  def _reduce_440(val, _values, result)
                          result = new_int(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1417)
  def _reduce_441(val, _values, result)
                          result = new_float(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1421)
  def _reduce_442(val, _values, result)
                        result = negate_num(new_int(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1425)
  def _reduce_443(val, _values, result)
                        result = negate_num(new_float(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1429)
  def _reduce_444(val, _values, result)
                        result = new_int(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1433)
  def _reduce_445(val, _values, result)
                        result = new_float(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1438)
  def _reduce_446(val, _values, result)
                          result = new_ident(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1442)
  def _reduce_447(val, _values, result)
                          result = new_ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1446)
  def _reduce_448(val, _values, result)
                          result = new_gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1450)
  def _reduce_449(val, _values, result)
                          result = new_const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1454)
  def _reduce_450(val, _values, result)
                          result = new_cvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1458)
  def _reduce_451(val, _values, result)
                          result = new_nil(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1462)
  def _reduce_452(val, _values, result)
                          result = new_self(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1466)
  def _reduce_453(val, _values, result)
                          result = new_true(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1470)
  def _reduce_454(val, _values, result)
                          result = new_false(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1474)
  def _reduce_455(val, _values, result)
                          result = new___FILE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1478)
  def _reduce_456(val, _values, result)
                          result = new___LINE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1483)
  def _reduce_457(val, _values, result)
                          result = new_var_ref(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1488)
  def _reduce_458(val, _values, result)
                          result = new_assignable val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1493)
  def _reduce_459(val, _values, result)
                          result = s(:nth_ref, value(val[0]))

    result
  end
.,.,

# reduce 460 omitted

module_eval(<<'.,.,', 'opal.y', 1499)
  def _reduce_461(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1503)
  def _reduce_462(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1507)
  def _reduce_463(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1512)
  def _reduce_464(val, _values, result)
                          result = val[1]
                      lexer.lex_state = :expr_beg

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1517)
  def _reduce_465(val, _values, result)
                          result = val[0]
                      lexer.lex_state = :expr_beg

    result
  end
.,.,

# reduce 466 omitted

# reduce 467 omitted

module_eval(<<'.,.,', 'opal.y', 1526)
  def _reduce_468(val, _values, result)
                          result = new_kwrestarg(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1530)
  def _reduce_469(val, _values, result)
                          result = new_kwrestarg()

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1535)
  def _reduce_470(val, _values, result)
                          result = new_sym(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1540)
  def _reduce_471(val, _values, result)
                          result = new_kwoptarg(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1544)
  def _reduce_472(val, _values, result)
                          result = new_kwarg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1549)
  def _reduce_473(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1553)
  def _reduce_474(val, _values, result)
                          result = val[0]
                      result << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1559)
  def _reduce_475(val, _values, result)
                          result = new_args_tail(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1563)
  def _reduce_476(val, _values, result)
                          result = new_args_tail(val[0], nil, val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1567)
  def _reduce_477(val, _values, result)
                          result = new_args_tail(nil, val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1571)
  def _reduce_478(val, _values, result)
                          result = new_args_tail(nil, nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1576)
  def _reduce_479(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1580)
  def _reduce_480(val, _values, result)
                          result = new_args_tail(nil, nil, nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1585)
  def _reduce_481(val, _values, result)
                          result = new_args(val[0], val[2], val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1589)
  def _reduce_482(val, _values, result)
                          result = new_args(val[0], val[2], nil, val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1593)
  def _reduce_483(val, _values, result)
                          result = new_args(val[0], nil, val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1597)
  def _reduce_484(val, _values, result)
                          result = new_args(val[0], nil, nil, val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1601)
  def _reduce_485(val, _values, result)
                          result = new_args(nil, val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1605)
  def _reduce_486(val, _values, result)
                          result = new_args(nil, val[0], nil, val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1609)
  def _reduce_487(val, _values, result)
                          result = new_args(nil, nil, val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1613)
  def _reduce_488(val, _values, result)
                          result = new_args(nil, nil, nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1617)
  def _reduce_489(val, _values, result)
                          result = new_args(nil, nil, nil, nil)

    result
  end
.,.,

# reduce 490 omitted

module_eval(<<'.,.,', 'opal.y', 1623)
  def _reduce_491(val, _values, result)
                          result = value(val[0]).to_sym
                      scope.add_local result

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1629)
  def _reduce_492(val, _values, result)
                          raise 'formal argument cannot be a constant'

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1633)
  def _reduce_493(val, _values, result)
                          raise 'formal argument cannot be an instance variable'

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1637)
  def _reduce_494(val, _values, result)
                          raise 'formal argument cannot be a class variable'

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1641)
  def _reduce_495(val, _values, result)
                          raise 'formal argument cannot be a global variable'

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1646)
  def _reduce_496(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1650)
  def _reduce_497(val, _values, result)
                          result = val[1]

    result
  end
.,.,

# reduce 498 omitted

# reduce 499 omitted

module_eval(<<'.,.,', 'opal.y', 1658)
  def _reduce_500(val, _values, result)
                          result = s(:lasgn, val[0])

    result
  end
.,.,

# reduce 501 omitted

module_eval(<<'.,.,', 'opal.y', 1664)
  def _reduce_502(val, _values, result)
                          result = s(:array, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1668)
  def _reduce_503(val, _values, result)
                          val[0] << val[2]
                      result = val[0]

    result
  end
.,.,

# reduce 504 omitted

# reduce 505 omitted

# reduce 506 omitted

# reduce 507 omitted

# reduce 508 omitted

module_eval(<<'.,.,', 'opal.y', 1680)
  def _reduce_509(val, _values, result)
                          result = [val[0]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1684)
  def _reduce_510(val, _values, result)
                          val[0] << val[2]
                      result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1690)
  def _reduce_511(val, _values, result)
                          result = new_assign(new_assignable(new_ident(val[0])), val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1695)
  def _reduce_512(val, _values, result)
                          result = s(:block, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1699)
  def _reduce_513(val, _values, result)
                          result = val[0]
                      val[0] << val[2]

    result
  end
.,.,

# reduce 514 omitted

# reduce 515 omitted

module_eval(<<'.,.,', 'opal.y', 1708)
  def _reduce_516(val, _values, result)
                          result = "*#{value(val[1])}".to_sym

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1712)
  def _reduce_517(val, _values, result)
                          result = :"*"

    result
  end
.,.,

# reduce 518 omitted

# reduce 519 omitted

module_eval(<<'.,.,', 'opal.y', 1720)
  def _reduce_520(val, _values, result)
                          result = "&#{value(val[1])}".to_sym

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1725)
  def _reduce_521(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1729)
  def _reduce_522(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1734)
  def _reduce_523(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1738)
  def _reduce_524(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1743)
  def _reduce_525(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1747)
  def _reduce_526(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1752)
  def _reduce_527(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1756)
  def _reduce_528(val, _values, result)
                          result = val[0].push(*val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1761)
  def _reduce_529(val, _values, result)
                          result = [val[0], val[2]]

    result
  end
.,.,

module_eval(<<'.,.,', 'opal.y', 1765)
  def _reduce_530(val, _values, result)
                          result = [new_sym(val[0]), val[1]]

    result
  end
.,.,

# reduce 531 omitted

# reduce 532 omitted

# reduce 533 omitted

# reduce 534 omitted

# reduce 535 omitted

# reduce 536 omitted

# reduce 537 omitted

# reduce 538 omitted

# reduce 539 omitted

# reduce 540 omitted

# reduce 541 omitted

# reduce 542 omitted

# reduce 543 omitted

# reduce 544 omitted

# reduce 545 omitted

# reduce 546 omitted

# reduce 547 omitted

# reduce 548 omitted

# reduce 549 omitted

# reduce 550 omitted

# reduce 551 omitted

# reduce 552 omitted

# reduce 553 omitted

module_eval(<<'.,.,', 'opal.y', 1802)
  def _reduce_554(val, _values, result)
                          result = nil

    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module Opal

#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'
module Huia
  class Parser < Racc::Parser

module_eval(<<'...end huia.y/module_eval...', 'huia.y', 211)

attr_accessor :lexer, :scopes, :state

def initialize lexer
  @lexer  = lexer
  @state  = []
  @scopes = []
  push_scope
end

def ast
  @ast ||= do_parse
  @scopes.first
end

def on_error t, val, vstack
  line = lexer.line
  col  = lexer.column
  message = "Unexpected #{token_to_str t} at #{lexer.filename} line #{line}:#{col}:\n\n"

  start = line - 5 > 0 ? line - 5 : 0
  i_size = line.to_s.size
  (start..(start + 5)).each do |i|
    message << sprintf("\t%#{i_size}d: %s\n", i, lexer.get_line(i))
    message << "\t#{' ' * i_size}  #{'-' * (col - 1)}^\n" if i == line
  end

  raise SyntaxError, message
end

def next_token
  nt = lexer.next_computed_token
  # just use a state stack for now, we'll have to do something
  # more sophisticated soon.
  if nt && nt.first == :state
    if nt.last
      state.push << nt.last
    else
      state.pop
    end
    next_token
  else
    nt
  end
end

def push_scope
  new_scope = Huia::AST::Scope.new scope
  new_scope.file   = lexer.filename
  new_scope.line   = lexer.line
  new_scope.column = lexer.column
  scopes.push new_scope
  new_scope
end

def pop_scope
  scopes.pop
end

def scope
  scopes.last
end

def binary left, right, method
  node(:MethodCall, left, node(:CallSignature, method, [right]))
end

def unary left, method
  node(:MethodCall, left, node(:CallSignature, method))
end

def node type, *args
  Huia::AST.const_get(type).new(*args).tap do |n|
    n.file   = lexer.filename
    n.line   = lexer.line
    n.column = lexer.column
  end
end
alias n node

def allocate_local name
  node(:Variable, name).tap do |n|
    scope.allocate_local n
  end
end

def allocate_local_assignment name, value
  node(:Assignment, name, value).tap do |n|
    scope.allocate_local n
  end
end

def this_closure
  allocate_local('@')
end

def scope_instance
  node(:ScopeInstance, scope)
end

def constant name
  return scope_instance if name == 'self'
  node(:Constant, name)
end

def to_string expr
  node(:MethodCall, expr, node(:CallSignature, 'toString'))
end
...end huia.y/module_eval...
##### State transition tables begin ###

clist = [
'81,106,40,37,61,62,123,153,135,71,72,77,155,178,179,39,164,37,37,5,6',
'137,152,73,74,75,36,36,76,28,154,80,166,172,180,22,23,37,26,27,182,60',
'63,19,164,40,36,61,62,186,,33,71,72,77,,,134,39,133,129,37,5,6,,,73',
'74,75,,36,76,28,134,80,169,129,,22,23,,26,27,,60,63,19,,40,,61,62,,',
'33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60',
'63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80',
',,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73',
'74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72',
'77,,114,,39,,,37,,,113,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63',
'19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,',
',22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74',
'75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77',
',,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40',
',61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23',
',26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36',
'76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,',
',37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62',
',,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27',
',60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28',
',80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,',
',,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71',
'72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19',
',40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22',
'23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75',
',36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,',
'39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61',
'62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26',
'27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76',
'28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37',
',,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33',
'71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63',
'19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,',
',22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74',
'75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77',
',,160,39,,,37,5,6,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19',
',40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22',
'23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75',
',36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,',
'39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61',
'62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26',
'27,,60,63,19,,40,,61,62,,,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76',
'28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62,,,33,71,72,77,,,160,39,',
',37,5,6,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27,,60,63,19,,40,,61,62',
',,33,71,72,77,,,,39,,,37,,,,,73,74,75,,36,76,28,,80,,,,22,23,,26,27',
',60,63,19,85,86,87,88,,84,,33,89,,,,,84,91,5,6,85,86,87,88,,91,,,89',
',,,,84,,92,93,94,95,96,97,98,91,92,93,94,95,96,97,98,,90,,,,,,,,,92',
'93,94,95,96,97,98,,90,85,86,87,88,,,,,89,,,,,84,,85,86,87,88,156,,,91',
'89,,,,,84,,,,,,,,,91,,,92,93,94,95,96,97,98,,90,,,,,,,92,93,94,95,96',
'97,98,,90,85,86,87,88,,,,,89,,,,,84,,165,85,86,87,88,,,91,,89,,,,167',
'84,,,,,,,,,91,,92,93,94,95,96,97,98,,90,,,,,,,,92,93,94,95,96,97,98',
',90,85,86,87,88,,,,,89,,,,,84,,85,86,87,88,,,,91,89,,,,,84,,,,,,,,,91',
',,92,93,94,95,96,97,98,,90,,,,,,,92,93,94,95,96,97,98,,90,85,86,87,88',
',,,,89,,,,,84,,85,86,87,88,,,,91,89,,,,,84,,,,,,,,,91,,,92,93,94,95',
'96,97,98,,90,,,,,,,92,93,94,95,96,97,98,,90,85,86,87,88,,,,,89,,,,,84',
',85,86,87,88,,,,91,89,,,,,84,,,,,,,,,91,,,92,93,94,95,96,97,98,,90,',
',,,,,92,93,94,95,96,97,98,,90,85,86,87,88,,,,,89,,,,,84,,85,86,87,88',
',,,91,89,,,,,84,,,,,,,,,91,,,92,93,94,95,96,97,98,,90,,,,,,,92,93,94',
'95,96,97,98,,90,85,86,87,88,,,,,89,,160,,,84,,5,6,85,86,87,88,,91,,',
'89,,,,,84,,,,,,,,,91,92,93,94,95,96,97,98,,90,,,,,,,,,92,93,94,95,96',
'97,98,,90,85,86,87,88,,,,,89,,,,,84,,85,86,87,88,,,,91,89,,,,,84,,181',
',,,,,,91,,,92,93,94,95,96,97,98,,90,,,,,,,92,93,94,95,96,97,98,,90,85',
'86,87,88,,,,,89,,,,,84,,85,86,87,88,,,,91,89,,,,,84,,,,,,,,,91,,,92',
'93,94,95,96,97,98,,90,,,,,,,92,93,94,95,96,97,98,,90,87,88,,,,,89,,',
',,84,87,88,,,,,89,,91,,,84,,87,88,,,,,89,91,,,,84,,92,93,94,95,96,97',
'98,91,90,,,,92,93,94,95,96,97,98,,90,,,,,92,93,94,95,96,97,98,89,90',
'87,88,,84,,,89,,,,,84,91,,,,,,,,91,,,89,,,,,84,,92,93,94,95,96,97,98',
'91,92,93,94,95,96,97,98,,90,,89,,,,,84,,92,93,94,95,96,97,98,91,89,',
',,,84,,,,,89,,,,91,84,,92,93,94,95,96,97,98,91,,,,,,,,92,93,94,95,96',
'97,98,,,,92,93,94,95,96,97,98' ]
        racc_action_table = arr = ::Array.new(2249, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'1,33,1,34,1,1,40,100,81,1,1,1,102,161,161,1,112,33,1,1,1,84,100,1,1',
'1,33,1,1,1,102,1,121,131,163,1,1,84,1,1,178,1,1,1,179,0,84,0,0,183,',
'1,0,0,0,,,80,0,80,80,0,0,0,,,0,0,0,,0,0,0,128,0,128,128,,0,0,,0,0,,0',
'0,0,,19,,19,19,,,0,19,19,19,,,,19,,,19,,,,,19,19,19,,19,19,19,,19,,',
',19,19,,19,19,,19,19,19,,23,,23,23,,,19,23,23,23,,,,23,,,23,,,,,23,23',
'23,,23,23,23,,23,,,,23,23,,23,23,,23,23,23,,27,,27,27,,,23,27,27,27',
',,,27,,,27,,,,,27,27,27,,27,27,27,,27,,,,27,27,,27,27,,27,27,27,,37',
',37,37,,,27,37,37,37,,37,,37,,,37,,,37,,37,37,37,,37,37,37,,37,,,,37',
'37,,37,37,,37,37,37,,39,,39,39,,,37,39,39,39,,,,39,,,39,,,,,39,39,39',
',39,39,39,,39,,,,39,39,,39,39,,39,39,39,,60,,60,60,,,39,60,60,60,,,',
'60,,,60,,,,,60,60,60,,60,60,60,,60,,,,60,60,,60,60,,60,60,60,,61,,61',
'61,,,60,61,61,61,,,,61,,,61,,,,,61,61,61,,61,61,61,,61,,,,61,61,,61',
'61,,61,61,61,,62,,62,62,,,61,62,62,62,,,,62,,,62,,,,,62,62,62,,62,62',
'62,,62,,,,62,62,,62,62,,62,62,62,,63,,63,63,,,62,63,63,63,,,,63,,,63',
',,,,63,63,63,,63,63,63,,63,,,,63,63,,63,63,,63,63,63,,85,,85,85,,,63',
'85,85,85,,,,85,,,85,,,,,85,85,85,,85,85,85,,85,,,,85,85,,85,85,,85,85',
'85,,86,,86,86,,,85,86,86,86,,,,86,,,86,,,,,86,86,86,,86,86,86,,86,,',
',86,86,,86,86,,86,86,86,,87,,87,87,,,86,87,87,87,,,,87,,,87,,,,,87,87',
'87,,87,87,87,,87,,,,87,87,,87,87,,87,87,87,,88,,88,88,,,87,88,88,88',
',,,88,,,88,,,,,88,88,88,,88,88,88,,88,,,,88,88,,88,88,,88,88,88,,89',
',89,89,,,88,89,89,89,,,,89,,,89,,,,,89,89,89,,89,89,89,,89,,,,89,89',
',89,89,,89,89,89,,90,,90,90,,,89,90,90,90,,,,90,,,90,,,,,90,90,90,,90',
'90,90,,90,,,,90,90,,90,90,,90,90,90,,91,,91,91,,,90,91,91,91,,,,91,',
',91,,,,,91,91,91,,91,91,91,,91,,,,91,91,,91,91,,91,91,91,,92,,92,92',
',,91,92,92,92,,,,92,,,92,,,,,92,92,92,,92,92,92,,92,,,,92,92,,92,92',
',92,92,92,,93,,93,93,,,92,93,93,93,,,,93,,,93,,,,,93,93,93,,93,93,93',
',93,,,,93,93,,93,93,,93,93,93,,94,,94,94,,,93,94,94,94,,,,94,,,94,,',
',,94,94,94,,94,94,94,,94,,,,94,94,,94,94,,94,94,94,,95,,95,95,,,94,95',
'95,95,,,,95,,,95,,,,,95,95,95,,95,95,95,,95,,,,95,95,,95,95,,95,95,95',
',96,,96,96,,,95,96,96,96,,,,96,,,96,,,,,96,96,96,,96,96,96,,96,,,,96',
'96,,96,96,,96,96,96,,97,,97,97,,,96,97,97,97,,,,97,,,97,,,,,97,97,97',
',97,97,97,,97,,,,97,97,,97,97,,97,97,97,,98,,98,98,,,97,98,98,98,,,',
'98,,,98,,,,,98,98,98,,98,98,98,,98,,,,98,98,,98,98,,98,98,98,,111,,111',
'111,,,98,111,111,111,,,111,111,,,111,111,111,,,111,111,111,,111,111',
'111,,111,,,,111,111,,111,111,,111,111,111,,123,,123,123,,,111,123,123',
'123,,,,123,,,123,,,,,123,123,123,,123,123,123,,123,,,,123,123,,123,123',
',123,123,123,,129,,129,129,,,123,129,129,129,,,,129,,,129,,,,,129,129',
'129,,129,129,129,,129,,,,129,129,,129,129,,129,129,129,,153,,153,153',
',,129,153,153,153,,,,153,,,153,,,,,153,153,153,,153,153,153,,153,,,',
'153,153,,153,153,,153,153,153,,155,,155,155,,,153,155,155,155,,,,155',
',,155,,,,,155,155,155,,155,155,155,,155,,,,155,155,,155,155,,155,155',
'155,,156,,156,156,,,155,156,156,156,,,,156,,,156,,,,,156,156,156,,156',
'156,156,,156,,,,156,156,,156,156,,156,156,156,,157,,157,157,,,156,157',
'157,157,,,157,157,,,157,157,157,,,157,157,157,,157,157,157,,157,,,,157',
'157,,157,157,,157,157,157,,180,,180,180,,,157,180,180,180,,,,180,,,180',
',,,,180,180,180,,180,180,180,,180,,,,180,180,,180,180,,180,180,180,3',
'3,3,3,,142,,180,3,,,,,3,142,3,3,99,99,99,99,,3,,,99,,,,,99,,142,142',
'142,142,142,142,142,99,3,3,3,3,3,3,3,,3,,,,,,,,,99,99,99,99,99,99,99',
',99,101,101,101,101,,,,,101,,,,,101,,104,104,104,104,104,,,101,104,',
',,,104,,,,,,,,,104,,,101,101,101,101,101,101,101,,101,,,,,,,104,104',
'104,104,104,104,104,,104,117,117,117,117,,,,,117,,,,,117,,117,122,122',
'122,122,,,117,,122,,,,122,122,,,,,,,,,122,,117,117,117,117,117,117,117',
',117,,,,,,,,122,122,122,122,122,122,122,,122,144,144,144,144,,,,,144',
',,,,144,,145,145,145,145,,,,144,145,,,,,145,,,,,,,,,145,,,144,144,144',
'144,144,144,144,,144,,,,,,,145,145,145,145,145,145,145,,145,146,146',
'146,146,,,,,146,,,,,146,,147,147,147,147,,,,146,147,,,,,147,,,,,,,,',
'147,,,146,146,146,146,146,146,146,,146,,,,,,,147,147,147,147,147,147',
'147,,147,148,148,148,148,,,,,148,,,,,148,,149,149,149,149,,,,148,149',
',,,,149,,,,,,,,,149,,,148,148,148,148,148,148,148,,148,,,,,,,149,149',
'149,149,149,149,149,,149,150,150,150,150,,,,,150,,,,,150,,151,151,151',
'151,,,,150,151,,,,,151,,,,,,,,,151,,,150,150,150,150,150,150,150,,150',
',,,,,,151,151,151,151,151,151,151,,151,158,158,158,158,,,,,158,,158',
',,158,,158,158,168,168,168,168,,158,,,168,,,,,168,,,,,,,,,168,158,158',
'158,158,158,158,158,,158,,,,,,,,,168,168,168,168,168,168,168,,168,171',
'171,171,171,,,,,171,,,,,171,,173,173,173,173,,,,171,173,,,,,173,,171',
',,,,,,173,,,171,171,171,171,171,171,171,,171,,,,,,,173,173,173,173,173',
'173,173,,173,175,175,175,175,,,,,175,,,,,175,,185,185,185,185,,,,175',
'185,,,,,185,,,,,,,,,185,,,175,175,175,175,175,175,175,,175,,,,,,,185',
'185,185,185,185,185,185,,185,125,125,,,,,125,,,,,125,126,126,,,,,126',
',125,,,126,,138,138,,,,,138,126,,,,138,,125,125,125,125,125,125,125',
'138,125,,,,126,126,126,126,126,126,126,,126,,,,,138,138,138,138,138',
'138,138,124,138,139,139,,124,,,139,,,,,139,124,,,,,,,,139,,,127,,,,',
'127,,124,124,124,124,124,124,124,127,139,139,139,139,139,139,139,,139',
',140,,,,,140,,127,127,127,127,127,127,127,140,141,,,,,141,,,,,143,,',
',141,143,,140,140,140,140,140,140,140,143,,,,,,,,141,141,141,141,141',
'141,141,,,,143,143,143,143,143,143,143' ]
        racc_action_check = arr = ::Array.new(2249, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
    43,     0,   nil,  1416,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    86,
   nil,   nil,   nil,   129,   nil,   nil,   nil,   172,   nil,   nil,
   nil,   nil,   nil,    -1,   -15,   nil,   nil,   215,   nil,   258,
     3,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   301,   344,   387,   430,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    27,     8,   nil,   nil,    19,   473,   516,   559,   602,   645,
   688,   731,   774,   817,   860,   903,   946,   989,  1032,  1433,
   -15,  1482,   -10,   nil,  1497,   nil,   nil,   nil,   nil,   nil,
   nil,  1075,    14,   nil,   nil,   nil,   nil,  1546,   nil,   nil,
   nil,    13,  1562,  1118,  2123,  2059,  2071,  2148,    43,  1161,
   nil,     3,   nil,   nil,   nil,   nil,   nil,   nil,  2084,  2131,
  2173,  2188,  1408,  2198,  1611,  1626,  1675,  1690,  1739,  1754,
  1803,  1818,   nil,  1204,   nil,  1247,  1290,  1333,  1867,   nil,
   nil,    -8,   nil,    31,   nil,   nil,   nil,   nil,  1884,   nil,
   nil,  1933,   nil,  1948,   nil,  1997,   nil,   nil,    21,    42,
  1376,   nil,   nil,    36,   nil,  2012,   nil ]

racc_action_default = [
  -140,  -140,    -1,    -4,    -5,    -6,    -7,   -10,   -11,   -12,
   -13,   -14,   -15,   -16,   -17,   -18,   -19,   -20,   -21,   -23,
   -24,   -25,   -26,  -140,   -30,   -31,   -32,  -140,   -37,   -55,
   -56,   -57,   -60,  -140,   -63,   -64,   -65,  -140,   -73,  -140,
   -76,   -77,   -78,   -79,   -80,   -81,   -82,   -83,   -84,   -85,
   -86,   -87,   -88,   -89,   -90,   -91,  -107,  -108,  -109,  -110,
  -140,  -140,  -140,  -140,  -115,  -116,  -117,  -118,  -119,  -120,
  -121,  -122,  -123,  -124,  -125,  -126,  -127,  -128,  -129,  -130,
  -140,  -140,    -2,    -3,  -140,  -140,  -140,  -140,  -140,  -140,
  -140,  -140,  -140,  -140,  -140,  -140,  -140,  -140,  -140,   -22,
  -140,   -28,  -140,   -34,  -140,   -61,   -62,   -74,   -38,   -39,
   -40,  -140,  -140,   -46,   -47,   -48,   -49,   -69,   -66,   -67,
   -68,   -71,  -140,  -140,  -111,  -112,  -113,  -114,  -140,  -140,
  -133,  -135,  -136,  -137,  -138,   187,   -58,   -59,   -93,   -94,
   -95,   -96,   -97,   -98,   -99,  -100,  -101,  -102,  -103,  -104,
  -105,  -106,   -27,  -140,   -33,  -140,  -140,  -140,    -4,   -43,
   -44,  -140,   -50,   -52,   -54,   -70,   -72,   -75,   -92,  -131,
  -134,  -140,  -139,   -29,   -35,   -36,   -41,   -42,    -9,  -140,
  -140,  -132,    -8,  -140,   -51,   -53,   -45 ]

racc_goto_table = [
    99,    82,   103,    83,   101,     1,   105,   130,   104,   183,
   100,   102,   159,   162,   121,   108,   109,   110,   117,   111,
   122,   115,   112,   161,   116,   107,   118,   119,   120,   128,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   124,   125,   126,   127,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   170,   nil,   136,   176,   177,
   nil,   nil,   nil,   nil,   nil,   nil,   138,   139,   140,   141,
   142,   143,   144,   145,   146,   147,   148,   149,   150,   151,
   184,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   158,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   168,   nil,   nil,   nil,   nil,   nil,
   171,   nil,   nil,   nil,   nil,   nil,   157,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   174,   nil,   nil,   nil,   173,   nil,   104,   175,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    82,    83,   nil,
   nil,   185 ]

racc_goto_check = [
     3,     2,    24,     4,     3,     1,    40,    77,     3,     5,
    20,    23,    30,    35,    25,    26,    27,    28,     3,    29,
     3,    31,    32,    33,    34,    43,    44,    45,    46,    75,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     3,     3,     3,     3,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    77,   nil,    40,    30,    30,
   nil,   nil,   nil,   nil,   nil,   nil,     3,     3,     3,     3,
     3,     3,     3,     3,     3,     3,     3,     3,     3,     3,
    35,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,     3,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     3,   nil,   nil,   nil,   nil,   nil,
     3,   nil,   nil,   nil,   nil,   nil,     1,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    24,   nil,   nil,   nil,     3,   nil,     3,     3,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     2,     4,   nil,
   nil,     3 ]

racc_goto_pointer = [
   nil,     5,     0,   -19,     0,  -169,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   -13,   nil,   nil,   -16,   -25,   -23,   -22,   -21,   -20,   -18,
   -99,   -16,   -15,   -89,   -13,   -99,   nil,   nil,   nil,   nil,
   -27,   nil,   nil,    -9,   -11,   -10,    -9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   -51,   nil,   -73,   nil ]

racc_goto_default = [
   nil,   nil,     2,     3,     4,   nil,     7,     8,     9,    10,
    11,    12,    13,    14,    15,    16,    17,    18,    20,    21,
   nil,    24,    25,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   163,    29,    30,    31,
    32,    34,    35,    38,   nil,   nil,   nil,    41,    42,    43,
    44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
    54,    55,    56,    57,    58,    59,    64,    65,    66,    67,
    68,    69,    70,    78,    79,   nil,   132,   nil,   131 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 54, :_reduce_none,
  2, 54, :_reduce_2,
  2, 55, :_reduce_3,
  1, 55, :_reduce_4,
  1, 55, :_reduce_5,
  1, 57, :_reduce_none,
  1, 57, :_reduce_none,
  1, 58, :_reduce_none,
  0, 58, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 56, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  2, 69, :_reduce_22,
  1, 70, :_reduce_23,
  1, 66, :_reduce_none,
  1, 66, :_reduce_none,
  1, 71, :_reduce_26,
  3, 72, :_reduce_27,
  1, 73, :_reduce_28,
  3, 73, :_reduce_29,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 74, :_reduce_32,
  3, 75, :_reduce_33,
  1, 76, :_reduce_34,
  3, 76, :_reduce_35,
  3, 77, :_reduce_36,
  1, 64, :_reduce_37,
  1, 78, :_reduce_none,
  1, 78, :_reduce_none,
  1, 78, :_reduce_none,
  3, 79, :_reduce_41,
  3, 80, :_reduce_42,
  2, 81, :_reduce_43,
  1, 83, :_reduce_44,
  5, 84, :_reduce_45,
  1, 85, :_reduce_46,
  1, 87, :_reduce_47,
  1, 82, :_reduce_none,
  1, 82, :_reduce_none,
  1, 86, :_reduce_none,
  3, 86, :_reduce_none,
  1, 88, :_reduce_52,
  3, 88, :_reduce_53,
  1, 89, :_reduce_54,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  3, 90, :_reduce_58,
  3, 90, :_reduce_59,
  1, 91, :_reduce_60,
  2, 92, :_reduce_61,
  2, 92, :_reduce_62,
  1, 93, :_reduce_none,
  1, 93, :_reduce_none,
  1, 95, :_reduce_65,
  2, 96, :_reduce_66,
  1, 97, :_reduce_none,
  1, 97, :_reduce_none,
  1, 98, :_reduce_none,
  2, 98, :_reduce_none,
  1, 99, :_reduce_none,
  2, 99, :_reduce_none,
  1, 94, :_reduce_73,
  2, 94, :_reduce_74,
  3, 60, :_reduce_75,
  1, 65, :_reduce_76,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  3, 100, :_reduce_92,
  3, 101, :_reduce_93,
  3, 102, :_reduce_94,
  3, 103, :_reduce_95,
  3, 104, :_reduce_96,
  3, 105, :_reduce_97,
  3, 106, :_reduce_98,
  3, 107, :_reduce_99,
  3, 108, :_reduce_100,
  3, 109, :_reduce_101,
  3, 110, :_reduce_102,
  3, 111, :_reduce_103,
  3, 112, :_reduce_104,
  3, 113, :_reduce_105,
  3, 114, :_reduce_106,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  2, 115, :_reduce_111,
  2, 116, :_reduce_112,
  2, 117, :_reduce_113,
  2, 118, :_reduce_114,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 120, :_reduce_122,
  1, 119, :_reduce_123,
  1, 122, :_reduce_124,
  1, 123, :_reduce_125,
  1, 124, :_reduce_126,
  1, 125, :_reduce_127,
  1, 121, :_reduce_128,
  1, 121, :_reduce_none,
  1, 121, :_reduce_none,
  3, 126, :_reduce_131,
  3, 129, :_reduce_132,
  1, 128, :_reduce_133,
  2, 128, :_reduce_134,
  1, 130, :_reduce_135,
  1, 130, :_reduce_136,
  2, 127, :_reduce_137,
  1, 131, :_reduce_138,
  2, 131, :_reduce_139 ]

racc_reduce_n = 140

racc_shift_n = 187

racc_token_table = {
  false => 0,
  :error => 1,
  :IDENTIFIER => 2,
  :EQUAL => 3,
  :PLUS => 4,
  :MINUS => 5,
  :ASTERISK => 6,
  :FWD_SLASH => 7,
  :COLON => 8,
  :FLOAT => 9,
  :INTEGER => 10,
  :STRING => 11,
  :EXPO => 12,
  :INDENT => 13,
  :OUTDENT => 14,
  :OPAREN => 15,
  :CPAREN => 16,
  :DOT => 17,
  :SIGNATURE => 18,
  :NL => 19,
  :EOF => 20,
  :PIPE => 21,
  :COMMA => 22,
  :NIL => 23,
  :TRUE => 24,
  :FALSE => 25,
  :EQUALITY => 26,
  :CALL => 27,
  :SELF => 28,
  :CONSTANT => 29,
  :CHAR => 30,
  :DOUBLE_TICK_STRING => 31,
  :DOUBLE_TICK_STRING_END => 32,
  :INTERPOLATE_START => 33,
  :INTERPOLATE_END => 34,
  :BOX => 35,
  :LSQUARE => 36,
  :RSQUARE => 37,
  :FACES => 38,
  :LFACE => 39,
  :RFACE => 40,
  :BANG => 41,
  :TILDE => 42,
  :RETURN => 43,
  :NOT_EQUALITY => 44,
  :OR => 45,
  :AND => 46,
  :GT => 47,
  :LT => 48,
  :GTE => 49,
  :LTE => 50,
  :AT => 51,
  :PERCENT => 52 }

racc_nt_base = 53

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "IDENTIFIER",
  "EQUAL",
  "PLUS",
  "MINUS",
  "ASTERISK",
  "FWD_SLASH",
  "COLON",
  "FLOAT",
  "INTEGER",
  "STRING",
  "EXPO",
  "INDENT",
  "OUTDENT",
  "OPAREN",
  "CPAREN",
  "DOT",
  "SIGNATURE",
  "NL",
  "EOF",
  "PIPE",
  "COMMA",
  "NIL",
  "TRUE",
  "FALSE",
  "EQUALITY",
  "CALL",
  "SELF",
  "CONSTANT",
  "CHAR",
  "DOUBLE_TICK_STRING",
  "DOUBLE_TICK_STRING_END",
  "INTERPOLATE_START",
  "INTERPOLATE_END",
  "BOX",
  "LSQUARE",
  "RSQUARE",
  "FACES",
  "LFACE",
  "RFACE",
  "BANG",
  "TILDE",
  "RETURN",
  "NOT_EQUALITY",
  "OR",
  "AND",
  "GT",
  "LT",
  "GTE",
  "LTE",
  "AT",
  "PERCENT",
  "$start",
  "statements",
  "statement",
  "expr",
  "eol",
  "nlq",
  "literal",
  "grouped_expr",
  "binary_op",
  "unary_op",
  "method_call",
  "constant",
  "variable",
  "array",
  "hash",
  "return",
  "return_expr",
  "return_nil",
  "empty_array",
  "array_list",
  "array_items",
  "empty_hash",
  "hash_list",
  "hash_items",
  "hash_item",
  "indented",
  "indented_w_stmts",
  "indented_w_expr",
  "indented_wo_stmts",
  "indent",
  "outdent",
  "indent_w_args",
  "indent_pipe",
  "indent_args",
  "indent_wo_args",
  "indent_arg",
  "arg_var",
  "method_call_on_object",
  "method_call_on_self",
  "method_call_on_closure",
  "call_signature",
  "call_arguments",
  "call_simple_name",
  "call_argument",
  "call_passed_arg",
  "call_passed_simple",
  "call_passed_indented",
  "assignment",
  "addition",
  "subtraction",
  "multiplication",
  "division",
  "exponentiation",
  "modulo",
  "equality",
  "not_equality",
  "logical_or",
  "logical_and",
  "greater_than",
  "less_than",
  "greater_or_eq",
  "less_or_eq",
  "unary_not",
  "unary_plus",
  "unary_minus",
  "unary_complement",
  "integer",
  "float",
  "string",
  "nil",
  "true",
  "false",
  "self",
  "interpolated_string",
  "empty_string",
  "interpolated_string_contents",
  "interpolation",
  "interpolated_string_chunk",
  "chars" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

module_eval(<<'.,.,', 'huia.y', 44)
  def _reduce_2(val, _values, result)
     return scope
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 46)
  def _reduce_3(val, _values, result)
     return scope.append val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 47)
  def _reduce_4(val, _values, result)
     return scope.append val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 48)
  def _reduce_5(val, _values, result)
     return scope
    result
  end
.,.,

# reduce 6 omitted

# reduce 7 omitted

# reduce 8 omitted

# reduce 9 omitted

# reduce 10 omitted

# reduce 11 omitted

# reduce 12 omitted

# reduce 13 omitted

# reduce 14 omitted

# reduce 15 omitted

# reduce 16 omitted

# reduce 17 omitted

# reduce 18 omitted

# reduce 19 omitted

# reduce 20 omitted

# reduce 21 omitted

module_eval(<<'.,.,', 'huia.y', 66)
  def _reduce_22(val, _values, result)
     return n(:Return, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 67)
  def _reduce_23(val, _values, result)
     return n(:Return, n(:Nil))
    result
  end
.,.,

# reduce 24 omitted

# reduce 25 omitted

module_eval(<<'.,.,', 'huia.y', 72)
  def _reduce_26(val, _values, result)
     return n :Array
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 74)
  def _reduce_27(val, _values, result)
     return val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 75)
  def _reduce_28(val, _values, result)
     return n :Array, [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 76)
  def _reduce_29(val, _values, result)
     val[0].append(val[2]); return val[0]
    result
  end
.,.,

# reduce 30 omitted

# reduce 31 omitted

module_eval(<<'.,.,', 'huia.y', 80)
  def _reduce_32(val, _values, result)
     return n :Hash
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 81)
  def _reduce_33(val, _values, result)
     return val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 82)
  def _reduce_34(val, _values, result)
     return n :Hash, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 83)
  def _reduce_35(val, _values, result)
     val[0].append(val[2]); return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 84)
  def _reduce_36(val, _values, result)
     return n :HashItem, val[0], val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 86)
  def _reduce_37(val, _values, result)
     return constant val[0]
    result
  end
.,.,

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

module_eval(<<'.,.,', 'huia.y', 91)
  def _reduce_41(val, _values, result)
     return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 92)
  def _reduce_42(val, _values, result)
     return val[0].append(val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 93)
  def _reduce_43(val, _values, result)
     return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 94)
  def _reduce_44(val, _values, result)
     return pop_scope
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 97)
  def _reduce_45(val, _values, result)
     return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 98)
  def _reduce_46(val, _values, result)
     return push_scope
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 99)
  def _reduce_47(val, _values, result)
     return push_scope
    result
  end
.,.,

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

module_eval(<<'.,.,', 'huia.y', 105)
  def _reduce_52(val, _values, result)
     return scope.add_argument val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 106)
  def _reduce_53(val, _values, result)
     return n :Assignment, val[0], val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 107)
  def _reduce_54(val, _values, result)
     return n :Variable, val[0]
    result
  end
.,.,

# reduce 55 omitted

# reduce 56 omitted

# reduce 57 omitted

module_eval(<<'.,.,', 'huia.y', 112)
  def _reduce_58(val, _values, result)
     return n :MethodCall, val[0], val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 113)
  def _reduce_59(val, _values, result)
     return n :MethodCall, val[0], n(:CallSignature, val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 114)
  def _reduce_60(val, _values, result)
     return n :MethodCall, scope_instance, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 116)
  def _reduce_61(val, _values, result)
     return n :MethodCall, this_closure, val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 117)
  def _reduce_62(val, _values, result)
     return n :MethodCall, this_closure, n(:CallSignature, val[1])
    result
  end
.,.,

# reduce 63 omitted

# reduce 64 omitted

module_eval(<<'.,.,', 'huia.y', 121)
  def _reduce_65(val, _values, result)
     return n :CallSignature, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 122)
  def _reduce_66(val, _values, result)
     return n :CallSignature, val[0], [val[1]]
    result
  end
.,.,

# reduce 67 omitted

# reduce 68 omitted

# reduce 69 omitted

# reduce 70 omitted

# reduce 71 omitted

# reduce 72 omitted

module_eval(<<'.,.,', 'huia.y', 129)
  def _reduce_73(val, _values, result)
     return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 130)
  def _reduce_74(val, _values, result)
     return val[0].concat_signature val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 132)
  def _reduce_75(val, _values, result)
     return n :Expression, val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 134)
  def _reduce_76(val, _values, result)
     return allocate_local val[0]
    result
  end
.,.,

# reduce 77 omitted

# reduce 78 omitted

# reduce 79 omitted

# reduce 80 omitted

# reduce 81 omitted

# reduce 82 omitted

# reduce 83 omitted

# reduce 84 omitted

# reduce 85 omitted

# reduce 86 omitted

# reduce 87 omitted

# reduce 88 omitted

# reduce 89 omitted

# reduce 90 omitted

# reduce 91 omitted

module_eval(<<'.,.,', 'huia.y', 152)
  def _reduce_92(val, _values, result)
     return allocate_local_assignment val[0], val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 153)
  def _reduce_93(val, _values, result)
     return binary val[0], val[2], 'plus:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 154)
  def _reduce_94(val, _values, result)
     return binary val[0], val[2], 'minus:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 155)
  def _reduce_95(val, _values, result)
     return binary val[0], val[2], 'multiplyBy:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 156)
  def _reduce_96(val, _values, result)
     return binary val[0], val[2], 'divideBy:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 157)
  def _reduce_97(val, _values, result)
     return binary val[0], val[2], 'toThePowerOf:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 158)
  def _reduce_98(val, _values, result)
     return binary val[0], val[2], 'moduloOf:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 159)
  def _reduce_99(val, _values, result)
     return binary val[0], val[2], 'isEqualTo:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 160)
  def _reduce_100(val, _values, result)
     return binary val[0], val[2], 'isNotEqualTo:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 161)
  def _reduce_101(val, _values, result)
     return binary val[0], val[2], 'logicalOr:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 162)
  def _reduce_102(val, _values, result)
     return binary val[0], val[2], 'logicalAnd:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 163)
  def _reduce_103(val, _values, result)
     return binary val[0], val[2], 'isGreaterThan:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 164)
  def _reduce_104(val, _values, result)
     return binary val[0], val[2], 'isLessThan:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 165)
  def _reduce_105(val, _values, result)
     return binary val[0], val[2], 'isGreaterOrEqualTo:'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 166)
  def _reduce_106(val, _values, result)
     return binary val[0], val[2], 'isLessOrEqualTo:'
    result
  end
.,.,

# reduce 107 omitted

# reduce 108 omitted

# reduce 109 omitted

# reduce 110 omitted

module_eval(<<'.,.,', 'huia.y', 173)
  def _reduce_111(val, _values, result)
     return unary val[1], 'unaryNot'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 174)
  def _reduce_112(val, _values, result)
     return unary val[1], 'unaryPlus'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 175)
  def _reduce_113(val, _values, result)
     return unary val[1], 'unaryMinus'
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 176)
  def _reduce_114(val, _values, result)
     return unary val[1], 'unaryComplement'
    result
  end
.,.,

# reduce 115 omitted

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

module_eval(<<'.,.,', 'huia.y', 186)
  def _reduce_122(val, _values, result)
     return n :Float,   val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 187)
  def _reduce_123(val, _values, result)
     return n :Integer, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 188)
  def _reduce_124(val, _values, result)
     return n :Nil
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 189)
  def _reduce_125(val, _values, result)
     return n :True
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 190)
  def _reduce_126(val, _values, result)
     return n :False
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 191)
  def _reduce_127(val, _values, result)
     return n :Self
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 193)
  def _reduce_128(val, _values, result)
     return n :String,  val[0]
    result
  end
.,.,

# reduce 129 omitted

# reduce 130 omitted

module_eval(<<'.,.,', 'huia.y', 197)
  def _reduce_131(val, _values, result)
     return val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 198)
  def _reduce_132(val, _values, result)
     return val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 199)
  def _reduce_133(val, _values, result)
     return n :InterpolatedString, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 200)
  def _reduce_134(val, _values, result)
     val[0].append(val[1]); return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 201)
  def _reduce_135(val, _values, result)
     return val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 202)
  def _reduce_136(val, _values, result)
     return to_string(val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 203)
  def _reduce_137(val, _values, result)
     return n :String, ''
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 205)
  def _reduce_138(val, _values, result)
     return n :String, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'huia.y', 206)
  def _reduce_139(val, _values, result)
     val[0].append(val[1]); return val[0]
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module Huia

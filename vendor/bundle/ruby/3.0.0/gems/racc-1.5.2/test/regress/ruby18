#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


require 'parser'

module Parser
  class Ruby18 < Racc::Parser

module_eval(<<'...end ruby18.y/module_eval...', 'ruby18.y', 1936)

  def version
    18
  end

  def default_encoding
    Encoding::BINARY if defined? Encoding
  end
...end ruby18.y/module_eval...
##### State transition tables begin ###

clist = [
'-480,195,196,195,196,489,814,-480,-480,-480,511,578,578,-480,-480,-80',
'-480,-429,579,579,489,72,531,-87,558,-480,99,489,-86,73,95,98,395,195',
'196,-480,-480,-82,-480,-480,-480,-480,-480,489,489,558,495,-84,496,-83',
'-81,463,659,658,662,661,186,99,557,558,291,291,98,-80,-480,-480,-480',
'-480,-480,-480,-480,-480,-480,-480,-480,-480,-480,-480,-87,557,-480',
'-480,-480,259,546,530,722,-74,-480,-61,99,-480,291,557,621,98,-480,-86',
'-480,-85,-480,-480,-480,-480,-480,-480,-480,-277,-480,-480,-480,187',
'-476,510,-277,-277,-277,99,-72,488,-277,-277,98,-277,-480,-480,-79,-75',
'-69,-480,-83,-78,99,99,488,621,-76,98,98,99,-74,488,-277,-277,98,-277',
'-277,-277,-277,-277,-76,-74,-75,-73,431,99,99,488,488,621,98,98,-477',
'259,502,195,196,258,503,-74,690,-277,-277,-277,-277,-277,-277,-277,-277',
'-277,-277,-277,-277,-277,-277,558,259,-277,-277,-277,-74,549,99,-74',
'620,-277,713,98,-277,691,-76,188,-77,-277,521,-277,521,-277,-277,-277',
'-277,-277,-277,-277,-272,-277,-82,-277,-76,557,189,-272,-272,-272,99',
'254,-272,-272,-272,98,-272,-277,-277,99,-77,620,-277,-85,98,-76,-272',
'-272,-76,477,190,-84,476,258,254,-272,-272,768,-272,-272,-272,-272,-272',
'99,194,620,477,234,98,479,521,523,522,523,522,519,258,477,358,282,482',
'673,360,359,-272,-272,-272,-272,-272,-272,-272,-272,-272,-272,-272,-272',
'-272,-272,521,192,-272,-272,-272,-81,283,-272,193,-79,-272,521,-87,-272',
'-272,291,-272,191,-272,349,-272,215,-272,-272,-272,-272,-272,-272,-272',
'-226,-272,361,-272,523,522,524,-226,-226,-226,814,394,-226,-226,-226',
'521,-226,-272,-272,-272,-272,396,-272,-473,212,606,-226,496,214,213',
'210,211,523,522,526,-226,-226,397,-226,-226,-226,-226,-226,523,522,527',
'251,477,426,-474,479,-480,-480,252,-319,-226,659,658,662,661,428,-319',
'-226,-226,-226,-417,-476,-226,-226,-226,-319,-226,-417,-417,431,-226',
'523,522,532,-429,-417,-226,-226,284,285,-473,436,254,-226,-417,-226',
'-226,-60,-226,-226,-226,-226,-226,463,-421,451,-480,-480,541,-477,542',
'-421,-473,-480,-480,-226,-474,680,-476,-476,-421,-476,-480,-480,452',
'215,453,-72,-476,-226,-80,-226,215,-476,-226,-226,391,-476,-474,99,-480',
'-480,-226,392,98,99,-78,254,-226,-86,98,-74,393,-76,-82,-476,-84,212',
'-428,-477,-73,214,213,-81,212,-428,-477,459,214,213,-226,-477,461,462',
'-428,-477,195,196,-421,-476,-476,-476,263,-476,-226,-421,-226,-476,-476',
'-226,291,-480,-476,-477,-476,-476,-476,-476,-476,-476,-476,195,196,734',
'606,-476,-476,-476,-476,-476,-476,-476,-427,215,254,-426,-425,662,661',
'-427,464,-476,-426,-425,-476,-476,-476,-476,-476,-476,-476,-476,-476',
'-476,465,-476,-476,-422,-476,-476,-476,-480,734,606,-422,212,215,-423',
'-480,214,213,210,211,-476,-423,457,471,-480,-424,472,-476,692,458,-476',
'-476,-424,-476,-476,291,481,-476,456,-476,484,-476,-480,-476,351,516',
'-271,466,498,500,501,-476,517,-271,467,499,-476,-476,-476,-476,-476',
'-476,-271,393,497,-476,-476,-477,-477,-477,535,-477,536,538,-83,-477',
'-477,-259,540,254,-477,215,-477,-477,-477,-477,-477,-477,-477,215,215',
'215,568,-477,-477,-477,-477,-477,-477,-477,667,668,575,669,93,94,291',
'580,234,-477,590,591,-477,-477,-477,-477,-477,-477,-477,-477,-477,-477',
'-69,-477,-477,592,-477,-477,-477,215,219,224,225,226,221,223,231,232',
'227,228,507,-497,-497,-278,469,229,230,505,-477,549,-278,-477,-477,606',
'-477,-477,506,291,-477,-278,-477,212,-477,218,-477,214,213,210,211,222',
'220,216,-477,217,616,496,624,-477,-477,-477,-477,-477,-477,672,-278',
'675,-477,-477,62,63,64,-278,51,436,436,-85,56,57,693,704,-278,60,431',
'58,59,61,23,24,65,66,431,243,707,708,22,28,27,88,87,89,90,715,717,17',
'721,254,254,215,537,215,41,724,-259,92,91,82,50,84,83,86,85,93,94,728',
'80,81,730,38,39,37,215,219,224,225,226,221,223,231,232,227,228,-277',
'208,209,-279,606,229,230,-277,200,738,-279,204,-477,739,52,53,-277,740',
'54,-279,743,212,745,218,40,214,213,210,211,222,220,216,18,217,749,753',
'755,79,72,74,75,76,77,758,759,760,73,78,761,99,233,763,-215,-277,98',
'62,63,64,7,51,-277,-260,769,56,57,-477,777,778,60,-277,58,59,61,23,24',
'65,66,568,568,254,254,22,28,27,88,87,89,90,234,568,17,101,102,103,104',
'105,6,41,8,9,92,91,82,50,84,83,86,85,93,94,790,80,81,791,38,39,37,215',
'219,224,225,226,221,223,231,232,227,228,-428,-497,-497,823,792,229,230',
'-428,36,797,824,30,799,805,52,53,-428,807,54,822,32,212,291,218,40,214',
'213,210,211,222,220,216,18,217,818,825,826,79,72,74,75,76,77,827,-271',
'829,73,78,62,63,64,-271,51,830,351,-278,56,57,832,835,-271,60,-278,58',
'59,61,246,247,65,66,839,-278,840,846,245,275,279,88,87,89,90,101,102',
'103,104,105,847,848,758,758,276,759,861,92,91,82,50,84,83,86,85,93,94',
'568,80,81,215,568,655,280,653,652,651,654,-277,471,874,-279,875,876',
'880,-277,229,230,-279,883,-477,758,885,772,-277,886,204,-279,568,52',
'53,568,212,54,218,568,214,213,210,211,645,,216,,217,,,,659,658,662,661',
'79,72,74,75,76,77,,,,73,78,,62,63,64,775,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,275,279,88,87,89,90,101,102,103,104,105,,,537',
',276,,,92,91,82,50,84,83,86,85,93,94,,80,81,,,,280,215,219,224,225,226',
'221,223,231,232,227,228,,208,209,,,229,230,,772,,,204,,,52,53,,,54,',
',212,,218,,214,213,210,211,222,220,216,,217,,,,79,72,74,75,76,77,,,',
'73,78,,,233,,855,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66',
',,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,',
',,40,655,,653,652,651,654,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63',
'64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,659,658,662,661,22,28,27',
'88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,36,,,265,,,52,53,,,54,,32,,,,40,655,,653',
'652,651,654,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56',
'57,,,,60,,58,59,61,23,24,65,66,659,658,662,661,22,28,27,88,87,89,90',
',,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,',
',,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,655,,653,652,651,654',
',18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,645',
'58,59,61,23,24,65,66,659,658,662,661,22,28,27,88,87,89,90,,,17,,,,,',
'6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215,-497,-497',
'-497,-497,221,223,,,-497,-497,,,,,,229,230,,36,,,30,,,52,53,,,54,,32',
'212,,218,40,214,213,210,211,222,220,216,18,217,,,,79,72,74,75,76,77',
',,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22',
'28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,',
'80,81,,38,39,37,215,,,,,,,,,,,,,,,,229,230,,36,,,30,,,52,53,,,54,,32',
'212,,218,40,214,213,210,211,,,216,18,217,,,,79,72,74,75,76,77,,,,73',
'78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27',
'88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,215,,,,,,,,,,,,,,,,229,230,,36,,,30,,,52,53,,,54,,32,212,',
'218,40,214,213,210,211,,,216,18,217,,,,79,72,74,75,76,77,,,,73,78,5',
'62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87',
'89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,215,,,,,,,,,,,,,,,,229,230,,36,,,265,,,52,53,,,54,,32,212,,218,40',
'214,213,210,211,,,216,18,217,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64',
'7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,',
',17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215',
'-497,-497,-497,-497,221,223,,,-497,-497,,,,,,229,230,,36,,,265,,,52',
'53,,,54,,32,212,,218,40,214,213,210,211,222,220,216,18,217,,,,79,72',
'74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24',
'65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,215,-497,-497,-497,-497,221,223,,,-497',
'-497,,,,,,229,230,,36,,,30,,,52,53,,,54,,32,212,,218,40,214,213,210',
'211,222,220,216,18,217,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51',
',,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,',
',,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215,-497',
'-497,-497,-497,221,223,,,-497,-497,,,,,,229,230,,36,,,30,,,52,53,,,54',
',32,212,,218,40,214,213,210,211,222,220,216,18,217,,,,79,72,74,75,76',
'77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,',
',22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,215,-497,-497,-497,-497,221,223,,,-497,-497,,,,',
',229,230,,36,,,30,,,52,53,,,54,,32,212,,218,40,214,213,210,211,222,220',
'216,18,217,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,',
',60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8',
'9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215,-497,-497,-497',
'-497,221,223,,,-497,-497,,,,,,229,230,,36,,,30,,,52,53,,,54,,32,212',
',218,40,214,213,210,211,222,220,216,18,217,,,,79,72,74,75,76,77,,,,73',
'78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27',
'88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,215,219,224,225,226,221,223,,,227,228,,,,,,229,230,,36,,,30',
',,52,53,,,54,,32,212,,218,40,214,213,210,211,222,220,216,18,217,,,,79',
'72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,215,219,224,225,226,221,223,231,,227',
'228,,,,,,229,230,,36,,,30,,,52,53,,,54,,32,212,,218,40,214,213,210,211',
'222,220,216,18,217,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,',
'56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,',
',6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215,,,,,,,',
',,,,,,,,229,230,,36,,,30,,,52,53,,,54,,32,212,,218,40,214,213,210,211',
',,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60',
',58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,215,,,,,,,,,,,,,,,,229,230',
',36,,,30,,,52,53,,,54,,32,212,,218,40,214,213,210,211,,,,18,,,,,79,72',
'74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24',
'65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,215,,,,,,,,,,,,,,,,229,230,,36,,,30,,,52',
'53,,,54,,32,212,,,40,214,213,210,211,,,,18,,,,,79,72,74,75,76,77,,,',
'73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28',
'27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,',
',,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,',
'58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,',
'30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5',
'62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87',
'89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79',
'72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,',
',54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7',
'51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17',
',,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,',
',,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75',
'76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66',
',,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,',
',,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56',
'57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6',
'41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,',
',,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77',
',,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22',
'28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,',
'80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,',
',,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60',
',58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,',
'30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5',
'62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87',
'89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79',
'72,74,75,76,77,,,,73,78,5,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,',
',54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,5,62,63,64,7',
'51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17',
',,,,,6,41,8,9,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,',
',,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40,,,,,,,,18,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,',
',,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,,,,40',
',,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,153,164,154,177,150,170,160',
'159,180,181,175,158,157,152,178,182,183,162,151,165,169,171,163,156',
',,172,179,174,173,166,176,161,149,168,167,,,,,,148,155,146,147,144,145',
'109,111,108,,110,,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,',
'141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125',
'118,,119,,,143,79,,,,,,,,,,78,153,164,154,177,150,170,160,159,180,181',
'175,158,157,152,178,182,183,162,151,165,169,171,163,156,,,172,179,174',
'173,166,176,161,149,168,167,,,,,,148,155,146,147,144,145,109,111,,,110',
',,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,',
',,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,,,143',
'79,,,62,63,64,,51,,,78,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27',
'88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38',
'39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,',
',,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86',
'85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54',
',,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56',
'57,,,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,',
'41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,',
',,,200,,,204,,,52,53,,,54,,241,,243,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,241,,243,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,241,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'-249,-249,-249,,-249,,,,-249,-249,,,,-249,,-249,-249,-249,-249,-249',
'-249,-249,,,,,-249,-249,-249,-249,-249,-249,-249,,,,,,,,,,-249,,,-249',
'-249,-249,-249,-249,-249,-249,-249,-249,-249,,-249,-249,,-249,-249,-249',
',,,,,,,,,,,,,,,,,,,-249,,,-249,254,,-249,-249,,,-249,,-249,,-249,,-249',
',,,,,,,-249,,,,,-249,-249,-249,-249,-249,-249,,,,-249,-249,-249,-249',
'-249,,-249,,,,-249,-249,,,,-249,,-249,-249,-249,-249,-249,-249,-249',
',,,,-249,-249,-249,-249,-249,-249,-249,,,,,,,,,,-249,,,-249,-249,-249',
'-249,-249,-249,-249,-249,-249,-249,,-249,-249,,-249,-249,-249,,,,,,',
',,,,,,,,,,,,,-249,,,-249,263,,-249,-249,,,-249,,-249,,-249,,-249,,,',
',,,,-249,,,,,-249,-249,-249,-249,-249,-249,,,,-249,-249,62,63,64,,51',
',,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,',
',,,,,,,,276,,,92,91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224',
'225,226,221,223,231,232,227,228,,208,209,,,229,230,273,,,270,,,52,53',
',,54,,269,,212,,218,,214,213,210,211,222,220,216,,217,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,233,51,568,,,56,57,,,,60,,58,59,61,246,247,65',
'66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,82,50,84,83,86,85',
'93,94,,80,81,,,,280,,215,219,224,225,226,221,223,231,232,227,228,,208',
'209,,,229,230,273,,,204,,,52,53,,,54,,,,212,,218,,214,213,210,211,222',
'220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63,64,233,51,,,,56,57,',
',,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41',
',,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,',
'200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73',
'78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,',
',,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,',
',,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245',
'28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,299,,,,40,,,,,',
',,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58',
'59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90',
',,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,',
',,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74',
'75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66',
',,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,,,229,230,315,,,30,,,52,53,,,54,,32,,212,,218',
',214,213,210,211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88',
'87,89,90,,,,,,,,,,276,,,92,91,320,50,84,83,321,85,93,94,,80,81,,,,280',
',215,219,224,225,226,221,223,231,232,227,228,,208,209,,327,229,230,322',
',,204,,,52,53,,,54,,,,212,,218,,214,213,210,211,222,220,216,,217,,,79',
'72,74,75,76,77,,,,73,78,62,63,64,233,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,320,50,84',
'83,321,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223,231,232,227',
'228,,208,209,,,229,230,322,,,204,,,52,53,,,54,,,,212,,218,,214,213,210',
'211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,-473,-473,-473,233',
'-473,,,,-473,-473,,,,-473,,-473,-473,-473,-473,-473,-473,-473,,-473',
',,-473,-473,-473,-473,-473,-473,-473,,,,,,,,,,-473,,,-473,-473,-473',
'-473,-473,-473,-473,-473,-473,-473,,-473,-473,,-473,-473,-473,,,,,,',
',,,,,,,,,,,,,-473,,,-473,-473,,-473,-473,,,-473,,-473,,-473,,-473,,',
',,,,,-473,,-473,,,-473,-473,-473,-473,-473,-473,,,,-473,-473,-474,-474',
'-474,,-474,,,,-474,-474,,,,-474,,-474,-474,-474,-474,-474,-474,-474',
',-474,,,-474,-474,-474,-474,-474,-474,-474,,,,,,,,,,-474,,,-474,-474',
'-474,-474,-474,-474,-474,-474,-474,-474,,-474,-474,,-474,-474,-474,',
',,,,,,,,,,,,,,,,,,-474,,,-474,-474,,-474,-474,,,-474,,-474,,-474,,-474',
',,,,,,,-474,,-474,,,-474,-474,-474,-474,-474,-474,,,,-474,-474,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90',
',,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,',
',,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74',
'75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66',
',,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90',
',,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,',
',,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74',
'75,76,77,,,,73,78,62,63,64,7,51,,,,56,57,,,,60,,58,59,61,23,24,65,66',
',,,,22,28,27,88,87,89,90,,,17,,,,,,6,41,8,9,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,36,,,30,,,52,53,,,54,,32,',
',,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57',
',,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,369,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73',
'78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,369,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86',
'85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54',
',,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56',
'57,,,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,',
'41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,',
',,,200,,,204,,,52,53,,,54,,299,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28',
'27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38',
'39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,',
',,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53',
',,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,',
',,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,',
',,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,',
',,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245',
'275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,',
',,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58',
'59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87',
'89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,',
',,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72',
'74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65',
'66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,',
',,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57',
',,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41',
',,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,',
'200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73',
'78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,',
',,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,',
',,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,',
',,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,',
',,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,',
',,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,',
',,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,',
',,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,',
',,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,',
',,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,',
',,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,241,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,445,53,,,54,,241,,243,,40,,,,,,,,207',
',,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,449,52',
'53,,,54,,241,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87',
'89,90,,,,,,,,,,276,,,92,91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215',
'219,224,225,226,221,223,231,232,227,228,,208,209,,,229,230,273,,,204',
',,52,53,,,54,,,,212,,218,,214,213,210,211,222,220,216,,217,,,79,72,74',
'75,76,77,,,,73,78,62,63,64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65',
'66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,469,,52,53,,,54',
',,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56',
'57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,',
',,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,',
',,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28',
'27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18',
',,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53',
',,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,',
',,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,',
',,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,',
',,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28',
'27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18',
',,,,79,72,74,75,76,77,,,,73,78,153,164,154,177,150,170,160,159,180,181',
'175,158,157,152,178,182,183,162,151,165,169,171,163,156,,,172,179,174',
'173,166,176,161,149,168,167,,,,,,148,155,146,147,144,145,109,111,,,110',
',,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,',
',,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,,,143',
'79,,,62,63,64,,51,,,78,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275',
'279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,',
'38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207',
',,,,79,72,74,75,76,77,,,,73,78,-249,-249,-249,,-249,,,,-249,-249,,,',
'-249,,-249,-249,-249,-249,-249,-249,-249,,,,,-249,-249,-249,-249,-249',
'-249,-249,,,,,,,,,,-249,,,-249,-249,-249,-249,-249,-249,-249,-249,-249',
'-249,,-249,-249,,-249,-249,-249,,,,,,,,,,,,,,,,,,,,-249,,,-249,254,',
'-249,-249,,,-249,,-249,,-249,,-249,,,,,,,,-249,,,,,-249,-249,-249,-249',
'-249,-249,,,,-249,-249,-478,-478,-478,,-478,,,,-478,-478,,,,-478,,-478',
'-478,-478,-478,-478,-478,-478,,,,,-478,-478,-478,-478,-478,-478,-478',
',,,,,,,,,-478,,,-478,-478,-478,-478,-478,-478,-478,-478,-478,-478,,-478',
'-478,,-478,-478,-478,,,,,,,,,,,,,,,,,,,,-478,,,-478,-478,,-478,-478',
',,-478,,-478,,-478,,-478,,,,,,,,-478,,,,,-478,-478,-478,-478,-478,-478',
',,,-478,-478,-479,-479,-479,,-479,,,,-479,-479,,,,-479,,-479,-479,-479',
'-479,-479,-479,-479,,,,,-479,-479,-479,-479,-479,-479,-479,,,,,,,,,',
'-479,,,-479,-479,-479,-479,-479,-479,-479,-479,-479,-479,,-479,-479',
',-479,-479,-479,,,,,,,,,,,,,,,,,,,,-479,,,-479,-479,,-479,-479,,,-479',
',-479,,-479,,-479,,,,,,,,-479,,,,,-479,-479,-479,-479,-479,-479,,,,-479',
'-479,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,28',
'27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38',
'39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,299,,,,40,,,,,,,,207',
',,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50',
'84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52',
'53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,',
'51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90',
',,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,',
',,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,560,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,',
'73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275',
'279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,',
'38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,564,,243,,40,,,',
',,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,',
'58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87',
'89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,584,,243,,40,,,,,,,,18,,',
',,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,299,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51',
',,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,',
',,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,',
',,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87',
'89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72',
'74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65',
'66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,369',
',,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57',
',,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41',
',,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,',
'200,,,204,,,52,53,,,54,,612,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,',
',,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245',
'275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,',
',,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58',
'59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91',
'82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204',
',,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87',
'89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,',
',,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,628,,,,40,,,,,,,,207,,,,,79',
'72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247',
'65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85',
'93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,299',
',,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57',
',,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,,41',
',,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,',
'200,,,204,,,52,53,,,54,,299,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,',
',,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28',
'27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18',
',,,,79,72,74,75,76,77,,,,73,78,153,164,154,177,150,170,160,159,180,181',
'175,158,157,152,178,182,183,162,151,165,169,171,163,156,,,172,179,174',
'173,166,176,161,149,168,167,,,,,,148,155,146,147,144,145,109,111,,,110',
',,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,',
',,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,,,143',
'79,,,62,63,64,,51,,,78,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275',
'279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,',
'38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207',
',,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50',
'84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52',
'53,,,54,,679,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64',
',51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,',
',,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,',
',,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,',
',22,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,',
',,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58',
'59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50',
'84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52',
'53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,',
'51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90',
',,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,',
',,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,,,280,,,,,,,,,,,,,,,,,,,,273,,,270',
',,52,53,,,54,,697,,698,,,,,,,,,699,,,,,,79,72,74,75,76,77,,,,73,78,62',
'63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79',
'72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247',
'65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86',
'85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54',
',,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56',
'57,,,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,,,,,',
'41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,',
',,,200,,,204,,,52,53,,,54,,560,,243,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,',
'245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94',
',80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,',
',,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23',
'24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,,,,,41,,,92,91,82,50,84,83',
'86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,',
'54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,',
'56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,',
',,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,',
',,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76',
'77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,23,24,65,66,,,,,22',
'28,27,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81',
',38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207',
',,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50',
'84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52',
'53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,',
'51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90',
',,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,',
',,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,,,229,230,772,,,204,,,52,53,,,54,,,,212,,218',
',214,213,210,211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,780,,243,,40,,,,,,,,207,',
',,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61',
'246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50',
'84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52',
'53,,,54,,786,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87',
'89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,',
',,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,788,,243,,40,,,,,,,,207,,',
',,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223,231,232,227',
'228,,208,209,,,229,230,772,,,204,,,52,53,,,54,,,,212,,218,,214,213,210',
'211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63,64,233,51,,',
',56,57,,,,60,,58,59,61,23,24,65,66,,,,,22,28,27,88,87,89,90,,,17,,,',
',,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,',
',,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,18,,,,,79,72,74,75,76,77',
',,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245',
'275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,802,,,,40,,',
',,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,,,280,,,,,,,,,,,,,,,,,,,,273,,,270,,,52,53,,',
'54,,821,,820,,,,,,,,,,,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,',
',,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,',
',,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,',
',,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78',
'62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279',
'88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39',
'37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,',
'79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246',
'247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,82,50,84',
'83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223,231,232,227',
'228,,208,209,,,229,230,772,,,204,,,52,53,,,54,,,,212,,218,,214,213,210',
'211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63,64,233,51,,',
',56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,28,27,88,87,89,90,,,,,',
',,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,',
',,,,,,,200,,,204,,,52,53,,,54,,299,,,,40,,,,,,,,207,,,,,79,72,74,75',
'76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,',
',,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92,91,82,50,84,83,86,85,93',
'94,,80,81,,,,280,,215,219,224,225,226,221,223,231,232,227,228,,208,209',
',,229,230,772,,,204,,,52,53,,,54,,,,212,,218,,214,213,210,211,222,220',
'216,,217,,,79,72,74,75,76,77,,,,73,78,62,63,64,233,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,',
',204,,,52,53,,,54,,864,,243,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,',
'73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275',
'279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,',
'38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,867,,243,,40,,,',
',,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,',
'58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,,,229,230,772,,,204,,,52,53,,,54,,,,212,,218',
',214,213,210,211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79',
'72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60,,58,59,61,246,247',
'65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86',
'85,93,94,,80,81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54',
',,,,,40,,,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56',
'57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,',
',,276,,,92,91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224,225',
'226,221,223,231,232,227,228,,208,209,,,229,230,772,,,204,,,52,53,,,54',
',,,212,,218,,214,213,210,211,222,220,216,,217,,,79,72,74,75,76,77,,',
',73,78,62,63,64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245',
'275,279,88,87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80',
'81,,38,39,37,,,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,889,,243,,40',
',,,,,,,207,,,,,79,72,74,75,76,77,,,,73,78,62,63,64,,51,,,,56,57,,,,60',
',58,59,61,246,247,65,66,,,,,245,275,279,88,87,89,90,,,,,,,,,,276,,,92',
'91,82,50,84,83,86,85,93,94,,80,81,,,,280,,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,,,229,230,772,,,204,,,52,53,,,54,,,,212,,218',
',214,213,210,211,222,220,216,,217,,,79,72,74,75,76,77,,,,73,78,62,63',
'64,233,51,,,,56,57,,,,60,,58,59,61,246,247,65,66,,,,,245,275,279,88',
'87,89,90,,,,,,,,,,41,,,92,91,82,50,84,83,86,85,93,94,,80,81,,38,39,37',
',,,,,,,,,,,,,,,,,,,200,,,204,,,52,53,,,54,,,,,,40,,,,,,,,207,,,,,79',
'72,74,75,76,77,,,,73,78,153,164,154,177,150,170,160,159,180,181,175',
'158,157,152,178,182,183,162,151,165,169,171,163,156,,,172,179,174,336',
'335,337,334,149,168,167,,,,,,148,155,146,147,332,333,330,111,84,83,331',
'85,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130',
',,,,,341,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,,',
'143,153,164,154,177,150,170,160,159,180,181,175,158,157,152,178,182',
'183,162,151,165,169,171,163,156,,,172,179,174,173,166,176,161,149,168',
'167,,,,,,148,155,146,147,144,145,109,111,,,110,,,,,,,,139,140,,137,121',
'122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120',
'138,136,135,131,132,127,125,118,,119,,,143,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,,,229,230,,,,-215,,,,,,,,,,,212,,218,,214,213',
'210,211,222,220,216,,217,,,,,,,,376,379,,,377,,,,,233,,-215,139,140',
',137,121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134',
'133,,120,138,136,135,131,132,127,125,118,,119,381,385,143,,383,,,,,',
',,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,',
',,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,433,379,143',
',434,,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129',
'130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,433',
'379,143,,434,,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141',
'142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118',
',119,550,379,143,,551,,,,,,,,139,140,,137,121,122,123,,126,128,,,124',
',,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127',
'125,118,,119,552,385,143,,553,,,,,,,,139,140,,137,121,122,123,,126,128',
',,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131',
'132,127,125,118,,119,,,143,215,219,224,225,226,221,223,231,232,227,228',
',208,209,,,229,230,,,,,,,,,,,,,,,212,,218,,214,213,210,211,222,220,216',
',217,,,,,,,594,379,,,595,,,,,,233,556,139,140,,137,121,122,123,,126',
'128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135',
'131,132,127,125,118,,119,597,385,143,,598,,,,,,,,139,140,,137,121,122',
'123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138',
'136,135,131,132,127,125,118,,119,550,379,143,,551,,,,,,,,139,140,,137',
'121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133',
',120,138,136,135,131,132,127,125,118,,119,552,385,143,,553,,,,,,,,139',
'140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,',
',134,133,,120,138,136,135,131,132,127,125,118,,119,630,379,143,,631',
',,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,',
',,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119,632,385',
'143,,633,,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,,141,142',
'129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125,118,,119',
'635,385,143,,636,,,,,,,,139,140,,137,121,122,123,,126,128,,,124,,,,',
'141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132,127,125',
'118,,119,433,379,143,,434,,,,,,,,139,140,,137,121,122,123,,126,128,',
',124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135,131,132',
'127,125,118,,119,433,379,143,,434,,,,,,,,139,140,,137,121,122,123,,126',
'128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138,136,135',
'131,132,127,125,118,,119,433,379,143,,434,,,,,,,,139,140,,137,121,122',
'123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133,,120,138',
'136,135,131,132,127,125,118,,119,857,379,143,,858,,,,,,,,139,140,,137',
'121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,,,134,133',
',120,138,136,135,131,132,127,125,118,,119,859,385,143,,860,,,,,,,,139',
'140,,137,121,122,123,,126,128,,,124,,,,,141,142,129,130,,,,,,,,,,,,',
',134,133,,120,138,136,135,131,132,127,125,118,,119,,,143,215,219,224',
'225,226,221,223,231,232,227,228,,208,209,,,229,230,,,,,,,,,,,,,,,212',
',218,,214,213,210,211,222,220,216,,217,,215,219,224,225,226,221,223',
'231,232,227,228,,208,209,291,233,229,230,,,,,,,,,,,,,,,212,,218,,214',
'213,210,211,222,220,216,,217,,215,219,224,225,226,221,223,231,232,227',
'228,,208,209,291,233,229,230,,,,,,,,,,,,,,,212,,218,,214,213,210,211',
'222,220,216,,217,,,,,,,,,,,,,,,,,233' ]
        racc_action_table = arr = ::Array.new(24340, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'381,512,512,518,518,306,755,381,381,381,326,440,711,381,381,313,381',
'199,440,711,307,70,347,316,424,381,3,627,500,70,1,3,201,603,603,381',
'381,630,381,381,381,381,381,727,798,446,311,631,311,632,743,394,755',
'755,755,755,8,310,424,447,440,711,310,199,381,381,381,381,381,381,381',
'381,381,381,381,381,381,381,201,446,381,381,381,55,381,347,603,594,381',
'591,512,381,518,447,491,512,381,394,381,633,381,381,381,381,381,381',
'381,383,381,381,381,9,632,326,383,383,383,306,313,306,383,383,306,383',
'381,381,316,381,591,381,381,500,307,326,307,492,595,307,326,627,630',
'627,383,383,627,383,383,383,383,383,631,594,632,743,582,727,798,727',
'798,744,727,798,633,26,322,15,15,55,322,594,550,383,383,383,383,383',
'383,383,383,383,383,383,383,383,383,574,277,383,383,383,594,383,491',
'594,491,383,582,491,383,551,595,10,633,383,528,383,343,383,383,383,383',
'383,383,383,50,383,550,383,595,574,11,50,50,50,511,26,50,50,50,511,50',
'383,383,492,383,492,383,383,492,595,50,50,595,297,12,551,297,26,277',
'50,50,693,50,50,50,50,50,744,14,744,298,20,744,298,344,528,528,343,343',
'343,277,301,108,34,301,528,108,108,50,50,50,50,50,50,50,50,50,50,50',
'50,50,50,345,13,50,50,50,693,36,50,13,14,50,346,14,50,50,41,50,13,50',
'95,50,419,50,50,50,50,50,50,50,432,50,184,50,344,344,344,432,432,432',
'848,200,432,432,432,348,432,50,50,50,50,202,50,320,419,803,432,803,419',
'419,419,419,345,345,345,432,432,203,432,432,432,432,432,346,346,346',
'25,303,239,321,303,597,635,25,42,435,848,848,848,848,240,42,435,435',
'435,330,857,435,435,435,42,435,330,320,244,432,348,348,348,35,320,435',
'432,37,37,320,253,432,432,320,435,435,264,435,435,435,435,435,283,321',
'266,597,635,376,858,377,321,320,597,635,432,321,540,597,635,321,857',
'597,635,267,400,268,35,857,432,35,432,401,857,432,435,198,857,321,640',
'597,635,435,198,640,646,283,435,435,283,646,376,198,377,376,857,377',
'400,271,858,540,400,400,540,401,271,858,273,401,401,435,858,280,280',
'271,858,294,294,331,552,552,552,275,552,435,331,435,552,552,435,276',
'859,552,858,552,552,552,552,552,552,552,305,305,615,615,552,552,552',
'552,552,552,552,332,420,279,333,334,758,758,332,284,552,333,334,552',
'552,552,552,552,552,552,552,552,552,285,552,552,335,552,552,552,859',
'870,870,335,420,288,336,859,420,420,420,420,859,336,272,292,859,337',
'293,552,552,272,552,552,337,552,552,296,300,552,272,552,302,552,859',
'552,312,339,274,286,314,315,317,552,339,274,286,314,552,552,552,552',
'552,552,274,286,314,552,552,553,553,553,362,553,363,368,552,553,553',
'371,375,382,553,402,553,553,553,553,553,553,553,403,404,405,429,553',
'553,553,553,553,553,553,522,522,438,522,522,522,439,441,442,553,448',
'450,553,553,553,553,553,553,553,553,553,553,451,553,553,455,553,553',
'553,398,398,398,398,398,398,398,398,398,398,398,324,398,398,459,460',
'398,398,324,553,553,459,553,553,470,553,553,324,473,553,459,553,398',
'553,398,553,398,398,398,398,398,398,398,553,398,487,493,494,553,553',
'553,553,553,553,526,503,531,553,553,60,60,60,503,60,543,547,553,60,60',
'554,561,503,60,563,60,60,60,60,60,60,60,566,568,576,577,60,60,60,60',
'60,60,60,583,586,60,593,596,599,601,366,602,60,604,607,60,60,60,60,60',
'60,60,60,60,60,608,60,60,611,60,60,60,366,366,366,366,366,366,366,366',
'366,366,366,598,366,366,721,617,366,366,598,60,623,721,60,598,625,60',
'60,598,626,60,721,629,366,638,366,60,366,366,366,366,366,366,366,60',
'366,643,647,648,60,60,60,60,60,60,649,655,660,60,60,663,60,366,665,366',
'636,60,97,97,97,97,97,636,678,696,97,97,636,700,701,97,636,97,97,97',
'97,97,97,97,702,705,709,710,97,97,97,97,97,97,97,712,716,97,4,4,4,4',
'4,97,97,97,97,97,97,97,97,97,97,97,97,97,97,718,97,97,719,97,97,97,399',
'399,399,399,399,399,399,399,399,399,399,770,399,399,771,720,399,399',
'770,97,732,771,97,735,746,97,97,770,750,97,771,97,399,751,399,97,399',
'399,399,399,399,399,399,97,399,764,772,774,97,97,97,97,97,97,775,773',
'782,97,97,697,697,697,773,697,783,97,825,697,697,784,787,773,697,825',
'697,697,697,697,697,697,697,800,825,801,806,697,697,697,697,697,697',
'697,106,106,106,106,106,808,809,810,813,697,814,826,697,697,697,697',
'697,697,697,697,697,697,833,697,697,410,836,514,697,514,514,514,514',
'860,837,854,876,855,856,865,860,410,410,876,871,860,872,873,697,860',
'874,697,876,878,697,697,881,410,697,410,892,410,410,410,410,514,,410',
',410,,,,514,514,514,514,697,697,697,697,697,697,,,,697,697,,821,821',
'821,697,821,,,,821,821,,,,821,,821,821,821,821,821,821,821,,,,,821,821',
'821,821,821,821,821,350,350,350,350,350,,,555,,821,,,821,821,821,821',
'821,821,821,821,821,821,,821,821,,,,821,555,555,555,555,555,555,555',
'555,555,555,555,,555,555,,,555,555,,821,,,821,,,821,821,,,821,,,555',
',555,,555,555,555,555,555,555,555,,555,,,,821,821,821,821,821,821,,',
',821,821,,,555,,821,0,0,0,0,0,0,,,,0,0,,,,0,,0,0,0,0,0,0,0,,,,,0,0,0',
'0,0,0,0,,,0,,,,,,0,0,0,0,0,0,0,0,0,0,0,0,0,0,,0,0,,0,0,0,,,,,,,,,,,',
',,,,,,,,0,,,0,,,0,0,,,0,,0,,,,0,645,,645,645,645,645,,0,,,,,0,0,0,0',
'0,0,,,,0,0,30,30,30,30,30,30,,,,30,30,,,,30,,30,30,30,30,30,30,30,645',
'645,645,645,30,30,30,30,30,30,30,,,30,,,,,,30,30,30,30,30,30,30,30,30',
'30,30,30,30,30,,30,30,,30,30,30,,,,,,,,,,,,,,,,,,,,30,,,30,,,30,30,',
',30,,30,,,,30,753,,753,753,753,753,,30,,,,,30,30,30,30,30,30,,,,30,30',
'51,51,51,51,51,51,,,,51,51,,,,51,,51,51,51,51,51,51,51,753,753,753,753',
'51,51,51,51,51,51,51,,,51,,,,,,51,51,51,51,51,51,51,51,51,51,51,51,51',
'51,,51,51,,51,51,51,,,,,,,,,,,,,,,,,,,,51,,,51,,,51,51,,,51,,51,,,,51',
'817,,817,817,817,817,,51,,,,,51,51,51,51,51,51,,,,51,51,186,186,186',
'186,186,186,,,,186,186,,,,186,817,186,186,186,186,186,186,186,817,817',
'817,817,186,186,186,186,186,186,186,,,186,,,,,,186,186,186,186,186,186',
'186,186,186,186,186,186,186,186,,186,186,,186,186,186,409,409,409,409',
'409,409,409,,,409,409,,,,,,409,409,,186,,,186,,,186,186,,,186,,186,409',
',409,186,409,409,409,409,409,409,409,186,409,,,,186,186,186,186,186',
'186,,,,186,186,187,187,187,187,187,187,,,,187,187,,,,187,,187,187,187',
'187,187,187,187,,,,,187,187,187,187,187,187,187,,,187,,,,,,187,187,187',
'187,187,187,187,187,187,187,187,187,187,187,,187,187,,187,187,187,411',
',,,,,,,,,,,,,,,411,411,,187,,,187,,,187,187,,,187,,187,411,,411,187',
'411,411,411,411,,,411,187,411,,,,187,187,187,187,187,187,,,,187,187',
'204,204,204,204,204,204,,,,204,204,,,,204,,204,204,204,204,204,204,204',
',,,,204,204,204,204,204,204,204,,,204,,,,,,204,204,204,204,204,204,204',
'204,204,204,204,204,204,204,,204,204,,204,204,204,412,,,,,,,,,,,,,,',
',412,412,,204,,,204,,,204,204,,,204,,204,412,,412,204,412,412,412,412',
',,412,204,412,,,,204,204,204,204,204,204,,,,204,204,265,265,265,265',
'265,265,,,,265,265,,,,265,,265,265,265,265,265,265,265,,,,,265,265,265',
'265,265,265,265,,,265,,,,,,265,265,265,265,265,265,265,265,265,265,265',
'265,265,265,,265,265,,265,265,265,413,,,,,,,,,,,,,,,,413,413,,265,,',
'265,,,265,265,,,265,,265,413,,413,265,413,413,413,413,,,413,265,413',
',,,265,265,265,265,265,265,,,,265,265,270,270,270,270,270,270,,,,270',
'270,,,,270,,270,270,270,270,270,270,270,,,,,270,270,270,270,270,270',
'270,,,270,,,,,,270,270,270,270,270,270,270,270,270,270,270,270,270,270',
',270,270,,270,270,270,414,414,414,414,414,414,414,,,414,414,,,,,,414',
'414,,270,,,270,,,270,270,,,270,,270,414,,414,270,414,414,414,414,414',
'414,414,270,414,,,,270,270,270,270,270,270,,,,270,270,486,486,486,486',
'486,486,,,,486,486,,,,486,,486,486,486,486,486,486,486,,,,,486,486,486',
'486,486,486,486,,,486,,,,,,486,486,486,486,486,486,486,486,486,486,486',
'486,486,486,,486,486,,486,486,486,415,415,415,415,415,415,415,,,415',
'415,,,,,,415,415,,486,,,486,,,486,486,,,486,,486,415,,415,486,415,415',
'415,415,415,415,415,486,415,,,,486,486,486,486,486,486,,,,486,486,490',
'490,490,490,490,490,,,,490,490,,,,490,,490,490,490,490,490,490,490,',
',,,490,490,490,490,490,490,490,,,490,,,,,,490,490,490,490,490,490,490',
'490,490,490,490,490,490,490,,490,490,,490,490,490,416,416,416,416,416',
'416,416,,,416,416,,,,,,416,416,,490,,,490,,,490,490,,,490,,490,416,',
'416,490,416,416,416,416,416,416,416,490,416,,,,490,490,490,490,490,490',
',,,490,490,495,495,495,495,495,495,,,,495,495,,,,495,,495,495,495,495',
'495,495,495,,,,,495,495,495,495,495,495,495,,,495,,,,,,495,495,495,495',
'495,495,495,495,495,495,495,495,495,495,,495,495,,495,495,495,417,417',
'417,417,417,417,417,,,417,417,,,,,,417,417,,495,,,495,,,495,495,,,495',
',495,417,,417,495,417,417,417,417,417,417,417,495,417,,,,495,495,495',
'495,495,495,,,,495,495,513,513,513,513,513,513,,,,513,513,,,,513,,513',
'513,513,513,513,513,513,,,,,513,513,513,513,513,513,513,,,513,,,,,,513',
'513,513,513,513,513,513,513,513,513,513,513,513,513,,513,513,,513,513',
'513,418,418,418,418,418,418,418,,,418,418,,,,,,418,418,,513,,,513,,',
'513,513,,,513,,513,418,,418,513,418,418,418,418,418,418,418,513,418',
',,,513,513,513,513,513,513,,,,513,513,559,559,559,559,559,559,,,,559',
'559,,,,559,,559,559,559,559,559,559,559,,,,,559,559,559,559,559,559',
'559,,,559,,,,,,559,559,559,559,559,559,559,559,559,559,559,559,559,559',
',559,559,,559,559,559,421,421,421,421,421,421,421,,,421,421,,,,,,421',
'421,,559,,,559,,,559,559,,,559,,559,421,,421,559,421,421,421,421,421',
'421,421,559,421,,,,559,559,559,559,559,559,,,,559,559,588,588,588,588',
'588,588,,,,588,588,,,,588,,588,588,588,588,588,588,588,,,,,588,588,588',
'588,588,588,588,,,588,,,,,,588,588,588,588,588,588,588,588,588,588,588',
'588,588,588,,588,588,,588,588,588,422,422,422,422,422,422,422,422,,422',
'422,,,,,,422,422,,588,,,588,,,588,588,,,588,,588,422,,422,588,422,422',
'422,422,422,422,422,588,422,,,,588,588,588,588,588,588,,,,588,588,589',
'589,589,589,589,589,,,,589,589,,,,589,,589,589,589,589,589,589,589,',
',,,589,589,589,589,589,589,589,,,589,,,,,,589,589,589,589,589,589,589',
'589,589,589,589,589,589,589,,589,589,,589,589,589,406,,,,,,,,,,,,,,',
',406,406,,589,,,589,,,589,589,,,589,,589,406,,406,589,406,406,406,406',
',,,589,,,,,589,589,589,589,589,589,,,,589,589,606,606,606,606,606,606',
',,,606,606,,,,606,,606,606,606,606,606,606,606,,,,,606,606,606,606,606',
'606,606,,,606,,,,,,606,606,606,606,606,606,606,606,606,606,606,606,606',
'606,,606,606,,606,606,606,407,,,,,,,,,,,,,,,,407,407,,606,,,606,,,606',
'606,,,606,,606,407,,407,606,407,407,407,407,,,,606,,,,,606,606,606,606',
'606,606,,,,606,606,639,639,639,639,639,639,,,,639,639,,,,639,,639,639',
'639,639,639,639,639,,,,,639,639,639,639,639,639,639,,,639,,,,,,639,639',
'639,639,639,639,639,639,639,639,639,639,639,639,,639,639,,639,639,639',
'408,,,,,,,,,,,,,,,,408,408,,639,,,639,,,639,639,,,639,,639,408,,,639',
'408,408,408,408,,,,639,,,,,639,639,639,639,639,639,,,,639,639,644,644',
'644,644,644,644,,,,644,644,,,,644,,644,644,644,644,644,644,644,,,,,644',
'644,644,644,644,644,644,,,644,,,,,,644,644,644,644,644,644,644,644,644',
'644,644,644,644,644,,644,644,,644,644,644,,,,,,,,,,,,,,,,,,,,644,,,644',
',,644,644,,,644,,644,,,,644,,,,,,,,644,,,,,644,644,644,644,644,644,',
',,644,644,671,671,671,671,671,671,,,,671,671,,,,671,,671,671,671,671',
'671,671,671,,,,,671,671,671,671,671,671,671,,,671,,,,,,671,671,671,671',
'671,671,671,671,671,671,671,671,671,671,,671,671,,671,671,671,,,,,,',
',,,,,,,,,,,,,671,,,671,,,671,671,,,671,,671,,,,671,,,,,,,,671,,,,,671',
'671,671,671,671,671,,,,671,671,706,706,706,706,706,706,,,,706,706,,',
',706,,706,706,706,706,706,706,706,,,,,706,706,706,706,706,706,706,,',
'706,,,,,,706,706,706,706,706,706,706,706,706,706,706,706,706,706,,706',
'706,,706,706,706,,,,,,,,,,,,,,,,,,,,706,,,706,,,706,706,,,706,,706,',
',,706,,,,,,,,706,,,,,706,706,706,706,706,706,,,,706,706,724,724,724',
'724,724,724,,,,724,724,,,,724,,724,724,724,724,724,724,724,,,,,724,724',
'724,724,724,724,724,,,724,,,,,,724,724,724,724,724,724,724,724,724,724',
'724,724,724,724,,724,724,,724,724,724,,,,,,,,,,,,,,,,,,,,724,,,724,',
',724,724,,,724,,724,,,,724,,,,,,,,724,,,,,724,724,724,724,724,724,,',
',724,724,736,736,736,736,736,736,,,,736,736,,,,736,,736,736,736,736',
'736,736,736,,,,,736,736,736,736,736,736,736,,,736,,,,,,736,736,736,736',
'736,736,736,736,736,736,736,736,736,736,,736,736,,736,736,736,,,,,,',
',,,,,,,,,,,,,736,,,736,,,736,736,,,736,,736,,,,736,,,,,,,,736,,,,,736',
'736,736,736,736,736,,,,736,736,737,737,737,737,737,737,,,,737,737,,',
',737,,737,737,737,737,737,737,737,,,,,737,737,737,737,737,737,737,,',
'737,,,,,,737,737,737,737,737,737,737,737,737,737,737,737,737,737,,737',
'737,,737,737,737,,,,,,,,,,,,,,,,,,,,737,,,737,,,737,737,,,737,,737,',
',,737,,,,,,,,737,,,,,737,737,737,737,737,737,,,,737,737,741,741,741',
'741,741,741,,,,741,741,,,,741,,741,741,741,741,741,741,741,,,,,741,741',
'741,741,741,741,741,,,741,,,,,,741,741,741,741,741,741,741,741,741,741',
'741,741,741,741,,741,741,,741,741,741,,,,,,,,,,,,,,,,,,,,741,,,741,',
',741,741,,,741,,741,,,,741,,,,,,,,741,,,,,741,741,741,741,741,741,,',
',741,741,748,748,748,748,748,748,,,,748,748,,,,748,,748,748,748,748',
'748,748,748,,,,,748,748,748,748,748,748,748,,,748,,,,,,748,748,748,748',
'748,748,748,748,748,748,748,748,748,748,,748,748,,748,748,748,,,,,,',
',,,,,,,,,,,,,748,,,748,,,748,748,,,748,,748,,,,748,,,,,,,,748,,,,,748',
'748,748,748,748,748,,,,748,748,794,794,794,794,794,794,,,,794,794,,',
',794,,794,794,794,794,794,794,794,,,,,794,794,794,794,794,794,794,,',
'794,,,,,,794,794,794,794,794,794,794,794,794,794,794,794,794,794,,794',
'794,,794,794,794,,,,,,,,,,,,,,,,,,,,794,,,794,,,794,794,,,794,,794,',
',,794,,,,,,,,794,,,,,794,794,794,794,794,794,,,,794,794,838,838,838',
'838,838,838,,,,838,838,,,,838,,838,838,838,838,838,838,838,,,,,838,838',
'838,838,838,838,838,,,838,,,,,,838,838,838,838,838,838,838,838,838,838',
'838,838,838,838,,838,838,,838,838,838,,,,,,,,,,,,,,,,,,,,838,,,838,',
',838,838,,,838,,838,,,,838,,,,,,,,838,,,,,838,838,838,838,838,838,,',
',838,838,845,845,845,845,845,845,,,,845,845,,,,845,,845,845,845,845',
'845,845,845,,,,,845,845,845,845,845,845,845,,,845,,,,,,845,845,845,845',
'845,845,845,845,845,845,845,845,845,845,,845,845,,845,845,845,,,,,,',
',,,,,,,,,,,,,845,,,845,,,845,845,,,845,,845,,,,845,,,,,,,,845,,,,,845',
'845,845,845,845,845,,,,845,845,852,852,852,852,852,852,,,,852,852,,',
',852,,852,852,852,852,852,852,852,,,,,852,852,852,852,852,852,852,,',
'852,,,,,,852,852,852,852,852,852,852,852,852,852,852,852,852,852,,852',
'852,,852,852,852,,,,,,,,,,,,,,,,,,,,852,,,852,,,852,852,,,852,,852,',
',,852,,,,,,,,852,,,,,852,852,852,852,852,852,,,,852,852,5,5,5,5,5,,',
',5,5,,,,5,,5,5,5,5,5,5,5,,,,,5,5,5,5,5,5,5,,,5,,,,,,5,5,5,5,5,5,5,5',
'5,5,5,5,5,5,,5,5,,5,5,5,,,,,,,,,,,,,,,,,,,,5,,,5,,,5,5,,,5,,5,,,,5,',
',,,,,,5,,,,,5,5,5,5,5,5,,,,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6',
'6,6,6,6,6,,,6,6,6,6,6,6,6,6,6,6,,,,,,6,6,6,6,6,6,6,6,6,,6,,,,,,,,6,6',
',6,6,6,6,,6,6,,,6,,,,,6,6,6,6,,,,,,,,,,,,,,6,6,,6,6,6,6,6,6,6,6,6,,6',
',,6,6,,,,,,,,,,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,,,7',
'7,7,7,7,7,7,7,7,7,,,,,,7,7,7,7,7,7,7,7,,,7,,,,,,,,7,7,,7,7,7,7,,7,7',
',,7,,,,,7,7,7,7,,,,,,,,,,,,,,7,7,,7,7,7,7,7,7,7,7,7,,7,,,7,7,,,17,17',
'17,,17,,,7,17,17,,,,17,,17,17,17,17,17,17,17,,,,,17,17,17,17,17,17,17',
',,17,,,,,,,17,,,17,17,17,17,17,17,17,17,17,17,,17,17,,17,17,17,,,,,',
',,,,,,,,,,,,,,17,,,17,,,17,17,,,17,,,,,,17,,,,,,,,17,,,,,17,17,17,17',
'17,17,,,,17,17,18,18,18,,18,,,,18,18,,,,18,,18,18,18,18,18,18,18,,,',
',18,18,18,18,18,18,18,,,,,,,,,,18,,,18,18,18,18,18,18,18,18,18,18,,18',
'18,,18,18,18,,,,,,,,,,,,,,,,,,,,18,,,18,,,18,18,,,18,,,,,,18,,,,,,,',
'18,,,,,18,18,18,18,18,18,,,,18,18,22,22,22,,22,,,,22,22,,,,22,,22,22',
'22,22,22,22,22,,,,,22,22,22,22,22,22,22,,,,,,,,,,22,,,22,22,22,22,22',
'22,22,22,22,22,,22,22,,22,22,22,,,,,,,,,,,,,,,,,,,,22,,,22,,,22,22,',
',22,,22,,22,,22,,,,,,,,22,,,,,22,22,22,22,22,22,,,,22,22,23,23,23,,23',
',,,23,23,,,,23,,23,23,23,23,23,23,23,,,,,23,23,23,23,23,23,23,,,,,,',
',,,23,,,23,23,23,23,23,23,23,23,23,23,,23,23,,23,23,23,,,,,,,,,,,,,',
',,,,,,23,,,23,,,23,23,,,23,,23,,23,,23,,,,,,,,23,,,,,23,23,23,23,23',
'23,,,,23,23,24,24,24,,24,,,,24,24,,,,24,,24,24,24,24,24,24,24,,,,,24',
'24,24,24,24,24,24,,,,,,,,,,24,,,24,24,24,24,24,24,24,24,24,24,,24,24',
',24,24,24,,,,,,,,,,,,,,,,,,,,24,,,24,,,24,24,,,24,,24,,24,,24,,,,,,',
',24,,,,,24,24,24,24,24,24,,,,24,24,27,27,27,,27,,,,27,27,,,,27,,27,27',
'27,27,27,27,27,,,,,27,27,27,27,27,27,27,,,,,,,,,,27,,,27,27,27,27,27',
'27,27,27,27,27,,27,27,,27,27,27,,,,,,,,,,,,,,,,,,,,27,,,27,27,,27,27',
',,27,,27,,27,,27,,,,,,,,27,,,,,27,27,27,27,27,27,,,,27,27,28,28,28,',
'28,,,,28,28,,,,28,,28,28,28,28,28,28,28,,,,,28,28,28,28,28,28,28,,,',
',,,,,,28,,,28,28,28,28,28,28,28,28,28,28,,28,28,,28,28,28,,,,,,,,,,',
',,,,,,,,,28,,,28,28,,28,28,,,28,,28,,28,,28,,,,,,,,28,,,,,28,28,28,28',
'28,28,,,,28,28,31,31,31,,31,,,,31,31,,,,31,,31,31,31,31,31,31,31,,,',
',31,31,31,31,31,31,31,,,,,,,,,,31,,,31,31,31,31,31,31,31,31,31,31,,31',
'31,,,,31,,828,828,828,828,828,828,828,828,828,828,828,,828,828,,,828',
'828,31,,,31,,,31,31,,,31,,31,,828,,828,,828,828,828,828,828,828,828',
',828,,,31,31,31,31,31,31,,,,31,31,32,32,32,828,32,828,,,32,32,,,,32',
',32,32,32,32,32,32,32,,,,,32,32,32,32,32,32,32,,,,,,,,,,32,,,32,32,32',
'32,32,32,32,32,32,32,,32,32,,,,32,,19,19,19,19,19,19,19,19,19,19,19',
',19,19,,,19,19,32,,,32,,,32,32,,,32,,,,19,,19,,19,19,19,19,19,19,19',
',19,,,32,32,32,32,32,32,,,,32,32,38,38,38,19,38,,,,38,38,,,,38,,38,38',
'38,38,38,38,38,,,,,38,38,38,38,38,38,38,,,,,,,,,,38,,,38,38,38,38,38',
'38,38,38,38,38,,38,38,,38,38,38,,,,,,,,,,,,,,,,,,,,38,,,38,,,38,38,',
',38,,,,,,38,,,,,,,,38,,,,,38,38,38,38,38,38,,,,38,38,39,39,39,,39,,',
',39,39,,,,39,,39,39,39,39,39,39,39,,,,,39,39,39,39,39,39,39,,,,,,,,',
',39,,,39,39,39,39,39,39,39,39,39,39,,39,39,,39,39,39,,,,,,,,,,,,,,,',
',,,,39,,,39,,,39,39,,,39,,,,,,39,,,,,,,,39,,,,,39,39,39,39,39,39,,,',
'39,39,40,40,40,,40,,,,40,40,,,,40,,40,40,40,40,40,40,40,,,,,40,40,40',
'40,40,40,40,,,,,,,,,,40,,,40,40,40,40,40,40,40,40,40,40,,40,40,,40,40',
'40,,,,,,,,,,,,,,,,,,,,40,,,40,,,40,40,,,40,,,,,,40,,,,,,,,40,,,,,40',
'40,40,40,40,40,,,,40,40,52,52,52,,52,,,,52,52,,,,52,,52,52,52,52,52',
'52,52,,,,,52,52,52,52,52,52,52,,,52,,,,,,,52,,,52,52,52,52,52,52,52',
'52,52,52,,52,52,,52,52,52,,,,,,,,,,,,,,,,,,,,52,,,52,,,52,52,,,52,,',
',,,52,,,,,,,,52,,,,,52,52,52,52,52,52,,,,52,52,53,53,53,,53,,,,53,53',
',,,53,,53,53,53,53,53,53,53,,,,,53,53,53,53,53,53,53,,,,,,,,,,53,,,53',
'53,53,53,53,53,53,53,53,53,,53,53,,53,53,53,,,,,,,,,,,,,,,,,,,,53,,',
'53,,,53,53,,,53,,53,,,,53,,,,,,,,53,,,,,53,53,53,53,53,53,,,,53,53,54',
'54,54,,54,,,,54,54,,,,54,,54,54,54,54,54,54,54,,,,,54,54,54,54,54,54',
'54,,,,,,,,,,54,,,54,54,54,54,54,54,54,54,54,54,,54,54,,54,54,54,,,,',
',,,,,,,,,,,,,,,54,,,54,,,54,54,,,54,,,,,,54,,,,,,,,54,,,,,54,54,54,54',
'54,54,,,,54,54,56,56,56,,56,,,,56,56,,,,56,,56,56,56,56,56,56,56,,,',
',56,56,56,56,56,56,56,,,56,,,,,,,56,,,56,56,56,56,56,56,56,56,56,56',
',56,56,,56,56,56,,,,,,,,,,,,,,,,,,,,56,,,56,,,56,56,,,56,,,,,,56,,,',
',,,,56,,,,,56,56,56,56,56,56,,,,56,56,57,57,57,,57,,,,57,57,,,,57,,57',
'57,57,57,57,57,57,,,,,57,57,57,57,57,57,57,,,57,,,,,,,57,,,57,57,57',
'57,57,57,57,57,57,57,,57,57,,57,57,57,,,,,,,,,,,,,,,,,,,,57,,,57,,,57',
'57,,,57,,,,,,57,,,,,,,,57,,,,,57,57,57,57,57,57,,,,57,57,61,61,61,,61',
',,,61,61,,,,61,,61,61,61,61,61,61,61,,,,,61,61,61,61,61,61,61,,,,,,',
',,,61,,,61,61,61,61,61,61,61,61,61,61,,61,61,,,,61,,237,237,237,237',
'237,237,237,237,237,237,237,,237,237,,,237,237,61,,,61,,,61,61,,,61',
',61,,237,,237,,237,237,237,237,237,237,237,,237,,,61,61,61,61,61,61',
',,,61,61,62,62,62,237,62,,,,62,62,,,,62,,62,62,62,62,62,62,62,,,,,62',
'62,62,62,62,62,62,,,,,,,,,,62,,,62,62,62,62,62,62,62,62,62,62,,62,62',
',,,62,,374,374,374,374,374,374,374,374,374,374,374,,374,374,,62,374',
'374,62,,,62,,,62,62,,,62,,,,374,,374,,374,374,374,374,374,374,374,,374',
',,62,62,62,62,62,62,,,,62,62,63,63,63,374,63,,,,63,63,,,,63,,63,63,63',
'63,63,63,63,,,,,63,63,63,63,63,63,63,,,,,,,,,,63,,,63,63,63,63,63,63',
'63,63,63,63,,63,63,,,,63,,388,388,388,388,388,388,388,388,388,388,388',
',388,388,,,388,388,63,,,63,,,63,63,,,63,,,,388,,388,,388,388,388,388',
'388,388,388,,388,,,63,63,63,63,63,63,,,,63,63,82,82,82,388,82,,,,82',
'82,,,,82,,82,82,82,82,82,82,82,,82,,,82,82,82,82,82,82,82,,,,,,,,,,82',
',,82,82,82,82,82,82,82,82,82,82,,82,82,,82,82,82,,,,,,,,,,,,,,,,,,,',
'82,,,82,82,,82,82,,,82,,82,,82,,82,,,,,,,,82,,82,,,82,82,82,82,82,82',
',,,82,82,86,86,86,,86,,,,86,86,,,,86,,86,86,86,86,86,86,86,,86,,,86',
'86,86,86,86,86,86,,,,,,,,,,86,,,86,86,86,86,86,86,86,86,86,86,,86,86',
',86,86,86,,,,,,,,,,,,,,,,,,,,86,,,86,86,,86,86,,,86,,86,,86,,86,,,,',
',,,86,,86,,,86,86,86,86,86,86,,,,86,86,101,101,101,,101,,,,101,101,',
',,101,,101,101,101,101,101,101,101,,,,,101,101,101,101,101,101,101,',
',101,,,,,,,101,,,101,101,101,101,101,101,101,101,101,101,,101,101,,101',
'101,101,,,,,,,,,,,,,,,,,,,,101,,,101,,,101,101,,,101,,,,,,101,,,,,,',
',101,,,,,101,101,101,101,101,101,,,,101,101,102,102,102,,102,,,,102',
'102,,,,102,,102,102,102,102,102,102,102,,,,,102,102,102,102,102,102',
'102,,,102,,,,,,,102,,,102,102,102,102,102,102,102,102,102,102,,102,102',
',102,102,102,,,,,,,,,,,,,,,,,,,,102,,,102,,,102,102,,,102,,,,,,102,',
',,,,,,102,,,,,102,102,102,102,102,102,,,,102,102,103,103,103,,103,,',
',103,103,,,,103,,103,103,103,103,103,103,103,,,,,103,103,103,103,103',
'103,103,,,103,,,,,,,103,,,103,103,103,103,103,103,103,103,103,103,,103',
'103,,103,103,103,,,,,,,,,,,,,,,,,,,,103,,,103,,,103,103,,,103,,,,,,103',
',,,,,,,103,,,,,103,103,103,103,103,103,,,,103,103,104,104,104,,104,',
',,104,104,,,,104,,104,104,104,104,104,104,104,,,,,104,104,104,104,104',
'104,104,,,104,,,,,,,104,,,104,104,104,104,104,104,104,104,104,104,,104',
'104,,104,104,104,,,,,,,,,,,,,,,,,,,,104,,,104,,,104,104,,,104,,,,,,104',
',,,,,,,104,,,,,104,104,104,104,104,104,,,,104,104,105,105,105,105,105',
',,,105,105,,,,105,,105,105,105,105,105,105,105,,,,,105,105,105,105,105',
'105,105,,,105,,,,,,105,105,105,105,105,105,105,105,105,105,105,105,105',
'105,,105,105,,105,105,105,,,,,,,,,,,,,,,,,,,,105,,,105,,,105,105,,,105',
',105,,,,105,,,,,,,,105,,,,,105,105,105,105,105,105,,,,105,105,188,188',
'188,,188,,,,188,188,,,,188,,188,188,188,188,188,188,188,,,,,188,188',
'188,188,188,188,188,,,,,,,,,,188,,,188,188,188,188,188,188,188,188,188',
'188,,188,188,,188,188,188,,,,,,,,,,,,,,,,,,,,188,,,188,,,188,188,,,188',
',188,,,,188,,,,,,,,188,,,,,188,188,188,188,188,188,,,,188,188,189,189',
'189,,189,,,,189,189,,,,189,,189,189,189,189,189,189,189,,,,,189,189',
'189,189,189,189,189,,,,,,,,,,189,,,189,189,189,189,189,189,189,189,189',
'189,,189,189,,189,189,189,,,,,,,,,,,,,,,,,,,,189,,,189,,,189,189,,,189',
',189,,,,189,,,,,,,,189,,,,,189,189,189,189,189,189,,,,189,189,190,190',
'190,,190,,,,190,190,,,,190,,190,190,190,190,190,190,190,,,,,190,190',
'190,190,190,190,190,,,,,,,,,,190,,,190,190,190,190,190,190,190,190,190',
'190,,190,190,,190,190,190,,,,,,,,,,,,,,,,,,,,190,,,190,,,190,190,,,190',
',,,,,190,,,,,,,,190,,,,,190,190,190,190,190,190,,,,190,190,191,191,191',
',191,,,,191,191,,,,191,,191,191,191,191,191,191,191,,,,,191,191,191',
'191,191,191,191,,,,,,,,,,191,,,191,191,191,191,191,191,191,191,191,191',
',191,191,,191,191,191,,,,,,,,,,,,,,,,,,,,191,,,191,,,191,191,,,191,',
'191,,,,191,,,,,,,,191,,,,,191,191,191,191,191,191,,,,191,191,194,194',
'194,,194,,,,194,194,,,,194,,194,194,194,194,194,194,194,,,,,194,194',
'194,194,194,194,194,,,,,,,,,,194,,,194,194,194,194,194,194,194,194,194',
'194,,194,194,,194,194,194,,,,,,,,,,,,,,,,,,,,194,,,194,,,194,194,,,194',
',,,,,194,,,,,,,,194,,,,,194,194,194,194,194,194,,,,194,194,195,195,195',
',195,,,,195,195,,,,195,,195,195,195,195,195,195,195,,,,,195,195,195',
'195,195,195,195,,,195,,,,,,,195,,,195,195,195,195,195,195,195,195,195',
'195,,195,195,,195,195,195,,,,,,,,,,,,,,,,,,,,195,,,195,,,195,195,,,195',
',,,,,195,,,,,,,,195,,,,,195,195,195,195,195,195,,,,195,195,196,196,196',
',196,,,,196,196,,,,196,,196,196,196,196,196,196,196,,,,,196,196,196',
'196,196,196,196,,,196,,,,,,,196,,,196,196,196,196,196,196,196,196,196',
'196,,196,196,,196,196,196,,,,,,,,,,,,,,,,,,,,196,,,196,,,196,196,,,196',
',,,,,196,,,,,,,,196,,,,,196,196,196,196,196,196,,,,196,196,207,207,207',
',207,,,,207,207,,,,207,,207,207,207,207,207,207,207,,,,,207,207,207',
'207,207,207,207,,,,,,,,,,207,,,207,207,207,207,207,207,207,207,207,207',
',207,207,,207,207,207,,,,,,,,,,,,,,,,,,,,207,,,207,,,207,207,,,207,',
',,,,207,,,,,,,,207,,,,,207,207,207,207,207,207,,,,207,207,208,208,208',
',208,,,,208,208,,,,208,,208,208,208,208,208,208,208,,,,,208,208,208',
'208,208,208,208,,,,,,,,,,208,,,208,208,208,208,208,208,208,208,208,208',
',208,208,,208,208,208,,,,,,,,,,,,,,,,,,,,208,,,208,,,208,208,,,208,',
',,,,208,,,,,,,,208,,,,,208,208,208,208,208,208,,,,208,208,209,209,209',
',209,,,,209,209,,,,209,,209,209,209,209,209,209,209,,,,,209,209,209',
'209,209,209,209,,,,,,,,,,209,,,209,209,209,209,209,209,209,209,209,209',
',209,209,,209,209,209,,,,,,,,,,,,,,,,,,,,209,,,209,,,209,209,,,209,',
',,,,209,,,,,,,,209,,,,,209,209,209,209,209,209,,,,209,209,210,210,210',
',210,,,,210,210,,,,210,,210,210,210,210,210,210,210,,,,,210,210,210',
'210,210,210,210,,,,,,,,,,210,,,210,210,210,210,210,210,210,210,210,210',
',210,210,,210,210,210,,,,,,,,,,,,,,,,,,,,210,,,210,,,210,210,,,210,',
',,,,210,,,,,,,,210,,,,,210,210,210,210,210,210,,,,210,210,211,211,211',
',211,,,,211,211,,,,211,,211,211,211,211,211,211,211,,,,,211,211,211',
'211,211,211,211,,,,,,,,,,211,,,211,211,211,211,211,211,211,211,211,211',
',211,211,,211,211,211,,,,,,,,,,,,,,,,,,,,211,,,211,,,211,211,,,211,',
',,,,211,,,,,,,,211,,,,,211,211,211,211,211,211,,,,211,211,212,212,212',
',212,,,,212,212,,,,212,,212,212,212,212,212,212,212,,,,,212,212,212',
'212,212,212,212,,,,,,,,,,212,,,212,212,212,212,212,212,212,212,212,212',
',212,212,,212,212,212,,,,,,,,,,,,,,,,,,,,212,,,212,,,212,212,,,212,',
',,,,212,,,,,,,,212,,,,,212,212,212,212,212,212,,,,212,212,213,213,213',
',213,,,,213,213,,,,213,,213,213,213,213,213,213,213,,,,,213,213,213',
'213,213,213,213,,,,,,,,,,213,,,213,213,213,213,213,213,213,213,213,213',
',213,213,,213,213,213,,,,,,,,,,,,,,,,,,,,213,,,213,,,213,213,,,213,',
',,,,213,,,,,,,,213,,,,,213,213,213,213,213,213,,,,213,213,214,214,214',
',214,,,,214,214,,,,214,,214,214,214,214,214,214,214,,,,,214,214,214',
'214,214,214,214,,,,,,,,,,214,,,214,214,214,214,214,214,214,214,214,214',
',214,214,,214,214,214,,,,,,,,,,,,,,,,,,,,214,,,214,,,214,214,,,214,',
',,,,214,,,,,,,,214,,,,,214,214,214,214,214,214,,,,214,214,215,215,215',
',215,,,,215,215,,,,215,,215,215,215,215,215,215,215,,,,,215,215,215',
'215,215,215,215,,,,,,,,,,215,,,215,215,215,215,215,215,215,215,215,215',
',215,215,,215,215,215,,,,,,,,,,,,,,,,,,,,215,,,215,,,215,215,,,215,',
',,,,215,,,,,,,,215,,,,,215,215,215,215,215,215,,,,215,215,216,216,216',
',216,,,,216,216,,,,216,,216,216,216,216,216,216,216,,,,,216,216,216',
'216,216,216,216,,,,,,,,,,216,,,216,216,216,216,216,216,216,216,216,216',
',216,216,,216,216,216,,,,,,,,,,,,,,,,,,,,216,,,216,,,216,216,,,216,',
',,,,216,,,,,,,,216,,,,,216,216,216,216,216,216,,,,216,216,217,217,217',
',217,,,,217,217,,,,217,,217,217,217,217,217,217,217,,,,,217,217,217',
'217,217,217,217,,,,,,,,,,217,,,217,217,217,217,217,217,217,217,217,217',
',217,217,,217,217,217,,,,,,,,,,,,,,,,,,,,217,,,217,,,217,217,,,217,',
',,,,217,,,,,,,,217,,,,,217,217,217,217,217,217,,,,217,217,218,218,218',
',218,,,,218,218,,,,218,,218,218,218,218,218,218,218,,,,,218,218,218',
'218,218,218,218,,,,,,,,,,218,,,218,218,218,218,218,218,218,218,218,218',
',218,218,,218,218,218,,,,,,,,,,,,,,,,,,,,218,,,218,,,218,218,,,218,',
',,,,218,,,,,,,,218,,,,,218,218,218,218,218,218,,,,218,218,219,219,219',
',219,,,,219,219,,,,219,,219,219,219,219,219,219,219,,,,,219,219,219',
'219,219,219,219,,,,,,,,,,219,,,219,219,219,219,219,219,219,219,219,219',
',219,219,,219,219,219,,,,,,,,,,,,,,,,,,,,219,,,219,,,219,219,,,219,',
',,,,219,,,,,,,,219,,,,,219,219,219,219,219,219,,,,219,219,220,220,220',
',220,,,,220,220,,,,220,,220,220,220,220,220,220,220,,,,,220,220,220',
'220,220,220,220,,,,,,,,,,220,,,220,220,220,220,220,220,220,220,220,220',
',220,220,,220,220,220,,,,,,,,,,,,,,,,,,,,220,,,220,,,220,220,,,220,',
',,,,220,,,,,,,,220,,,,,220,220,220,220,220,220,,,,220,220,221,221,221',
',221,,,,221,221,,,,221,,221,221,221,221,221,221,221,,,,,221,221,221',
'221,221,221,221,,,,,,,,,,221,,,221,221,221,221,221,221,221,221,221,221',
',221,221,,221,221,221,,,,,,,,,,,,,,,,,,,,221,,,221,,,221,221,,,221,',
',,,,221,,,,,,,,221,,,,,221,221,221,221,221,221,,,,221,221,222,222,222',
',222,,,,222,222,,,,222,,222,222,222,222,222,222,222,,,,,222,222,222',
'222,222,222,222,,,,,,,,,,222,,,222,222,222,222,222,222,222,222,222,222',
',222,222,,222,222,222,,,,,,,,,,,,,,,,,,,,222,,,222,,,222,222,,,222,',
',,,,222,,,,,,,,222,,,,,222,222,222,222,222,222,,,,222,222,223,223,223',
',223,,,,223,223,,,,223,,223,223,223,223,223,223,223,,,,,223,223,223',
'223,223,223,223,,,,,,,,,,223,,,223,223,223,223,223,223,223,223,223,223',
',223,223,,223,223,223,,,,,,,,,,,,,,,,,,,,223,,,223,,,223,223,,,223,',
',,,,223,,,,,,,,223,,,,,223,223,223,223,223,223,,,,223,223,224,224,224',
',224,,,,224,224,,,,224,,224,224,224,224,224,224,224,,,,,224,224,224',
'224,224,224,224,,,,,,,,,,224,,,224,224,224,224,224,224,224,224,224,224',
',224,224,,224,224,224,,,,,,,,,,,,,,,,,,,,224,,,224,,,224,224,,,224,',
',,,,224,,,,,,,,224,,,,,224,224,224,224,224,224,,,,224,224,225,225,225',
',225,,,,225,225,,,,225,,225,225,225,225,225,225,225,,,,,225,225,225',
'225,225,225,225,,,,,,,,,,225,,,225,225,225,225,225,225,225,225,225,225',
',225,225,,225,225,225,,,,,,,,,,,,,,,,,,,,225,,,225,,,225,225,,,225,',
',,,,225,,,,,,,,225,,,,,225,225,225,225,225,225,,,,225,225,226,226,226',
',226,,,,226,226,,,,226,,226,226,226,226,226,226,226,,,,,226,226,226',
'226,226,226,226,,,,,,,,,,226,,,226,226,226,226,226,226,226,226,226,226',
',226,226,,226,226,226,,,,,,,,,,,,,,,,,,,,226,,,226,,,226,226,,,226,',
',,,,226,,,,,,,,226,,,,,226,226,226,226,226,226,,,,226,226,227,227,227',
',227,,,,227,227,,,,227,,227,227,227,227,227,227,227,,,,,227,227,227',
'227,227,227,227,,,,,,,,,,227,,,227,227,227,227,227,227,227,227,227,227',
',227,227,,227,227,227,,,,,,,,,,,,,,,,,,,,227,,,227,,,227,227,,,227,',
',,,,227,,,,,,,,227,,,,,227,227,227,227,227,227,,,,227,227,228,228,228',
',228,,,,228,228,,,,228,,228,228,228,228,228,228,228,,,,,228,228,228',
'228,228,228,228,,,,,,,,,,228,,,228,228,228,228,228,228,228,228,228,228',
',228,228,,228,228,228,,,,,,,,,,,,,,,,,,,,228,,,228,,,228,228,,,228,',
',,,,228,,,,,,,,228,,,,,228,228,228,228,228,228,,,,228,228,229,229,229',
',229,,,,229,229,,,,229,,229,229,229,229,229,229,229,,,,,229,229,229',
'229,229,229,229,,,,,,,,,,229,,,229,229,229,229,229,229,229,229,229,229',
',229,229,,229,229,229,,,,,,,,,,,,,,,,,,,,229,,,229,,,229,229,,,229,',
',,,,229,,,,,,,,229,,,,,229,229,229,229,229,229,,,,229,229,230,230,230',
',230,,,,230,230,,,,230,,230,230,230,230,230,230,230,,,,,230,230,230',
'230,230,230,230,,,,,,,,,,230,,,230,230,230,230,230,230,230,230,230,230',
',230,230,,230,230,230,,,,,,,,,,,,,,,,,,,,230,,,230,,,230,230,,,230,',
',,,,230,,,,,,,,230,,,,,230,230,230,230,230,230,,,,230,230,231,231,231',
',231,,,,231,231,,,,231,,231,231,231,231,231,231,231,,,,,231,231,231',
'231,231,231,231,,,,,,,,,,231,,,231,231,231,231,231,231,231,231,231,231',
',231,231,,231,231,231,,,,,,,,,,,,,,,,,,,,231,,,231,,,231,231,,,231,',
',,,,231,,,,,,,,231,,,,,231,231,231,231,231,231,,,,231,231,232,232,232',
',232,,,,232,232,,,,232,,232,232,232,232,232,232,232,,,,,232,232,232',
'232,232,232,232,,,,,,,,,,232,,,232,232,232,232,232,232,232,232,232,232',
',232,232,,232,232,232,,,,,,,,,,,,,,,,,,,,232,,,232,,,232,232,,,232,',
',,,,232,,,,,,,,232,,,,,232,232,232,232,232,232,,,,232,232,233,233,233',
',233,,,,233,233,,,,233,,233,233,233,233,233,233,233,,,,,233,233,233',
'233,233,233,233,,,,,,,,,,233,,,233,233,233,233,233,233,233,233,233,233',
',233,233,,233,233,233,,,,,,,,,,,,,,,,,,,,233,,,233,,,233,233,,,233,',
',,,,233,,,,,,,,233,,,,,233,233,233,233,233,233,,,,233,233,241,241,241',
',241,,,,241,241,,,,241,,241,241,241,241,241,241,241,,,,,241,241,241',
'241,241,241,241,,,,,,,,,,241,,,241,241,241,241,241,241,241,241,241,241',
',241,241,,241,241,241,,,,,,,,,,,,,,,,,,,,241,,,241,,,241,241,,,241,',
',,,,241,,,,,,,,241,,,,,241,241,241,241,241,241,,,,241,241,243,243,243',
',243,,,,243,243,,,,243,,243,243,243,243,243,243,243,,,,,243,243,243',
'243,243,243,243,,,,,,,,,,243,,,243,243,243,243,243,243,243,243,243,243',
',243,243,,243,243,243,,,,,,,,,,,,,,,,,,,,243,,,243,,,243,243,,,243,',
',,,,243,,,,,,,,243,,,,,243,243,243,243,243,243,,,,243,243,254,254,254',
',254,,,,254,254,,,,254,,254,254,254,254,254,254,254,,,,,254,254,254',
'254,254,254,254,,,,,,,,,,254,,,254,254,254,254,254,254,254,254,254,254',
',254,254,,254,254,254,,,,,,,,,,,,,,,,,,,,254,,,254,,,254,254,,,254,',
'254,,254,,254,,,,,,,,254,,,,,254,254,254,254,254,254,,,,254,254,255',
'255,255,,255,,,,255,255,,,,255,,255,255,255,255,255,255,255,,,,,255',
'255,255,255,255,255,255,,,,,,,,,,255,,,255,255,255,255,255,255,255,255',
'255,255,,255,255,,255,255,255,,,,,,,,,,,,,,,,,,,,255,,,255,,,255,255',
',,255,,255,,255,,255,,,,,,,,255,,,,,255,255,255,255,255,255,,,,255,255',
'263,263,263,,263,,,,263,263,,,,263,,263,263,263,263,263,263,263,,,,',
'263,263,263,263,263,263,263,,,,,,,,,,263,,,263,263,263,263,263,263,263',
'263,263,263,,263,263,,263,263,263,,,,,,,,,,,,,,,,,,,,263,,,263,,263',
'263,263,,,263,,263,,263,,263,,,,,,,,263,,,,,263,263,263,263,263,263',
',,,263,263,269,269,269,,269,,,,269,269,,,,269,,269,269,269,269,269,269',
'269,,,,,269,269,269,269,269,269,269,,,,,,,,,,269,,,269,269,269,269,269',
'269,269,269,269,269,,269,269,,,,269,,468,468,468,468,468,468,468,468',
'468,468,468,,468,468,,,468,468,269,,,269,,,269,269,,,269,,,,468,,468',
',468,468,468,468,468,468,468,,468,,,269,269,269,269,269,269,,,,269,269',
'290,290,290,468,290,,,,290,290,,,,290,,290,290,290,290,290,290,290,',
',,,290,290,290,290,290,290,290,,,,,,,,,,290,,,290,290,290,290,290,290',
'290,290,290,290,,290,290,,290,290,290,,,,,,,,,,,,,,,,,,,,290,,,290,290',
',290,290,,,290,,,,,,290,,,,,,,,290,,,,,290,290,290,290,290,290,,,,290',
'290,299,299,299,,299,,,,299,299,,,,299,,299,299,299,299,299,299,299',
',,,,299,299,299,299,299,299,299,,,,,,,,,,299,,,299,299,299,299,299,299',
'299,299,299,299,,299,299,,299,299,299,,,,,,,,,,,,,,,,,,,,299,,,299,',
',299,299,,,299,,,,,,299,,,,,,,,299,,,,,299,299,299,299,299,299,,,,299',
'299,308,308,308,,308,,,,308,308,,,,308,,308,308,308,308,308,308,308',
',,,,308,308,308,308,308,308,308,,,308,,,,,,,308,,,308,308,308,308,308',
'308,308,308,308,308,,308,308,,308,308,308,,,,,,,,,,,,,,,,,,,,308,,,308',
',,308,308,,,308,,,,,,308,,,,,,,,308,,,,,308,308,308,308,308,308,,,,308',
'308,309,309,309,,309,,,,309,309,,,,309,,309,309,309,309,309,309,309',
',,,,309,309,309,309,309,309,309,,,309,,,,,,,309,,,309,309,309,309,309',
'309,309,309,309,309,,309,309,,309,309,309,,,,,,,,,,,,,,,,,,,,309,,,309',
',,309,309,,,309,,,,,,309,,,,,,,,309,,,,,309,309,309,309,309,309,,,,309',
'309,327,327,327,,327,,,,327,327,,,,327,,327,327,327,327,327,327,327',
',,,,327,327,327,327,327,327,327,,,327,,,,,,,327,,,327,327,327,327,327',
'327,327,327,327,327,,327,327,,327,327,327,,,,,,,,,,,,,,,,,,,,327,,,327',
',,327,327,,,327,,,,,,327,,,,,,,,327,,,,,327,327,327,327,327,327,,,,327',
'327,341,341,341,,341,,,,341,341,,,,341,,341,341,341,341,341,341,341',
',,,,341,341,341,341,341,341,341,,,341,,,,,,,341,,,341,341,341,341,341',
'341,341,341,341,341,,341,341,,341,341,341,,,,,,,,,,,,,,,,,,,,341,,,341',
',,341,341,,,341,,,,,,341,,,,,,,,341,,,,,341,341,341,341,341,341,,,,341',
'341,357,357,357,357,357,357,357,357,357,357,357,357,357,357,357,357',
'357,357,357,357,357,357,357,357,,,357,357,357,357,357,357,357,357,357',
'357,,,,,,357,357,357,357,357,357,357,357,,,357,,,,,,,,357,357,,357,357',
'357,357,,357,357,,,357,,,,,357,357,357,357,,,,,,,,,,,,,,357,357,,357',
'357,357,357,357,357,357,357,357,,357,,,357,357,,,369,369,369,,369,,',
'357,369,369,,,,369,,369,369,369,369,369,369,369,,,,,369,369,369,369',
'369,369,369,,,,,,,,,,369,,,369,369,369,369,369,369,369,369,369,369,',
'369,369,,369,369,369,,,,,,,,,,,,,,,,,,,,369,,,369,,,369,369,,,369,,',
',,,369,,,,,,,,369,,,,,369,369,369,369,369,369,,,,369,369,378,378,378',
',378,,,,378,378,,,,378,,378,378,378,378,378,378,378,,,,,378,378,378',
'378,378,378,378,,,,,,,,,,378,,,378,378,378,378,378,378,378,378,378,378',
',378,378,,378,378,378,,,,,,,,,,,,,,,,,,,,378,,,378,378,,378,378,,,378',
',378,,378,,378,,,,,,,,378,,,,,378,378,378,378,378,378,,,,378,378,385',
'385,385,,385,,,,385,385,,,,385,,385,385,385,385,385,385,385,,,,,385',
'385,385,385,385,385,385,,,,,,,,,,385,,,385,385,385,385,385,385,385,385',
'385,385,,385,385,,385,385,385,,,,,,,,,,,,,,,,,,,,385,,,385,385,,385',
'385,,,385,,385,,385,,385,,,,,,,,385,,,,,385,385,385,385,385,385,,,,385',
'385,386,386,386,,386,,,,386,386,,,,386,,386,386,386,386,386,386,386',
',,,,386,386,386,386,386,386,386,,,,,,,,,,386,,,386,386,386,386,386,386',
'386,386,386,386,,386,386,,386,386,386,,,,,,,,,,,,,,,,,,,,386,,,386,386',
',386,386,,,386,,386,,386,,386,,,,,,,,386,,,,,386,386,386,386,386,386',
',,,386,386,393,393,393,,393,,,,393,393,,,,393,,393,393,393,393,393,393',
'393,,,,,393,393,393,393,393,393,393,,,,,,,,,,393,,,393,393,393,393,393',
'393,393,393,393,393,,393,393,,393,393,393,,,,,,,,,,,,,,,,,,,,393,,,393',
',,393,393,,,393,,393,,,,393,,,,,,,,393,,,,,393,393,393,393,393,393,',
',,393,393,395,395,395,,395,,,,395,395,,,,395,,395,395,395,395,395,395',
'395,,,,,395,395,395,395,395,395,395,,,,,,,,,,395,,,395,395,395,395,395',
'395,395,395,395,395,,395,395,,395,395,395,,,,,,,,,,,,,,,,,,,,395,,,395',
',,395,395,,,395,,,,,,395,,,,,,,,395,,,,,395,395,395,395,395,395,,,,395',
'395,396,396,396,,396,,,,396,396,,,,396,,396,396,396,396,396,396,396',
',,,,396,396,396,396,396,396,396,,,,,,,,,,396,,,396,396,396,396,396,396',
'396,396,396,396,,396,396,,396,396,396,,,,,,,,,,,,,,,,,,,,396,,,396,',
',396,396,,,396,,,,,,396,,,,,,,,396,,,,,396,396,396,396,396,396,,,,396',
'396,397,397,397,,397,,,,397,397,,,,397,,397,397,397,397,397,397,397',
',,,,397,397,397,397,397,397,397,,,,,,,,,,397,,,397,397,397,397,397,397',
'397,397,397,397,,397,397,,397,397,397,,,,,,,,,,,,,,,,,,,,397,,,397,',
',397,397,,,397,,,,,,397,,,,,,,,397,,,,,397,397,397,397,397,397,,,,397',
'397,426,426,426,,426,,,,426,426,,,,426,,426,426,426,426,426,426,426',
',,,,426,426,426,426,426,426,426,,,,,,,,,,426,,,426,426,426,426,426,426',
'426,426,426,426,,426,426,,426,426,426,,,,,,,,,,,,,,,,,,,,426,,,426,',
',426,426,,,426,,426,,426,,426,,,,,,,,426,,,,,426,426,426,426,426,426',
',,,426,426,428,428,428,,428,,,,428,428,,,,428,,428,428,428,428,428,428',
'428,,,,,428,428,428,428,428,428,428,,,,,,,,,,428,,,428,428,428,428,428',
'428,428,428,428,428,,428,428,,428,428,428,,,,,,,,,,,,,,,,,,,,428,,,428',
',,428,428,,,428,,428,,428,,428,,,,,,,,428,,,,,428,428,428,428,428,428',
',,,428,428,431,431,431,,431,,,,431,431,,,,431,,431,431,431,431,431,431',
'431,,,,,431,431,431,431,431,431,431,,,,,,,,,,431,,,431,431,431,431,431',
'431,431,431,431,431,,431,431,,431,431,431,,,,,,,,,,,,,,,,,,,,431,,,431',
',,431,431,,,431,,,,,,431,,,,,,,,431,,,,,431,431,431,431,431,431,,,,431',
'431,445,445,445,,445,,,,445,445,,,,445,,445,445,445,445,445,445,445',
',,,,445,445,445,445,445,445,445,,,445,,,,,,,445,,,445,445,445,445,445',
'445,445,445,445,445,,445,445,,445,445,445,,,,,,,,,,,,,,,,,,,,445,,,445',
',,445,445,,,445,,445,,445,,445,,,,,,,,445,,,,,445,445,445,445,445,445',
',,,445,445,456,456,456,,456,,,,456,456,,,,456,,456,456,456,456,456,456',
'456,,,,,456,456,456,456,456,456,456,,,,,,,,,,456,,,456,456,456,456,456',
'456,456,456,456,456,,456,456,,456,456,456,,,,,,,,,,,,,,,,,,,,456,,,456',
',,456,456,,,456,,456,,,,456,,,,,,,,456,,,,,456,456,456,456,456,456,',
',,456,456,463,463,463,,463,,,,463,463,,,,463,,463,463,463,463,463,463',
'463,,,,,463,463,463,463,463,463,463,,,,,,,,,,463,,,463,463,463,463,463',
'463,463,463,463,463,,463,463,,463,463,463,,,,,,,,,,,,,,,,,,,,463,,,463',
',,463,463,,,463,,,,,,463,,,,,,,,463,,,,,463,463,463,463,463,463,,,,463',
'463,464,464,464,,464,,,,464,464,,,,464,,464,464,464,464,464,464,464',
',,,,464,464,464,464,464,464,464,,,,,,,,,,464,,,464,464,464,464,464,464',
'464,464,464,464,,464,464,,464,464,464,,,,,,,,,,,,,,,,,,,,464,,,464,',
',464,464,,,464,,,,,,464,,,,,,,,464,,,,,464,464,464,464,464,464,,,,464',
'464,465,465,465,,465,,,,465,465,,,,465,,465,465,465,465,465,465,465',
',,,,465,465,465,465,465,465,465,,,,,,,,,,465,,,465,465,465,465,465,465',
'465,465,465,465,,465,465,,465,465,465,,,,,,,,,,,,,,,,,,,,465,,,465,',
',465,465,,,465,,,,,,465,,,,,,,,465,,,,,465,465,465,465,465,465,,,,465',
'465,469,469,469,,469,,,,469,469,,,,469,,469,469,469,469,469,469,469',
',,,,469,469,469,469,469,469,469,,,469,,,,,,,469,,,469,469,469,469,469',
'469,469,469,469,469,,469,469,,469,469,469,,,,,,,,,,,,,,,,,,,,469,,,469',
',,469,469,,,469,,,,,,469,,,,,,,,469,,,,,469,469,469,469,469,469,,,,469',
'469,471,471,471,,471,,,,471,471,,,,471,,471,471,471,471,471,471,471',
',,,,471,471,471,471,471,471,471,,,,,,,,,,471,,,471,471,471,471,471,471',
'471,471,471,471,,471,471,,471,471,471,,,,,,,,,,,,,,,,,,,,471,,,471,',
',471,471,,,471,,471,,,,471,,,,,,,,471,,,,,471,471,471,471,471,471,,',
',471,471,476,476,476,,476,,,,476,476,,,,476,,476,476,476,476,476,476',
'476,,,,,476,476,476,476,476,476,476,,,,,,,,,,476,,,476,476,476,476,476',
'476,476,476,476,476,,476,476,,476,476,476,,,,,,,,,,,,,,,,,,,,476,,,476',
',,476,476,,,476,,476,,,,476,,,,,,,,476,,,,,476,476,476,476,476,476,',
',,476,476,479,479,479,,479,,,,479,479,,,,479,,479,479,479,479,479,479',
'479,,,,,479,479,479,479,479,479,479,,,,,,,,,,479,,,479,479,479,479,479',
'479,479,479,479,479,,479,479,,479,479,479,,,,,,,,,,,,,,,,,,,,479,,,479',
',,479,479,,,479,,,,,,479,,,,,,,,479,,,,,479,479,479,479,479,479,,,,479',
'479,482,482,482,,482,,,,482,482,,,,482,,482,482,482,482,482,482,482',
',,,,482,482,482,482,482,482,482,,,,,,,,,,482,,,482,482,482,482,482,482',
'482,482,482,482,,482,482,,482,482,482,,,,,,,,,,,,,,,,,,,,482,,,482,',
',482,482,,,482,,,,,,482,,,,,,,,482,,,,,482,482,482,482,482,482,,,,482',
'482,496,496,496,,496,,,,496,496,,,,496,,496,496,496,496,496,496,496',
',,,,496,496,496,496,496,496,496,,,,,,,,,,496,,,496,496,496,496,496,496',
'496,496,496,496,,496,496,,496,496,496,,,,,,,,,,,,,,,,,,,,496,,,496,',
',496,496,,,496,,496,,,,496,,,,,,,,496,,,,,496,496,496,496,496,496,,',
',496,496,497,497,497,,497,,,,497,497,,,,497,,497,497,497,497,497,497',
'497,,,,,497,497,497,497,497,497,497,,,,,,,,,,497,,,497,497,497,497,497',
'497,497,497,497,497,,497,497,,497,497,497,,,,,,,,,,,,,,,,,,,,497,,,497',
',,497,497,,,497,,497,,,,497,,,,,,,,497,,,,,497,497,497,497,497,497,',
',,497,497,506,506,506,,506,,,,506,506,,,,506,,506,506,506,506,506,506',
'506,,,,,506,506,506,506,506,506,506,,,,,,,,,,506,,,506,506,506,506,506',
'506,506,506,506,506,,506,506,,506,506,506,,,,,,,,,,,,,,,,,,,,506,,,506',
',,506,506,,,506,,506,,,,506,,,,,,,,506,,,,,506,506,506,506,506,506,',
',,506,506,510,510,510,,510,,,,510,510,,,,510,,510,510,510,510,510,510',
'510,,,,,510,510,510,510,510,510,510,,,510,,,,,,,510,,,510,510,510,510',
'510,510,510,510,510,510,,510,510,,510,510,510,,,,,,,,,,,,,,,,,,,,510',
',,510,,,510,510,,,510,,,,,,510,,,,,,,,510,,,,,510,510,510,510,510,510',
',,,510,510,534,534,534,534,534,534,534,534,534,534,534,534,534,534,534',
'534,534,534,534,534,534,534,534,534,,,534,534,534,534,534,534,534,534',
'534,534,,,,,,534,534,534,534,534,534,534,534,,,534,,,,,,,,534,534,,534',
'534,534,534,,534,534,,,534,,,,,534,534,534,534,,,,,,,,,,,,,,534,534',
',534,534,534,534,534,534,534,534,534,,534,,,534,534,,,537,537,537,,537',
',,534,537,537,,,,537,,537,537,537,537,537,537,537,,,,,537,537,537,537',
'537,537,537,,,,,,,,,,537,,,537,537,537,537,537,537,537,537,537,537,',
'537,537,,537,537,537,,,,,,,,,,,,,,,,,,,,537,,,537,,,537,537,,,537,,',
',,,537,,,,,,,,537,,,,,537,537,537,537,537,537,,,,537,537,538,538,538',
',538,,,,538,538,,,,538,,538,538,538,538,538,538,538,,,,,538,538,538',
'538,538,538,538,,,,,,,,,,538,,,538,538,538,538,538,538,538,538,538,538',
',538,538,,538,538,538,,,,,,,,,,,,,,,,,,,,538,,,538,,,538,538,,,538,',
'538,,,,538,,,,,,,,538,,,,,538,538,538,538,538,538,,,,538,538,541,541',
'541,,541,,,,541,541,,,,541,,541,541,541,541,541,541,541,,,,,541,541',
'541,541,541,541,541,,,,,,,,,,541,,,541,541,541,541,541,541,541,541,541',
'541,,541,541,,541,541,541,,,,,,,,,,,,,,,,,,,,541,,,541,,,541,541,,,541',
',,,,,541,,,,,,,,541,,,,,541,541,541,541,541,541,,,,541,541,542,542,542',
',542,,,,542,542,,,,542,,542,542,542,542,542,542,542,,,,,542,542,542',
'542,542,542,542,,,,,,,,,,542,,,542,542,542,542,542,542,542,542,542,542',
',542,542,,542,542,542,,,,,,,,,,,,,,,,,,,,542,,,542,,,542,542,,,542,',
',,,,542,,,,,,,,542,,,,,542,542,542,542,542,542,,,,542,542,546,546,546',
',546,,,,546,546,,,,546,,546,546,546,546,546,546,546,,,,,546,546,546',
'546,546,546,546,,,,,,,,,,546,,,546,546,546,546,546,546,546,546,546,546',
',546,546,,546,546,546,,,,,,,,,,,,,,,,,,,,546,,,546,,,546,546,,,546,',
',,,,546,,,,,,,,546,,,,,546,546,546,546,546,546,,,,546,546,549,549,549',
',549,,,,549,549,,,,549,,549,549,549,549,549,549,549,,,,,549,549,549',
'549,549,549,549,,,,,,,,,,549,,,549,549,549,549,549,549,549,549,549,549',
',549,549,,549,549,549,,,,,,,,,,,,,,,,,,,,549,,,549,,,549,549,,,549,',
',,,,549,,,,,,,,549,,,,,549,549,549,549,549,549,,,,549,549,556,556,556',
',556,,,,556,556,,,,556,,556,556,556,556,556,556,556,,,,,556,556,556',
'556,556,556,556,,,,,,,,,,556,,,556,556,556,556,556,556,556,556,556,556',
',556,556,,556,556,556,,,,,,,,,,,,,,,,,,,,556,,,556,,,556,556,,,556,',
',,,,556,,,,,,,,556,,,,,556,556,556,556,556,556,,,,556,556,557,557,557',
',557,,,,557,557,,,,557,,557,557,557,557,557,557,557,,,,,557,557,557',
'557,557,557,557,,,,,,,,,,557,,,557,557,557,557,557,557,557,557,557,557',
',557,557,,,,557,,,,,,,,,,,,,,,,,,,,557,,,557,,,557,557,,,557,,557,,557',
',,,,,,,,557,,,,,,557,557,557,557,557,557,,,,557,557,560,560,560,,560',
',,,560,560,,,,560,,560,560,560,560,560,560,560,,,,,560,560,560,560,560',
'560,560,,,,,,,,,,560,,,560,560,560,560,560,560,560,560,560,560,,560',
'560,,560,560,560,,,,,,,,,,,,,,,,,,,,560,,,560,,,560,560,,,560,,,,,,560',
',,,,,,,560,,,,,560,560,560,560,560,560,,,,560,560,564,564,564,,564,',
',,564,564,,,,564,,564,564,564,564,564,564,564,,,,,564,564,564,564,564',
'564,564,,,,,,,,,,564,,,564,564,564,564,564,564,564,564,564,564,,564',
'564,,564,564,564,,,,,,,,,,,,,,,,,,,,564,,,564,,,564,564,,,564,,,,,,564',
',,,,,,,564,,,,,564,564,564,564,564,564,,,,564,564,580,580,580,,580,',
',,580,580,,,,580,,580,580,580,580,580,580,580,,,,,580,580,580,580,580',
'580,580,,,,,,,,,,580,,,580,580,580,580,580,580,580,580,580,580,,580',
'580,,580,580,580,,,,,,,,,,,,,,,,,,,,580,,,580,,,580,580,,,580,,580,',
'580,,580,,,,,,,,580,,,,,580,580,580,580,580,580,,,,580,580,584,584,584',
',584,,,,584,584,,,,584,,584,584,584,584,584,584,584,,,,,584,584,584',
'584,584,584,584,,,,,,,,,,584,,,584,584,584,584,584,584,584,584,584,584',
',584,584,,584,584,584,,,,,,,,,,,,,,,,,,,,584,,,584,,,584,584,,,584,',
',,,,584,,,,,,,,584,,,,,584,584,584,584,584,584,,,,584,584,612,612,612',
',612,,,,612,612,,,,612,,612,612,612,612,612,612,612,,,,,612,612,612',
'612,612,612,612,,,,,,,,,,612,,,612,612,612,612,612,612,612,612,612,612',
',612,612,,612,612,612,,,,,,,,,,,,,,,,,,,,612,,,612,,,612,612,,,612,',
',,,,612,,,,,,,,612,,,,,612,612,612,612,612,612,,,,612,612,628,628,628',
',628,,,,628,628,,,,628,,628,628,628,628,628,628,628,,,,,628,628,628',
'628,628,628,628,,,,,,,,,,628,,,628,628,628,628,628,628,628,628,628,628',
',628,628,,628,628,628,,,,,,,,,,,,,,,,,,,,628,,,628,,,628,628,,,628,',
',,,,628,,,,,,,,628,,,,,628,628,628,628,628,628,,,,628,628,634,634,634',
',634,,,,634,634,,,,634,,634,634,634,634,634,634,634,,,,,634,634,634',
'634,634,634,634,,,634,,,,,,,634,,,634,634,634,634,634,634,634,634,634',
'634,,634,634,,634,634,634,,,,,,,,,,,,,,,,,,,,634,,,634,,,634,634,,,634',
',,,,,634,,,,,,,,634,,,,,634,634,634,634,634,634,,,,634,634,679,679,679',
',679,,,,679,679,,,,679,,679,679,679,679,679,679,679,,,,,679,679,679',
'679,679,679,679,,,,,,,,,,679,,,679,679,679,679,679,679,679,679,679,679',
',679,679,,679,679,679,,,,,,,,,,,,,,,,,,,,679,,,679,,,679,679,,,679,',
',,,,679,,,,,,,,679,,,,,679,679,679,679,679,679,,,,679,679,680,680,680',
',680,,,,680,680,,,,680,,680,680,680,680,680,680,680,,,,,680,680,680',
'680,680,680,680,,,,,,,,,,680,,,680,680,680,680,680,680,680,680,680,680',
',680,680,,680,680,680,,,,,,,,,,,,,,,,,,,,680,,,680,,,680,680,,,680,',
',,,,680,,,,,,,,680,,,,,680,680,680,680,680,680,,,,680,680,690,690,690',
',690,,,,690,690,,,,690,,690,690,690,690,690,690,690,,,,,690,690,690',
'690,690,690,690,,,,,,,,,,690,,,690,690,690,690,690,690,690,690,690,690',
',690,690,,690,690,690,,,,,,,,,,,,,,,,,,,,690,,,690,,,690,690,,,690,',
',,,,690,,,,,,,,690,,,,,690,690,690,690,690,690,,,,690,690,691,691,691',
',691,,,,691,691,,,,691,,691,691,691,691,691,691,691,,,,,691,691,691',
'691,691,691,691,,,,,,,,,,691,,,691,691,691,691,691,691,691,691,691,691',
',691,691,,691,691,691,,,,,,,,,,,,,,,,,,,,691,,,691,,,691,691,,,691,',
',,,,691,,,,,,,,691,,,,,691,691,691,691,691,691,,,,691,691,692,692,692',
',692,,,,692,692,,,,692,,692,692,692,692,692,692,692,,,,,692,692,692',
'692,692,692,692,,,,,,,,,,692,,,692,692,692,692,692,692,692,692,692,692',
',692,692,,692,692,692,,,,,,,,,,,,,,,,,,,,692,,,692,,,692,692,,,692,',
',,,,692,,,,,,,,692,,,,,692,692,692,692,692,692,,,,692,692,698,698,698',
',698,,,,698,698,,,,698,,698,698,698,698,698,698,698,,,,,698,698,698',
'698,698,698,698,,,,,,,,,,698,,,698,698,698,698,698,698,698,698,698,698',
',698,698,,,,698,,600,600,600,600,600,600,600,600,600,600,600,,600,600',
',,600,600,698,,,698,,,698,698,,,698,,,,600,,600,,600,600,600,600,600',
'600,600,,600,,,698,698,698,698,698,698,,,,698,698,704,704,704,600,704',
',,,704,704,,,,704,,704,704,704,704,704,704,704,,,,,704,704,704,704,704',
'704,704,,,,,,,,,,704,,,704,704,704,704,704,704,704,704,704,704,,704',
'704,,704,704,704,,,,,,,,,,,,,,,,,,,,704,,,704,,,704,704,,,704,,704,',
'704,,704,,,,,,,,704,,,,,704,704,704,704,704,704,,,,704,704,713,713,713',
',713,,,,713,713,,,,713,,713,713,713,713,713,713,713,,,,,713,713,713',
'713,713,713,713,,,,,,,,,,713,,,713,713,713,713,713,713,713,713,713,713',
',713,713,,713,713,713,,,,,,,,,,,,,,,,,,,,713,,,713,,,713,713,,,713,',
'713,,713,,713,,,,,,,,713,,,,,713,713,713,713,713,713,,,,713,713,715',
'715,715,,715,,,,715,715,,,,715,,715,715,715,715,715,715,715,,,,,715',
'715,715,715,715,715,715,,,,,,,,,,715,,,715,715,715,715,715,715,715,715',
'715,715,,715,715,,715,715,715,,,,,,,,,,,,,,,,,,,,715,,,715,,,715,715',
',,715,,715,,715,,715,,,,,,,,715,,,,,715,715,715,715,715,715,,,,715,715',
'728,728,728,,728,,,,728,728,,,,728,,728,728,728,728,728,728,728,,,,',
'728,728,728,728,728,728,728,,,,,,,,,,728,,,728,728,728,728,728,728,728',
'728,728,728,,728,728,,,,728,,677,677,677,677,677,677,677,677,677,677',
'677,,677,677,,,677,677,728,,,728,,,728,728,,,728,,,,677,,677,,677,677',
'677,677,677,677,677,,677,,,728,728,728,728,728,728,,,,728,728,734,734',
'734,677,734,,,,734,734,,,,734,,734,734,734,734,734,734,734,,,,,734,734',
'734,734,734,734,734,,,734,,,,,,,734,,,734,734,734,734,734,734,734,734',
'734,734,,734,734,,734,734,734,,,,,,,,,,,,,,,,,,,,734,,,734,,,734,734',
',,734,,,,,,734,,,,,,,,734,,,,,734,734,734,734,734,734,,,,734,734,740',
'740,740,,740,,,,740,740,,,,740,,740,740,740,740,740,740,740,,,,,740',
'740,740,740,740,740,740,,,,,,,,,,740,,,740,740,740,740,740,740,740,740',
'740,740,,740,740,,740,740,740,,,,,,,,,,,,,,,,,,,,740,,,740,,,740,740',
',,740,,740,,,,740,,,,,,,,740,,,,,740,740,740,740,740,740,,,,740,740',
'759,759,759,,759,,,,759,759,,,,759,,759,759,759,759,759,759,759,,,,',
'759,759,759,759,759,759,759,,,,,,,,,,759,,,759,759,759,759,759,759,759',
'759,759,759,,759,759,,759,759,759,,,,,,,,,,,,,,,,,,,,759,,,759,,,759',
'759,,,759,,,,,,759,,,,,,,,759,,,,,759,759,759,759,759,759,,,,759,759',
'768,768,768,,768,,,,768,768,,,,768,,768,768,768,768,768,768,768,,,,',
'768,768,768,768,768,768,768,,,,,,,,,,768,,,768,768,768,768,768,768,768',
'768,768,768,,768,768,,768,768,768,,,,,,,,,,,,,,,,,,,,768,,,768,,,768',
'768,,,768,,,,,,768,,,,,,,,768,,,,,768,768,768,768,768,768,,,,768,768',
'769,769,769,,769,,,,769,769,,,,769,,769,769,769,769,769,769,769,,,,',
'769,769,769,769,769,769,769,,,,,,,,,,769,,,769,769,769,769,769,769,769',
'769,769,769,,769,769,,,,769,,,,,,,,,,,,,,,,,,,,769,,,769,,,769,769,',
',769,,769,,769,,,,,,,,,,,,,,,769,769,769,769,769,769,,,,769,769,780',
'780,780,,780,,,,780,780,,,,780,,780,780,780,780,780,780,780,,,,,780',
'780,780,780,780,780,780,,,,,,,,,,780,,,780,780,780,780,780,780,780,780',
'780,780,,780,780,,780,780,780,,,,,,,,,,,,,,,,,,,,780,,,780,,,780,780',
',,780,,,,,,780,,,,,,,,780,,,,,780,780,780,780,780,780,,,,780,780,786',
'786,786,,786,,,,786,786,,,,786,,786,786,786,786,786,786,786,,,,,786',
'786,786,786,786,786,786,,,,,,,,,,786,,,786,786,786,786,786,786,786,786',
'786,786,,786,786,,786,786,786,,,,,,,,,,,,,,,,,,,,786,,,786,,,786,786',
',,786,,,,,,786,,,,,,,,786,,,,,786,786,786,786,786,786,,,,786,786,788',
'788,788,,788,,,,788,788,,,,788,,788,788,788,788,788,788,788,,,,,788',
'788,788,788,788,788,788,,,,,,,,,,788,,,788,788,788,788,788,788,788,788',
'788,788,,788,788,,788,788,788,,,,,,,,,,,,,,,,,,,,788,,,788,,,788,788',
',,788,,,,,,788,,,,,,,,788,,,,,788,788,788,788,788,788,,,,788,788,802',
'802,802,,802,,,,802,802,,,,802,,802,802,802,802,802,802,802,,,,,802',
'802,802,802,802,802,802,,,,,,,,,,802,,,802,802,802,802,802,802,802,802',
'802,802,,802,802,,802,802,802,,,,,,,,,,,,,,,,,,,,802,,,802,,,802,802',
',,802,,,,,,802,,,,,,,,802,,,,,802,802,802,802,802,802,,,,802,802,820',
'820,820,,820,,,,820,820,,,,820,,820,820,820,820,820,820,820,,,,,820',
'820,820,820,820,820,820,,,,,,,,,,820,,,820,820,820,820,820,820,820,820',
'820,820,,820,820,,,,820,,682,682,682,682,682,682,682,682,682,682,682',
',682,682,,,682,682,820,,,820,,,820,820,,,820,,,,682,,682,,682,682,682',
'682,682,682,682,,682,,,820,820,820,820,820,820,,,,820,820,822,822,822',
'682,822,,,,822,822,,,,822,,822,822,822,822,822,822,822,,,,,822,822,822',
'822,822,822,822,,,,,,,,,,822,,,822,822,822,822,822,822,822,822,822,822',
',822,822,,822,822,822,,,,,,,,,,,,,,,,,,,,822,,,822,,,822,822,,,822,',
'822,,,,822,,,,,,,,822,,,,,822,822,822,822,822,822,,,,822,822,827,827',
'827,,827,,,,827,827,,,,827,,827,827,827,827,827,827,827,,,,,827,827',
'827,827,827,827,827,,,,,,,,,,827,,,827,827,827,827,827,827,827,827,827',
'827,,827,827,,,,827,,684,684,684,684,684,684,684,684,684,684,684,,684',
'684,,,684,684,827,,,827,,,827,827,,,827,,,,684,,684,,684,684,684,684',
'684,684,684,,684,,,827,827,827,827,827,827,,,,827,827,832,832,832,684',
'832,,,,832,832,,,,832,,832,832,832,832,832,832,832,,,,,832,832,832,832',
'832,832,832,,,,,,,,,,832,,,832,832,832,832,832,832,832,832,832,832,',
'832,832,,832,832,832,,,,,,,,,,,,,,,,,,,,832,,,832,,,832,832,,,832,,832',
',832,,832,,,,,,,,832,,,,,832,832,832,832,832,832,,,,832,832,835,835',
'835,,835,,,,835,835,,,,835,,835,835,835,835,835,835,835,,,,,835,835',
'835,835,835,835,835,,,,,,,,,,835,,,835,835,835,835,835,835,835,835,835',
'835,,835,835,,835,835,835,,,,,,,,,,,,,,,,,,,,835,,,835,,,835,835,,,835',
',835,,835,,835,,,,,,,,835,,,,,835,835,835,835,835,835,,,,835,835,861',
'861,861,,861,,,,861,861,,,,861,,861,861,861,861,861,861,861,,,,,861',
'861,861,861,861,861,861,,,,,,,,,,861,,,861,861,861,861,861,861,861,861',
'861,861,,861,861,,,,861,,687,687,687,687,687,687,687,687,687,687,687',
',687,687,,,687,687,861,,,861,,,861,861,,,861,,,,687,,687,,687,687,687',
'687,687,687,687,,687,,,861,861,861,861,861,861,,,,861,861,864,864,864',
'687,864,,,,864,864,,,,864,,864,864,864,864,864,864,864,,,,,864,864,864',
'864,864,864,864,,,,,,,,,,864,,,864,864,864,864,864,864,864,864,864,864',
',864,864,,864,864,864,,,,,,,,,,,,,,,,,,,,864,,,864,,,864,864,,,864,',
',,,,864,,,,,,,,864,,,,,864,864,864,864,864,864,,,,864,864,867,867,867',
',867,,,,867,867,,,,867,,867,867,867,867,867,867,867,,,,,867,867,867',
'867,867,867,867,,,,,,,,,,867,,,867,867,867,867,867,867,867,867,867,867',
',867,867,,867,867,867,,,,,,,,,,,,,,,,,,,,867,,,867,,,867,867,,,867,',
',,,,867,,,,,,,,867,,,,,867,867,867,867,867,867,,,,867,867,875,875,875',
',875,,,,875,875,,,,875,,875,875,875,875,875,875,875,,,,,875,875,875',
'875,875,875,875,,,,,,,,,,875,,,875,875,875,875,875,875,875,875,875,875',
',875,875,,,,875,,689,689,689,689,689,689,689,689,689,689,689,,689,689',
',,689,689,875,,,875,,,875,875,,,875,,,,689,,689,,689,689,689,689,689',
'689,689,,689,,,875,875,875,875,875,875,,,,875,875,880,880,880,689,880',
',,,880,880,,,,880,,880,880,880,880,880,880,880,,,,,880,880,880,880,880',
'880,880,,,,,,,,,,880,,,880,880,880,880,880,880,880,880,880,880,,880',
'880,,880,880,880,,,,,,,,,,,,,,,,,,,,880,,,880,,,880,880,,,880,,880,',
'880,,880,,,,,,,,880,,,,,880,880,880,880,880,880,,,,880,880,886,886,886',
',886,,,,886,886,,,,886,,886,886,886,886,886,886,886,,,,,886,886,886',
'886,886,886,886,,,,,,,,,,886,,,886,886,886,886,886,886,886,886,886,886',
',886,886,,,,886,,694,694,694,694,694,694,694,694,694,694,694,,694,694',
',,694,694,886,,,886,,,886,886,,,886,,,,694,,694,,694,694,694,694,694',
'694,694,,694,,,886,886,886,886,886,886,,,,886,886,889,889,889,694,889',
',,,889,889,,,,889,,889,889,889,889,889,889,889,,,,,889,889,889,889,889',
'889,889,,,,,,,,,,889,,,889,889,889,889,889,889,889,889,889,889,,889',
'889,,889,889,889,,,,,,,,,,,,,,,,,,,,889,,,889,,,889,889,,,889,,,,,,889',
',,,,,,,889,,,,,889,889,889,889,889,889,,,,889,889,64,64,64,64,64,64',
'64,64,64,64,64,64,64,64,64,64,64,64,64,64,64,64,64,64,,,64,64,64,64',
'64,64,64,64,64,64,,,,,,64,64,64,64,64,64,64,64,64,64,64,64,,,,,,,64',
'64,,64,64,64,64,,64,64,,,64,,,,,64,64,64,64,,,,,,64,,,,,,,,64,64,,64',
'64,64,64,64,64,64,64,64,,64,,,64,664,664,664,664,664,664,664,664,664',
'664,664,664,664,664,664,664,664,664,664,664,664,664,664,664,,,664,664',
'664,664,664,664,664,664,664,664,,,,,,664,664,664,664,664,664,664,664',
',,664,,,,,,,,664,664,,664,664,664,664,,664,664,,,664,,,,,664,664,664',
'664,,,,,,,,,,,,,,664,664,,664,664,664,664,664,664,664,664,664,,664,',
',664,581,581,581,581,581,581,581,581,581,581,581,,581,581,,,581,581',
',,,581,,,,,,,,,,,581,,581,,581,581,581,581,581,581,581,,581,,,,,,,,192',
'192,,,192,,,,,581,,581,192,192,,192,192,192,192,,192,192,,,192,,,,,192',
'192,192,192,,,,,,,,,,,,,,192,192,,192,192,192,192,192,192,192,192,192',
',192,193,193,192,,193,,,,,,,,193,193,,193,193,193,193,,193,193,,,193',
',,,,193,193,193,193,,,,,,,,,,,,,,193,193,,193,193,193,193,193,193,193',
'193,193,,193,251,251,193,,251,,,,,,,,251,251,,251,251,251,251,,251,251',
',,251,,,,,251,251,251,251,,,,,,,,,,,,,,251,251,,251,251,251,251,251',
'251,251,251,251,,251,252,252,251,,252,,,,,,,,252,252,,252,252,252,252',
',252,252,,,252,,,,,252,252,252,252,,,,,,,,,,,,,,252,252,,252,252,252',
'252,252,252,252,252,252,,252,391,391,252,,391,,,,,,,,391,391,,391,391',
'391,391,,391,391,,,391,,,,,391,391,391,391,,,,,,,,,,,,,,391,391,,391',
'391,391,391,391,391,391,391,391,,391,392,392,391,,392,,,,,,,,392,392',
',392,392,392,392,,392,392,,,392,,,,,392,392,392,392,,,,,,,,,,,,,,392',
'392,,392,392,392,392,392,392,392,392,392,,392,,,392,423,423,423,423',
'423,423,423,423,423,423,423,,423,423,,,423,423,,,,,,,,,,,,,,,423,,423',
',423,423,423,423,423,423,423,,423,,,,,,,457,457,,,457,,,,,,423,423,457',
'457,,457,457,457,457,,457,457,,,457,,,,,457,457,457,457,,,,,,,,,,,,',
',457,457,,457,457,457,457,457,457,457,457,457,,457,458,458,457,,458',
',,,,,,,458,458,,458,458,458,458,,458,458,,,458,,,,,458,458,458,458,',
',,,,,,,,,,,,458,458,,458,458,458,458,458,458,458,458,458,,458,466,466',
'458,,466,,,,,,,,466,466,,466,466,466,466,,466,466,,,466,,,,,466,466',
'466,466,,,,,,,,,,,,,,466,466,,466,466,466,466,466,466,466,466,466,,466',
'467,467,466,,467,,,,,,,,467,467,,467,467,467,467,,467,467,,,467,,,,',
'467,467,467,467,,,,,,,,,,,,,,467,467,,467,467,467,467,467,467,467,467',
'467,,467,498,498,467,,498,,,,,,,,498,498,,498,498,498,498,,498,498,',
',498,,,,,498,498,498,498,,,,,,,,,,,,,,498,498,,498,498,498,498,498,498',
'498,498,498,,498,499,499,498,,499,,,,,,,,499,499,,499,499,499,499,,499',
'499,,,499,,,,,499,499,499,499,,,,,,,,,,,,,,499,499,,499,499,499,499',
'499,499,499,499,499,,499,505,505,499,,505,,,,,,,,505,505,,505,505,505',
'505,,505,505,,,505,,,,,505,505,505,505,,,,,,,,,,,,,,505,505,,505,505',
'505,505,505,505,505,505,505,,505,507,507,505,,507,,,,,,,,507,507,,507',
'507,507,507,,507,507,,,507,,,,,507,507,507,507,,,,,,,,,,,,,,507,507',
',507,507,507,507,507,507,507,507,507,,507,578,578,507,,578,,,,,,,,578',
'578,,578,578,578,578,,578,578,,,578,,,,,578,578,578,578,,,,,,,,,,,,',
',578,578,,578,578,578,578,578,578,578,578,578,,578,579,579,578,,579',
',,,,,,,579,579,,579,579,579,579,,579,579,,,579,,,,,579,579,579,579,',
',,,,,,,,,,,,579,579,,579,579,579,579,579,579,579,579,579,,579,823,823',
'579,,823,,,,,,,,823,823,,823,823,823,823,,823,823,,,823,,,,,823,823',
'823,823,,,,,,,,,,,,,,823,823,,823,823,823,823,823,823,823,823,823,,823',
'824,824,823,,824,,,,,,,,824,824,,824,824,824,824,,824,824,,,824,,,,',
'824,824,824,824,,,,,,,,,,,,,,824,824,,824,824,824,824,824,824,824,824',
'824,,824,,,824,480,480,480,480,480,480,480,480,480,480,480,,480,480',
',,480,480,,,,,,,,,,,,,,,480,,480,,480,480,480,480,480,480,480,,480,',
'731,731,731,731,731,731,731,731,731,731,731,,731,731,480,480,731,731',
',,,,,,,,,,,,,,731,,731,,731,731,731,731,731,731,731,,731,,767,767,767',
'767,767,767,767,767,767,767,767,,767,767,731,731,767,767,,,,,,,,,,,',
',,,767,,767,,767,767,767,767,767,767,767,,767,,,,,,,,,,,,,,,,,767' ]
        racc_action_check = arr = ::Array.new(24340, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
  1233,    30,   nil,   -98,   863,  4770,  4891,  5012,   -53,     2,
    71,    90,   160,   218,   174,   130,   nil,  5125,  5246,  6094,
   234,   nil,  5367,  5488,  5609,   292,   138,  5730,  5851,   nil,
  1355,  5972,  6093,   nil,   146,   316,   245,   346,  6214,  6335,
  6456,   183,   300,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   214,  1477,  6577,  6698,  6819,    58,  6940,  7061,   nil,   nil,
   735,  7182,  7303,  7424, 22775,   nil,   nil,   nil,   nil,   nil,
   -92,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  7545,   nil,   nil,   nil,  7666,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   311,   nil,   863,   nil,   nil,
   nil,  7787,  7908,  8029,  8150,  8271,   981,   nil,   221,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   196,   nil,  1599,  1721,  8392,  8513,
  8634,  8755, 23000, 23060,  8876,  8997,  9118,   nil,   377,   -67,
   279,   -52,   212,   274,  1843,   nil,   nil,  9239,  9360,  9481,
  9602,  9723,  9844,  9965, 10086, 10207, 10328, 10449, 10570, 10691,
 10812, 10933, 11054, 11175, 11296, 11417, 11538, 11659, 11780, 11901,
 12022, 12143, 12264, 12385,   nil,   nil,   nil,  7183,   nil,   242,
   254, 12506,   nil, 12627,   310,   nil,   nil,   nil,   nil,   nil,
   nil, 23120, 23180,   313, 12748, 12869,   nil,   nil,   nil,   nil,
   nil,   nil,   nil, 12990,   325,  1965,   333,   352,   315, 13111,
  2087,   399,   496,   430,   520,   411,   382,   161,   nil,   443,
   430,   nil,   nil,   335,   472,   486,   521,   nil,   498,   nil,
 13232,   nil,   565,   566,   457,   nil,   460,   119,   136, 13353,
   493,   147,   479,   244,   nil,   482,    -7,     8, 13474, 13595,
   -67,    32,   464,    -9,   522,   545,    -1,   575,   nil,   nil,
   319,   345,   115,   nil,   614,   nil,     9, 13716,   nil,   nil,
   311,   419,   453,   456,   457,   478,   488,   499,   nil,   519,
   nil, 13837,   nil,   149,   207,   235,   246,   -35,   279,   nil,
  1103,   nil,   nil,   nil,   nil,   nil,   nil, 13958,   nil,   nil,
   nil,   nil,   509,   511,   nil,   nil,   735,   nil,   495, 14071,
   nil,   499,   nil,   nil,  7304,   535,   340,   342, 14192,   nil,
   nil,     0,   541,   107,   nil, 14313, 14434,   nil,  7425,   nil,
   nil, 23240, 23300, 14555,   -33, 14676, 14797, 14918,   614,   863,
   377,   384,   566,   574,   575,   576,  2941,  3063,  3185,  1599,
   980,  1721,  1843,  1965,  2087,  2209,  2331,  2453,  2575,   249,
   465,  2697,  2819, 23348,   -48,   nil, 15039,   nil, 15160,   514,
   nil, 15281,   321,   nil,   nil,   376,   nil,   nil,   563,   531,
   -64,   529,   630,   nil,   nil, 15402,   -27,   -13,   571,   nil,
   572,   544,   nil,   nil,   nil,   586, 15523, 23414, 23474,   617,
   606,   nil,   nil, 15644, 15765, 15886, 23534, 23594, 13112, 16007,
   688, 16128,   nil,   582,   nil,   nil, 16249,   nil,   nil, 16370,
 24122,   nil, 16491,   nil,   nil,   nil,  2209,   711,   nil,   nil,
  2331,    68,   109,   708,   716,  2453, 16612, 16733, 23654, 23714,
     4,   nil,   nil,   658,   nil, 23774, 16854, 23834,   nil,   nil,
 16975,   100,   -34,  2575,   997,   nil,   nil,   nil,   -32,   nil,
   nil,   nil,   598,   nil,   nil,   nil,   609,   nil,   147,   nil,
   nil,   606,   nil,   nil, 17096,   nil,   nil, 17209, 17330,   nil,
   349, 17451, 17572,   648,   nil,   nil, 17693,   649,   nil, 17814,
    86,   115,   493,   614,   655,  1106, 17935, 18056,   nil,  2697,
 18177,   621,   nil,   666, 18298,   nil,   674,   nil,   663,   nil,
   nil,   nil,   nil,   nil,   113,   nil,   673,   674, 23894, 23954,
 18419, 22933,    69,   643, 18540,   nil,   683,   nil,  2819,  2941,
   nil,     1,   nil,   681,    63,   112,   687,   347,   735,   688,
 19630,   712,   714,    -2,   772,   nil,  3063,   654,   707,   nil,
   nil,   707, 18661,   nil,   nil,   506,   nil,   800,   nil,   nil,
   nil,   nil,   nil,   810,   nil,   814,   700,    15, 18782,   738,
    13,    23,    25,    75, 18903,   348,   788,   nil,   740,  3185,
   331,   nil,   nil,   835,  3307,  1284,   337,   718,   719,   726,
   nil,   nil,   nil,   nil,   nil,   724,   nil,   nil,   nil,   nil,
   806,   nil,   nil,   809, 22885,   773,   nil,   nil,   nil,   nil,
   nil,  3429,   nil,   nil,   nil,   nil,   nil, 20114,   744, 19024,
 19145,   nil, 21324,   nil, 21566,   nil,   nil, 21929,   nil, 22292,
 19266, 19387, 19508,   167, 22534,   nil,   745,   984, 19629,   nil,
   770,   868,   760,   nil, 19750,   761,  3551,   nil,   nil,   802,
   803,   -63,   871, 19871,   nil, 19992,   772,   nil,   832,   813,
   933,   738,   nil,   nil,  3673,   nil,   nil,    31, 20113,   nil,
   nil, 24168,   938,   nil, 20234,   941,  3795,  3917,   nil,   nil,
 20355,  4039,   nil,    26,   133,   nil,   942,   nil,  4161,   nil,
   946,   836,   nil,  1406,   nil,   -43,   nil,   nil,   436, 20476,
   nil,   nil,   nil,   nil,   862,   nil,   nil, 24214, 20597, 20718,
   863,   866,   920,   907,   847,   884,   nil,   nil,   nil,   nil,
 20839,   nil,   873,   903,   869,   nil, 20960,   870, 21081,   nil,
   nil,   nil,   nil,   nil,  4283,   nil,   nil,   nil,    32,   nil,
   999,  1001, 21202,   332,   nil,   nil,  1002,   nil,   936,   898,
   899,   nil,   nil,   900,   899,   nil,   nil,  1528,   nil,   nil,
 21323,  1106, 21444, 24014, 24074,   918,   933, 21565,  5973,   nil,
   nil,   nil, 21686,   914,   nil, 21807,   918,  1046,  4405,   nil,
   nil,   nil,   nil,   nil,   nil,  4527,   nil,   nil,   282,   nil,
   nil,   nil,  4649,   nil,   927,   959,   965,   362,   400,   482,
   977, 21928,   nil,   nil, 22049,   931,   nil, 22170,   nil,   nil,
   545,  1054,   938,  1057,   972, 22291,   980,   nil,   945,   nil,
 22412,   948,   nil,   nil,   nil,   nil, 22533,   nil,   nil, 22654,
   nil,   nil,   952,   nil ]

racc_action_default = [
    -4,  -497,    -1,  -485,    -5,  -497,  -497,  -497,  -497,  -497,
  -497,  -497,  -497,  -497,  -271,   -32,   -33,  -497,  -497,   -38,
   -40,   -41,  -282,  -315,  -316,   -45,  -249,  -361,  -285,   -58,
    -4,   -62,   -67,   -68,  -497,  -428,  -497,  -497,  -497,  -497,
  -497,  -487,  -214,  -264,  -265,  -266,  -267,  -268,  -269,  -270,
  -475,    -4,  -497,  -496,  -467,  -288,  -497,  -497,  -292,  -295,
  -485,  -497,  -497,  -497,  -497,  -317,  -318,  -381,  -382,  -383,
  -384,  -385,  -399,  -388,  -401,  -401,  -392,  -397,  -411,  -401,
  -413,  -414,  -417,  -418,  -419,  -420,  -421,  -422,  -423,  -424,
  -425,  -426,  -427,  -430,  -431,  -497,    -3,  -486,  -492,  -493,
  -494,  -497,  -497,  -497,  -497,  -497,    -6,    -8,  -497,   -93,
   -94,   -95,   -96,   -97,   -98,   -99,  -100,  -101,  -105,  -106,
  -107,  -108,  -109,  -110,  -111,  -112,  -113,  -114,  -115,  -116,
  -117,  -118,  -119,  -120,  -121,  -122,  -123,  -124,  -125,  -126,
  -127,  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,
  -137,  -138,  -139,  -140,  -141,  -142,  -143,  -144,  -145,  -146,
  -147,  -148,  -149,  -150,  -151,  -152,  -153,  -154,  -155,  -156,
  -157,  -158,  -159,  -160,  -161,  -162,  -163,  -164,  -165,  -166,
  -167,  -168,  -169,  -170,   -13,  -102,    -4,    -4,  -497,  -497,
  -497,  -496,  -497,  -497,  -497,  -497,  -497,   -36,  -497,  -428,
  -497,  -271,  -497,  -497,    -4,   -37,  -206,  -497,  -497,  -497,
  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,
  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,  -497,
  -497,  -497,  -497,  -497,  -351,  -353,   -42,  -215,  -228,  -258,
  -258,  -497,  -236,  -497,  -259,  -282,  -315,  -316,  -470,   -43,
   -44,  -497,  -497,   -50,  -496,  -497,  -287,  -356,  -362,  -364,
   -56,  -360,   -57,  -497,   -58,    -4,  -497,  -497,   -63,   -65,
    -4,   -72,  -497,  -497,   -79,  -285,  -487,  -497,  -319,  -361,
  -497,   -66,   -70,  -278,  -415,  -416,  -497,  -191,  -192,  -207,
  -497,  -488,  -373,  -497,  -274,  -216,  -487,  -489,  -489,  -497,
  -497,  -489,  -497,  -489,  -289,   -39,  -497,  -497,  -497,  -497,
  -485,  -497,  -486,  -428,  -497,  -497,  -271,  -497,  -331,  -332,
   -88,   -89,  -497,   -91,  -497,  -271,  -497,  -497,  -428,  -308,
   -93,   -94,  -131,  -132,  -148,  -153,  -160,  -163,  -310,  -497,
  -465,  -497,  -386,  -497,  -497,  -497,  -497,  -497,  -497,   894,
    -7,  -495,   -14,   -15,   -16,   -17,   -18,  -497,   -10,   -11,
   -12,  -103,  -497,  -497,   -21,   -29,  -171,  -259,  -497,  -497,
   -22,   -30,   -31,   -23,  -173,  -497,  -476,  -477,  -226,  -478,
  -479,  -476,  -249,  -477,  -359,  -481,  -482,   -28,  -180,   -34,
   -35,  -497,  -497,  -496,  -278,  -497,  -497,  -497,  -181,  -182,
  -183,  -184,  -185,  -186,  -187,  -188,  -193,  -194,  -195,  -196,
  -197,  -198,  -199,  -200,  -201,  -202,  -203,  -204,  -205,  -208,
  -209,  -210,  -211,  -497,  -347,  -229,  -497,  -231,  -497,  -258,
  -256,  -497,  -249,  -476,  -477,  -249,   -48,   -51,  -497,  -487,
  -487,  -258,  -228,  -250,  -251,  -252,  -347,  -347,  -497,  -284,
  -497,   -59,  -276,   -71,   -64,  -497,  -496,  -497,  -497,   -78,
  -497,  -415,  -416,  -497,  -497,  -497,  -497,  -497,  -212,  -497,
  -496,  -496,  -273,  -487,  -217,  -218,  -491,  -490,  -220,  -491,
  -487,  -280,  -491,  -469,  -281,  -468,    -4,  -320,  -321,  -322,
    -4,  -497,  -497,  -497,  -497,    -4,  -497,  -496,  -497,  -497,
  -278,  -301,   -88,   -89,   -90,  -497,  -496,  -497,  -304,  -432,
  -497,  -497,  -497,    -4,  -445,  -312,  -483,  -484,  -487,  -387,
  -400,  -403,  -497,  -405,  -389,  -402,  -497,  -391,  -497,  -394,
  -396,  -497,  -412,    -9,  -497,   -19,   -20,  -497,  -497,  -263,
  -279,  -497,  -497,   -52,  -227,  -357,  -497,   -54,  -358,  -497,
  -476,  -477,  -480,  -277,  -497,  -171,  -497,  -497,  -349,    -4,
  -497,  -258,  -257,  -260,  -497,  -471,  -497,  -235,  -497,  -472,
   -46,  -354,   -47,  -355,  -347,  -222,  -497,  -497,  -497,  -497,
  -497,   -38,  -497,  -258,  -497,  -248,  -497,  -254,    -4,    -4,
  -283,   -59,   -69,  -497,  -476,  -477,  -226,   -75,   -77,  -497,
  -179,  -189,  -190,  -497,  -496,  -329,    -4,  -374,  -496,  -375,
  -376,  -497,  -497,  -260,  -221,  -496,  -323,  -496,  -293,  -324,
  -325,  -326,  -296,  -497,  -299,  -497,  -367,  -497,  -497,  -497,
  -476,  -477,  -480,  -277,  -497,   -88,   -89,   -92,  -497,    -4,
  -497,  -434,  -306,  -497,    -4,  -445,  -497,  -464,  -464,  -464,
  -444,  -446,  -447,  -448,  -449,  -450,  -451,  -454,  -456,  -457,
  -459,  -460,  -461,  -497,  -497,  -497,  -404,  -407,  -408,  -409,
  -410,    -4,  -390,  -393,  -395,  -398,  -104,  -172,  -261,  -497,
  -497,   -25,  -175,   -26,  -176,   -53,   -27,  -177,   -55,  -178,
  -497,  -497,  -497,  -279,  -213,  -333,  -335,  -345,  -497,  -348,
  -497,  -497,  -258,  -233,  -497,  -258,    -4,  -223,  -224,  -226,
  -226,  -487,  -497,  -497,  -241,  -497,  -258,  -253,  -497,  -497,
  -497,   -73,  -286,    -2,    -4,  -380,  -330,  -497,  -497,  -378,
  -275,  -487,  -497,  -327,  -497,  -497,    -4,    -4,  -298,  -300,
  -497,    -4,  -369,  -279,  -497,  -279,  -497,  -433,    -4,  -309,
  -497,  -487,  -436,  -497,  -440,  -497,  -442,  -443,  -497,  -497,
  -458,  -462,  -313,  -466,  -497,  -262,   -24,  -174,  -497,  -336,
   -80,  -497,  -497,   -87,  -344,  -497,  -346,  -350,  -352,  -230,
  -497,  -232,  -497,  -497,  -258,  -238,  -497,  -258,  -497,  -247,
  -255,  -363,  -365,  -379,    -4,  -377,  -219,  -290,  -497,  -291,
  -497,  -497,  -497,  -496,  -302,  -305,  -497,  -311,  -497,  -464,
  -464,  -452,  -463,  -464,  -497,  -455,  -453,  -445,  -406,  -334,
  -497,  -341,  -496,  -497,  -497,   -86,  -497,  -497,  -258,   -49,
  -225,  -237,  -497,  -258,  -243,  -497,  -258,  -373,    -4,  -294,
  -297,  -368,  -366,  -370,  -371,    -4,  -307,  -435,  -497,  -438,
  -439,  -441,    -4,  -337,  -340,  -497,  -497,   -82,   -84,   -83,
   -85,  -497,  -343,  -234,  -497,  -258,  -239,  -497,  -242,  -372,
  -496,  -497,  -464,  -497,  -497,  -497,   -81,  -342,  -258,  -244,
  -497,  -258,  -328,  -303,  -437,  -314,  -497,  -339,  -240,  -497,
  -245,  -338,  -258,  -246 ]

clist = [
'10,205,240,240,240,10,268,438,112,112,100,300,338,470,293,644,242,242',
'242,440,107,185,238,238,238,115,115,117,117,494,10,486,490,650,303,437',
'239,239,239,253,260,262,604,290,365,372,732,618,622,96,565,10,815,296',
'257,261,281,571,504,520,573,318,529,236,249,250,112,100,301,559,810',
'264,813,266,106,475,478,344,345,483,1,485,348,723,754,756,757,326,329',
'306,307,588,589,310,97,13,184,10,319,357,13,565,574,534,587,10,311,443',
'586,718,340,302,198,198,304,317,508,198,198,198,339,515,473,308,736',
'13,272,272,309,737,634,278,278,845,352,353,354,355,639,748,513,197,514',
'664,817,815,13,198,198,375,696,198,198,700,424,198,314,324,324,446,447',
'278,278,278,650,872,350,627,842,608,727,364,370,373,356,342,294,387',
'343,346,305,305,528,347,305,666,10,10,671,735,751,296,13,809,811,386',
'198,198,198,198,13,2,368,368,10,,,,,,,623,,,,,,,,706,,,,605,610,305',
'305,305,305,,,267,,,240,240,,,,,,637,,240,674,,849,850,242,242,851,',
',,442,238,,242,,,,,,238,,10,,,441,239,10,,,,,,,239,460,,,13,13,198,198',
'198,198,,,198,198,198,14,454,,439,444,14,474,13,804,882,,427,448,257',
'264,261,450,884,,264,,455,,,,100,852,389,390,,14,274,274,685,565,609',
',688,,,,509,571,573,650,565,,,,491,492,14,,711,,,,198,198,554,741,316',
'325,325,493,725,198,112,13,729,,,272,13,,,605,278,605,533,,,,843,115',
',117,,,,,,,,,362,363,14,,543,,296,386,547,,14,,,,,,198,198,267,561,',
',,548,,,,593,,,,,,,,198,,,583,,,,,,,305,305,,,198,,,585,,576,577,,,570',
',,572,,,512,794,,,629,296,565,,,386,,,,638,518,267,,,386,,267,,,,611',
'643,,14,14,,,614,,,368,10,,198,,10,,567,,,10,14,296,619,619,,386,565',
',,,296,386,,,626,,,10,,,,,641,642,665,844,,838,681,683,,,,686,,,695',
',,,112,,,,198,,,640,,,,,676,,,198,,115,,117,605,14,869,10,561,274,14',
',198,,,,,294,,,,,,,,,,,712,13,,,,13,,,10,10,13,,198,603,,,378,382,35',
',,198,746,35,,198,10,750,13,,,,762,12,,,,,12,,,,,605,703,,,548,35,271',
'271,,,,305,,198,198,,10,,198,,12,10,,714,747,,35,,,272,752,13,432,435',
'278,,313,328,328,328,766,12,,112,744,,,,10,,,,198,,,,,,,,13,13,,,615',
',,,617,,787,,35,625,774,776,,,13,,35,,,10,785,,,12,806,,783,,,,,12,',
',,,,10,,,784,795,198,,,,796,13,,10,10,,13,,10,,,819,,,,10,,,619,,808',
',,,,305,,701,,,,,779,13,798,781,,,,,,,198,,,14,789,856,,14,,35,35,,14',
',719,720,,771,771,10,378,382,278,278,12,12,13,35,,,14,,726,,,,,865,',
'670,873,,12,,13,853,854,296,771,,,386,862,278,198,,13,13,,,,13,,10,',
',,,13,,10,,831,,274,834,14,10,,,,,,305,,,877,599,35,272,,,271,35,278',
',599,764,,,887,,,12,,14,14,,12,,,891,,,13,,863,,,,,866,,14,868,,,,599',
',,,782,,599,,,,,,771,771,198,,,278,278,771,,,793,,278,879,,,14,,13,',
',14,800,801,,13,888,803,,890,,,13,,,,,,,,893,771,,,,,278,,14,,,,,,,771',
',,,,278,,,,,,771,709,710,,,278,,,773,773,,,,,837,,,14,,,,,,,,,,,,,,',
',,,14,,,,773,,,,,,,,14,14,,,,14,,,,,,870,14,,,,,,871,,,,,,,,,,,,,,,274',
',,,,,,,,,,,,,,,,,,,,,,35,,14,,35,,,,,35,,,,,,12,,,,12,,,,,12,,,35,773',
'773,,,,,,773,,,,,,,12,,,,14,,,,,,,14,,,,,,,14,,,,,,,,,773,,271,,35,',
',,,,,,,,773,,,,,12,,,,,,773,,,,,,,,35,35,,,,,,,,,,,,,,12,12,,35,,,,',
',,,,,,,,,,12,,,,,,,,,599,,,,,,,,,35,,,,,35,,,,,,,,,,12,,,,,12,,,,,,',
',,,,,35,,,,,,,,,,,,,,,12,,,,,,,,,,,770,770,,,,,,,,35,,,206,,,,237,237',
'237,,,,,,12,,,35,,,,770,287,288,289,,,,,35,35,,12,,35,,,237,237,,,35',
',,12,12,,,,12,,,,,367,371,12,,,,,,271,,,,,,,,,,,,,,,,,,,,,,,,,35,,,',
',,,,,,,,,,,12,,,,,429,,430,,,,770,770,,,,,,770,,,,,,,,,,,35,,,,,,,35',
',,,,,,35,12,,,,,,,12,770,,,,,,12,,,,,,,,770,,,,366,237,374,237,,,388',
'770,,,,,,,,,,,,206,398,399,400,401,402,403,404,405,406,407,408,409,410',
'411,412,413,414,415,416,417,418,419,420,421,422,423,,,,,,,,237,,237',
',,,,,,,539,,,237,237,,,,,,,,237,,,,,,,,,,,,,,,,,,,,,,,,,,,468,,,,,,',
',,480,,,,,,,,,563,,566,,,569,,,,,,,,,,,,,,582,,,,,,,,,,,,,,,,,,,,,,',
',,,607,,,,,613,,,566,,,613,,,,,237,,,,,,,,,367,,,,,,,,,,,,,,,237,,388',
'555,374,,,,,,,,,,,,,,,,,,,,,,,678,,,,,,237,,237,,,237,,,,,,,,,,,702',
',,581,705,,,,,,,,,,237,,,,,,563,600,601,602,716,,26,,,237,,26,,,237',
',,237,,,237,,,26,26,,,,26,26,26,,,,237,237,26,,,,,,,,237,,,,742,,,,',
',,,,26,26,26,,,26,26,,,26,,,,,,,,,677,237,,,682,684,,,,687,,,689,,,',
',,,694,,,,237,765,,,237,26,,,,26,26,26,26,26,,,,,,,237,,,,237,,566,',
',,,,,,,,,566,,,,,,,,,,,,,,,731,,,,,,,,,,613,,,,,,237,,,,,,,,,,,,,816',
',,,,,,,,,,,,26,26,26,26,26,26,,,26,26,26,,,,833,,836,,26,,,,,,,237,767',
',,,841,,,,,,682,684,687,,,,,,,,,,,,237,,,,,,,,,237,563,237,,566,,,,',
'26,26,,,,,,,,26,,26,,,,,26,,237,,,,,,878,,,881,,,,,,,,,,237,,,566,,',
',,,767,,,892,,,,,26,26,,,828,,,,,,237,,237,,,,,,,26,,,,,,,237,,,,,,',
'26,,,,,,,,,,,,,237,,,,,,,,,,237,,,237,,,,,,,,,,,,,,,,,,,,,,,,,,26,,',
'237,,,237,,,,,,,,,,,,,237,,,,,,,,,237,,,,,,,,,,,,,,,,,,,,,,,,26,,,,',
',,,,,,26,,,,,,,,,,,,,26,,,,,,,,,,,,,,,,,26,,,,26,,,,,26,,26,,,,,,,,',
'26,,,,26,,,26,,,,,,,,,,,,,,,,,,,,,,,,,,,,26,26,,,,26,,,,,,,,,,,,,26',
',,,,,,,,,,,,,,,,,,,,26,,,,,,,,26,26,,,,,,,,,,,,,,,,,26,,,,,,,,,,,,,',
',,,,,,,,,,,,,,26,,,,,26,,,,,26,,,,,,,,,,,,,,,,,,,,,,,,,,,26,,,,,,,,',
'26,,,,,,,,,,,,,,,,,,,,,,,,,,26,,,,,,,,,,,,,,,,,,26,,,,,,,,,,26,,26,26',
',,,26,,,,,,,26,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,26,,,,,',
',,,,,,,,,,,,,,,,,,,,,,26,,,,,,,,,,,,,,,,26,,,,,,,26,,,,,,,26' ]
        racc_goto_table = arr = ::Array.new(2610, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'14,15,56,56,56,14,39,53,45,45,82,19,44,4,3,83,60,60,60,29,11,11,26,26',
'26,48,48,49,49,79,14,76,76,126,56,32,54,54,54,31,31,31,5,51,21,21,77',
'78,78,8,131,14,128,26,57,57,40,58,42,117,58,14,117,28,28,28,45,82,54',
'33,124,36,124,37,9,55,55,114,114,55,1,55,114,6,125,125,125,43,43,13',
'13,33,33,13,10,18,12,14,16,24,18,131,34,50,61,14,8,62,64,65,72,73,18',
'18,74,80,81,18,18,18,84,85,86,87,88,18,18,18,89,90,91,52,52,92,13,13',
'13,13,93,94,95,23,96,97,98,128,18,18,18,19,99,18,18,100,102,18,18,18',
'18,104,105,52,52,52,126,124,9,106,107,108,109,15,15,15,9,112,23,15,113',
'115,23,23,116,118,23,119,14,14,120,5,121,26,18,123,127,45,18,18,18,18',
'18,2,54,54,14,,,,,,,79,,,,,,,,33,,,,53,53,23,23,23,23,,,2,,,56,56,,',
',,,42,,56,117,,125,125,60,60,125,,,,26,26,,60,,,,,,26,,14,,,54,54,14',
',,,,,,54,51,,,18,18,18,18,18,18,,,18,18,18,20,40,,28,28,20,51,18,78',
'77,,59,28,57,36,57,37,125,,36,,37,,,,82,83,23,23,,20,20,20,32,131,21',
',32,,,,82,58,58,126,131,,,,13,13,20,,29,,,,18,18,19,76,20,20,20,8,53',
'18,45,18,53,,,18,18,,,53,52,53,11,,,,5,48,,49,,,,,,,,,2,2,20,,31,,26',
'45,31,,20,,,,,,18,18,2,56,,,,57,,,,19,,,,,,,,18,,,56,,,,,,,23,23,,,18',
',,60,,51,51,,,31,,,31,,,23,76,,,19,26,131,,,45,,,,19,23,2,,,45,,2,,',
',51,3,,20,20,,,51,,,54,14,,18,,14,,59,,,14,20,26,82,82,,45,131,,,,26',
'45,,,54,,,14,,,,,82,82,51,79,,76,15,15,,,,15,,,39,,,,45,,,,18,,,13,',
',,,11,,,18,,48,,49,53,20,4,14,56,20,20,,18,,,,,23,,,,,,,,,,,26,18,,',
',18,,,14,14,18,,18,23,,,30,30,41,,,18,3,41,,18,14,3,18,,,,44,17,,,,',
'17,,,,,53,59,,,57,41,41,41,,,,23,,18,18,,14,,18,,17,14,,59,82,,41,,',
'18,82,18,30,30,52,,41,41,41,41,15,17,,45,13,,,,14,,,,18,,,,,,,,18,18',
',,2,,,,2,,56,,41,2,14,14,,,18,,41,,,14,60,,,17,3,,51,,,,,17,,,,,,14',
',,54,14,18,,,,51,18,,14,14,,18,,14,,,39,,,,14,,,82,,51,,,,,23,,2,,,',
',59,18,13,59,,,,,,,18,,,20,59,19,,20,,41,41,,20,,2,2,,18,18,14,30,30',
'52,52,17,17,18,41,,,20,,2,,,,,56,,20,3,,17,,18,14,14,26,18,,,45,14,52',
'18,,18,18,,,,18,,14,,,,,18,,14,,59,,20,59,20,14,,,,,,23,,,14,30,41,18',
',,41,41,52,,30,2,,,14,,,17,,20,20,,17,,,14,,,18,,59,,,,,59,,20,59,,',
',30,,,,2,,30,,,,,,18,18,18,,,52,52,18,,,2,,52,59,,,20,,18,,,20,2,2,',
'18,59,2,,59,,,18,,,,,,,,59,18,,,,,52,,20,,,,,,,18,,,,,52,,,,,,18,30',
'30,,,52,,,20,20,,,,,2,,,20,,,,,,,,,,,,,,,,,,20,,,,20,,,,,,,,20,20,,',
',20,,,,,,2,20,,,,,,2,,,,,,,,,,,,,,,20,,,,,,,,,,,,,,,,,,,,,,,41,,20,',
'41,,,,,41,,,,,,17,,,,17,,,,,17,,,41,20,20,,,,,,20,,,,,,,17,,,,20,,,',
',,,20,,,,,,,20,,,,,,,,,20,,41,,41,,,,,,,,,,20,,,,,17,,,,,,20,,,,,,,',
'41,41,,,,,,,,,,,,,,17,17,,41,,,,,,,,,,,,,,,17,,,,,,,,,30,,,,,,,,,41',
',,,,41,,,,,,,,,,17,,,,,17,,,,,,,,,,,,41,,,,,,,,,,,,,,,17,,,,,,,,,,,41',
'41,,,,,,,,41,,,25,,,,25,25,25,,,,,,17,,,41,,,,41,25,25,25,,,,,41,41',
',17,,41,,,25,25,,,41,,,17,17,,,,17,,,,,22,22,17,,,,,,41,,,,,,,,,,,,',
',,,,,,,,,,,,41,,,,,,,,,,,,,,,17,,,,,22,,22,,,,41,41,,,,,,41,,,,,,,,',
',,41,,,,,,,41,,,,,,,41,17,,,,,,,17,41,,,,,,17,,,,,,,,41,,,,25,25,25',
'25,,,25,41,,,,,,,,,,,,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25',
'25,25,25,25,25,25,25,25,25,25,25,,,,,,,,25,,25,,,,,,,,22,,,25,25,,,',
',,,,25,,,,,,,,,,,,,,,,,,,,,,,,,,,25,,,,,,,,,25,,,,,,,,,22,,22,,,22,',
',,,,,,,,,,,,22,,,,,,,,,,,,,,,,,,,,,,,,,,22,,,,,22,,,22,,,22,,,,,25,',
',,,,,,,22,,,,,,,,,,,,,,,25,,25,25,25,,,,,,,,,,,,,,,,,,,,,,,22,,,,,,25',
',25,,,25,,,,,,,,,,,22,,,25,22,,,,,,,,,,25,,,,,,22,25,25,25,22,,35,,',
'25,,35,,,25,,,25,,,25,,,35,35,,,,35,35,35,,,,25,25,35,,,,,,,,25,,,,22',
',,,,,,,,35,35,35,,,35,35,,,35,,,,,,,,,25,25,,,25,25,,,,25,,,25,,,,,',
',25,,,,25,22,,,25,35,,,,35,35,35,35,35,,,,,,,25,,,,25,,22,,,,,,,,,,',
'22,,,,,,,,,,,,,,,25,,,,,,,,,,22,,,,,,25,,,,,,,,,,,,,22,,,,,,,,,,,,,35',
'35,35,35,35,35,,,35,35,35,,,,22,,22,,35,,,,,,,25,25,,,,22,,,,,,25,25',
'25,,,,,,,,,,,,25,,,,,,,,,25,22,25,,22,,,,,35,35,,,,,,,,35,,35,,,,,35',
',25,,,,,,22,,,22,,,,,,,,,,25,,,22,,,,,,25,,,22,,,,,35,35,,,25,,,,,,25',
',25,,,,,,,35,,,,,,,25,,,,,,,35,,,,,,,,,,,,,25,,,,,,,,,,25,,,25,,,,,',
',,,,,,,,,,,,,,,,,,,,35,,,25,,,25,,,,,,,,,,,,,25,,,,,,,,,25,,,,,,,,,',
',,,,,,,,,,,,,,35,,,,,,,,,,,35,,,,,,,,,,,,,35,,,,,,,,,,,,,,,,,35,,,,35',
',,,,35,,35,,,,,,,,,35,,,,35,,,35,,,,,,,,,,,,,,,,,,,,,,,,,,,,35,35,,',
',35,,,,,,,,,,,,,35,,,,,,,,,,,,,,,,,,,,,35,,,,,,,,35,35,,,,,,,,,,,,,',
',,,35,,,,,,,,,,,,,,,,,,,,,,,,,,,,35,,,,,35,,,,,35,,,,,,,,,,,,,,,,,,',
',,,,,,,,35,,,,,,,,,35,,,,,,,,,,,,,,,,,,,,,,,,,,35,,,,,,,,,,,,,,,,,,35',
',,,,,,,,,35,,35,35,,,,35,,,,,,,35,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,',
',,,,,,,,,,,35,,,,,,,,,,,,,,,,,,,,,,,,,,,,35,,,,,,,,,,,,,,,,35,,,,,,',
'35,,,,,,,35' ]
        racc_goto_check = arr = ::Array.new(2610, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_goto_pointer = [
   nil,    80,   201,   -37,  -279,  -428,  -521,   nil,    46,    69,
    91,    14,    89,    33,     0,   -17,    37,   613,    95,   -42,
   292,  -144,  1171,   124,    -8,  1289,     0,   nil,    41,  -235,
   404,    13,  -218,  -355,  -334,  1757,    41,    43,   nil,   -25,
    24,   598,  -264,    25,   -52,     2,   nil,   nil,    19,    21,
  -258,     2,   100,  -247,    14,  -222,   -20,    28,  -375,    63,
    -6,  -341,  -148,   nil,  -337,  -478,   nil,   nil,   nil,   nil,
   nil,   nil,    46,    57,    59,   nil,  -275,  -569,  -444,  -282,
    54,  -210,     7,  -499,    56,  -218,  -172,    65,  -494,    69,
  -493,  -371,  -671,  -370,  -503,  -189,  -196,  -372,  -618,  -407,
  -404,   nil,   -80,   nil,   -99,   -99,  -329,  -635,  -302,  -438,
   nil,   nil,   105,   106,     3,   103,  -164,  -284,   106,  -337,
  -335,  -455,   nil,  -560,  -683,  -563,  -481,  -559,  -703,   nil,
   nil,  -378 ]

racc_goto_default = [
   nil,   nil,   292,   nil,   nil,   733,   nil,     3,   nil,     4,
   312,   nil,   nil,   nil,   202,    16,    11,   203,   286,   nil,
   201,   nil,   244,    15,   nil,    19,    20,    21,   nil,    25,
   596,   nil,   nil,   nil,   nil,   277,    29,   nil,    31,    34,
    33,   199,   323,   nil,   114,   380,   113,   116,    68,    69,
   nil,   nil,    42,   295,   297,   nil,   298,   544,   545,   425,
   562,   nil,   nil,   255,   nil,   nil,    43,    44,    45,    46,
    47,    48,    49,   nil,   256,    55,   nil,   nil,   nil,   nil,
   nil,   nil,   487,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   235,   nil,   384,   nil,   nil,   nil,   nil,   nil,   nil,
    67,    70,    71,   nil,   nil,   nil,   nil,   525,   nil,   nil,
   nil,   646,   647,   648,   649,   nil,   812,   656,   657,   660,
   663,   248 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 133, :_reduce_1,
  4, 135, :_reduce_2,
  2, 134, :_reduce_3,
  0, 139, :_reduce_4,
  1, 139, :_reduce_5,
  2, 139, :_reduce_6,
  3, 139, :_reduce_7,
  0, 156, :_reduce_8,
  4, 141, :_reduce_9,
  3, 141, :_reduce_10,
  3, 141, :_reduce_11,
  3, 141, :_reduce_12,
  2, 141, :_reduce_13,
  3, 141, :_reduce_14,
  3, 141, :_reduce_15,
  3, 141, :_reduce_16,
  3, 141, :_reduce_17,
  3, 141, :_reduce_18,
  4, 141, :_reduce_19,
  4, 141, :_reduce_20,
  3, 141, :_reduce_21,
  3, 141, :_reduce_22,
  3, 141, :_reduce_23,
  6, 141, :_reduce_24,
  5, 141, :_reduce_25,
  5, 141, :_reduce_26,
  5, 141, :_reduce_27,
  3, 141, :_reduce_28,
  3, 141, :_reduce_29,
  3, 141, :_reduce_30,
  3, 141, :_reduce_31,
  1, 141, :_reduce_none,
  1, 155, :_reduce_none,
  3, 155, :_reduce_34,
  3, 155, :_reduce_35,
  2, 155, :_reduce_36,
  2, 155, :_reduce_37,
  1, 155, :_reduce_none,
  1, 145, :_reduce_none,
  1, 147, :_reduce_none,
  1, 147, :_reduce_none,
  2, 147, :_reduce_42,
  2, 147, :_reduce_43,
  2, 147, :_reduce_44,
  1, 159, :_reduce_none,
  4, 159, :_reduce_46,
  4, 159, :_reduce_47,
  0, 166, :_reduce_48,
  5, 164, :_reduce_49,
  2, 158, :_reduce_50,
  3, 158, :_reduce_51,
  4, 158, :_reduce_52,
  5, 158, :_reduce_53,
  4, 158, :_reduce_54,
  5, 158, :_reduce_55,
  2, 158, :_reduce_56,
  2, 158, :_reduce_57,
  1, 148, :_reduce_58,
  3, 148, :_reduce_59,
  1, 169, :_reduce_60,
  3, 169, :_reduce_61,
  1, 168, :_reduce_62,
  2, 168, :_reduce_63,
  3, 168, :_reduce_64,
  2, 168, :_reduce_65,
  2, 168, :_reduce_66,
  1, 168, :_reduce_67,
  1, 171, :_reduce_none,
  3, 171, :_reduce_69,
  2, 170, :_reduce_70,
  3, 170, :_reduce_71,
  1, 172, :_reduce_72,
  4, 172, :_reduce_73,
  3, 172, :_reduce_74,
  3, 172, :_reduce_75,
  3, 172, :_reduce_76,
  3, 172, :_reduce_77,
  2, 172, :_reduce_78,
  1, 172, :_reduce_79,
  1, 146, :_reduce_80,
  4, 146, :_reduce_81,
  3, 146, :_reduce_82,
  3, 146, :_reduce_83,
  3, 146, :_reduce_84,
  3, 146, :_reduce_85,
  2, 146, :_reduce_86,
  1, 146, :_reduce_87,
  1, 174, :_reduce_88,
  1, 174, :_reduce_none,
  2, 175, :_reduce_90,
  1, 175, :_reduce_91,
  3, 175, :_reduce_92,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 176, :_reduce_none,
  1, 179, :_reduce_98,
  1, 179, :_reduce_none,
  1, 143, :_reduce_none,
  1, 143, :_reduce_none,
  1, 144, :_reduce_102,
  0, 182, :_reduce_103,
  4, 144, :_reduce_104,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 177, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  1, 178, :_reduce_none,
  3, 157, :_reduce_171,
  5, 157, :_reduce_172,
  3, 157, :_reduce_173,
  6, 157, :_reduce_174,
  5, 157, :_reduce_175,
  5, 157, :_reduce_176,
  5, 157, :_reduce_177,
  5, 157, :_reduce_178,
  4, 157, :_reduce_179,
  3, 157, :_reduce_180,
  3, 157, :_reduce_181,
  3, 157, :_reduce_182,
  3, 157, :_reduce_183,
  3, 157, :_reduce_184,
  3, 157, :_reduce_185,
  3, 157, :_reduce_186,
  3, 157, :_reduce_187,
  3, 157, :_reduce_188,
  4, 157, :_reduce_189,
  4, 157, :_reduce_190,
  2, 157, :_reduce_191,
  2, 157, :_reduce_192,
  3, 157, :_reduce_193,
  3, 157, :_reduce_194,
  3, 157, :_reduce_195,
  3, 157, :_reduce_196,
  3, 157, :_reduce_197,
  3, 157, :_reduce_198,
  3, 157, :_reduce_199,
  3, 157, :_reduce_200,
  3, 157, :_reduce_201,
  3, 157, :_reduce_202,
  3, 157, :_reduce_203,
  3, 157, :_reduce_204,
  3, 157, :_reduce_205,
  2, 157, :_reduce_206,
  2, 157, :_reduce_207,
  3, 157, :_reduce_208,
  3, 157, :_reduce_209,
  3, 157, :_reduce_210,
  3, 157, :_reduce_211,
  3, 157, :_reduce_212,
  5, 157, :_reduce_213,
  1, 157, :_reduce_none,
  1, 154, :_reduce_none,
  1, 151, :_reduce_none,
  2, 151, :_reduce_217,
  2, 151, :_reduce_218,
  5, 151, :_reduce_219,
  2, 151, :_reduce_220,
  3, 151, :_reduce_221,
  3, 189, :_reduce_222,
  4, 189, :_reduce_223,
  4, 189, :_reduce_224,
  6, 189, :_reduce_225,
  0, 190, :_reduce_226,
  1, 190, :_reduce_none,
  1, 160, :_reduce_228,
  2, 160, :_reduce_229,
  5, 160, :_reduce_230,
  2, 160, :_reduce_231,
  5, 160, :_reduce_232,
  4, 160, :_reduce_233,
  7, 160, :_reduce_234,
  3, 160, :_reduce_235,
  1, 160, :_reduce_236,
  4, 193, :_reduce_237,
  3, 193, :_reduce_238,
  5, 193, :_reduce_239,
  7, 193, :_reduce_240,
  2, 193, :_reduce_241,
  5, 193, :_reduce_242,
  4, 193, :_reduce_243,
  6, 193, :_reduce_244,
  7, 193, :_reduce_245,
  9, 193, :_reduce_246,
  3, 193, :_reduce_247,
  1, 193, :_reduce_248,
  0, 195, :_reduce_249,
  2, 163, :_reduce_250,
  1, 194, :_reduce_251,
  0, 196, :_reduce_252,
  3, 194, :_reduce_253,
  0, 197, :_reduce_254,
  4, 194, :_reduce_255,
  2, 192, :_reduce_256,
  2, 191, :_reduce_257,
  0, 191, :_reduce_258,
  1, 186, :_reduce_259,
  3, 186, :_reduce_260,
  3, 153, :_reduce_261,
  4, 153, :_reduce_262,
  2, 153, :_reduce_263,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_none,
  1, 184, :_reduce_272,
  3, 184, :_reduce_273,
  0, 218, :_reduce_274,
  5, 184, :_reduce_275,
  3, 184, :_reduce_276,
  3, 184, :_reduce_277,
  2, 184, :_reduce_278,
  4, 184, :_reduce_279,
  3, 184, :_reduce_280,
  3, 184, :_reduce_281,
  1, 184, :_reduce_282,
  4, 184, :_reduce_283,
  3, 184, :_reduce_284,
  1, 184, :_reduce_285,
  5, 184, :_reduce_286,
  2, 184, :_reduce_287,
  1, 184, :_reduce_none,
  2, 184, :_reduce_289,
  6, 184, :_reduce_290,
  6, 184, :_reduce_291,
  0, 219, :_reduce_292,
  0, 220, :_reduce_293,
  7, 184, :_reduce_294,
  0, 221, :_reduce_295,
  0, 222, :_reduce_296,
  7, 184, :_reduce_297,
  5, 184, :_reduce_298,
  4, 184, :_reduce_299,
  5, 184, :_reduce_300,
  0, 223, :_reduce_301,
  0, 224, :_reduce_302,
  9, 184, :_reduce_303,
  0, 225, :_reduce_304,
  6, 184, :_reduce_305,
  0, 226, :_reduce_306,
  7, 184, :_reduce_307,
  0, 227, :_reduce_308,
  5, 184, :_reduce_309,
  0, 228, :_reduce_310,
  6, 184, :_reduce_311,
  0, 229, :_reduce_312,
  0, 230, :_reduce_313,
  9, 184, :_reduce_314,
  1, 184, :_reduce_315,
  1, 184, :_reduce_316,
  1, 184, :_reduce_317,
  1, 184, :_reduce_318,
  1, 150, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  1, 208, :_reduce_none,
  2, 208, :_reduce_323,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 210, :_reduce_none,
  1, 209, :_reduce_none,
  5, 209, :_reduce_328,
  1, 137, :_reduce_none,
  2, 137, :_reduce_330,
  1, 212, :_reduce_none,
  1, 212, :_reduce_none,
  1, 231, :_reduce_333,
  3, 231, :_reduce_334,
  1, 232, :_reduce_none,
  2, 232, :_reduce_none,
  4, 232, :_reduce_337,
  7, 232, :_reduce_338,
  6, 232, :_reduce_339,
  4, 232, :_reduce_340,
  3, 232, :_reduce_341,
  5, 232, :_reduce_342,
  4, 232, :_reduce_343,
  2, 232, :_reduce_344,
  1, 232, :_reduce_345,
  2, 232, :_reduce_346,
  0, 165, :_reduce_347,
  2, 165, :_reduce_348,
  1, 165, :_reduce_349,
  3, 165, :_reduce_350,
  0, 234, :_reduce_351,
  5, 233, :_reduce_352,
  2, 161, :_reduce_353,
  4, 161, :_reduce_354,
  4, 161, :_reduce_355,
  2, 207, :_reduce_356,
  4, 207, :_reduce_357,
  4, 207, :_reduce_358,
  3, 207, :_reduce_359,
  2, 207, :_reduce_360,
  1, 207, :_reduce_361,
  0, 236, :_reduce_362,
  5, 206, :_reduce_363,
  0, 237, :_reduce_364,
  5, 206, :_reduce_365,
  5, 211, :_reduce_366,
  1, 238, :_reduce_none,
  4, 238, :_reduce_368,
  2, 238, :_reduce_369,
  1, 239, :_reduce_370,
  1, 239, :_reduce_none,
  6, 136, :_reduce_372,
  0, 136, :_reduce_373,
  1, 240, :_reduce_374,
  1, 240, :_reduce_none,
  1, 240, :_reduce_none,
  2, 241, :_reduce_377,
  1, 241, :_reduce_none,
  2, 138, :_reduce_379,
  1, 138, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 198, :_reduce_none,
  1, 199, :_reduce_384,
  1, 243, :_reduce_385,
  2, 243, :_reduce_386,
  3, 244, :_reduce_387,
  1, 244, :_reduce_388,
  3, 200, :_reduce_389,
  4, 201, :_reduce_390,
  3, 202, :_reduce_391,
  0, 247, :_reduce_392,
  3, 247, :_reduce_393,
  1, 248, :_reduce_394,
  2, 248, :_reduce_395,
  3, 203, :_reduce_396,
  0, 250, :_reduce_397,
  3, 250, :_reduce_398,
  0, 245, :_reduce_399,
  2, 245, :_reduce_400,
  0, 246, :_reduce_401,
  2, 246, :_reduce_402,
  1, 249, :_reduce_403,
  2, 249, :_reduce_404,
  0, 252, :_reduce_405,
  4, 249, :_reduce_406,
  1, 251, :_reduce_407,
  1, 251, :_reduce_408,
  1, 251, :_reduce_409,
  1, 251, :_reduce_none,
  1, 180, :_reduce_411,
  3, 181, :_reduce_412,
  1, 242, :_reduce_413,
  1, 242, :_reduce_414,
  2, 242, :_reduce_415,
  2, 242, :_reduce_416,
  1, 173, :_reduce_417,
  1, 173, :_reduce_418,
  1, 173, :_reduce_419,
  1, 173, :_reduce_420,
  1, 173, :_reduce_421,
  1, 173, :_reduce_422,
  1, 173, :_reduce_423,
  1, 173, :_reduce_424,
  1, 173, :_reduce_425,
  1, 173, :_reduce_426,
  1, 173, :_reduce_427,
  1, 204, :_reduce_428,
  1, 149, :_reduce_429,
  1, 152, :_reduce_430,
  1, 152, :_reduce_431,
  1, 213, :_reduce_432,
  3, 213, :_reduce_433,
  2, 213, :_reduce_434,
  4, 215, :_reduce_435,
  2, 215, :_reduce_436,
  6, 253, :_reduce_437,
  4, 253, :_reduce_438,
  4, 253, :_reduce_439,
  2, 253, :_reduce_440,
  4, 253, :_reduce_441,
  2, 253, :_reduce_442,
  2, 253, :_reduce_443,
  1, 253, :_reduce_444,
  0, 253, :_reduce_445,
  1, 259, :_reduce_446,
  1, 259, :_reduce_447,
  1, 259, :_reduce_448,
  1, 259, :_reduce_449,
  1, 259, :_reduce_450,
  1, 254, :_reduce_451,
  3, 254, :_reduce_452,
  3, 260, :_reduce_453,
  1, 255, :_reduce_454,
  3, 255, :_reduce_455,
  1, 261, :_reduce_none,
  1, 261, :_reduce_none,
  2, 256, :_reduce_458,
  1, 256, :_reduce_459,
  1, 262, :_reduce_none,
  1, 262, :_reduce_none,
  2, 258, :_reduce_462,
  2, 257, :_reduce_463,
  0, 257, :_reduce_464,
  1, 216, :_reduce_none,
  4, 216, :_reduce_466,
  0, 205, :_reduce_467,
  2, 205, :_reduce_468,
  2, 205, :_reduce_469,
  1, 188, :_reduce_470,
  3, 188, :_reduce_471,
  3, 263, :_reduce_472,
  1, 167, :_reduce_none,
  1, 167, :_reduce_none,
  1, 167, :_reduce_none,
  1, 162, :_reduce_none,
  1, 162, :_reduce_none,
  1, 162, :_reduce_none,
  1, 162, :_reduce_none,
  1, 235, :_reduce_none,
  1, 235, :_reduce_none,
  1, 235, :_reduce_none,
  1, 217, :_reduce_none,
  1, 217, :_reduce_none,
  0, 140, :_reduce_none,
  1, 140, :_reduce_none,
  0, 183, :_reduce_none,
  1, 183, :_reduce_none,
  0, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 187, :_reduce_none,
  1, 214, :_reduce_492,
  1, 214, :_reduce_none,
  1, 142, :_reduce_none,
  2, 142, :_reduce_none,
  0, 185, :_reduce_496 ]

racc_reduce_n = 497

racc_shift_n = 894

racc_token_table = {
  false => 0,
  :error => 1,
  :kCLASS => 2,
  :kMODULE => 3,
  :kDEF => 4,
  :kUNDEF => 5,
  :kBEGIN => 6,
  :kRESCUE => 7,
  :kENSURE => 8,
  :kEND => 9,
  :kIF => 10,
  :kUNLESS => 11,
  :kTHEN => 12,
  :kELSIF => 13,
  :kELSE => 14,
  :kCASE => 15,
  :kWHEN => 16,
  :kWHILE => 17,
  :kUNTIL => 18,
  :kFOR => 19,
  :kBREAK => 20,
  :kNEXT => 21,
  :kREDO => 22,
  :kRETRY => 23,
  :kIN => 24,
  :kDO => 25,
  :kDO_COND => 26,
  :kDO_BLOCK => 27,
  :kRETURN => 28,
  :kYIELD => 29,
  :kSUPER => 30,
  :kSELF => 31,
  :kNIL => 32,
  :kTRUE => 33,
  :kFALSE => 34,
  :kAND => 35,
  :kOR => 36,
  :kNOT => 37,
  :kIF_MOD => 38,
  :kUNLESS_MOD => 39,
  :kWHILE_MOD => 40,
  :kUNTIL_MOD => 41,
  :kRESCUE_MOD => 42,
  :kALIAS => 43,
  :kDEFINED => 44,
  :klBEGIN => 45,
  :klEND => 46,
  :k__LINE__ => 47,
  :k__FILE__ => 48,
  :tIDENTIFIER => 49,
  :tFID => 50,
  :tGVAR => 51,
  :tIVAR => 52,
  :tCONSTANT => 53,
  :tCVAR => 54,
  :tNTH_REF => 55,
  :tBACK_REF => 56,
  :tSTRING_CONTENT => 57,
  :tINTEGER => 58,
  :tFLOAT => 59,
  :tREGEXP_END => 60,
  :tUPLUS => 61,
  :tUMINUS => 62,
  :tUMINUS_NUM => 63,
  :tPOW => 64,
  :tCMP => 65,
  :tEQ => 66,
  :tEQQ => 67,
  :tNEQ => 68,
  :tGEQ => 69,
  :tLEQ => 70,
  :tANDOP => 71,
  :tOROP => 72,
  :tMATCH => 73,
  :tNMATCH => 74,
  :tDOT => 75,
  :tDOT2 => 76,
  :tDOT3 => 77,
  :tAREF => 78,
  :tASET => 79,
  :tLSHFT => 80,
  :tRSHFT => 81,
  :tCOLON2 => 82,
  :tCOLON3 => 83,
  :tOP_ASGN => 84,
  :tASSOC => 85,
  :tLPAREN => 86,
  :tLPAREN2 => 87,
  :tRPAREN => 88,
  :tLPAREN_ARG => 89,
  :tLBRACK => 90,
  :tLBRACK2 => 91,
  :tRBRACK => 92,
  :tLBRACE => 93,
  :tLBRACE_ARG => 94,
  :tSTAR => 95,
  :tSTAR2 => 96,
  :tAMPER => 97,
  :tAMPER2 => 98,
  :tTILDE => 99,
  :tPERCENT => 100,
  :tDIVIDE => 101,
  :tPLUS => 102,
  :tMINUS => 103,
  :tLT => 104,
  :tGT => 105,
  :tPIPE => 106,
  :tBANG => 107,
  :tCARET => 108,
  :tLCURLY => 109,
  :tRCURLY => 110,
  :tBACK_REF2 => 111,
  :tSYMBEG => 112,
  :tSTRING_BEG => 113,
  :tXSTRING_BEG => 114,
  :tREGEXP_BEG => 115,
  :tWORDS_BEG => 116,
  :tQWORDS_BEG => 117,
  :tSTRING_DBEG => 118,
  :tSTRING_DVAR => 119,
  :tSTRING_END => 120,
  :tSTRING => 121,
  :tSYMBOL => 122,
  :tREGEXP_OPT => 123,
  :tNL => 124,
  :tEH => 125,
  :tCOLON => 126,
  :tCOMMA => 127,
  :tSPACE => 128,
  :tSEMI => 129,
  :tEQL => 130,
  :tLOWEST => 131 }

racc_nt_base = 132

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "kCLASS",
  "kMODULE",
  "kDEF",
  "kUNDEF",
  "kBEGIN",
  "kRESCUE",
  "kENSURE",
  "kEND",
  "kIF",
  "kUNLESS",
  "kTHEN",
  "kELSIF",
  "kELSE",
  "kCASE",
  "kWHEN",
  "kWHILE",
  "kUNTIL",
  "kFOR",
  "kBREAK",
  "kNEXT",
  "kREDO",
  "kRETRY",
  "kIN",
  "kDO",
  "kDO_COND",
  "kDO_BLOCK",
  "kRETURN",
  "kYIELD",
  "kSUPER",
  "kSELF",
  "kNIL",
  "kTRUE",
  "kFALSE",
  "kAND",
  "kOR",
  "kNOT",
  "kIF_MOD",
  "kUNLESS_MOD",
  "kWHILE_MOD",
  "kUNTIL_MOD",
  "kRESCUE_MOD",
  "kALIAS",
  "kDEFINED",
  "klBEGIN",
  "klEND",
  "k__LINE__",
  "k__FILE__",
  "tIDENTIFIER",
  "tFID",
  "tGVAR",
  "tIVAR",
  "tCONSTANT",
  "tCVAR",
  "tNTH_REF",
  "tBACK_REF",
  "tSTRING_CONTENT",
  "tINTEGER",
  "tFLOAT",
  "tREGEXP_END",
  "tUPLUS",
  "tUMINUS",
  "tUMINUS_NUM",
  "tPOW",
  "tCMP",
  "tEQ",
  "tEQQ",
  "tNEQ",
  "tGEQ",
  "tLEQ",
  "tANDOP",
  "tOROP",
  "tMATCH",
  "tNMATCH",
  "tDOT",
  "tDOT2",
  "tDOT3",
  "tAREF",
  "tASET",
  "tLSHFT",
  "tRSHFT",
  "tCOLON2",
  "tCOLON3",
  "tOP_ASGN",
  "tASSOC",
  "tLPAREN",
  "tLPAREN2",
  "tRPAREN",
  "tLPAREN_ARG",
  "tLBRACK",
  "tLBRACK2",
  "tRBRACK",
  "tLBRACE",
  "tLBRACE_ARG",
  "tSTAR",
  "tSTAR2",
  "tAMPER",
  "tAMPER2",
  "tTILDE",
  "tPERCENT",
  "tDIVIDE",
  "tPLUS",
  "tMINUS",
  "tLT",
  "tGT",
  "tPIPE",
  "tBANG",
  "tCARET",
  "tLCURLY",
  "tRCURLY",
  "tBACK_REF2",
  "tSYMBEG",
  "tSTRING_BEG",
  "tXSTRING_BEG",
  "tREGEXP_BEG",
  "tWORDS_BEG",
  "tQWORDS_BEG",
  "tSTRING_DBEG",
  "tSTRING_DVAR",
  "tSTRING_END",
  "tSTRING",
  "tSYMBOL",
  "tREGEXP_OPT",
  "tNL",
  "tEH",
  "tCOLON",
  "tCOMMA",
  "tSPACE",
  "tSEMI",
  "tEQL",
  "tLOWEST",
  "$start",
  "program",
  "compstmt",
  "bodystmt",
  "opt_rescue",
  "opt_else",
  "opt_ensure",
  "stmts",
  "opt_terms",
  "stmt",
  "terms",
  "fitem",
  "undef_list",
  "expr_value",
  "lhs",
  "command_call",
  "mlhs",
  "var_lhs",
  "primary_value",
  "aref_args",
  "backref",
  "mrhs",
  "arg_value",
  "expr",
  "@1",
  "arg",
  "command",
  "block_command",
  "call_args",
  "block_call",
  "operation2",
  "command_args",
  "cmd_brace_block",
  "opt_block_var",
  "@2",
  "operation",
  "mlhs_basic",
  "mlhs_entry",
  "mlhs_head",
  "mlhs_item",
  "mlhs_node",
  "variable",
  "cname",
  "cpath",
  "fname",
  "op",
  "reswords",
  "fsym",
  "symbol",
  "dsym",
  "@3",
  "opt_nl",
  "primary",
  "none",
  "args",
  "trailer",
  "assocs",
  "paren_args",
  "opt_paren_args",
  "opt_block_arg",
  "block_arg",
  "call_args2",
  "open_args",
  "@4",
  "@5",
  "@6",
  "literal",
  "strings",
  "xstring",
  "regexp",
  "words",
  "qwords",
  "var_ref",
  "assoc_list",
  "brace_block",
  "method_call",
  "then",
  "if_tail",
  "do",
  "case_body",
  "for_var",
  "superclass",
  "term",
  "f_arglist",
  "singleton",
  "dot_or_colon",
  "@7",
  "@8",
  "@9",
  "@10",
  "@11",
  "@12",
  "@13",
  "@14",
  "@15",
  "@16",
  "@17",
  "@18",
  "@19",
  "block_par",
  "block_var",
  "do_block",
  "@20",
  "operation3",
  "@21",
  "@22",
  "when_args",
  "cases",
  "exc_list",
  "exc_var",
  "numeric",
  "string",
  "string1",
  "string_contents",
  "xstring_contents",
  "word_list",
  "word",
  "string_content",
  "qword_list",
  "string_dvar",
  "@23",
  "f_args",
  "f_arg",
  "f_optarg",
  "f_rest_arg",
  "opt_f_block_arg",
  "f_block_arg",
  "f_norm_arg",
  "f_opt",
  "restarg_mark",
  "blkarg_mark",
  "assoc" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'ruby18.y', 73)
  def _reduce_1(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 78)
  def _reduce_2(val, _values, result)
                          rescue_bodies     = val[1]
                      else_t,   else_   = val[2]
                      ensure_t, ensure_ = val[3]

                      if rescue_bodies.empty? && !else_.nil?
                        diagnostic :warning, :useless_else, nil, else_t
                      end

                      result = @builder.begin_body(val[0],
                                  rescue_bodies,
                                  else_t,   else_,
                                  ensure_t, ensure_)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 94)
  def _reduce_3(val, _values, result)
                          result = @builder.compstmt(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 99)
  def _reduce_4(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 103)
  def _reduce_5(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 107)
  def _reduce_6(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 111)
  def _reduce_7(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 116)
  def _reduce_8(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 120)
  def _reduce_9(val, _values, result)
                          result = @builder.alias(val[0], val[1], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 124)
  def _reduce_10(val, _values, result)
                          result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.gvar(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 130)
  def _reduce_11(val, _values, result)
                          result = @builder.alias(val[0],
                                  @builder.gvar(val[1]),
                                  @builder.back_ref(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 136)
  def _reduce_12(val, _values, result)
                          diagnostic :error, :nth_ref_alias, nil, val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 140)
  def _reduce_13(val, _values, result)
                          result = @builder.undef_method(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 144)
  def _reduce_14(val, _values, result)
                          result = @builder.condition_mod(val[0], nil,
                                                      val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 149)
  def _reduce_15(val, _values, result)
                          result = @builder.condition_mod(nil, val[0],
                                                      val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 154)
  def _reduce_16(val, _values, result)
                          result = @builder.loop_mod(:while, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 158)
  def _reduce_17(val, _values, result)
                          result = @builder.loop_mod(:until, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 162)
  def _reduce_18(val, _values, result)
                          rescue_body = @builder.rescue_body(val[1],
                                        nil, nil, nil,
                                        nil, val[2])

                      result = @builder.begin_body(val[0], [ rescue_body ])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 170)
  def _reduce_19(val, _values, result)
                          if in_def?
                        diagnostic :error, :begin_in_method, nil, val[0]
                      end

                      result = @builder.preexe(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 178)
  def _reduce_20(val, _values, result)
                          result = @builder.postexe(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 182)
  def _reduce_21(val, _values, result)
                          result = @builder.assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 186)
  def _reduce_22(val, _values, result)
                          result = @builder.multi_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 190)
  def _reduce_23(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 194)
  def _reduce_24(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 201)
  def _reduce_25(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 208)
  def _reduce_26(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 215)
  def _reduce_27(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 222)
  def _reduce_28(val, _values, result)
                          @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 226)
  def _reduce_29(val, _values, result)
                          result = @builder.assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 231)
  def _reduce_30(val, _values, result)
                          result = @builder.multi_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 235)
  def _reduce_31(val, _values, result)
                          result = @builder.multi_assign(val[0], val[1],
                                  @builder.array(nil, val[2], nil))

    result
  end
.,.,

# reduce 32 omitted

# reduce 33 omitted

module_eval(<<'.,.,', 'ruby18.y', 243)
  def _reduce_34(val, _values, result)
                          result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 247)
  def _reduce_35(val, _values, result)
                          result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 251)
  def _reduce_36(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 255)
  def _reduce_37(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[1], nil)

    result
  end
.,.,

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

module_eval(<<'.,.,', 'ruby18.y', 265)
  def _reduce_42(val, _values, result)
                          result = @builder.keyword_cmd(:return, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 270)
  def _reduce_43(val, _values, result)
                          result = @builder.keyword_cmd(:break, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 275)
  def _reduce_44(val, _values, result)
                          result = @builder.keyword_cmd(:next, val[0],
                                  nil, val[1], nil)

    result
  end
.,.,

# reduce 45 omitted

module_eval(<<'.,.,', 'ruby18.y', 282)
  def _reduce_46(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 288)
  def _reduce_47(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 295)
  def _reduce_48(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 299)
  def _reduce_49(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 306)
  def _reduce_50(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 312)
  def _reduce_51(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      method_call = @builder.call_method(nil, nil, val[0],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[2]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 322)
  def _reduce_52(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)


    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 329)
  def _reduce_53(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 339)
  def _reduce_54(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 345)
  def _reduce_55(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      method_call = @builder.call_method(val[0], val[1], val[2],
                                      lparen_t, args, rparen_t)

                      begin_t, block_args, body, end_t = val[4]
                      result      = @builder.block(method_call,
                                      begin_t, block_args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 355)
  def _reduce_56(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 361)
  def _reduce_57(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:yield, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 368)
  def _reduce_58(val, _values, result)
                          result = @builder.multi_lhs(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 372)
  def _reduce_59(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 377)
  def _reduce_60(val, _values, result)
                          result = @builder.multi_lhs(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 381)
  def _reduce_61(val, _values, result)
                          result = @builder.multi_lhs(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 386)
  def _reduce_62(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 390)
  def _reduce_63(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 394)
  def _reduce_64(val, _values, result)
                          result = val[0] << @builder.splat(val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 398)
  def _reduce_65(val, _values, result)
                          result = val[0] << @builder.splat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 402)
  def _reduce_66(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 406)
  def _reduce_67(val, _values, result)
                          result = [ @builder.splat(val[0]) ]

    result
  end
.,.,

# reduce 68 omitted

module_eval(<<'.,.,', 'ruby18.y', 412)
  def _reduce_69(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 417)
  def _reduce_70(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 421)
  def _reduce_71(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 426)
  def _reduce_72(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 430)
  def _reduce_73(val, _values, result)
                          result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 434)
  def _reduce_74(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 438)
  def _reduce_75(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 442)
  def _reduce_76(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 446)
  def _reduce_77(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 451)
  def _reduce_78(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 456)
  def _reduce_79(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 461)
  def _reduce_80(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 465)
  def _reduce_81(val, _values, result)
                          result = @builder.index_asgn(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 469)
  def _reduce_82(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 473)
  def _reduce_83(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 477)
  def _reduce_84(val, _values, result)
                          result = @builder.attr_asgn(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 481)
  def _reduce_85(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_fetch(val[0], val[1], val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 486)
  def _reduce_86(val, _values, result)
                          result = @builder.assignable(
                                  @builder.const_global(val[0], val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 491)
  def _reduce_87(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 496)
  def _reduce_88(val, _values, result)
                          diagnostic :error, :module_name_const, nil, val[0]

    result
  end
.,.,

# reduce 89 omitted

module_eval(<<'.,.,', 'ruby18.y', 502)
  def _reduce_90(val, _values, result)
                          result = @builder.const_global(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 506)
  def _reduce_91(val, _values, result)
                          result = @builder.const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 510)
  def _reduce_92(val, _values, result)
                          result = @builder.const_fetch(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 93 omitted

# reduce 94 omitted

# reduce 95 omitted

# reduce 96 omitted

# reduce 97 omitted

module_eval(<<'.,.,', 'ruby18.y', 519)
  def _reduce_98(val, _values, result)
                          result = @builder.symbol(val[0])

    result
  end
.,.,

# reduce 99 omitted

# reduce 100 omitted

# reduce 101 omitted

module_eval(<<'.,.,', 'ruby18.y', 528)
  def _reduce_102(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 532)
  def _reduce_103(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 536)
  def _reduce_104(val, _values, result)
                          result = val[0] << val[3]

    result
  end
.,.,

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

# reduce 108 omitted

# reduce 109 omitted

# reduce 110 omitted

# reduce 111 omitted

# reduce 112 omitted

# reduce 113 omitted

# reduce 114 omitted

# reduce 115 omitted

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

# reduce 139 omitted

# reduce 140 omitted

# reduce 141 omitted

# reduce 142 omitted

# reduce 143 omitted

# reduce 144 omitted

# reduce 145 omitted

# reduce 146 omitted

# reduce 147 omitted

# reduce 148 omitted

# reduce 149 omitted

# reduce 150 omitted

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

# reduce 154 omitted

# reduce 155 omitted

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

# reduce 167 omitted

# reduce 168 omitted

# reduce 169 omitted

# reduce 170 omitted

module_eval(<<'.,.,', 'ruby18.y', 555)
  def _reduce_171(val, _values, result)
                          result = @builder.assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 559)
  def _reduce_172(val, _values, result)
                          rescue_body = @builder.rescue_body(val[3],
                                        nil, nil, nil,
                                        nil, val[4])

                      rescue_ = @builder.begin_body(val[2], [ rescue_body ])

                      result  = @builder.assign(val[0], val[1], rescue_)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 569)
  def _reduce_173(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 573)
  def _reduce_174(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.index(
                                    val[0], val[1], val[2], val[3]),
                                  val[4], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 580)
  def _reduce_175(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 587)
  def _reduce_176(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 594)
  def _reduce_177(val, _values, result)
                          result = @builder.op_assign(
                                  @builder.call_method(
                                    val[0], val[1], val[2]),
                                  val[3], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 601)
  def _reduce_178(val, _values, result)
                          diagnostic :error, :dynamic_const, nil, val[2], [ val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 605)
  def _reduce_179(val, _values, result)
                          diagnostic :error, :dynamic_const, nil, val[1], [ val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 609)
  def _reduce_180(val, _values, result)
                          result = @builder.op_assign(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 613)
  def _reduce_181(val, _values, result)
                          result = @builder.range_inclusive(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 617)
  def _reduce_182(val, _values, result)
                          result = @builder.range_exclusive(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 621)
  def _reduce_183(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 625)
  def _reduce_184(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 629)
  def _reduce_185(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 633)
  def _reduce_186(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 637)
  def _reduce_187(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 641)
  def _reduce_188(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 645)
  def _reduce_189(val, _values, result)
                          result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.integer(val[1]),
                                      val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 652)
  def _reduce_190(val, _values, result)
                          result = @builder.unary_op(val[0],
                                  @builder.binary_op(
                                    @builder.float(val[1]),
                                      val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 659)
  def _reduce_191(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 663)
  def _reduce_192(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 667)
  def _reduce_193(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 671)
  def _reduce_194(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 675)
  def _reduce_195(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 679)
  def _reduce_196(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 683)
  def _reduce_197(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 687)
  def _reduce_198(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 691)
  def _reduce_199(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 695)
  def _reduce_200(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 699)
  def _reduce_201(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 703)
  def _reduce_202(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 707)
  def _reduce_203(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 711)
  def _reduce_204(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 715)
  def _reduce_205(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 719)
  def _reduce_206(val, _values, result)
                          result = @builder.not_op(val[0], nil, val[1], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 723)
  def _reduce_207(val, _values, result)
                          result = @builder.unary_op(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 727)
  def _reduce_208(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 731)
  def _reduce_209(val, _values, result)
                          result = @builder.binary_op(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 735)
  def _reduce_210(val, _values, result)
                          result = @builder.logical_op(:and, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 739)
  def _reduce_211(val, _values, result)
                          result = @builder.logical_op(:or, val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 743)
  def _reduce_212(val, _values, result)
                          result = @builder.keyword_cmd(:defined?, val[0], nil, [ val[2] ], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 747)
  def _reduce_213(val, _values, result)
                          result = @builder.ternary(val[0], val[1],
                                                val[2], val[3], val[4])

    result
  end
.,.,

# reduce 214 omitted

# reduce 215 omitted

# reduce 216 omitted

module_eval(<<'.,.,', 'ruby18.y', 757)
  def _reduce_217(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 761)
  def _reduce_218(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 765)
  def _reduce_219(val, _values, result)
                          result = val[0] << @builder.splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 769)
  def _reduce_220(val, _values, result)
                          result = [ @builder.associate(nil, val[0], nil) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 773)
  def _reduce_221(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 778)
  def _reduce_222(val, _values, result)
                          result = [ val[0], [], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 782)
  def _reduce_223(val, _values, result)
                          result = [ val[0], val[1], val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 786)
  def _reduce_224(val, _values, result)
                          result = [ val[0], [ val[1] ], val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 790)
  def _reduce_225(val, _values, result)
                          result = [ val[0], val[1] << val[3], val[5] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 795)
  def _reduce_226(val, _values, result)
                          result = [ nil, [], nil ]

    result
  end
.,.,

# reduce 227 omitted

module_eval(<<'.,.,', 'ruby18.y', 801)
  def _reduce_228(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 805)
  def _reduce_229(val, _values, result)
                          result = val[0].concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 809)
  def _reduce_230(val, _values, result)
                          result = val[0].concat(
                                [ @builder.splat(val[2], val[3]),
                                   *val[4] ])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 815)
  def _reduce_231(val, _values, result)
                          result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 820)
  def _reduce_232(val, _values, result)
                          result =  [ @builder.associate(nil, val[0], nil),
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 826)
  def _reduce_233(val, _values, result)
                          result = val[0].concat(
                                [ @builder.associate(nil, val[2], nil),
                                  *val[3] ])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 832)
  def _reduce_234(val, _values, result)
                          result = val[0].concat(
                                [ @builder.associate(nil, val[2], nil),
                                  @builder.splat(val[4], val[5]),
                                  *val[6] ])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 839)
  def _reduce_235(val, _values, result)
                          result =  [ @builder.splat(val[0], val[1]),
                                  *val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 844)
  def _reduce_236(val, _values, result)
                          result =  [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 849)
  def _reduce_237(val, _values, result)
                          result = [ val[0], *val[2].concat(val[3]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 853)
  def _reduce_238(val, _values, result)
                          result = [ val[0], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 857)
  def _reduce_239(val, _values, result)
                          result =  [ val[0],
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 863)
  def _reduce_240(val, _values, result)
                          result =  [ val[0],
                                  *val[2].
                                    push(@builder.splat(val[4], val[5])).
                                    concat(val[6]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 870)
  def _reduce_241(val, _values, result)
                          result =  [ @builder.associate(nil, val[0], nil),
                                  *val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 875)
  def _reduce_242(val, _values, result)
                          result =  [ @builder.associate(nil, val[0], nil),
                                  @builder.splat(val[2], val[3]),
                                  *val[4] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 881)
  def _reduce_243(val, _values, result)
                          result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  *val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 887)
  def _reduce_244(val, _values, result)
                          result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    concat(val[5]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 894)
  def _reduce_245(val, _values, result)
                          result =  [ val[0],
                                  @builder.associate(nil, val[2], nil),
                                  @builder.splat(val[4], val[5]),
                                  *val[6] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 901)
  def _reduce_246(val, _values, result)
                          result =  [ val[0],
                                  *val[2].
                                    push(@builder.associate(nil, val[4], nil)).
                                    push(@builder.splat(val[6], val[7])).
                                    concat(val[8]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 909)
  def _reduce_247(val, _values, result)
                          result =  [ @builder.splat(val[0], val[1]),
                                  *val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 914)
  def _reduce_248(val, _values, result)
                          result =  [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 918)
  def _reduce_249(val, _values, result)
                          result = @lexer.cmdarg.dup
                      @lexer.cmdarg.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 923)
  def _reduce_250(val, _values, result)
                          @lexer.cmdarg = val[0]

                      result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 930)
  def _reduce_251(val, _values, result)
                          result = [ nil, val[0], nil ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 934)
  def _reduce_252(val, _values, result)
                          @lexer.state = :expr_endarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 938)
  def _reduce_253(val, _values, result)
                          result = [ val[0], [], val[2] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 942)
  def _reduce_254(val, _values, result)
                          @lexer.state = :expr_endarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 946)
  def _reduce_255(val, _values, result)
                          result = [ val[0], val[1], val[3] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 951)
  def _reduce_256(val, _values, result)
                          result = @builder.block_pass(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 956)
  def _reduce_257(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 960)
  def _reduce_258(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 965)
  def _reduce_259(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 969)
  def _reduce_260(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 974)
  def _reduce_261(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 978)
  def _reduce_262(val, _values, result)
                          result = val[0] << @builder.splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 982)
  def _reduce_263(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

# reduce 264 omitted

# reduce 265 omitted

# reduce 266 omitted

# reduce 267 omitted

# reduce 268 omitted

# reduce 269 omitted

# reduce 270 omitted

# reduce 271 omitted

module_eval(<<'.,.,', 'ruby18.y', 995)
  def _reduce_272(val, _values, result)
                          result = @builder.call_method(nil, nil, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 999)
  def _reduce_273(val, _values, result)
                          result = @builder.begin_keyword(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1003)
  def _reduce_274(val, _values, result)
                          @lexer.state = :expr_endarg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1007)
  def _reduce_275(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1011)
  def _reduce_276(val, _values, result)
                          result = @builder.begin(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1015)
  def _reduce_277(val, _values, result)
                          result = @builder.const_fetch(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1019)
  def _reduce_278(val, _values, result)
                          result = @builder.const_global(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1023)
  def _reduce_279(val, _values, result)
                          result = @builder.index(val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1027)
  def _reduce_280(val, _values, result)
                          result = @builder.array(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1031)
  def _reduce_281(val, _values, result)
                          result = @builder.associate(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1035)
  def _reduce_282(val, _values, result)
                          result = @builder.keyword_cmd(:return, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1039)
  def _reduce_283(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0], val[1], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1043)
  def _reduce_284(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0], val[1], [], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1047)
  def _reduce_285(val, _values, result)
                          result = @builder.keyword_cmd(:yield, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1051)
  def _reduce_286(val, _values, result)
                          result = @builder.keyword_cmd(:defined?, val[0],
                                                    val[2], [ val[3] ], val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1056)
  def _reduce_287(val, _values, result)
                          method_call = @builder.call_method(nil, nil, val[0])

                      begin_t, args, body, end_t = val[1]
                      result      = @builder.block(method_call,
                                      begin_t, args, body, end_t)

    result
  end
.,.,

# reduce 288 omitted

module_eval(<<'.,.,', 'ruby18.y', 1065)
  def _reduce_289(val, _values, result)
                          begin_t, args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1071)
  def _reduce_290(val, _values, result)
                          else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  val[3], else_t,
                                                  else_,  val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1078)
  def _reduce_291(val, _values, result)
                          else_t, else_ = val[4]
                      result = @builder.condition(val[0], val[1], val[2],
                                                  else_,  else_t,
                                                  val[3], val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1085)
  def _reduce_292(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1089)
  def _reduce_293(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1093)
  def _reduce_294(val, _values, result)
                          result = @builder.loop(:while, val[0], val[2], val[3],
                                             val[5], val[6])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1098)
  def _reduce_295(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1102)
  def _reduce_296(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1106)
  def _reduce_297(val, _values, result)
                          result = @builder.loop(:until, val[0], val[2], val[3],
                                             val[5], val[6])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1111)
  def _reduce_298(val, _values, result)
                          when_bodies       = val[3][0..-2]
                      else_t, else_body = val[3][-1]

                      result = @builder.case(val[0], val[1],
                                             when_bodies, else_t, else_body,
                                             val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1120)
  def _reduce_299(val, _values, result)
                          when_bodies       = val[2][0..-2]
                      else_t, else_body = val[2][-1]

                      result = @builder.case(val[0], nil,
                                             when_bodies, else_t, else_body,
                                             val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1129)
  def _reduce_300(val, _values, result)
                          result = @builder.case(val[0], nil,
                                             [], val[2], val[3],
                                             val[4])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1135)
  def _reduce_301(val, _values, result)
                          @lexer.cond.push(true)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1139)
  def _reduce_302(val, _values, result)
                          @lexer.cond.pop

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1143)
  def _reduce_303(val, _values, result)
                          result = @builder.for(val[0], val[1],
                                            val[2], val[4],
                                            val[5], val[7], val[8])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1149)
  def _reduce_304(val, _values, result)
                          @static_env.extend_static

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1153)
  def _reduce_305(val, _values, result)
                          if in_def?
                        diagnostic :error, :class_in_def, nil, val[0]
                      end

                      lt_t, superclass = val[2]
                      result = @builder.def_class(val[0], val[1],
                                                  lt_t, superclass,
                                                  val[4], val[5])

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1166)
  def _reduce_306(val, _values, result)
                          result = @def_level
                      @def_level = 0

                      @static_env.extend_static

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1173)
  def _reduce_307(val, _values, result)
                          result = @builder.def_sclass(val[0], val[1], val[2],
                                                   val[5], val[6])

                      @static_env.unextend

                      @def_level = val[4]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1182)
  def _reduce_308(val, _values, result)
                          @static_env.extend_static

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1186)
  def _reduce_309(val, _values, result)
                          if in_def?
                        diagnostic :error, :module_in_def, nil, val[0]
                      end

                      result = @builder.def_module(val[0], val[1],
                                                   val[3], val[4])

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1197)
  def _reduce_310(val, _values, result)
                          @def_level += 1
                      @static_env.extend_static

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1202)
  def _reduce_311(val, _values, result)
                          result = @builder.def_method(val[0], val[1],
                                  val[3], val[4], val[5])

                      @static_env.unextend
                      @def_level -= 1

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1210)
  def _reduce_312(val, _values, result)
                          @lexer.state = :expr_fname

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1214)
  def _reduce_313(val, _values, result)
                          @def_level += 1
                      @static_env.extend_static

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1219)
  def _reduce_314(val, _values, result)
                          result = @builder.def_singleton(val[0], val[1], val[2],
                                  val[4], val[6], val[7], val[8])

                      @static_env.unextend
                      @def_level -= 1

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1227)
  def _reduce_315(val, _values, result)
                          result = @builder.keyword_cmd(:break, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1231)
  def _reduce_316(val, _values, result)
                          result = @builder.keyword_cmd(:next, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1235)
  def _reduce_317(val, _values, result)
                          result = @builder.keyword_cmd(:redo, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1239)
  def _reduce_318(val, _values, result)
                          result = @builder.keyword_cmd(:retry, val[0])

    result
  end
.,.,

# reduce 319 omitted

# reduce 320 omitted

# reduce 321 omitted

# reduce 322 omitted

module_eval(<<'.,.,', 'ruby18.y', 1249)
  def _reduce_323(val, _values, result)
                          result = val[1]

    result
  end
.,.,

# reduce 324 omitted

# reduce 325 omitted

# reduce 326 omitted

# reduce 327 omitted

module_eval(<<'.,.,', 'ruby18.y', 1259)
  def _reduce_328(val, _values, result)
                          else_t, else_ = val[4]
                      result = [ val[0],
                                 @builder.condition(val[0], val[1], val[2],
                                                    val[3], else_t,
                                                    else_,  nil),
                               ]

    result
  end
.,.,

# reduce 329 omitted

module_eval(<<'.,.,', 'ruby18.y', 1270)
  def _reduce_330(val, _values, result)
                          result = val

    result
  end
.,.,

# reduce 331 omitted

# reduce 332 omitted

module_eval(<<'.,.,', 'ruby18.y', 1278)
  def _reduce_333(val, _values, result)
                          result = [ @builder.arg_expr(val[0]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1282)
  def _reduce_334(val, _values, result)
                          result = val[0] << @builder.arg_expr(val[2])

    result
  end
.,.,

# reduce 335 omitted

# reduce 336 omitted

module_eval(<<'.,.,', 'ruby18.y', 1289)
  def _reduce_337(val, _values, result)
                          result =  val[0].
                                  push(@builder.blockarg_expr(val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1294)
  def _reduce_338(val, _values, result)
                          result =  val[0].
                                  push(@builder.restarg_expr(val[2], val[3])).
                                  push(@builder.blockarg_expr(val[5], val[6]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1300)
  def _reduce_339(val, _values, result)
                          result =  val[0].
                                  push(@builder.restarg_expr(val[2])).
                                  push(@builder.blockarg_expr(val[4], val[5]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1306)
  def _reduce_340(val, _values, result)
                          result =  val[0].
                                  push(@builder.restarg_expr(val[2], val[3]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1311)
  def _reduce_341(val, _values, result)
                          result =  val[0].
                                  push(@builder.restarg_expr(val[2]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1316)
  def _reduce_342(val, _values, result)
                          result =  [ @builder.restarg_expr(val[0], val[1]),
                                  @builder.blockarg_expr(val[3], val[4]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1321)
  def _reduce_343(val, _values, result)
                          result =  [ @builder.restarg_expr(val[0]),
                                  @builder.blockarg_expr(val[2], val[3]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1326)
  def _reduce_344(val, _values, result)
                          result =  [ @builder.restarg_expr(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1330)
  def _reduce_345(val, _values, result)
                          result =  [ @builder.restarg_expr(val[0]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1334)
  def _reduce_346(val, _values, result)
                          result =  [ @builder.blockarg_expr(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1340)
  def _reduce_347(val, _values, result)
                          result = @builder.args(nil, [], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1344)
  def _reduce_348(val, _values, result)
                          result = @builder.args(val[0], [], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1348)
  def _reduce_349(val, _values, result)
                          result = @builder.args(val[0], [], val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1352)
  def _reduce_350(val, _values, result)
                          result = @builder.args(val[0], val[1], val[2], false)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1357)
  def _reduce_351(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1361)
  def _reduce_352(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1368)
  def _reduce_353(val, _values, result)
                          begin_t, block_args, body, end_t = val[1]
                      result      = @builder.block(val[0],
                                      begin_t, block_args, body, end_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1374)
  def _reduce_354(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1380)
  def _reduce_355(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1387)
  def _reduce_356(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.call_method(nil, nil, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1393)
  def _reduce_357(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1399)
  def _reduce_358(val, _values, result)
                          lparen_t, args, rparen_t = val[3]
                      result = @builder.call_method(val[0], val[1], val[2],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1405)
  def _reduce_359(val, _values, result)
                          result = @builder.call_method(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1409)
  def _reduce_360(val, _values, result)
                          lparen_t, args, rparen_t = val[1]
                      result = @builder.keyword_cmd(:super, val[0],
                                  lparen_t, args, rparen_t)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1415)
  def _reduce_361(val, _values, result)
                          result = @builder.keyword_cmd(:zsuper, val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1420)
  def _reduce_362(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1424)
  def _reduce_363(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1430)
  def _reduce_364(val, _values, result)
                          @static_env.extend_dynamic

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1434)
  def _reduce_365(val, _values, result)
                          result = [ val[0], val[2], val[3], val[4] ]

                      @static_env.unextend

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1441)
  def _reduce_366(val, _values, result)
                          result = [ @builder.when(val[0], val[1], val[2], val[3]),
                                 *val[4] ]

    result
  end
.,.,

# reduce 367 omitted

module_eval(<<'.,.,', 'ruby18.y', 1448)
  def _reduce_368(val, _values, result)
                          result = val[0] << @builder.splat(val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1452)
  def _reduce_369(val, _values, result)
                          result = [ @builder.splat(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1457)
  def _reduce_370(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

# reduce 371 omitted

module_eval(<<'.,.,', 'ruby18.y', 1463)
  def _reduce_372(val, _values, result)
                          assoc_t, exc_var = val[2]

                      if val[1]
                        exc_list = @builder.array(nil, val[1], nil)
                      end

                      result = [ @builder.rescue_body(val[0],
                                      exc_list, assoc_t, exc_var,
                                      val[3], val[4]),
                                 *val[5] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1476)
  def _reduce_373(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1481)
  def _reduce_374(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

# reduce 375 omitted

# reduce 376 omitted

module_eval(<<'.,.,', 'ruby18.y', 1488)
  def _reduce_377(val, _values, result)
                          result = [ val[0], val[1] ]

    result
  end
.,.,

# reduce 378 omitted

module_eval(<<'.,.,', 'ruby18.y', 1494)
  def _reduce_379(val, _values, result)
                          result = [ val[0], val[1] ]

    result
  end
.,.,

# reduce 380 omitted

# reduce 381 omitted

# reduce 382 omitted

# reduce 383 omitted

module_eval(<<'.,.,', 'ruby18.y', 1504)
  def _reduce_384(val, _values, result)
                          result = @builder.string_compose(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1509)
  def _reduce_385(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1513)
  def _reduce_386(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1518)
  def _reduce_387(val, _values, result)
                          result = @builder.string_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1522)
  def _reduce_388(val, _values, result)
                          result = @builder.string(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1527)
  def _reduce_389(val, _values, result)
                          result = @builder.xstring_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1532)
  def _reduce_390(val, _values, result)
                          opts   = @builder.regexp_options(val[3])
                      result = @builder.regexp_compose(val[0], val[1], val[2], opts)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1538)
  def _reduce_391(val, _values, result)
                          result = @builder.words_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1543)
  def _reduce_392(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1547)
  def _reduce_393(val, _values, result)
                          result = val[0] << @builder.word(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1552)
  def _reduce_394(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1556)
  def _reduce_395(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1561)
  def _reduce_396(val, _values, result)
                          result = @builder.words_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1566)
  def _reduce_397(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1570)
  def _reduce_398(val, _values, result)
                          result = val[0] << @builder.string_internal(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1575)
  def _reduce_399(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1579)
  def _reduce_400(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1584)
  def _reduce_401(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1588)
  def _reduce_402(val, _values, result)
                          result = val[0] << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1593)
  def _reduce_403(val, _values, result)
                          result = @builder.string_internal(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1597)
  def _reduce_404(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1601)
  def _reduce_405(val, _values, result)
                          @lexer.cond.push(false)
                      @lexer.cmdarg.push(false)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1606)
  def _reduce_406(val, _values, result)
                          @lexer.cond.lexpop
                      @lexer.cmdarg.lexpop

                      result = @builder.begin(val[0], val[2], val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1614)
  def _reduce_407(val, _values, result)
                          result = @builder.gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1618)
  def _reduce_408(val, _values, result)
                          result = @builder.ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1622)
  def _reduce_409(val, _values, result)
                          result = @builder.cvar(val[0])

    result
  end
.,.,

# reduce 410 omitted

module_eval(<<'.,.,', 'ruby18.y', 1629)
  def _reduce_411(val, _values, result)
                          result = @builder.symbol(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1634)
  def _reduce_412(val, _values, result)
                          result = @builder.symbol_compose(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1639)
  def _reduce_413(val, _values, result)
                          result = @builder.integer(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1643)
  def _reduce_414(val, _values, result)
                          result = @builder.float(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1647)
  def _reduce_415(val, _values, result)
                          result = @builder.negate(val[0],
                                  @builder.integer(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1652)
  def _reduce_416(val, _values, result)
                          result = @builder.negate(val[0],
                                  @builder.float(val[1]))

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1658)
  def _reduce_417(val, _values, result)
                          result = @builder.ident(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1662)
  def _reduce_418(val, _values, result)
                          result = @builder.ivar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1666)
  def _reduce_419(val, _values, result)
                          result = @builder.gvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1670)
  def _reduce_420(val, _values, result)
                          result = @builder.cvar(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1674)
  def _reduce_421(val, _values, result)
                          result = @builder.const(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1678)
  def _reduce_422(val, _values, result)
                          result = @builder.nil(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1682)
  def _reduce_423(val, _values, result)
                          result = @builder.self(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1686)
  def _reduce_424(val, _values, result)
                          result = @builder.true(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1690)
  def _reduce_425(val, _values, result)
                          result = @builder.false(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1694)
  def _reduce_426(val, _values, result)
                          result = @builder.__FILE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1698)
  def _reduce_427(val, _values, result)
                          result = @builder.__LINE__(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1703)
  def _reduce_428(val, _values, result)
                          result = @builder.accessible(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1708)
  def _reduce_429(val, _values, result)
                          result = @builder.assignable(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1713)
  def _reduce_430(val, _values, result)
                          result = @builder.nth_ref(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1717)
  def _reduce_431(val, _values, result)
                          result = @builder.back_ref(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1722)
  def _reduce_432(val, _values, result)
                          result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1726)
  def _reduce_433(val, _values, result)
                          result = [ val[0], val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1730)
  def _reduce_434(val, _values, result)
                          yyerrok
                      result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1736)
  def _reduce_435(val, _values, result)
                          result = @builder.args(val[0], val[1], val[3])

                      @lexer.state = :expr_beg

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1742)
  def _reduce_436(val, _values, result)
                          result = @builder.args(nil, val[0], nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1747)
  def _reduce_437(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[4]).
                                  concat(val[5])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1754)
  def _reduce_438(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1760)
  def _reduce_439(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1766)
  def _reduce_440(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1771)
  def _reduce_441(val, _values, result)
                          result = val[0].
                                  concat(val[2]).
                                  concat(val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1777)
  def _reduce_442(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1782)
  def _reduce_443(val, _values, result)
                          result = val[0].
                                  concat(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1787)
  def _reduce_444(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1791)
  def _reduce_445(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1796)
  def _reduce_446(val, _values, result)
                          diagnostic :error, :argument_const, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1800)
  def _reduce_447(val, _values, result)
                          diagnostic :error, :argument_ivar, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1804)
  def _reduce_448(val, _values, result)
                          diagnostic :error, :argument_gvar, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1808)
  def _reduce_449(val, _values, result)
                          diagnostic :error, :argument_cvar, nil, val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1812)
  def _reduce_450(val, _values, result)
                          @static_env.declare val[0][0]

                      result = @builder.arg(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1819)
  def _reduce_451(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1823)
  def _reduce_452(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1828)
  def _reduce_453(val, _values, result)
                          @static_env.declare val[0][0]

                      result = @builder.optarg(val[0], val[1], val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1835)
  def _reduce_454(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1839)
  def _reduce_455(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

# reduce 456 omitted

# reduce 457 omitted

module_eval(<<'.,.,', 'ruby18.y', 1846)
  def _reduce_458(val, _values, result)
                          @static_env.declare val[1][0]

                      result = [ @builder.restarg(val[0], val[1]) ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1852)
  def _reduce_459(val, _values, result)
                          result = [ @builder.restarg(val[0]) ]

    result
  end
.,.,

# reduce 460 omitted

# reduce 461 omitted

module_eval(<<'.,.,', 'ruby18.y', 1859)
  def _reduce_462(val, _values, result)
                          @static_env.declare val[1][0]

                      result = @builder.blockarg(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1866)
  def _reduce_463(val, _values, result)
                          result = [ val[1] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1870)
  def _reduce_464(val, _values, result)
                          result = []

    result
  end
.,.,

# reduce 465 omitted

module_eval(<<'.,.,', 'ruby18.y', 1876)
  def _reduce_466(val, _values, result)
                          result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1881)
  def _reduce_467(val, _values, result)
                          result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1885)
  def _reduce_468(val, _values, result)
                          result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1889)
  def _reduce_469(val, _values, result)
                          result = @builder.pair_list_18(val[0])

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1894)
  def _reduce_470(val, _values, result)
                          result = [ val[0] ]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1898)
  def _reduce_471(val, _values, result)
                          result = val[0] << val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'ruby18.y', 1903)
  def _reduce_472(val, _values, result)
                          result = @builder.pair(val[0], val[1], val[2])

    result
  end
.,.,

# reduce 473 omitted

# reduce 474 omitted

# reduce 475 omitted

# reduce 476 omitted

# reduce 477 omitted

# reduce 478 omitted

# reduce 479 omitted

# reduce 480 omitted

# reduce 481 omitted

# reduce 482 omitted

# reduce 483 omitted

# reduce 484 omitted

# reduce 485 omitted

# reduce 486 omitted

# reduce 487 omitted

# reduce 488 omitted

# reduce 489 omitted

# reduce 490 omitted

# reduce 491 omitted

module_eval(<<'.,.,', 'ruby18.y', 1916)
  def _reduce_492(val, _values, result)
                          yyerrok

    result
  end
.,.,

# reduce 493 omitted

# reduce 494 omitted

# reduce 495 omitted

module_eval(<<'.,.,', 'ruby18.y', 1925)
  def _reduce_496(val, _values, result)
                          result = nil

    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Ruby18
end   # module Parser

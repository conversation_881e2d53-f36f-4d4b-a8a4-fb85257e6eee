#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'

require 'mediacloth/mediawikiast'

class MediaWikiParser < Racc::Parser

module_eval(<<'...end mediacloth.y/module_eval...', 'mediacloth.y', 564)

attr_accessor :lexer

def initialize
    @nodes = []
    @context = []
    @wiki_ast_length = 0
    super
end

#Tokenizes input string and parses it.
def parse(input)
    @yydebug=true
    lexer.tokenize(input)
    do_parse
    return @nodes.last
end

#Asks the lexer to return the next token.
def next_token
    token = @lexer.lex
    if token[0].to_s.upcase.include? "_START"
        @context << token[2..3]
    elsif token[0].to_s.upcase.include? "_END"
        @ast_index = @context.last[0]
        @ast_length = token[2] + token[3] - @context.last[0]
        @context.pop
    else
        @ast_index = token[2]
        @ast_length = token[3]
    end

    @wiki_ast_length += token[3]

    return token[0..1]
end
...end mediacloth.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
    22,    28,    50,    29,    61,    13,    63,   132,    15,    86,
    37,    87,    24,    33,    86,    34,    87,    35,    85,    23,
    26,    25,    27,    12,    86,    30,    87,    31,    86,    42,
    87,    32,    44,    86,    48,    87,    57,    17,    57,    22,
    28,    21,    29,    46,    13,    51,    67,    15,    68,    11,
    36,    24,    33,    14,    34,    69,    35,    36,    23,    26,
    25,    27,    12,    86,    30,    87,    31,    75,    73,    70,
    32,    48,    81,    71,    72,    76,    17,    77,    22,    28,
    21,    29,    82,    13,    45,    51,    15,   -65,    11,    36,
    24,    33,    14,    34,   -65,    35,    63,    23,    26,    25,
    27,    12,    96,    30,    63,    31,   106,   109,   110,    32,
   113,   114,    48,   117,   118,    17,   124,    22,    28,    21,
    29,    57,    13,   127,   128,    15,   131,    11,    36,    24,
    33,    14,    34,   136,    35,   137,    23,    26,    25,    27,
    12,   138,    30,    75,    31,    51,   141,   -63,    32,   106,
   106,   150,   153,    51,    17,   nil,   nil,   nil,    21,   nil,
    22,    28,    53,    29,   nil,    13,    11,    36,    15,   nil,
    14,   nil,    24,    33,   nil,    34,   nil,    35,   nil,    23,
    26,    25,    27,    12,   nil,    30,   nil,    31,   nil,   nil,
   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,
    28,    21,    29,    55,    13,   nil,   nil,    15,   nil,    11,
    36,    24,    33,    14,    34,   nil,    35,   nil,    23,    26,
    25,    27,    12,   nil,    30,   nil,    31,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,    28,
    21,    29,   nil,    13,   nil,   nil,    15,   nil,    11,    36,
    24,    33,    14,    34,   nil,    35,   nil,    23,    26,    25,
    27,    12,   nil,    30,   nil,    31,   nil,   nil,   nil,    32,
   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,    28,    21,
    29,   nil,    13,   nil,   nil,    15,   nil,    11,    36,    24,
    33,    14,    34,   nil,    35,   nil,    23,    26,    25,    27,
    12,   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,    17,   nil,    22,    28,    21,    29,
   nil,    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,
    14,    34,   nil,    35,   nil,    23,    26,    25,    27,    12,
   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,
    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,
    34,   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,
    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,
   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,    34,
   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,    30,
   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,    17,    78,   nil,   nil,    21,   nil,    22,    28,    88,
    29,   nil,    13,    11,    36,    15,   nil,    14,   nil,    24,
    33,   nil,    34,   nil,    35,   nil,    23,    26,    25,    27,
    12,   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,
   nil,   nil,   nil,   nil,    17,   nil,    22,    28,    21,    29,
    89,    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,
    14,    34,   nil,    35,   nil,    23,    26,    25,    27,    12,
   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,
    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,
    34,   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,
    30,   nil,    31,   nil,   nil,    93,    32,   nil,   nil,   nil,
   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,
   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,    34,
   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,    30,
   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,    98,   nil,
   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,   nil,
   nil,    15,   nil,    11,    36,    24,    33,    14,    34,   nil,
    35,   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,
    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   101,
    17,   nil,   nil,   nil,    21,   nil,    22,    28,   nil,    29,
   nil,    13,    11,    36,    15,   nil,    14,   nil,    24,    33,
   102,    34,   nil,    35,   nil,    23,    26,    25,    27,    12,
   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,
    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,
    34,   103,    35,   nil,    23,    26,    25,    27,    12,   nil,
    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,
   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,    34,
   nil,    35,   104,    23,    26,    25,    27,    12,   nil,    30,
   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,   nil,
   nil,    15,   nil,    11,    36,    24,    33,    14,    34,   nil,
    35,   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,
    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
    17,   nil,    22,    28,    21,    29,   nil,    13,   nil,   nil,
    15,   nil,    11,    36,    24,    33,    14,    34,   nil,    35,
   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,    31,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,
   nil,    22,    28,    21,    29,   nil,    13,   nil,   nil,    15,
   nil,    11,    36,    24,    33,    14,    34,   nil,    35,   nil,
    23,    26,    25,    27,    12,   nil,    30,   nil,    31,   nil,
   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,   115,
    22,    28,    21,    29,   nil,    13,   nil,   nil,    15,   nil,
    11,    36,    24,    33,    14,    34,   nil,    35,   nil,    23,
    26,    25,    27,    12,   nil,    30,   nil,    31,   nil,   nil,
   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,
    28,    21,    29,   nil,    13,   nil,   121,    15,   nil,    11,
    36,    24,    33,    14,    34,   nil,    35,   nil,    23,    26,
    25,    27,    12,   nil,    30,   nil,    31,   nil,   nil,   nil,
    32,   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,    28,
    21,    29,   nil,    13,   nil,   nil,    15,   123,    11,    36,
    24,    33,    14,    34,   nil,    35,   nil,    23,    26,    25,
    27,    12,   nil,    30,   nil,    31,   nil,   nil,   126,    32,
   nil,   nil,   nil,   nil,   nil,    17,   nil,    22,    28,    21,
    29,   nil,    13,   nil,   nil,    15,   nil,    11,    36,    24,
    33,    14,    34,   nil,    35,   nil,    23,    26,    25,    27,
    12,   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,
   nil,   129,   nil,   nil,    17,   nil,    22,    28,    21,    29,
   nil,    13,   nil,   nil,    15,   nil,    11,    36,    24,    33,
    14,    34,   nil,    35,   nil,    23,    26,    25,    27,    12,
   nil,    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,
   nil,   nil,   130,    17,   nil,    22,    28,    21,    29,   nil,
    13,   nil,   134,    15,   nil,    11,    36,    24,    33,    14,
    34,   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,
    30,   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,
   nil,   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,
   nil,   nil,    15,   nil,    11,    36,    24,    33,    14,    34,
   nil,    35,   nil,    23,    26,    25,    27,    12,   nil,    30,
   nil,    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,
   nil,    17,   nil,    22,    28,    21,    29,   nil,    13,   nil,
   nil,    15,   nil,    11,    36,    24,    33,    14,    34,   nil,
    35,   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,
    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
    17,   nil,    22,    28,    21,    29,   nil,    13,   nil,   142,
    15,   nil,    11,    36,    24,    33,    14,    34,   nil,    35,
   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,    31,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,
   nil,    22,    28,    21,    29,   nil,    13,   nil,   nil,    15,
   144,    11,    36,    24,    33,    14,    34,   nil,    35,   nil,
    23,    26,    25,    27,    12,   nil,    30,   nil,    31,   nil,
   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,   nil,
   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    11,    36,    22,    28,    14,    29,   nil,    13,   nil,   nil,
    15,   nil,   136,   nil,    24,    33,   nil,    34,   nil,    35,
   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,    31,
   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,    17,
   nil,   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    11,    36,    22,    28,    14,    29,   nil,    13,   nil,
   nil,    15,   nil,   136,   nil,    24,    33,   nil,    34,   nil,
    35,   nil,    23,    26,    25,    27,    12,   nil,    30,   nil,
    31,   nil,   nil,   nil,    32,   nil,   nil,   nil,   nil,   nil,
    17,   nil,   nil,   nil,    21,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    11,    36,   nil,   nil,    14 ]

racc_action_check = [
     0,     0,    21,     0,    32,     0,    32,   106,     0,    85,
     1,    85,     0,     0,   121,     0,   121,     0,    51,     0,
     0,     0,     0,     0,   123,     0,   123,     0,   142,    13,
   142,     0,    15,   144,    17,   144,    30,     0,    31,     2,
     2,     0,     2,    15,     2,    21,    36,     2,    37,     0,
     0,     2,     2,     0,     2,    39,     2,   106,     2,     2,
     2,     2,     2,    51,     2,    51,     2,    44,    44,    41,
     2,    48,    48,    42,    43,    45,     2,    46,    12,    12,
     2,    12,    49,    12,    15,    50,    12,    56,     2,     2,
    12,    12,     2,    12,    58,    12,    59,    12,    12,    12,
    12,    12,    60,    12,    62,    12,    67,    73,    74,    12,
    76,    77,    81,    83,    84,    12,    90,    14,    14,    12,
    14,    91,    14,    94,    95,    14,   105,    12,    12,    14,
    14,    12,    14,   109,    14,   112,    14,    14,    14,    14,
    14,   113,    14,   114,    14,   118,   119,   125,    14,   132,
   133,   135,   139,   141,    14,   nil,   nil,   nil,    14,   nil,
    28,    28,    28,    28,   nil,    28,    14,    14,    28,   nil,
    14,   nil,    28,    28,   nil,    28,   nil,    28,   nil,    28,
    28,    28,    28,    28,   nil,    28,   nil,    28,   nil,   nil,
   nil,    28,   nil,   nil,   nil,   nil,   nil,    28,   nil,    29,
    29,    28,    29,    29,    29,   nil,   nil,    29,   nil,    28,
    28,    29,    29,    28,    29,   nil,    29,   nil,    29,    29,
    29,    29,    29,   nil,    29,   nil,    29,   nil,   nil,   nil,
    29,   nil,   nil,   nil,   nil,   nil,    29,   nil,    33,    33,
    29,    33,   nil,    33,   nil,   nil,    33,   nil,    29,    29,
    33,    33,    29,    33,   nil,    33,   nil,    33,    33,    33,
    33,    33,   nil,    33,   nil,    33,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,    33,   nil,    34,    34,    33,
    34,   nil,    34,   nil,   nil,    34,   nil,    33,    33,    34,
    34,    33,    34,   nil,    34,   nil,    34,    34,    34,    34,
    34,   nil,    34,   nil,    34,   nil,   nil,   nil,    34,   nil,
   nil,   nil,   nil,   nil,    34,   nil,    35,    35,    34,    35,
   nil,    35,   nil,   nil,    35,   nil,    34,    34,    35,    35,
    34,    35,   nil,    35,   nil,    35,    35,    35,    35,    35,
   nil,    35,   nil,    35,   nil,   nil,   nil,    35,   nil,   nil,
   nil,   nil,   nil,    35,   nil,    40,    40,    35,    40,   nil,
    40,   nil,   nil,    40,   nil,    35,    35,    40,    40,    35,
    40,   nil,    40,   nil,    40,    40,    40,    40,    40,   nil,
    40,   nil,    40,   nil,   nil,   nil,    40,   nil,   nil,   nil,
   nil,   nil,    40,   nil,    47,    47,    40,    47,   nil,    47,
   nil,   nil,    47,   nil,    40,    40,    47,    47,    40,    47,
   nil,    47,   nil,    47,    47,    47,    47,    47,   nil,    47,
   nil,    47,   nil,   nil,   nil,    47,   nil,   nil,   nil,   nil,
   nil,    47,    47,   nil,   nil,    47,   nil,    52,    52,    52,
    52,   nil,    52,    47,    47,    52,   nil,    47,   nil,    52,
    52,   nil,    52,   nil,    52,   nil,    52,    52,    52,    52,
    52,   nil,    52,   nil,    52,   nil,   nil,   nil,    52,   nil,
   nil,   nil,   nil,   nil,    52,   nil,    54,    54,    52,    54,
    54,    54,   nil,   nil,    54,   nil,    52,    52,    54,    54,
    52,    54,   nil,    54,   nil,    54,    54,    54,    54,    54,
   nil,    54,   nil,    54,   nil,   nil,   nil,    54,   nil,   nil,
   nil,   nil,   nil,    54,   nil,    57,    57,    54,    57,   nil,
    57,   nil,   nil,    57,   nil,    54,    54,    57,    57,    54,
    57,   nil,    57,   nil,    57,    57,    57,    57,    57,   nil,
    57,   nil,    57,   nil,   nil,    57,    57,   nil,   nil,   nil,
   nil,   nil,    57,   nil,    61,    61,    57,    61,   nil,    61,
   nil,   nil,    61,   nil,    57,    57,    61,    61,    57,    61,
   nil,    61,   nil,    61,    61,    61,    61,    61,   nil,    61,
   nil,    61,   nil,   nil,   nil,    61,   nil,   nil,    61,   nil,
   nil,    61,   nil,    63,    63,    61,    63,   nil,    63,   nil,
   nil,    63,   nil,    61,    61,    63,    63,    61,    63,   nil,
    63,   nil,    63,    63,    63,    63,    63,   nil,    63,   nil,
    63,   nil,   nil,   nil,    63,   nil,   nil,   nil,   nil,    63,
    63,   nil,   nil,   nil,    63,   nil,    64,    64,   nil,    64,
   nil,    64,    63,    63,    64,   nil,    63,   nil,    64,    64,
    64,    64,   nil,    64,   nil,    64,    64,    64,    64,    64,
   nil,    64,   nil,    64,   nil,   nil,   nil,    64,   nil,   nil,
   nil,   nil,   nil,    64,   nil,    65,    65,    64,    65,   nil,
    65,   nil,   nil,    65,   nil,    64,    64,    65,    65,    64,
    65,    65,    65,   nil,    65,    65,    65,    65,    65,   nil,
    65,   nil,    65,   nil,   nil,   nil,    65,   nil,   nil,   nil,
   nil,   nil,    65,   nil,    66,    66,    65,    66,   nil,    66,
   nil,   nil,    66,   nil,    65,    65,    66,    66,    65,    66,
   nil,    66,    66,    66,    66,    66,    66,    66,   nil,    66,
   nil,    66,   nil,   nil,   nil,    66,   nil,   nil,   nil,   nil,
   nil,    66,   nil,    71,    71,    66,    71,   nil,    71,   nil,
   nil,    71,   nil,    66,    66,    71,    71,    66,    71,   nil,
    71,   nil,    71,    71,    71,    71,    71,   nil,    71,   nil,
    71,   nil,   nil,   nil,    71,   nil,   nil,   nil,   nil,   nil,
    71,   nil,    75,    75,    71,    75,   nil,    75,   nil,   nil,
    75,   nil,    71,    71,    75,    75,    71,    75,   nil,    75,
   nil,    75,    75,    75,    75,    75,   nil,    75,   nil,    75,
   nil,   nil,   nil,    75,   nil,   nil,   nil,   nil,   nil,    75,
   nil,    79,    79,    75,    79,   nil,    79,   nil,   nil,    79,
   nil,    75,    75,    79,    79,    75,    79,   nil,    79,   nil,
    79,    79,    79,    79,    79,   nil,    79,   nil,    79,   nil,
   nil,   nil,    79,   nil,   nil,   nil,   nil,   nil,    79,    79,
    86,    86,    79,    86,   nil,    86,   nil,   nil,    86,   nil,
    79,    79,    86,    86,    79,    86,   nil,    86,   nil,    86,
    86,    86,    86,    86,   nil,    86,   nil,    86,   nil,   nil,
   nil,    86,   nil,   nil,   nil,   nil,   nil,    86,   nil,    87,
    87,    86,    87,   nil,    87,   nil,    86,    87,   nil,    86,
    86,    87,    87,    86,    87,   nil,    87,   nil,    87,    87,
    87,    87,    87,   nil,    87,   nil,    87,   nil,   nil,   nil,
    87,   nil,   nil,   nil,   nil,   nil,    87,   nil,    92,    92,
    87,    92,   nil,    92,   nil,   nil,    92,    87,    87,    87,
    92,    92,    87,    92,   nil,    92,   nil,    92,    92,    92,
    92,    92,   nil,    92,   nil,    92,   nil,   nil,    92,    92,
   nil,   nil,   nil,   nil,   nil,    92,   nil,    97,    97,    92,
    97,   nil,    97,   nil,   nil,    97,   nil,    92,    92,    97,
    97,    92,    97,   nil,    97,   nil,    97,    97,    97,    97,
    97,   nil,    97,   nil,    97,   nil,   nil,   nil,    97,   nil,
   nil,    97,   nil,   nil,    97,   nil,   100,   100,    97,   100,
   nil,   100,   nil,   nil,   100,   nil,    97,    97,   100,   100,
    97,   100,   nil,   100,   nil,   100,   100,   100,   100,   100,
   nil,   100,   nil,   100,   nil,   nil,   nil,   100,   nil,   nil,
   nil,   nil,   100,   100,   nil,   108,   108,   100,   108,   nil,
   108,   nil,   108,   108,   nil,   100,   100,   108,   108,   100,
   108,   nil,   108,   nil,   108,   108,   108,   108,   108,   nil,
   108,   nil,   108,   nil,   nil,   nil,   108,   nil,   nil,   nil,
   nil,   nil,   108,   nil,   111,   111,   108,   111,   nil,   111,
   nil,   nil,   111,   nil,   108,   108,   111,   111,   108,   111,
   nil,   111,   nil,   111,   111,   111,   111,   111,   nil,   111,
   nil,   111,   nil,   nil,   nil,   111,   nil,   nil,   nil,   nil,
   nil,   111,   nil,   120,   120,   111,   120,   nil,   120,   nil,
   nil,   120,   nil,   111,   111,   120,   120,   111,   120,   nil,
   120,   nil,   120,   120,   120,   120,   120,   nil,   120,   nil,
   120,   nil,   nil,   nil,   120,   nil,   nil,   nil,   nil,   nil,
   120,   nil,   122,   122,   120,   122,   nil,   122,   nil,   120,
   122,   nil,   120,   120,   122,   122,   120,   122,   nil,   122,
   nil,   122,   122,   122,   122,   122,   nil,   122,   nil,   122,
   nil,   nil,   nil,   122,   nil,   nil,   nil,   nil,   nil,   122,
   nil,   134,   134,   122,   134,   nil,   134,   nil,   nil,   134,
   122,   122,   122,   134,   134,   122,   134,   nil,   134,   nil,
   134,   134,   134,   134,   134,   nil,   134,   nil,   134,   nil,
   nil,   nil,   134,   nil,   nil,   nil,   nil,   nil,   134,   nil,
   nil,   nil,   134,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   134,   134,   136,   136,   134,   136,   nil,   136,   nil,   nil,
   136,   nil,   136,   nil,   136,   136,   nil,   136,   nil,   136,
   nil,   136,   136,   136,   136,   136,   nil,   136,   nil,   136,
   nil,   nil,   nil,   136,   nil,   nil,   nil,   nil,   nil,   136,
   nil,   nil,   nil,   136,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   136,   136,   152,   152,   136,   152,   nil,   152,   nil,
   nil,   152,   nil,   152,   nil,   152,   152,   nil,   152,   nil,
   152,   nil,   152,   152,   152,   152,   152,   nil,   152,   nil,
   152,   nil,   nil,   nil,   152,   nil,   nil,   nil,   nil,   nil,
   152,   nil,   nil,   nil,   152,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   152,   152,   nil,   nil,   152 ]

racc_action_pointer = [
    -2,    10,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    76,    27,   115,    30,   nil,    -7,   nil,   nil,
   nil,     0,   nil,   nil,   nil,   nil,   nil,   nil,   158,   197,
     5,     7,   -31,   236,   275,   314,    44,    48,   nil,    29,
   353,    61,    64,    18,    55,    73,    23,   392,    30,    38,
    40,    16,   435,   nil,   474,   nil,    59,   513,    64,    59,
    68,   552,    67,   591,   634,   673,   712,    94,   nil,   nil,
   nil,   751,   nil,   105,    97,   790,    98,   109,   nil,   829,
   nil,    71,   nil,    69,    68,   -38,   868,   907,   nil,   nil,
    88,    90,   946,   nil,    93,    90,   nil,   985,   nil,   nil,
  1024,   nil,   nil,   nil,   nil,    73,     5,   nil,  1063,   121,
   nil,  1102,   124,   139,   131,   nil,   nil,   nil,   100,   100,
  1141,   -33,  1180,   -23,   nil,   116,   nil,   nil,   nil,   nil,
   nil,   nil,   137,   138,  1219,   140,  1270,   nil,   nil,   141,
   nil,   108,   -19,   nil,   -14,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,  1321,   nil,   nil,   nil,   nil,   nil ]

racc_action_default = [
   -83,   -83,    -1,    -2,    -3,    -4,    -5,    -6,    -7,    -8,
    -9,   -10,   -19,   -83,   -19,   -83,   -18,   -23,   -37,   -39,
   -40,   -43,   -51,   -52,   -53,   -54,   -55,   -56,   -83,   -83,
   -83,   -83,   -73,   -83,   -83,   -83,   -83,   -83,   -38,   -83,
   -20,   -83,   -26,   -83,   -30,   -83,   -83,   -83,   -23,   -83,
   -43,   -46,   -83,   -57,   -83,   -58,   -63,   -83,   -63,   -73,
   -83,   -83,   -73,   -83,   -83,   -83,   -83,   -80,   158,   -11,
   -12,   -83,   -13,   -83,   -83,   -83,   -32,   -83,   -21,   -83,
   -24,   -23,   -41,   -83,   -83,   -46,   -83,   -83,   -59,   -60,
   -83,   -83,   -83,   -66,   -83,   -83,   -69,   -83,   -70,   -72,
   -83,   -74,   -76,   -77,   -78,   -83,   -83,   -27,   -28,   -34,
   -15,   -31,   -83,   -83,   -30,   -22,   -25,   -42,   -43,   -83,
   -83,   -46,   -83,   -46,   -61,   -65,   -67,   -62,   -68,   -71,
   -75,   -79,   -80,   -80,   -83,   -83,   -34,   -16,   -33,   -83,
   -44,   -43,   -46,   -47,   -46,   -49,   -64,   -81,   -82,   -29,
   -14,   -35,   -34,   -17,   -45,   -48,   -50,   -36 ]

racc_goto_table = [
    38,    84,    74,   105,    49,    39,    90,    43,    94,    60,
   135,     1,   133,     2,    47,    41,   107,   112,    59,    56,
    58,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    83,   nil,   119,    95,   151,    38,    99,
   nil,    52,    54,   nil,   nil,    80,    64,    65,    66,   nil,
    38,   nil,    38,   157,   nil,   nil,   nil,   nil,   nil,   nil,
    79,   nil,    38,    38,    38,   nil,   nil,   nil,   147,   148,
    92,   143,   139,   145,    97,   146,   100,    38,   116,   149,
   125,   nil,   nil,   nil,   108,   nil,   nil,   nil,   111,   nil,
    38,   nil,   155,   nil,   156,    38,   nil,   nil,    38,   120,
   122,   140,   nil,   nil,   nil,   nil,    38,   nil,   nil,    38,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    38,   nil,
    38,   nil,   nil,   nil,   154,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   108,   nil,   152,
    38 ]

racc_goto_check = [
     3,    23,    15,    30,    22,    12,    25,    12,    25,    28,
    14,     1,    11,     2,    18,    13,    19,    16,    27,    24,
    24,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    22,   nil,    23,    28,    14,     3,    28,
   nil,     2,     2,   nil,   nil,    18,     2,     2,     2,   nil,
     3,   nil,     3,    14,   nil,   nil,   nil,   nil,   nil,   nil,
     2,   nil,     3,     3,     3,   nil,   nil,   nil,    30,    30,
     2,    23,    15,    23,     2,    25,     2,     3,    18,    19,
    24,   nil,   nil,   nil,     2,   nil,   nil,   nil,     2,   nil,
     3,   nil,    23,   nil,    23,     3,   nil,   nil,     3,     2,
     2,    22,   nil,   nil,   nil,   nil,     3,   nil,   nil,     3,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     3,   nil,
     3,   nil,   nil,   nil,    22,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     2,   nil,     2,
     3 ]

racc_goto_pointer = [
   nil,    11,    13,    -2,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   -94,    -7,     2,   -99,   -42,   -59,   nil,    -3,   -55,
   nil,   nil,   -17,   -50,   -11,   -50,   nil,   -14,   -23,   nil,
   -64 ]

racc_goto_default = [
   nil,   nil,    40,    18,     3,     4,     5,     6,     7,     8,
     9,    10,   nil,   nil,   nil,   nil,   nil,    16,   nil,   nil,
    19,    20,   nil,   nil,   nil,   nil,    91,   nil,   nil,    62,
   nil ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 58, :_reduce_1,
  1, 60, :_reduce_2,
  1, 60, :_reduce_3,
  1, 60, :_reduce_4,
  1, 60, :_reduce_5,
  1, 60, :_reduce_6,
  1, 60, :_reduce_7,
  1, 60, :_reduce_8,
  1, 60, :_reduce_9,
  1, 60, :_reduce_10,
  3, 60, :_reduce_11,
  3, 60, :_reduce_12,
  3, 60, :_reduce_13,
  6, 60, :_reduce_14,
  4, 60, :_reduce_15,
  5, 60, :_reduce_16,
  6, 60, :_reduce_17,
  1, 60, :_reduce_none,
  0, 69, :_reduce_19,
  1, 69, :_reduce_20,
  3, 67, :_reduce_21,
  4, 67, :_reduce_22,
  0, 75, :_reduce_23,
  2, 75, :_reduce_24,
  3, 75, :_reduce_25,
  1, 70, :_reduce_26,
  3, 70, :_reduce_27,
  1, 76, :_reduce_28,
  3, 76, :_reduce_29,
  0, 72, :_reduce_30,
  2, 72, :_reduce_31,
  0, 73, :_reduce_32,
  2, 73, :_reduce_33,
  0, 71, :_reduce_34,
  2, 71, :_reduce_35,
  3, 71, :_reduce_36,
  1, 59, :_reduce_37,
  2, 59, :_reduce_38,
  1, 61, :_reduce_39,
  1, 61, :_reduce_40,
  3, 74, :_reduce_41,
  4, 74, :_reduce_42,
  0, 79, :_reduce_43,
  4, 79, :_reduce_44,
  5, 79, :_reduce_45,
  0, 80, :_reduce_46,
  3, 80, :_reduce_47,
  4, 80, :_reduce_48,
  3, 80, :_reduce_49,
  4, 80, :_reduce_50,
  1, 77, :_reduce_51,
  1, 77, :_reduce_52,
  1, 77, :_reduce_53,
  1, 77, :_reduce_54,
  1, 77, :_reduce_55,
  1, 77, :_reduce_56,
  2, 78, :_reduce_57,
  2, 78, :_reduce_58,
  3, 78, :_reduce_59,
  3, 78, :_reduce_60,
  4, 62, :_reduce_61,
  4, 63, :_reduce_62,
  0, 83, :_reduce_63,
  3, 82, :_reduce_64,
  0, 82, :_reduce_65,
  2, 81, :_reduce_66,
  3, 81, :_reduce_67,
  4, 64, :_reduce_68,
  3, 64, :_reduce_69,
  2, 84, :_reduce_70,
  3, 84, :_reduce_71,
  2, 85, :_reduce_72,
  0, 85, :_reduce_73,
  2, 86, :_reduce_74,
  3, 86, :_reduce_75,
  3, 65, :_reduce_76,
  3, 65, :_reduce_77,
  3, 66, :_reduce_78,
  4, 68, :_reduce_79,
  0, 87, :_reduce_80,
  3, 87, :_reduce_81,
  3, 87, :_reduce_82 ]

racc_reduce_n = 83

racc_shift_n = 158

racc_token_table = {
  false => 0,
  :error => 1,
  :TEXT => 2,
  :BOLD_START => 3,
  :BOLD_END => 4,
  :ITALIC_START => 5,
  :ITALIC_END => 6,
  :LINK_START => 7,
  :LINK_END => 8,
  :LINKSEP => 9,
  :INTLINK_START => 10,
  :INTLINK_END => 11,
  :INTLINKSEP => 12,
  :RESOURCESEP => 13,
  :CHAR_ENT => 14,
  :PRE_START => 15,
  :PRE_END => 16,
  :PREINDENT_START => 17,
  :PREINDENT_END => 18,
  :SECTION_START => 19,
  :SECTION_END => 20,
  :HLINE => 21,
  :SIGNATURE_NAME => 22,
  :SIGNATURE_DATE => 23,
  :SIGNATURE_FULL => 24,
  :PARA_START => 25,
  :PARA_END => 26,
  :UL_START => 27,
  :UL_END => 28,
  :OL_START => 29,
  :OL_END => 30,
  :LI_START => 31,
  :LI_END => 32,
  :DL_START => 33,
  :DL_END => 34,
  :DT_START => 35,
  :DT_END => 36,
  :DD_START => 37,
  :DD_END => 38,
  :TAG_START => 39,
  :TAG_END => 40,
  :ATTR_NAME => 41,
  :ATTR_VALUE => 42,
  :TABLE_START => 43,
  :TABLE_END => 44,
  :ROW_START => 45,
  :ROW_END => 46,
  :HEAD_START => 47,
  :HEAD_END => 48,
  :CELL_START => 49,
  :CELL_END => 50,
  :KEYWORD => 51,
  :TEMPLATE_START => 52,
  :TEMPLATE_END => 53,
  :CATEGORY => 54,
  :PASTE_START => 55,
  :PASTE_END => 56 }

racc_nt_base = 57

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "TEXT",
  "BOLD_START",
  "BOLD_END",
  "ITALIC_START",
  "ITALIC_END",
  "LINK_START",
  "LINK_END",
  "LINKSEP",
  "INTLINK_START",
  "INTLINK_END",
  "INTLINKSEP",
  "RESOURCESEP",
  "CHAR_ENT",
  "PRE_START",
  "PRE_END",
  "PREINDENT_START",
  "PREINDENT_END",
  "SECTION_START",
  "SECTION_END",
  "HLINE",
  "SIGNATURE_NAME",
  "SIGNATURE_DATE",
  "SIGNATURE_FULL",
  "PARA_START",
  "PARA_END",
  "UL_START",
  "UL_END",
  "OL_START",
  "OL_END",
  "LI_START",
  "LI_END",
  "DL_START",
  "DL_END",
  "DT_START",
  "DT_END",
  "DD_START",
  "DD_END",
  "TAG_START",
  "TAG_END",
  "ATTR_NAME",
  "ATTR_VALUE",
  "TABLE_START",
  "TABLE_END",
  "ROW_START",
  "ROW_END",
  "HEAD_START",
  "HEAD_END",
  "CELL_START",
  "CELL_END",
  "KEYWORD",
  "TEMPLATE_START",
  "TEMPLATE_END",
  "CATEGORY",
  "PASTE_START",
  "PASTE_END",
  "$start",
  "wiki",
  "repeated_contents",
  "contents",
  "text",
  "bulleted_list",
  "numbered_list",
  "dictionary_list",
  "preformatted",
  "section",
  "tag",
  "template",
  "para_contents",
  "link_contents",
  "reslink_repeated_contents",
  "intlink_repeated_contents",
  "cat_sort_contents",
  "table",
  "tag_attributes",
  "link_repeated_contents",
  "element",
  "formatted_element",
  "table_contents",
  "row_contents",
  "list_item",
  "list_contents",
  "@1",
  "dictionary_term",
  "dictionary_contents",
  "dictionary_definition",
  "template_parameters" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'mediacloth.y', 47)
  def _reduce_1(val, _values, result)
                @nodes.push WikiAST.new(0, @wiki_ast_length)
            #@nodes.last.children.insert(0, val[0])
            #puts val[0]
            @nodes.last.children += val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 57)
  def _reduce_2(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 61)
  def _reduce_3(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 65)
  def _reduce_4(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 69)
  def _reduce_5(val, _values, result)
                list = ListAST.new(@ast_index, @ast_length)
            list.list_type = :Dictionary
            list.children = val[0]
            result = list

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 76)
  def _reduce_6(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 80)
  def _reduce_7(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 84)
  def _reduce_8(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 88)
  def _reduce_9(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 92)
  def _reduce_10(val, _values, result)
                k = KeywordAST.new(@ast_index, @ast_length)
            k.text = val[0]
            result = k

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 98)
  def _reduce_11(val, _values, result)
                p = ParagraphAST.new(@ast_index, @ast_length)
            p.children = val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 104)
  def _reduce_12(val, _values, result)
                l = LinkAST.new(@ast_index, @ast_length)
            l.link_type = val[0]
            l.url = val[1][0]
            l.children += val[1][1..-1] if val[1].length > 1
            result = l

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 112)
  def _reduce_13(val, _values, result)
                p = PasteAST.new(@ast_index, @ast_length)
            p.children = val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 118)
  def _reduce_14(val, _values, result)
                l = ResourceLinkAST.new(@ast_index, @ast_length)
            l.prefix = val[1]
            l.locator = val[3]
            l.children = val[4] unless val[4].nil? or val[4].empty?
            result = l

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 126)
  def _reduce_15(val, _values, result)
                l = InternalLinkAST.new(@ast_index, @ast_length)
            l.locator = val[1]
            l.children = val[2] unless val[2].nil? or val[2].empty?
            result = l

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 133)
  def _reduce_16(val, _values, result)
                l = CategoryAST.new(@ast_index, @ast_length)
            l.locator = val[2]
            l.sort_as = val[3]
            result = l

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 140)
  def _reduce_17(val, _values, result)
                l = CategoryLinkAST.new(@ast_index, @ast_length)
            l.locator = val[3]
            l.children = val[4] unless val[4].nil? or val[4].empty?
            result = l

    result
  end
.,.,

# reduce 18 omitted

module_eval(<<'.,.,', 'mediacloth.y', 150)
  def _reduce_19(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 154)
  def _reduce_20(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 161)
  def _reduce_21(val, _values, result)
                if val[0] != val[2]
                raise Racc::ParseError.new("XHTML end tag #{val[2]} does not match start tag #{val[0]}")
            end
            elem = ElementAST.new(@ast_index, @ast_length)
            elem.name = val[0]
            elem.attributes = val[1]
            result = elem

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 171)
  def _reduce_22(val, _values, result)
                if val[0] != val[3]
                raise Racc::ParseError.new("XHTML end tag #{val[3]} does not match start tag #{val[0]}")
            end
            elem = ElementAST.new(@ast_index, @ast_length)
            elem.name = val[0]
            elem.attributes = val[1]
            elem.children += val[2]
            result = elem

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 184)
  def _reduce_23(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 188)
  def _reduce_24(val, _values, result)
                attr_map = val[2] ? val[2] : {}
            attr_map[val[0]] = true
            result = attr_map

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 194)
  def _reduce_25(val, _values, result)
                attr_map = val[2] ? val[2] : {}
            attr_map[val[0]] = val[1]
            result = attr_map

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 204)
  def _reduce_26(val, _values, result)
                result = val

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 208)
  def _reduce_27(val, _values, result)
                result = [val[0]]
            result += val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 217)
  def _reduce_28(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 221)
  def _reduce_29(val, _values, result)
                result = val[0]
            result += val[2] if val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 229)
  def _reduce_30(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 233)
  def _reduce_31(val, _values, result)
                result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 239)
  def _reduce_32(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 243)
  def _reduce_33(val, _values, result)
                result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 249)
  def _reduce_34(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 253)
  def _reduce_35(val, _values, result)
                result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 257)
  def _reduce_36(val, _values, result)
                i = InternalLinkItemAST.new(@ast_index, @ast_length)
            i.children = val[1]
            result = [i]
            result += val[2] if val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 266)
  def _reduce_37(val, _values, result)
                result = []
            result << val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 271)
  def _reduce_38(val, _values, result)
                result = []
            result += val[0]
            result << val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 279)
  def _reduce_39(val, _values, result)
                p = TextAST.new(@ast_index, @ast_length)
            p.formatting = val[0][0]
            p.contents = val[0][1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 286)
  def _reduce_40(val, _values, result)
                result = val[0]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 293)
  def _reduce_41(val, _values, result)
                table = TableAST.new(@ast_index, @ast_length)
            table.children = val[1] unless val[1].nil? or val[1].empty?
            result = table

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 299)
  def _reduce_42(val, _values, result)
                table = TableAST.new(@ast_index, @ast_length)
            table.options = val[1]
            table.children = val[2] unless val[2].nil? or val[2].empty?
            result = table

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 307)
  def _reduce_43(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 311)
  def _reduce_44(val, _values, result)
                row = TableRowAST.new(@ast_index, @ast_length)
            row.children = val[1] unless val[1].nil? or val[1].empty?
            result = [row]
            result += val[3] unless val[3].nil? or val[3].empty?

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 318)
  def _reduce_45(val, _values, result)
                row = TableRowAST.new(@ast_index, @ast_length)
            row.children = val[2] unless val[2].nil? or val[2].empty?
            row.options = val[1]
            result = [row]
            result += val[4] unless val[4].nil? or val[4].empty?

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 327)
  def _reduce_46(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 331)
  def _reduce_47(val, _values, result)
                cell = TableCellAST.new(@ast_index, @ast_length)
            cell.type = :head
            result = [cell]
            result += val[2] unless val[2].nil? or val[2].empty?

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 338)
  def _reduce_48(val, _values, result)
                cell = TableCellAST.new(@ast_index, @ast_length)
            cell.children = val[1] unless val[1].nil? or val[1].empty?
            cell.type = :head
            result = [cell]
            result += val[3] unless val[3].nil? or val[3].empty?

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 346)
  def _reduce_49(val, _values, result)
                cell = TableCellAST.new(@ast_index, @ast_length)
            cell.type = :body
            result = [cell]
            result += val[2] unless val[2].nil? or val[2].empty?

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 353)
  def _reduce_50(val, _values, result)
                if val[2] == 'attributes'
                result = []
            else
                cell = TableCellAST.new(@ast_index, @ast_length)
                cell.children = val[1] unless val[1].nil? or val[1].empty?
                cell.type = :body
                result = [cell]
            end
            result += val[3] unless val[3].nil? or val[3].empty?
            if val[2] == 'attributes' and val[3] and val[3].first.class == TableCellAST
                val[3].first.attributes = val[1]
            end
            result

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 371)
  def _reduce_51(val, _values, result)
     return [:None, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 373)
  def _reduce_52(val, _values, result)
     return [:HLine, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 375)
  def _reduce_53(val, _values, result)
     return [:CharacterEntity, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 377)
  def _reduce_54(val, _values, result)
     return [:SignatureDate, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 379)
  def _reduce_55(val, _values, result)
     return [:SignatureName, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 381)
  def _reduce_56(val, _values, result)
     return [:SignatureFull, val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 387)
  def _reduce_57(val, _values, result)
                result = FormattedAST.new(@ast_index, @ast_length)
            result.formatting = :Bold
            result

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 393)
  def _reduce_58(val, _values, result)
                result = FormattedAST.new(@ast_index, @ast_length)
            result.formatting = :Italic
            result

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 399)
  def _reduce_59(val, _values, result)
                p = FormattedAST.new(@ast_index, @ast_length)
            p.formatting = :Bold
            p.children += val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 406)
  def _reduce_60(val, _values, result)
                p = FormattedAST.new(@ast_index, @ast_length)
            p.formatting = :Italic
            p.children += val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 415)
  def _reduce_61(val, _values, result)
                list = ListAST.new(@ast_index, @ast_length)
            list.list_type = :Bulleted
            list.children << val[1]
            list.children += val[2]
            result = list

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 425)
  def _reduce_62(val, _values, result)
                list = ListAST.new(@ast_index, @ast_length)
            list.list_type = :Numbered
            list.children << val[1]
            list.children += val[2]
            result = list

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 434)
  def _reduce_63(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 437)
  def _reduce_64(val, _values, result)
                result << val[1]
            result += val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 441)
  def _reduce_65(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 447)
  def _reduce_66(val, _values, result)
                result = ListItemAST.new(@ast_index, @ast_length)

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 451)
  def _reduce_67(val, _values, result)
                li = ListItemAST.new(@ast_index, @ast_length)
            li.children += val[1]
            result = li

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 460)
  def _reduce_68(val, _values, result)
                result = [val[1]]
            result += val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 465)
  def _reduce_69(val, _values, result)
                result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 472)
  def _reduce_70(val, _values, result)
                result = ListTermAST.new(@ast_index, @ast_length)

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 476)
  def _reduce_71(val, _values, result)
                term = ListTermAST.new(@ast_index, @ast_length)
            term.children += val[1]
            result = term

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 484)
  def _reduce_72(val, _values, result)
                result = [val[0]]
            result += val[1] if val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 489)
  def _reduce_73(val, _values, result)
                result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 495)
  def _reduce_74(val, _values, result)
                result = ListDefinitionAST.new(@ast_index, @ast_length)

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 499)
  def _reduce_75(val, _values, result)
                term = ListDefinitionAST.new(@ast_index, @ast_length)
            term.children += val[1]
            result = term

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 506)
  def _reduce_76(val, _values, result)
                p = PreformattedAST.new(@ast_index, @ast_length)
            p.children += val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 512)
  def _reduce_77(val, _values, result)
                p = PreformattedAST.new(@ast_index, @ast_length)
            p.indented = true
            p.children += val[1]
            result = p

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 520)
  def _reduce_78(val, _values, result)
     result = [val[1], val[0].length]
            s = SectionAST.new(@ast_index, @ast_length)
            s.children = val[1]
            s.level = val[0].length
            result = s

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 530)
  def _reduce_79(val, _values, result)
                t = TemplateAST.new(@ast_index, @ast_length)
            t.template_name = val[1]
            t.children = val[2] unless val[2].nil? or val[2].empty?
            result = t

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 539)
  def _reduce_80(val, _values, result)
                result = nil

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 543)
  def _reduce_81(val, _values, result)
                p = TemplateParameterAST.new(@ast_index, @ast_length)
            p.parameter_value = val[1]
            result = [p]
            result += val[2] if val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'mediacloth.y', 550)
  def _reduce_82(val, _values, result)
                p = TemplateParameterAST.new(@ast_index, @ast_length)
            p.children << val[1]
            result = [p]
            result += val[2] if val[2]

    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

end   # class MediaWikiParser

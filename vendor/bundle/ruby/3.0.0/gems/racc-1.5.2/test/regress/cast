#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


require 'set'

# Error classes
module C
  class ParseError < StandardError; end
end

# Local variables:
#   mode: ruby
# end:
module C
  class Parser < Racc::Parser

module_eval(<<'...end cast.y/module_eval...', 'cast.y', 564)
  # A.1.9 -- Preprocessing numbers -- skip
  # A.1.8 -- Header names -- skip

  # A.1.7 -- Puncuators -- we don't bother with {##,#,%:,%:%:} since
  # we don't do preprocessing
  @@punctuators = %r'\+\+|-[->]|&&|\|\||\.\.\.|(?:<<|>>|[<>=!*/%+\-&^|])=?|[\[\](){}.~?:;,]'
  @@digraphs    = %r'<[:%]|[:%]>'

  # A.1.6 -- String Literals -- simple for us because we don't decode
  # the string (and indeed accept some illegal strings)
  @@string_literal = %r'L?"(?:[^\\]|\\.)*?"'m

  # A.1.5 -- Constants
  @@decimal_floating_constant     = %r'(?:(?:\d*\.\d+|\d+\.)(?:e[-+]?\d+)?|\d+e[-+]?\d+)[fl]?'i
  @@hexadecimal_floating_constant = %r'0x(?:(?:[0-9a-f]*\.[0-9a-f]+|[0-9a-f]+\.)|[0-9a-f]+)p[-+]?\d+[fl]?'i

  @@integer_constant     = %r'(?:[1-9][0-9]*|0x[0-9a-f]+|0[0-7]*)(?:ul?l?|ll?u?)?'i
  @@floating_constant    = %r'#{@@decimal_floating_constant}|#{@@hexadecimal_floating_constant}'
  @@enumeration_constant = %r'[a-zA-Z_\\][a-zA-Z_\\0-9]*'
  @@character_constant   = %r"L?'(?:[^\\]|\\.)+?'"
  # (note that as with string-literals, we accept some illegal
  # character-constants)

  # A.1.4 -- Universal character names -- skip

  # A.1.3 -- Identifiers -- skip, since an identifier is lexically
  # identical to an enumeration constant

  # A.1.2 Keywords
  keywords = %w'auto break case char const continue default do
double else enum extern float for goto if inline int long register
restrict return short signed sizeof static struct switch typedef union
 unsigned void volatile while _Bool _Complex _Imaginary'
  @@keywords = %r"#{keywords.join('|')}"

  def initialize
    @type_names = ::Set.new

    @warning_proc = lambda{}
    @pos          = C::Node::Pos.new(nil, 1, 0)
  end
  def initialize_copy(x)
    @pos        = x.pos.dup
    @type_names = x.type_names.dup
  end
  attr_accessor :pos, :type_names

  def parse(str)
    if str.respond_to? :read
      str = str.read
    end
    @str = str
    begin
      prepare_lexer(str)
      return do_parse
    rescue ParseError => e
      e.set_backtrace(caller)
      raise
    end
  end

  #
  # Error handler, as used by racc.
  #
  def on_error(error_token_id, error_value, value_stack)
    if error_value == '$'
      parse_error @pos, "unexpected EOF"
    else
      parse_error(error_value.pos,
                  "parse error on #{token_to_str(error_token_id)} (#{error_value.val})")
    end
  end

  def self.feature(name)
    attr_writer "#{name}_enabled"
    class_eval <<-EOS
      def enable_#{name}
        @#{name}_enabled = true
      end
      def #{name}_enabled?
        @#{name}_enabled
      end
    EOS
  end
  private_class_method :feature

  #
  # Allow blocks in parentheses as expressions, as per the gcc
  # extension.  [http://rubyurl.com/iB7]
  #
  feature :block_expressions

  private  # ---------------------------------------------------------

  class Token
    attr_accessor :pos, :val
    def initialize(pos, val)
      @pos = pos
      @val = val
    end
  end
  def eat(str)
    lines = str.split(/\r\n|[\r\n]/, -1)
    if lines.length == 1
      @pos.col_num += lines[0].length
    else
      @pos.line_num += lines.length - 1
      @pos.col_num = lines[-1].length
    end
  end

  #
  # Make a Declaration from the given specs and declarators.
  #
  def make_declaration(pos, specs, declarators)
    specs.all?{|x| x.is_a?(Symbol) || x.is_a?(Type)} or raise specs.map{|x| x.class}.inspect
    decl = Declaration.new_at(pos, nil, declarators)

    # set storage class
    storage_classes = specs.find_all do |x|
      [:typedef, :extern, :static, :auto, :register].include? x
    end
    # 6.7.1p2: at most, one storage-class specifier may be given in
    # the declaration specifiers in a declaration
    storage_classes.length <= 1 or
      begin
        if declarators.length == 0
          for_name = ''
        else
          for_name = "for `#{declarators[0].name}'"
        end
        parse_error pos, "multiple or duplicate storage classes given #{for_name}'"
      end
    decl.storage = storage_classes[0]

    # set type (specifiers, qualifiers)
    decl.type = make_direct_type(pos, specs)

    # set function specifiers
    decl.inline = specs.include?(:inline)

    # look for new type names
    if decl.typedef?
      decl.declarators.each do |d|
        if d.name
          @type_names << d.name
        end
      end
    end

    return decl
  end

  def make_function_def(pos, specs, func_declarator, decl_list, defn)
    add_decl_type(func_declarator, make_direct_type(pos, specs))

    # get types from decl_list if necessary
    function = func_declarator.indirect_type
    function.is_a? Function or
      parse_error pos, "non function type for function `#{func_declarator.name}'"
    params = function.params
    if decl_list
      params.all?{|p| p.type.nil?} or
        parse_error pos, "both prototype and declaration list given for `#{func_declarator.name}'"
      decl_list.each do |declaration|
        declaration.declarators.each do |declarator|
          param = params.find{|p| p.name == declarator.name} or
            parse_error pos, "no parameter named #{declarator.name}"
          if declarator.indirect_type
            param.type = declarator.indirect_type
            param.type.direct_type = declaration.type.dup
          else
            param.type = declaration.type.dup
          end
        end
      end
      params.all?{|p| p.type} or
        begin
          s = params.find_all{|p| p.type.nil?}.map{|p| "`#{p.name}'"}.join(' and ')
          parse_error pos, "types missing for parameters #{s}"
        end
    end

    fd = FunctionDef.new_at(pos,
                            function.detach,
                            func_declarator.name,
                            defn,
                            :no_prototype => !decl_list.nil?)

    # set storage class
    # 6.9.1p4: only extern or static allowed
    specs.each do |s|
      [:typedef, :auto, :register].include?(s) and
        "`#{s}' illegal for function"
    end
    storage_classes = specs.find_all do |s|
      s == :extern || s == :static
    end
    # 6.7.1p2: at most, one storage-class specifier may be given in
    # the declaration specifiers in a declaration
    storage_classes.length <= 1 or
      "multiple or duplicate storage classes given for `#{func_declarator.name}'"
    fd.storage = storage_classes[0] if storage_classes[0]

    # set function specifiers
    # 6.7.4p5 'inline' can be repeated
    fd.inline = specs.include?(:inline)

    return fd
  end

  #
  # Make a direct type from the list of type specifiers and type
  # qualifiers.
  #
  def make_direct_type(pos, specs)
    specs_order = [:signed, :unsigned, :short, :long, :double, :void,
      :char, :int, :float, :_Bool, :_Complex, :_Imaginary]

    type_specs = specs.find_all do |x|
      specs_order.include?(x) || !x.is_a?(Symbol)
    end
    type_specs.sort! do |a, b|
      (specs_order.index(a)||100) <=> (specs_order.index(b)||100)
    end

    # set type specifiers
    # 6.7.2p2: the specifier list should be one of these
    type =
      case type_specs
      when [:void]
        Void.new
      when [:char]
        Char.new
      when [:signed, :char]
        Char.new :signed => true
      when [:unsigned, :char]
        Char.new :signed => false
      when [:short], [:signed, :short], [:short, :int],
        [:signed, :short, :int]
        Int.new :longness => -1
      when [:unsigned, :short], [:unsigned, :short, :int]
        Int.new :unsigned => true, :longness => -1
      when [:int], [:signed], [:signed, :int]
        Int.new
      when [:unsigned], [:unsigned, :int]
        Int.new :unsigned => true
      when [:long], [:signed, :long], [:long, :int],
        [:signed, :long, :int]
        Int.new :longness => 1
      when [:unsigned, :long], [:unsigned, :long, :int]
        Int.new :longness => 1, :unsigned => true
      when [:long, :long], [:signed, :long, :long],
        [:long, :long, :int], [:signed, :long, :long, :int]
        Int.new :longness => 2
      when [:unsigned, :long, :long], [:unsigned, :long, :long, :int]
        Int.new :longness => 2, :unsigned => true
      when [:float]
        Float.new
      when [:double]
        Float.new :longness => 1
      when [:long, :double]
        Float.new :longness => 2
      when [:_Bool]
        Bool.new
      when [:float, :_Complex]
        Complex.new
      when [:double, :_Complex]
        Complex.new :longness => 1
      when [:long, :double, :_Complex]
        Complex.new :longness => 2
      when [:float, :_Imaginary]
        Imaginary.new
      when [:double, :_Imaginary]
        Imaginary.new :longness => 1
      when [:long, :double, :_Imaginary]
        Imaginary.new :longness => 2
      else
        if type_specs.length == 1 &&
            [CustomType, Struct, Union, Enum].any?{|c| type_specs[0].is_a? c}
          type_specs[0]
        else
          if type_specs == []
            parse_error pos, "no type specifiers given"
          else
            parse_error pos, "invalid type specifier combination: #{type_specs.join(' ')}"
          end
        end
      end
    type.pos ||= pos

    # set type qualifiers
    # 6.7.3p4: type qualifiers can be repeated
    type.const    = specs.any?{|x| x.equal? :const   }
    type.restrict = specs.any?{|x| x.equal? :restrict}
    type.volatile = specs.any?{|x| x.equal? :volatile}

    return type
  end

  def make_parameter(pos, specs, indirect_type, name)
    type = indirect_type
    if type
      type.direct_type = make_direct_type(pos, specs)
    else
      type = make_direct_type(pos, specs)
    end
    [:typedef, :extern, :static, :auto, :inline].each do |sym|
      specs.include? sym and
        parse_error pos, "parameter `#{declarator.name}' declared `#{sym}'"
    end
    return Parameter.new_at(pos, type, name,
                            :register => specs.include?(:register))
  end

  def add_type_quals(type, quals)
    type.const    = quals.include?(:const   )
    type.restrict = quals.include?(:restrict)
    type.volatile = quals.include?(:volatile)
    return type
  end

  #
  # Add te given type as the "most direct" type to the given
  # declarator.  Return the declarator.
  #
  def add_decl_type(declarator, type)
    if declarator.indirect_type
      declarator.indirect_type.direct_type = type
    else
      declarator.indirect_type = type
    end
    return declarator
  end

  def param_list(params, var_args)
    if params.length == 1 &&
        params[0].type.is_a?(Void) &&
        params[0].name.nil?
      return NodeArray[]
    elsif params.empty?
      return nil
    else
      return params
    end
  end

  def parse_error(pos, str)
    raise ParseError, "#{pos}: #{str}"
  end

...end cast.y/module_eval...
##### State transition tables begin ###

clist = [
'99,100,65,103,108,109,120,312,61,110,111,112,113,114,115,116,117,77',
'48,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35',
'36,37,72,281,128,49,38,196,391,123,124,126,127,129,130,131,132,183,277',
'83,273,84,238,288,293,88,196,72,290,38,274,184,372,373,386,239,240,289',
'294,71,241,242,50,147,148,149,150,99,100,65,193,108,109,120,50,281,110',
'111,112,113,114,115,116,117,71,50,10,11,12,13,14,15,16,17,18,19,20,21',
'22,23,24,25,26,31,32,33,34,35,36,37,56,366,128,89,38,50,169,123,124',
'126,127,129,130,131,132,99,100,65,88,108,109,120,88,88,110,111,112,113',
'114,115,116,117,346,349,238,59,68,48,365,195,50,147,148,149,150,38,347',
'239,240,69,178,283,196,312,366,243,244,48,262,88,128,185,38,263,284',
'123,124,126,127,129,130,131,132,99,100,65,49,108,109,120,38,50,110,111',
'112,113,114,115,116,117,281,365,309,440,249,250,49,238,50,147,148,149',
'150,313,379,196,227,439,178,413,239,240,88,196,50,48,50,196,128,187',
'38,191,196,123,124,126,127,129,130,131,132,99,100,65,50,108,109,120',
'416,428,110,111,112,113,114,115,116,117,255,256,192,196,196,197,49,198',
'50,147,148,149,150,228,229,230,231,232,233,234,235,236,237,43,199,48',
'241,242,128,202,38,241,242,123,124,126,127,129,130,131,132,99,100,65',
'50,108,109,120,375,376,110,111,112,113,114,115,116,117,196,196,205,377',
'387,427,49,433,50,147,148,149,150,196,388,196,437,196,206,445,209,447',
'450,43,251,48,196,252,128,196,38,196,196,123,124,126,127,129,130,131',
'132,99,100,65,50,108,109,120,454,253,110,111,112,113,114,115,116,117',
'196,34,35,36,243,244,49,49,50,147,148,149,150,34,35,36,243,244,254,49',
'245,246,247,248,67,48,243,244,128,268,38,243,244,123,124,126,127,129',
'130,131,132,99,100,65,50,108,109,120,249,250,110,111,112,113,114,115',
'116,117,245,246,247,248,271,272,49,275,50,147,148,149,150,245,246,247',
'248,285,296,192,303,307,308,314,315,277,50,50,128,50,38,353,355,123',
'124,126,127,129,130,131,132,99,100,65,50,108,109,120,357,50,110,111',
'112,113,114,115,116,117,378,389,390,251,281,252,49,253,50,147,148,149',
'150,254,395,396,397,398,399,405,406,384,384,423,424,425,426,442,128',
',38,,,123,124,126,127,129,130,131,132,99,100,65,50,108,109,120,,,110',
'111,112,113,114,115,116,117,,,,,,,,,50,147,148,149,150,,,,,,,,,,,,,',
',,128,,38,,,123,124,126,127,129,130,131,132,99,100,65,,108,109,120,',
',110,111,112,113,114,115,116,117,,,,,,,,,50,147,148,149,150,,,,,,,,',
',,,,,,,128,,38,,,123,124,126,127,129,130,131,132,99,100,65,,108,109',
'120,,,110,111,112,113,114,115,116,117,,,,,,,,,50,147,148,149,150,,,',
',,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131,132,99,100,65,,108',
'109,120,,,110,111,112,113,114,115,116,117,,,,,,,,,50,147,148,149,150',
',,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131,132,99,100,65,',
'108,109,120,,,110,111,112,113,114,115,116,117,,,,,,,,,50,147,148,149',
'150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131,132,99,100',
'65,,108,109,120,,,110,111,112,113,114,115,116,117,,,,,,,,,50,147,148',
'149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131,132,99',
'100,65,,108,109,120,,,110,111,112,113,114,115,116,117,,,,,,,,,50,147',
'148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131,132',
'99,100,65,,108,109,120,,,110,111,112,113,114,115,116,117,,,,,,,,,50',
'147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130,131',
'132,99,100,65,,108,109,120,,,110,111,112,113,114,115,116,117,,,,,,,',
',50,147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129,130',
'131,132,99,100,65,,108,109,120,,,110,111,112,113,114,115,116,117,,,',
',,,,,50,147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127,129',
'130,131,132,99,100,65,,108,109,120,,,110,111,112,113,114,115,116,117',
',,,,,,,,50,147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126,127',
'129,130,131,132,99,100,65,,108,109,120,,,110,111,112,113,114,115,116',
'117,,,,,,,,,50,147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124,126',
'127,129,130,131,132,99,100,65,,108,109,120,,,110,111,112,113,114,115',
'116,117,,,,,,,,,50,147,148,149,150,,,,,,,,,,,,,,,,128,,38,,,123,124',
'126,127,129,130,131,132,99,100,65,,108,109,120,,,110,111,112,113,114',
'115,116,117,154,,,,120,,,,50,147,148,149,150,,,,,,,215,,,,,,,,,128,',
'38,,,123,124,126,127,129,130,131,132,,,128,,,,,123,124,126,127,129,130',
'131,132,214,,,,,216,217,218,219,,50,147,148,149,150,65,,,,120,,,,,,50',
'147,148,149,150,154,,,,120,,,15,16,17,18,19,20,21,22,23,24,25,26,31',
'32,33,34,35,36,,,,128,,38,,,123,124,126,127,129,130,131,132,262,,128',
',,263,,123,124,126,127,129,130,131,132,,,,,,,,,,,50,147,148,149,150',
'65,,,,120,,,,,,50,147,148,149,150,,,,,,,,15,16,17,18,19,20,21,22,23',
'24,25,26,31,32,33,34,35,36,,,,128,,38,,,123,124,126,127,129,130,131',
'132,,,,,,,,,65,,,,120,,,,,,,,,,,,,50,147,148,149,150,15,16,17,18,19',
'20,21,22,23,24,25,26,31,32,33,34,35,36,,,,128,,38,,,123,124,126,127',
'129,130,131,132,154,,,,120,,,,,,,,,384,,,,120,,,,,,,,50,147,148,149',
'150,,,,,,,,,,,,,,128,,,,,123,124,126,127,129,130,131,132,128,,,,,123',
'124,126,127,129,130,131,132,154,392,,,120,,,,,,,,50,147,148,149,150',
'154,,,,120,,,,50,147,148,149,150,,,,,,,,,,,,262,,128,,,263,,123,124',
'126,127,129,130,131,132,,,262,,128,,,263,,123,124,126,127,129,130,131',
'132,154,,,,120,,,,50,147,148,149,150,,,154,453,,,120,,,,,,50,147,148',
'149,150,,,,,,,,,,,,,,128,,,,,123,124,126,127,129,130,131,132,262,,128',
',208,263,120,123,124,126,127,129,130,131,132,,,,,,,,,,,50,147,148,149',
'150,,,,,,,,,,,50,147,148,149,150,128,,,,,123,124,126,127,129,130,131',
'132,,,,,,,,,,,,,,,,305,,120,,,,,,,,50,147,148,149,150,10,11,12,13,14',
'15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,,,128,,38,',
',123,124,126,127,129,130,131,132,381,,120,,,,,,,,,,,383,,120,,,,,,,',
',,50,147,148,149,150,,,,,,,,,,,,128,,,,,123,124,126,127,129,130,131',
'132,128,,412,,120,123,124,126,127,129,130,131,132,,,,,,,,,,,,,50,147',
'148,149,150,,,,,,,,,50,147,148,149,150,128,,,,,123,124,126,127,129,130',
'131,132,120,,,,,,,,,,,,,120,,160,,,,,,,,,,50,147,148,149,150,,,,34,35',
'36,,,159,161,,,,,123,124,126,127,129,130,131,132,128,,,,220,123,124',
'126,127,129,130,131,132,,,,,220,,,,,,,,50,147,148,149,150,,,,,,,,,50',
'147,148,149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123',
'124,126,127,129,130,131,132,,,,,224,,,,,,,,50,147,148,149,150,,,,,,',
',,50,147,148,149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,',
',123,124,126,127,129,130,131,132,,,120,,,,,,,,,,50,147,148,149,150,266',
',,,120,,,,50,147,148,149,150,,,,,,34,35,36,,,265,267,,,,120,123,124',
'126,127,129,130,131,132,34,35,36,,,,128,,,,120,123,124,126,127,129,130',
'131,132,,,,,,,50,147,148,149,150,128,,,,120,123,124,126,127,129,130',
'131,132,,50,147,148,149,150,128,,,,120,123,124,126,127,129,130,131,132',
',,,,,,50,147,148,149,150,128,,,,120,123,124,126,127,129,130,131,132',
',50,147,148,149,150,128,,,,120,123,124,126,127,129,130,131,132,,,,,',
',50,147,148,149,150,128,,,,120,123,124,126,127,129,130,131,132,,50,147',
'148,149,150,128,,,,,123,124,126,127,129,130,131,132,,,,,,,50,147,148',
'149,150,128,,,,,123,124,126,127,129,130,131,132,,50,147,148,149,150',
',,,,,,120,318,,,,,,,,,,,,50,147,148,149,150,15,16,17,18,19,20,21,22',
'23,24,25,26,31,32,33,34,35,36,,,,128,,38,,120,123,124,126,127,129,130',
'131,132,,,,,120,,,,,,,,,,,,,,,,,,,,,50,147,148,149,150,128,,,,,123,124',
'126,127,129,130,131,132,128,,,,120,123,124,126,127,129,130,131,132,',
',,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147,148,149,150,128,,,,',
'123,124,126,127,129,130,131,132,128,,,,120,123,124,126,127,129,130,131',
'132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147,148,149,150,128',
',,,,123,124,126,127,129,130,131,132,128,,,,120,123,124,126,127,129,130',
'131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147,148,149,150',
'128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123,124,126,127,129',
'130,131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147,148,149',
'150,128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123,124,126,127',
'129,130,131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147,148',
'149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123,124,126',
'127,129,130,131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50,147',
'148,149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123,124',
'126,127,129,130,131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,,,,50',
'147,148,149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,,120,123',
'124,126,127,129,130,131,132,,,,,120,,,,,,,,50,147,148,149,150,,,,,,',
',,50,147,148,149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,',
'120,123,124,126,127,129,130,131,132,,,,,120,,,,,,,,50,147,148,149,150',
',,,,,,,,50,147,148,149,150,128,,,,,123,124,126,127,129,130,131,132,128',
',,,120,123,124,126,127,129,130,131,132,,,,,120,,,,,,,,50,147,148,149',
'150,,,,,,,,,50,147,148,149,150,128,,,,,123,124,126,127,129,130,131,132',
'128,,,,,123,124,126,127,129,130,131,132,120,,,,,,,,,,,,50,147,148,149',
'150,120,,,,,,,,50,147,148,149,150,,,,34,35,36,,,,128,,,,,123,124,126',
'127,129,130,131,132,,,,368,369,,,,120,123,124,126,127,129,130,131,132',
',,,,,120,,,50,147,148,149,150,,,,,,,,,,,,,50,147,148,149,150,128,,,',
',123,124,126,127,129,130,131,132,401,402,,,,120,123,124,126,127,129',
'130,131,132,,,,,120,415,,,,,,50,147,148,149,150,,,,,,,,,,50,147,148',
'149,150,128,,,,,123,124,126,127,129,130,131,132,128,,,,,123,124,126',
'127,129,130,131,132,120,418,,,,,,,,,,,50,147,148,149,150,,,,,,,,,50',
'147,148,149,150,,,,,,,,,,128,,,,,123,124,126,127,129,130,131,132,,,',
',,,,,,,,,120,,,,,,,,,,,,,50,147,148,149,150,15,16,17,18,19,20,21,22',
'23,24,25,26,31,32,33,34,35,36,,,,128,,38,,120,123,124,126,127,129,130',
'131,132,,,,,120,430,,,,,,,,,,,,,,,,,,,,50,147,148,149,150,128,,,,,123',
'124,126,127,129,130,131,132,128,,,,,123,124,126,127,129,130,131,132',
'120,432,,,,,,,,,,,50,147,148,149,150,120,436,,,,,,,50,147,148,149,150',
',,,,,,,,,128,,,,,123,124,126,127,129,130,131,132,,,,,128,,,,,123,124',
'126,127,129,130,131,132,120,444,,,,,,,50,147,148,149,150,,,,,,,,,,,',
',50,147,148,149,150,,,,,,,,,,128,,,,,123,124,126,127,129,130,131,132',
',,,,,,,,,,,,,,,,,277,364,,,,,,,50,147,148,149,150,10,11,12,13,14,15',
'16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,281,164,49,,38',
',,,,,,,,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33',
'34,35,36,37,,,,50,38,,,,,,,,,,,,,,,39,,,,,,,,,,,,,,,,,,,,,50,10,11,12',
'13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,65,,',
',38,,,,,,,,,,,,67,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26',
'31,32,33,34,35,36,37,65,,,,38,,,,,,,,,,,,,10,11,12,13,14,15,16,17,18',
'19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,173,,,,38,,,,,,,,,,,,,',
',,,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,282,,,,,38',
',,,,,,,,,,,,,,,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36',
'287,,,,,38,,,,,,,,,,,,,,,,15,16,17,18,19,20,21,22,23,24,25,26,31,32',
'33,34,35,36,312,364,,,,38,,,,,,,,10,11,12,13,14,15,16,17,18,19,20,21',
'22,23,24,25,26,31,32,33,34,35,36,37,281,404,49,,38,,,,,,,,,10,11,12',
'13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,,,,,38',
'10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33,34,35,36',
'37,,,,,38,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,32,33',
'34,35,36,37,,,,,38,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26',
'31,32,33,34,35,36,37,,,,,38,10,11,12,13,14,15,16,17,18,19,20,21,22,23',
'24,25,26,31,32,33,34,35,36,37,,,,,38,10,11,12,13,14,15,16,17,18,19,20',
'21,22,23,24,25,26,31,32,33,34,35,36,37,,,,,38,10,11,12,13,14,15,16,17',
'18,19,20,21,22,23,24,25,26,31,32,33,34,35,36,37,,,,359,38,15,16,17,18',
'19,20,21,22,23,24,25,26,31,32,33,34,35,36,,,,,,38,15,16,17,18,19,20',
'21,22,23,24,25,26,31,32,33,34,35,36,,,,,,38,15,16,17,18,19,20,21,22',
'23,24,25,26,31,32,33,34,35,36,,,,,,38,15,16,17,18,19,20,21,22,23,24',
'25,26,31,32,33,34,35,36,,,,,,38,15,16,17,18,19,20,21,22,23,24,25,26',
'31,32,33,34,35,36,,,,,,38' ]
        racc_action_table = arr = ::Array.new(4321, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'65,65,65,65,65,65,65,210,39,65,65,65,65,65,65,65,65,55,45,65,65,65,65',
'65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,65,46,210,65',
'210,65,316,345,65,65,65,65,65,65,65,65,85,276,57,163,58,135,182,186',
'59,345,70,184,114,163,85,289,294,316,135,135,182,186,46,136,136,65,65',
'65,65,65,102,102,102,102,102,102,102,45,276,102,102,102,102,102,102',
'102,102,70,114,102,102,102,102,102,102,102,102,102,102,102,102,102,102',
'102,102,102,102,102,102,102,102,102,102,30,280,102,60,102,276,73,102',
'102,102,102,102,102,102,102,112,112,112,184,112,112,112,289,294,112',
'112,112,112,112,112,112,112,257,260,330,33,42,48,280,107,102,102,102',
'102,102,30,257,330,330,42,80,175,107,311,361,137,137,80,260,84,112,87',
'112,260,175,112,112,112,112,112,112,112,112,187,187,187,48,187,187,187',
'33,30,187,187,187,187,187,187,187,187,311,361,207,419,139,139,80,331',
'112,112,112,112,112,211,304,207,133,419,284,380,331,331,89,211,48,284',
'33,304,187,98,187,100,380,187,187,187,187,187,187,187,187,191,191,191',
'80,191,191,191,382,411,191,191,191,191,191,191,191,191,144,144,101,382',
'411,109,284,110,187,187,187,187,187,133,133,133,133,133,133,133,133',
'133,133,5,111,5,332,332,191,113,191,333,333,191,191,191,191,191,191',
'191,191,192,192,192,284,192,192,192,300,301,192,192,192,192,192,192',
'192,192,300,301,115,302,317,410,5,414,191,191,191,191,191,302,317,410',
'417,414,116,429,119,431,435,66,140,66,417,141,192,429,192,431,435,192',
'192,192,192,192,192,192,192,296,296,296,5,296,296,296,443,142,296,296',
'296,296,296,296,296,296,443,49,49,49,334,334,66,49,192,192,192,192,192',
'74,74,74,335,335,143,74,138,138,138,138,151,69,336,336,296,158,296,337',
'337,296,296,296,296,296,296,296,296,375,375,375,66,375,375,375,340,340',
'375,375,375,375,375,375,375,375,338,338,338,338,161,162,69,165,296,296',
'296,296,296,339,339,339,339,177,188,200,201,203,204,212,213,167,216',
'217,375,263,375,264,267,375,375,375,375,375,375,375,375,376,376,376',
'69,376,376,376,270,274,376,376,376,376,376,376,376,376,303,324,325,341',
'167,342,167,343,375,375,375,375,375,344,351,354,356,362,363,367,369',
'389,390,400,402,403,407,427,376,,376,,,376,376,376,376,376,376,376,376',
'377,377,377,167,377,377,377,,,377,377,377,377,377,377,377,377,,,,,,',
',,376,376,376,376,376,,,,,,,,,,,,,,,,377,,377,,,377,377,377,377,377',
'377,377,377,415,415,415,,415,415,415,,,415,415,415,415,415,415,415,415',
',,,,,,,,377,377,377,377,377,,,,,,,,,,,,,,,,415,,415,,,415,415,415,415',
'415,415,415,415,418,418,418,,418,418,418,,,418,418,418,418,418,418,418',
'418,,,,,,,,,415,415,415,415,415,,,,,,,,,,,,,,,,418,,418,,,418,418,418',
'418,418,418,418,418,426,426,426,,426,426,426,,,426,426,426,426,426,426',
'426,426,,,,,,,,,418,418,418,418,418,,,,,,,,,,,,,,,,426,,426,,,426,426',
'426,426,426,426,426,426,430,430,430,,430,430,430,,,430,430,430,430,430',
'430,430,430,,,,,,,,,426,426,426,426,426,,,,,,,,,,,,,,,,430,,430,,,430',
'430,430,430,430,430,430,430,432,432,432,,432,432,432,,,432,432,432,432',
'432,432,432,432,,,,,,,,,430,430,430,430,430,,,,,,,,,,,,,,,,432,,432',
',,432,432,432,432,432,432,432,432,433,433,433,,433,433,433,,,433,433',
'433,433,433,433,433,433,,,,,,,,,432,432,432,432,432,,,,,,,,,,,,,,,,433',
',433,,,433,433,433,433,433,433,433,433,436,436,436,,436,436,436,,,436',
'436,436,436,436,436,436,436,,,,,,,,,433,433,433,433,433,,,,,,,,,,,,',
',,,436,,436,,,436,436,436,436,436,436,436,436,437,437,437,,437,437,437',
',,437,437,437,437,437,437,437,437,,,,,,,,,436,436,436,436,436,,,,,,',
',,,,,,,,,437,,437,,,437,437,437,437,437,437,437,437,444,444,444,,444',
'444,444,,,444,444,444,444,444,444,444,444,,,,,,,,,437,437,437,437,437',
',,,,,,,,,,,,,,,444,,444,,,444,444,444,444,444,444,444,444,445,445,445',
',445,445,445,,,445,445,445,445,445,445,445,445,,,,,,,,,444,444,444,444',
'444,,,,,,,,,,,,,,,,445,,445,,,445,445,445,445,445,445,445,445,447,447',
'447,,447,447,447,,,447,447,447,447,447,447,447,447,,,,,,,,,445,445,445',
'445,445,,,,,,,,,,,,,,,,447,,447,,,447,447,447,447,447,447,447,447,450',
'450,450,,450,450,450,,,450,450,450,450,450,450,450,450,,,,,,,,,447,447',
'447,447,447,,,,,,,,,,,,,,,,450,,450,,,450,450,450,450,450,450,450,450',
'454,454,454,,454,454,454,,,454,454,454,454,454,454,454,454,67,,,,67',
',,,450,450,450,450,450,,,,,,,122,,,,,,,,,454,,454,,,454,454,454,454',
'454,454,454,454,,,67,,,,,67,67,67,67,67,67,67,67,122,,,,,122,122,122',
'122,,454,454,454,454,454,120,,,,120,,,,,,67,67,67,67,67,154,,,,154,',
',120,120,120,120,120,120,120,120,120,120,120,120,120,120,120,120,120',
'120,,,,120,,120,,,120,120,120,120,120,120,120,120,154,,154,,,154,,154',
'154,154,154,154,154,154,154,,,,,,,,,,,120,120,120,120,120,220,,,,220',
',,,,,154,154,154,154,154,,,,,,,,220,220,220,220,220,220,220,220,220',
'220,220,220,220,220,220,220,220,220,,,,220,,220,,,220,220,220,220,220',
'220,220,220,,,,,,,,,224,,,,224,,,,,,,,,,,,,220,220,220,220,220,224,224',
'224,224,224,224,224,224,224,224,224,224,224,224,224,224,224,224,,,,224',
',224,,,224,224,224,224,224,224,224,224,258,,,,258,,,,,,,,,315,,,,315',
',,,,,,,224,224,224,224,224,,,,,,,,,,,,,,258,,,,,258,258,258,258,258',
'258,258,258,315,,,,,315,315,315,315,315,315,315,315,347,347,,,347,,',
',,,,,258,258,258,258,258,384,,,,384,,,,315,315,315,315,315,,,,,,,,,',
',,347,,347,,,347,,347,347,347,347,347,347,347,347,,,384,,384,,,384,',
'384,384,384,384,384,384,384,384,393,,,,393,,,,347,347,347,347,347,,',
'439,439,,,439,,,,,,384,384,384,384,384,,,,,,,,,,,,,,393,,,,,393,393',
'393,393,393,393,393,393,439,,439,,117,439,117,439,439,439,439,439,439',
'439,439,,,,,,,,,,,393,393,393,393,393,,,,,,,,,,,439,439,439,439,439',
'117,,,,,117,117,117,117,117,117,117,117,,,,,,,,,,,,,,,,202,,202,,,,',
',,,117,117,117,117,117,202,202,202,202,202,202,202,202,202,202,202,202',
'202,202,202,202,202,202,202,202,202,202,202,202,,,202,,202,,,202,202',
'202,202,202,202,202,202,305,,305,,,,,,,,,,,306,,306,,,,,,,,,,202,202',
'202,202,202,,,,,,,,,,,,305,,,,,305,305,305,305,305,305,305,305,306,',
'379,,379,306,306,306,306,306,306,306,306,,,,,,,,,,,,,305,305,305,305',
'305,,,,,,,,,306,306,306,306,306,379,,,,,379,379,379,379,379,379,379',
'379,71,,,,,,,,,,,,,99,,71,,,,,,,,,,379,379,379,379,379,,,,71,71,71,',
',71,71,,,,,71,71,71,71,71,71,71,71,99,,,,123,99,99,99,99,99,99,99,99',
',,,,124,,,,,,,,71,71,71,71,71,,,,,,,,,99,99,99,99,99,123,,,,,123,123',
'123,123,123,123,123,123,124,,,,125,124,124,124,124,124,124,124,124,',
',,,126,,,,,,,,123,123,123,123,123,,,,,,,,,124,124,124,124,124,125,,',
',,125,125,125,125,125,125,125,125,126,,,,,126,126,126,126,126,126,126',
'126,,,157,,,,,,,,,,125,125,125,125,125,157,,,,160,,,,126,126,126,126',
'126,,,,,,157,157,157,,,157,157,,,,178,157,157,157,157,157,157,157,157',
'160,160,160,,,,160,,,,185,160,160,160,160,160,160,160,160,,,,,,,157',
'157,157,157,157,178,,,,196,178,178,178,178,178,178,178,178,,160,160',
'160,160,160,185,,,,197,185,185,185,185,185,185,185,185,,,,,,,178,178',
'178,178,178,196,,,,198,196,196,196,196,196,196,196,196,,185,185,185',
'185,185,197,,,,199,197,197,197,197,197,197,197,197,,,,,,,196,196,196',
'196,196,198,,,,214,198,198,198,198,198,198,198,198,,197,197,197,197',
'197,199,,,,,199,199,199,199,199,199,199,199,,,,,,,198,198,198,198,198',
'214,,,,,214,214,214,214,214,214,214,214,,199,199,199,199,199,,,,,,,215',
'215,,,,,,,,,,,,214,214,214,214,214,215,215,215,215,215,215,215,215,215',
'215,215,215,215,215,215,215,215,215,,,,215,,215,,226,215,215,215,215',
'215,215,215,215,,,,,238,,,,,,,,,,,,,,,,,,,,,215,215,215,215,215,226',
',,,,226,226,226,226,226,226,226,226,238,,,,239,238,238,238,238,238,238',
'238,238,,,,,240,,,,,,,,226,226,226,226,226,,,,,,,,,238,238,238,238,238',
'239,,,,,239,239,239,239,239,239,239,239,240,,,,241,240,240,240,240,240',
'240,240,240,,,,,242,,,,,,,,239,239,239,239,239,,,,,,,,,240,240,240,240',
'240,241,,,,,241,241,241,241,241,241,241,241,242,,,,243,242,242,242,242',
'242,242,242,242,,,,,244,,,,,,,,241,241,241,241,241,,,,,,,,,242,242,242',
'242,242,243,,,,,243,243,243,243,243,243,243,243,244,,,,245,244,244,244',
'244,244,244,244,244,,,,,246,,,,,,,,243,243,243,243,243,,,,,,,,,244,244',
'244,244,244,245,,,,,245,245,245,245,245,245,245,245,246,,,,247,246,246',
'246,246,246,246,246,246,,,,,248,,,,,,,,245,245,245,245,245,,,,,,,,,246',
'246,246,246,246,247,,,,,247,247,247,247,247,247,247,247,248,,,,249,248',
'248,248,248,248,248,248,248,,,,,250,,,,,,,,247,247,247,247,247,,,,,',
',,,248,248,248,248,248,249,,,,,249,249,249,249,249,249,249,249,250,',
',,251,250,250,250,250,250,250,250,250,,,,,252,,,,,,,,249,249,249,249',
'249,,,,,,,,,250,250,250,250,250,251,,,,,251,251,251,251,251,251,251',
'251,252,,,,253,252,252,252,252,252,252,252,252,,,,,254,,,,,,,,251,251',
'251,251,251,,,,,,,,,252,252,252,252,252,253,,,,,253,253,253,253,253',
'253,253,253,254,,,,255,254,254,254,254,254,254,254,254,,,,,256,,,,,',
',,253,253,253,253,253,,,,,,,,,254,254,254,254,254,255,,,,,255,255,255',
'255,255,255,255,255,256,,,,262,256,256,256,256,256,256,256,256,,,,,266',
',,,,,,,255,255,255,255,255,,,,,,,,,256,256,256,256,256,262,,,,,262,262',
'262,262,262,262,262,262,266,,,,,266,266,266,266,266,266,266,266,269',
',,,,,,,,,,,262,262,262,262,262,281,,,,,,,,266,266,266,266,266,,,,269',
'269,269,,,,269,,,,,269,269,269,269,269,269,269,269,,,,281,281,,,,285',
'281,281,281,281,281,281,281,281,,,,,,365,,,269,269,269,269,269,,,,,',
',,,,,,,281,281,281,281,281,285,,,,,285,285,285,285,285,285,285,285,365',
'365,,,,378,365,365,365,365,365,365,365,365,,,,,381,381,,,,,,285,285',
'285,285,285,,,,,,,,,,365,365,365,365,365,378,,,,,378,378,378,378,378',
'378,378,378,381,,,,,381,381,381,381,381,381,381,381,383,383,,,,,,,,',
',,378,378,378,378,378,,,,,,,,,381,381,381,381,381,,,,,,,,,,383,,,,,383',
'383,383,383,383,383,383,383,,,,,,,,,,,,,388,,,,,,,,,,,,,383,383,383',
'383,383,388,388,388,388,388,388,388,388,388,388,388,388,388,388,388',
'388,388,388,,,,388,,388,,391,388,388,388,388,388,388,388,388,,,,,412',
'412,,,,,,,,,,,,,,,,,,,,388,388,388,388,388,391,,,,,391,391,391,391,391',
'391,391,391,412,,,,,412,412,412,412,412,412,412,412,413,413,,,,,,,,',
',,391,391,391,391,391,416,416,,,,,,,412,412,412,412,412,,,,,,,,,,413',
',,,,413,413,413,413,413,413,413,413,,,,,416,,,,,416,416,416,416,416',
'416,416,416,428,428,,,,,,,413,413,413,413,413,,,,,,,,,,,,,416,416,416',
'416,416,,,,,,,,,,428,,,,,428,428,428,428,428,428,428,428,,,,,,,,,,,',
',,,,,,277,277,,,,,,,428,428,428,428,428,277,277,277,277,277,277,277',
'277,277,277,277,277,277,277,277,277,277,277,277,277,277,277,277,277',
'277,72,277,,277,,,,,,,,,72,72,72,72,72,72,72,72,72,72,72,72,72,72,72',
'72,72,72,72,72,72,72,72,72,,,,277,72,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,',
',,,,,72,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,41,,,,1,,,,',
',,,,,,,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41,41',
'41,41,41,41,62,,,,41,,,,,,,,,,,,,62,62,62,62,62,62,62,62,62,62,62,62',
'62,62,62,62,62,62,62,62,62,62,62,62,78,,,,62,,,,,,,,,,,,,,,,,78,78,78',
'78,78,78,78,78,78,78,78,78,78,78,78,78,78,78,172,,,,,78,,,,,,,,,,,,',
',,,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172',
'172,181,,,,,172,,,,,,,,,,,,,,,,181,181,181,181,181,181,181,181,181,181',
'181,181,181,181,181,181,181,181,312,312,,,,181,,,,,,,,312,312,312,312',
'312,312,312,312,312,312,312,312,312,312,312,312,312,312,312,312,312',
'312,312,312,312,366,312,,312,,,,,,,,,366,366,366,366,366,366,366,366',
'366,366,366,366,366,366,366,366,366,366,366,366,366,366,366,366,,,,',
'366,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,,,,,0,6,6,6,6,6',
'6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,,,,,6,7,7,7,7,7,7,7,7,7,7,7,7',
'7,7,7,7,7,7,7,7,7,7,7,7,,,,,7,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8,8',
'8,8,8,8,8,,,,,8,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,9,,,,',
'9,275,275,275,275,275,275,275,275,275,275,275,275,275,275,275,275,275',
'275,275,275,275,275,275,275,,,,275,275,56,56,56,56,56,56,56,56,56,56',
'56,56,56,56,56,56,56,56,,,,,,56,77,77,77,77,77,77,77,77,77,77,77,77',
'77,77,77,77,77,77,,,,,,77,81,81,81,81,81,81,81,81,81,81,81,81,81,81',
'81,81,81,81,,,,,,81,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82',
'82,82,,,,,,82,83,83,83,83,83,83,83,83,83,83,83,83,83,83,83,83,83,83',
',,,,,83' ]
        racc_action_check = arr = ::Array.new(4321, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
  4005,  3703,   nil,   nil,   nil,   295,  4034,  4063,  4092,  4121,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   126,   nil,   nil,   161,   nil,   nil,   nil,   nil,   nil,     8,
   nil,  3744,   160,   nil,   nil,     9,    34,   nil,   159,   354,
   nil,   nil,   nil,   nil,   nil,    12,  4174,    55,    57,   -20,
   129,   nil,  3785,   nil,   nil,    -3,   353,  1318,   nil,   411,
    59,  2026,  3638,   127,   366,   nil,   nil,  4198,  3825,   nil,
   179,  4222,  4246,  4270,   104,    52,   nil,   171,   nil,   157,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   248,  2039,
   250,   279,    85,   nil,   nil,   nil,   nil,   163,   nil,   275,
   277,   294,   143,   299,    20,   332,   348,  1795,   nil,   267,
  1389,   nil,  1333,  2082,  2095,  2138,  2151,   nil,   nil,   nil,
   nil,   nil,   nil,   216,   nil,    15,    24,   123,   350,   156,
   305,   293,   314,   340,   205,   nil,   nil,   nil,   nil,   nil,
   nil,   398,   nil,   nil,  1404,   nil,   nil,  2205,   377,   nil,
  2224,   410,   448,    51,   nil,   440,   nil,   469,   nil,   nil,
   nil,   nil,  3864,   nil,   nil,   175,   nil,   468,  2248,   nil,
   nil,  3903,    58,   nil,    63,  2267,    59,   201,   469,   nil,
   nil,   259,   317,   nil,   nil,   nil,  2291,  2310,  2334,  2353,
   470,   460,  1864,   467,   468,   nil,   nil,   216,   nil,   nil,
    -2,   224,   466,   467,  2377,  2441,   393,   394,   nil,   nil,
  1475,   nil,   nil,   nil,  1539,   nil,  2484,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,  2497,  2540,
  2553,  2596,  2609,  2652,  2665,  2708,  2721,  2764,  2777,  2820,
  2833,  2876,  2889,  2932,  2945,  2988,  3001,   157,  1595,   nil,
   143,   nil,  3044,   396,   437,   nil,  3057,   438,   nil,  3109,
   454,   nil,   nil,   nil,   416,  4150,    50,  3601,   nil,   nil,
   123,  3126,   nil,   nil,   237,  3169,   nil,   nil,   nil,    67,
   nil,   nil,   nil,   nil,    68,   nil,   375,   nil,   nil,   nil,
   317,   318,   330,   502,   228,  1918,  1931,   nil,   nil,   nil,
   nil,   175,  3939,   nil,   nil,  1608,    28,   331,   nil,   nil,
   nil,   nil,   nil,   nil,   502,   503,   nil,   nil,   nil,   nil,
   117,   180,   248,   253,   336,   348,   358,   363,   388,   401,
   374,   458,   445,   446,   451,    47,   nil,  1664,   nil,   nil,
   nil,   478,   nil,   nil,   479,   nil,   480,   nil,   nil,   nil,
   nil,   176,   518,   519,   nil,  3183,  3976,   483,   nil,   484,
   nil,   nil,   nil,   nil,   nil,   433,   491,   549,  3226,  1974,
   233,  3239,   262,  3291,  1681,   nil,   nil,   nil,  3355,   527,
   528,  3398,   nil,  1737,   nil,   nil,   nil,   nil,   nil,   nil,
   487,   nil,   488,   526,   nil,   nil,   nil,   526,   nil,   nil,
   332,   263,  3411,  3463,   334,   607,  3480,   343,   665,   218,
   nil,   nil,   nil,   nil,   nil,   nil,   723,   531,  3532,   346,
   781,   348,   839,   897,   nil,   349,   955,  1013,   nil,  1752,
   nil,   nil,   nil,   375,  1071,  1129,   nil,  1187,   nil,   nil,
  1245,   nil,   nil,   nil,  1303,   nil,   nil,   nil,   nil,   nil ]

racc_action_default = [
  -265,  -265,    -1,    -3,    -4,  -265,   -53,   -55,   -57,   -59,
   -64,   -65,   -66,   -67,   -68,   -69,   -70,   -71,   -72,   -73,
   -74,   -75,   -76,   -77,   -78,   -79,   -80,   -81,   -82,   -83,
  -265,   -89,   -90,  -265,  -115,  -116,  -117,  -118,  -166,  -265,
    -2,   -62,  -265,   -51,   -60,  -265,  -120,  -121,  -265,  -136,
  -258,   -52,   -54,   -56,   -58,   -86,  -265,   -88,  -107,  -265,
  -110,   460,  -265,    -6,    -7,  -265,  -265,  -265,   -50,  -265,
  -119,  -265,  -265,  -265,  -135,  -138,  -139,  -265,  -265,   -91,
  -265,   -95,   -97,  -265,  -265,  -265,  -111,  -113,  -262,  -265,
    -5,    -8,    -9,   -10,   -11,   -12,   -13,   -14,  -179,  -265,
  -265,   -83,  -265,   -20,   -21,   -23,   -24,  -265,   -26,  -265,
  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -180,  -181,
  -265,  -184,  -198,  -265,  -265,  -265,  -265,  -204,  -205,  -206,
  -207,  -208,  -209,  -210,  -212,  -216,  -219,  -222,  -227,  -230,
  -232,  -234,  -236,  -238,  -240,  -242,  -255,  -259,  -260,  -261,
  -264,   -62,   -63,  -167,  -265,  -179,   -61,  -265,  -265,  -126,
  -265,  -205,  -265,  -265,  -134,  -141,  -143,  -147,  -148,  -122,
  -137,  -140,  -265,   -85,   -92,  -265,   -98,  -100,  -265,   -94,
   -96,  -265,  -265,  -104,  -265,  -265,  -265,  -265,  -265,  -210,
  -257,  -265,  -265,   -19,   -22,   -25,  -265,  -265,  -265,  -265,
  -265,  -265,  -265,  -265,  -265,   -45,   -46,  -265,   -48,  -263,
  -151,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -190,  -191,
  -265,  -199,  -200,  -201,  -265,  -202,  -265,  -244,  -245,  -246,
  -247,  -248,  -249,  -250,  -251,  -252,  -253,  -254,  -265,  -265,
  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,
  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -171,
  -265,  -175,  -265,  -265,  -265,  -124,  -265,  -205,  -125,  -265,
  -265,  -131,  -132,  -133,  -265,  -265,  -152,  -265,  -145,  -146,
  -154,  -265,   -84,   -93,  -265,  -265,  -102,   -87,  -103,  -265,
  -106,  -112,  -114,  -108,  -265,   -15,  -265,   -17,   -18,  -256,
  -265,  -265,  -265,  -265,  -265,  -265,  -265,   -44,   -49,   -47,
  -150,  -152,  -265,  -182,  -183,  -265,  -265,  -265,  -187,  -194,
  -196,  -197,  -188,  -189,  -265,  -265,  -243,  -213,  -214,  -215,
  -217,  -218,  -220,  -221,  -223,  -224,  -225,  -226,  -228,  -229,
  -231,  -233,  -235,  -237,  -239,  -265,  -168,  -265,  -170,  -174,
  -176,  -265,  -178,  -123,  -265,  -130,  -265,  -128,  -149,  -142,
  -144,  -153,  -265,  -265,  -165,  -265,  -265,  -265,  -159,  -205,
   -99,  -101,  -105,  -109,   -16,  -265,  -265,  -265,  -265,  -265,
  -265,  -265,  -265,  -265,  -265,  -211,  -185,  -186,  -265,  -265,
  -203,  -265,  -169,  -265,  -173,  -177,  -129,  -127,  -155,  -164,
  -265,  -157,  -205,  -265,  -163,  -158,  -161,   -27,   -29,   -30,
  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,  -265,
  -195,  -241,  -172,  -156,  -160,  -162,  -265,  -265,  -265,  -265,
  -265,  -265,  -265,  -265,   -39,  -265,  -265,  -265,   -43,  -265,
  -192,   -28,   -31,  -265,  -265,  -265,   -35,  -265,   -37,   -38,
  -265,   -41,   -42,  -193,  -265,   -33,   -34,   -36,   -40,   -32 ]

racc_goto_table = [
    47,   152,    57,   162,    75,    60,    70,   319,   153,     5,
     5,   257,   158,   176,   174,    51,    52,    53,    54,    76,
   393,   291,   279,   361,    74,    55,    82,     1,    58,   170,
    62,   104,   179,   180,   190,   223,    63,   101,   102,    41,
    47,    76,   188,    47,   171,    64,   157,    82,    82,    78,
   156,    82,    82,    82,   213,    85,   175,    90,   361,   163,
    98,    47,     2,    40,    47,   310,    91,   168,   194,   105,
   172,   210,   330,   331,   101,    47,   181,   332,   333,   360,
   182,   167,   338,   339,   200,   186,   204,   350,   317,   340,
    82,   334,   335,   336,   337,   153,   341,    98,   264,   342,
   151,   270,   343,   151,   344,   226,   105,    98,   174,   203,
   nil,   nil,   393,   190,   177,   212,   nil,   174,   nil,    81,
   190,   286,   276,   nil,   nil,   nil,   291,   171,   292,   nil,
    76,   291,   nil,   nil,   nil,   269,   nil,   299,   nil,   nil,
    81,    81,    82,   nil,    81,    81,    81,   nil,   327,   328,
   329,    82,   nil,   nil,   324,   nil,   320,   nil,   325,   200,
   nil,   nil,    47,   200,   200,   311,   210,   326,   nil,   nil,
   201,   210,   nil,   nil,   nil,   210,   nil,   nil,   nil,   nil,
   420,   nil,    98,    81,   nil,    82,    98,    98,   nil,   nil,
    82,   nil,   348,   nil,    82,   nil,   nil,   190,   nil,   153,
   nil,   278,   nil,   nil,   nil,   351,   306,   354,   nil,   nil,
   356,   322,   323,   nil,   nil,   212,   nil,   370,   nil,   212,
   190,   nil,   367,   nil,   nil,   385,   nil,   nil,   371,   nil,
   nil,   nil,   276,   nil,   nil,    81,   nil,    70,   nil,   171,
   nil,   419,   nil,   nil,    81,   295,   nil,   nil,   nil,   297,
   298,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   352,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   311,   200,   358,
   nil,    47,    47,   nil,   nil,   nil,   nil,   nil,    81,    47,
   nil,   394,   nil,    81,   167,   nil,   167,    81,   153,   nil,
   nil,    98,   nil,   nil,   nil,   nil,   nil,   403,   nil,   nil,
   nil,   nil,   nil,   nil,   207,   nil,   400,   211,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   177,   nil,
   nil,   167,   nil,   nil,   nil,   153,   421,   422,   nil,   320,
   nil,   nil,   nil,   nil,   153,   nil,   nil,   nil,   nil,   210,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   200,   200,   200,
   nil,   nil,   nil,   nil,   374,   nil,   nil,   nil,    82,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    98,    98,    98,   394,   nil,   167,   nil,   nil,   nil,   nil,
   153,   nil,   nil,   nil,   300,   301,   302,   200,   nil,   304,
   200,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   200,   nil,
   nil,   316,   200,   nil,   200,   200,   nil,   211,   200,   200,
    98,   211,   nil,    98,   nil,   nil,   200,   200,   nil,   200,
   nil,    98,   200,   nil,   nil,    98,   200,    98,    98,   nil,
   nil,    98,    98,   407,   408,   409,   nil,   nil,   189,    98,
    98,   nil,    98,   345,   nil,    98,   nil,   nil,   nil,    98,
   nil,    81,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   221,   222,   189,   225,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   434,   nil,   nil,   438,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   441,   nil,   nil,   nil,   446,   nil,
   448,   449,   380,   382,   451,   452,   nil,   nil,   nil,   nil,
   nil,   nil,   455,   456,   nil,   457,   nil,   nil,   458,   nil,
   nil,   nil,   459,   nil,   nil,   nil,   nil,   189,   nil,   nil,
   nil,   nil,   nil,   nil,   189,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   410,   411,   nil,   414,   nil,
   417,   nil,   nil,   nil,   nil,   nil,   nil,   189,   189,   189,
   189,   189,   189,   189,   189,   189,   189,   189,   189,   189,
   189,   189,   189,   189,   189,   nil,   nil,   nil,   nil,   429,
   431,   189,   nil,   435,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   443,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   189,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   189,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   189 ]

racc_goto_check = [
    15,    27,    17,    43,    39,    17,    40,    59,    42,     5,
     5,    50,    42,    35,    32,     5,     5,     5,     5,    24,
    51,    37,    47,    49,    41,    15,    24,     1,    15,    39,
     7,    19,    33,    33,    73,    62,     8,    17,    18,     6,
    15,    24,    16,    15,    24,     4,    41,    24,    24,    31,
    26,    24,    24,    24,    48,    36,    34,     8,    49,    44,
    15,    15,     2,     2,    15,    47,     4,    15,    19,     4,
    31,    33,    63,    63,    17,    15,    31,    64,    64,    46,
    36,     5,    66,    66,    17,    36,    17,    53,    58,    67,
    24,    65,    65,    65,    65,    42,    68,    15,    42,    69,
     6,    42,    70,     6,    71,    74,     4,    15,    32,    15,
   nil,   nil,    51,    73,     6,     8,   nil,    32,   nil,    23,
    73,    16,    39,   nil,   nil,   nil,    37,    24,    16,   nil,
    24,    37,   nil,   nil,   nil,    41,   nil,    42,   nil,   nil,
    23,    23,    24,   nil,    23,    23,    23,   nil,    62,    62,
    62,    24,   nil,   nil,    48,   nil,    42,   nil,    48,    17,
   nil,   nil,    15,    17,    17,    39,    33,    42,   nil,   nil,
     9,    33,   nil,   nil,   nil,    33,   nil,   nil,   nil,   nil,
    59,   nil,    15,    23,   nil,    24,    15,    15,   nil,   nil,
    24,   nil,    27,   nil,    24,   nil,   nil,    73,   nil,    42,
   nil,     6,   nil,   nil,   nil,    16,     4,    42,   nil,   nil,
    42,    15,    15,   nil,   nil,     8,   nil,    35,   nil,     8,
    73,   nil,    42,   nil,   nil,    62,   nil,   nil,    16,   nil,
   nil,   nil,    39,   nil,   nil,    23,   nil,    40,   nil,    24,
   nil,    50,   nil,   nil,    23,     9,   nil,   nil,   nil,     9,
     9,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    39,    17,    15,
   nil,    15,    15,   nil,   nil,   nil,   nil,   nil,    23,    15,
   nil,    27,   nil,    23,     5,   nil,     5,    23,    42,   nil,
   nil,    15,   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,
   nil,   nil,   nil,   nil,    20,   nil,    42,    20,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     6,   nil,
   nil,     5,   nil,   nil,   nil,    42,    73,    27,   nil,    42,
   nil,   nil,   nil,   nil,    42,   nil,   nil,   nil,   nil,    33,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,    17,    17,
   nil,   nil,   nil,   nil,     9,   nil,   nil,   nil,    24,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    15,    15,    15,    27,   nil,     5,   nil,   nil,   nil,   nil,
    42,   nil,   nil,   nil,    20,    20,    20,    17,   nil,    20,
    17,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,
   nil,    20,    17,   nil,    17,    17,   nil,    20,    17,    17,
    15,    20,   nil,    15,   nil,   nil,    17,    17,   nil,    17,
   nil,    15,    17,   nil,   nil,    15,    17,    15,    15,   nil,
   nil,    15,    15,     9,     9,     9,   nil,   nil,    60,    15,
    15,   nil,    15,    20,   nil,    15,   nil,   nil,   nil,    15,
   nil,    23,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    60,    60,    60,    60,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,     9,   nil,   nil,     9,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,     9,   nil,   nil,   nil,     9,   nil,
     9,     9,    20,    20,     9,     9,   nil,   nil,   nil,   nil,
   nil,   nil,     9,     9,   nil,     9,   nil,   nil,     9,   nil,
   nil,   nil,     9,   nil,   nil,   nil,   nil,    60,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,    20,   nil,    20,   nil,
    20,   nil,   nil,   nil,   nil,   nil,   nil,    60,    60,    60,
    60,    60,    60,    60,    60,    60,    60,    60,    60,    60,
    60,    60,    60,    60,    60,   nil,   nil,   nil,   nil,    20,
    20,    60,   nil,    20,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    20,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    60,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    60 ]

racc_goto_pointer = [
   nil,    27,    62,   nil,     4,     9,    34,   -11,    -5,    58,
   nil,   nil,   nil,   nil,   nil,    -5,   -57,   -28,   -27,   -34,
   187,   nil,   nil,    63,   -30,   nil,   -19,   -66,   nil,   nil,
   nil,    -7,   -64,   -49,   -24,   -67,    -4,  -163,   nil,   -45,
   -39,   -25,   -59,   -69,   -13,   nil,  -196,  -145,   -66,  -253,
  -143,  -327,   nil,  -173,   nil,   nil,   nil,   nil,  -127,  -208,
   339,   nil,   -90,  -169,  -166,  -154,  -167,  -162,  -156,  -154,
  -152,  -151,   nil,   -65,   -28 ]

racc_goto_default = [
   nil,   nil,   nil,     3,     4,    66,    73,   nil,    93,   106,
    92,    94,    95,    96,    97,   155,   nil,    29,   nil,   nil,
   107,    42,     6,     7,     8,     9,    44,   259,    27,    28,
    30,   nil,    79,    80,   nil,   nil,   nil,    86,    87,    45,
    46,   nil,   146,   363,   nil,   165,   166,   362,   321,   280,
   nil,   258,   260,   261,   121,   118,   119,   122,   nil,   nil,
   133,   125,   134,   135,   136,   137,   138,   139,   140,   141,
   142,   143,   144,   145,   nil ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 92, :_reduce_1,
  2, 92, :_reduce_2,
  1, 93, :_reduce_3,
  1, 93, :_reduce_4,
  4, 94, :_reduce_5,
  3, 94, :_reduce_6,
  1, 98, :_reduce_7,
  2, 98, :_reduce_8,
  1, 100, :_reduce_9,
  1, 100, :_reduce_10,
  1, 100, :_reduce_11,
  1, 100, :_reduce_12,
  1, 100, :_reduce_13,
  1, 100, :_reduce_14,
  3, 101, :_reduce_15,
  4, 101, :_reduce_16,
  3, 101, :_reduce_17,
  3, 101, :_reduce_18,
  3, 99, :_reduce_19,
  2, 99, :_reduce_20,
  1, 109, :_reduce_21,
  2, 109, :_reduce_22,
  1, 110, :_reduce_23,
  1, 110, :_reduce_24,
  2, 102, :_reduce_25,
  1, 102, :_reduce_26,
  5, 103, :_reduce_27,
  7, 103, :_reduce_28,
  5, 103, :_reduce_29,
  5, 104, :_reduce_30,
  7, 104, :_reduce_31,
  9, 104, :_reduce_32,
  8, 104, :_reduce_33,
  8, 104, :_reduce_34,
  7, 104, :_reduce_35,
  8, 104, :_reduce_36,
  7, 104, :_reduce_37,
  7, 104, :_reduce_38,
  6, 104, :_reduce_39,
  8, 104, :_reduce_40,
  7, 104, :_reduce_41,
  7, 104, :_reduce_42,
  6, 104, :_reduce_43,
  3, 105, :_reduce_44,
  2, 105, :_reduce_45,
  2, 105, :_reduce_46,
  3, 105, :_reduce_47,
  2, 105, :_reduce_48,
  3, 105, :_reduce_49,
  3, 95, :_reduce_50,
  2, 95, :_reduce_51,
  2, 96, :_reduce_52,
  1, 96, :_reduce_53,
  2, 96, :_reduce_54,
  1, 96, :_reduce_55,
  2, 96, :_reduce_56,
  1, 96, :_reduce_57,
  2, 96, :_reduce_58,
  1, 96, :_reduce_59,
  1, 112, :_reduce_60,
  3, 112, :_reduce_61,
  1, 117, :_reduce_62,
  3, 117, :_reduce_63,
  1, 113, :_reduce_64,
  1, 113, :_reduce_65,
  1, 113, :_reduce_66,
  1, 113, :_reduce_67,
  1, 113, :_reduce_68,
  1, 114, :_reduce_69,
  1, 114, :_reduce_70,
  1, 114, :_reduce_71,
  1, 114, :_reduce_72,
  1, 114, :_reduce_73,
  1, 114, :_reduce_74,
  1, 114, :_reduce_75,
  1, 114, :_reduce_76,
  1, 114, :_reduce_77,
  1, 114, :_reduce_78,
  1, 114, :_reduce_79,
  1, 114, :_reduce_80,
  1, 114, :_reduce_81,
  1, 114, :_reduce_82,
  1, 114, :_reduce_83,
  5, 119, :_reduce_84,
  4, 119, :_reduce_85,
  2, 119, :_reduce_86,
  5, 119, :_reduce_87,
  2, 119, :_reduce_88,
  1, 121, :_reduce_89,
  1, 121, :_reduce_90,
  1, 122, :_reduce_91,
  2, 122, :_reduce_92,
  3, 123, :_reduce_93,
  2, 124, :_reduce_94,
  1, 124, :_reduce_95,
  2, 124, :_reduce_96,
  1, 124, :_reduce_97,
  1, 125, :_reduce_98,
  3, 125, :_reduce_99,
  1, 126, :_reduce_100,
  3, 126, :_reduce_101,
  2, 126, :_reduce_102,
  5, 120, :_reduce_103,
  4, 120, :_reduce_104,
  6, 120, :_reduce_105,
  5, 120, :_reduce_106,
  2, 120, :_reduce_107,
  5, 120, :_reduce_108,
  6, 120, :_reduce_109,
  2, 120, :_reduce_110,
  1, 127, :_reduce_111,
  3, 127, :_reduce_112,
  1, 128, :_reduce_113,
  3, 128, :_reduce_114,
  1, 115, :_reduce_115,
  1, 115, :_reduce_116,
  1, 115, :_reduce_117,
  1, 116, :_reduce_118,
  2, 97, :_reduce_119,
  1, 97, :_reduce_120,
  1, 131, :_reduce_121,
  3, 131, :_reduce_122,
  5, 131, :_reduce_123,
  4, 131, :_reduce_124,
  4, 131, :_reduce_125,
  3, 131, :_reduce_126,
  6, 131, :_reduce_127,
  5, 131, :_reduce_128,
  6, 131, :_reduce_129,
  5, 131, :_reduce_130,
  4, 131, :_reduce_131,
  4, 131, :_reduce_132,
  4, 131, :_reduce_133,
  3, 131, :_reduce_134,
  2, 130, :_reduce_135,
  1, 130, :_reduce_136,
  3, 130, :_reduce_137,
  2, 130, :_reduce_138,
  1, 132, :_reduce_139,
  2, 132, :_reduce_140,
  1, 134, :_reduce_141,
  3, 134, :_reduce_142,
  1, 136, :_reduce_143,
  3, 136, :_reduce_144,
  2, 137, :_reduce_145,
  2, 137, :_reduce_146,
  1, 137, :_reduce_147,
  1, 135, :_reduce_148,
  3, 135, :_reduce_149,
  2, 139, :_reduce_150,
  1, 139, :_reduce_151,
  1, 138, :_reduce_152,
  2, 138, :_reduce_153,
  1, 138, :_reduce_154,
  3, 140, :_reduce_155,
  4, 140, :_reduce_156,
  3, 140, :_reduce_157,
  3, 140, :_reduce_158,
  2, 140, :_reduce_159,
  4, 140, :_reduce_160,
  3, 140, :_reduce_161,
  4, 140, :_reduce_162,
  3, 140, :_reduce_163,
  3, 140, :_reduce_164,
  2, 140, :_reduce_165,
  1, 108, :_reduce_166,
  1, 118, :_reduce_167,
  3, 118, :_reduce_168,
  4, 118, :_reduce_169,
  2, 141, :_reduce_170,
  1, 141, :_reduce_171,
  4, 141, :_reduce_172,
  3, 141, :_reduce_173,
  2, 142, :_reduce_174,
  1, 143, :_reduce_175,
  2, 143, :_reduce_176,
  3, 144, :_reduce_177,
  2, 144, :_reduce_178,
  1, 145, :_reduce_179,
  1, 145, :_reduce_180,
  1, 145, :_reduce_181,
  3, 145, :_reduce_182,
  3, 145, :_reduce_183,
  1, 148, :_reduce_184,
  4, 148, :_reduce_185,
  4, 148, :_reduce_186,
  3, 148, :_reduce_187,
  3, 148, :_reduce_188,
  3, 148, :_reduce_189,
  2, 148, :_reduce_190,
  2, 148, :_reduce_191,
  6, 148, :_reduce_192,
  7, 148, :_reduce_193,
  1, 149, :_reduce_194,
  3, 149, :_reduce_195,
  1, 150, :_reduce_196,
  1, 150, :_reduce_197,
  1, 151, :_reduce_198,
  2, 151, :_reduce_199,
  2, 151, :_reduce_200,
  2, 151, :_reduce_201,
  2, 151, :_reduce_202,
  4, 151, :_reduce_203,
  1, 152, :_reduce_204,
  1, 152, :_reduce_205,
  1, 152, :_reduce_206,
  1, 152, :_reduce_207,
  1, 152, :_reduce_208,
  1, 152, :_reduce_209,
  1, 153, :_reduce_210,
  4, 153, :_reduce_211,
  1, 154, :_reduce_212,
  3, 154, :_reduce_213,
  3, 154, :_reduce_214,
  3, 154, :_reduce_215,
  1, 155, :_reduce_216,
  3, 155, :_reduce_217,
  3, 155, :_reduce_218,
  1, 156, :_reduce_219,
  3, 156, :_reduce_220,
  3, 156, :_reduce_221,
  1, 157, :_reduce_222,
  3, 157, :_reduce_223,
  3, 157, :_reduce_224,
  3, 157, :_reduce_225,
  3, 157, :_reduce_226,
  1, 158, :_reduce_227,
  3, 158, :_reduce_228,
  3, 158, :_reduce_229,
  1, 159, :_reduce_230,
  3, 159, :_reduce_231,
  1, 160, :_reduce_232,
  3, 160, :_reduce_233,
  1, 161, :_reduce_234,
  3, 161, :_reduce_235,
  1, 162, :_reduce_236,
  3, 162, :_reduce_237,
  1, 163, :_reduce_238,
  3, 163, :_reduce_239,
  1, 164, :_reduce_240,
  5, 164, :_reduce_241,
  1, 133, :_reduce_242,
  3, 133, :_reduce_243,
  1, 165, :_reduce_244,
  1, 165, :_reduce_245,
  1, 165, :_reduce_246,
  1, 165, :_reduce_247,
  1, 165, :_reduce_248,
  1, 165, :_reduce_249,
  1, 165, :_reduce_250,
  1, 165, :_reduce_251,
  1, 165, :_reduce_252,
  1, 165, :_reduce_253,
  1, 165, :_reduce_254,
  1, 111, :_reduce_255,
  3, 111, :_reduce_256,
  1, 107, :_reduce_257,
  1, 106, :_reduce_258,
  1, 146, :_reduce_259,
  1, 146, :_reduce_260,
  1, 146, :_reduce_261,
  1, 129, :_reduce_262,
  2, 147, :_reduce_263,
  1, 147, :_reduce_264 ]

racc_reduce_n = 265

racc_shift_n = 460

racc_token_table = {
  false => 0,
  :error => 1,
  :COLON => 2,
  :CASE => 3,
  :DEFAULT => 4,
  :LBRACE => 5,
  :RBRACE => 6,
  :SEMICOLON => 7,
  :IF => 8,
  :LPAREN => 9,
  :RPAREN => 10,
  :ELSE => 11,
  :SWITCH => 12,
  :WHILE => 13,
  :DO => 14,
  :FOR => 15,
  :GOTO => 16,
  :CONTINUE => 17,
  :BREAK => 18,
  :RETURN => 19,
  :COMMA => 20,
  :EQ => 21,
  :TYPEDEF => 22,
  :EXTERN => 23,
  :STATIC => 24,
  :AUTO => 25,
  :REGISTER => 26,
  :VOID => 27,
  :CHAR => 28,
  :SHORT => 29,
  :INT => 30,
  :LONG => 31,
  :FLOAT => 32,
  :DOUBLE => 33,
  :SIGNED => 34,
  :UNSIGNED => 35,
  :BOOL => 36,
  :COMPLEX => 37,
  :IMAGINARY => 38,
  :STRUCT => 39,
  :UNION => 40,
  :ENUM => 41,
  :CONST => 42,
  :RESTRICT => 43,
  :VOLATILE => 44,
  :INLINE => 45,
  :LBRACKET => 46,
  :RBRACKET => 47,
  :MUL => 48,
  :ELLIPSIS => 49,
  :TYPENAME => 50,
  :DOT => 51,
  :ARROW => 52,
  :INC => 53,
  :DEC => 54,
  :SIZEOF => 55,
  :AND => 56,
  :ADD => 57,
  :SUB => 58,
  :NOT => 59,
  :BANG => 60,
  :DIV => 61,
  :MOD => 62,
  :LSHIFT => 63,
  :RSHIFT => 64,
  :LT => 65,
  :GT => 66,
  :LEQ => 67,
  :GEQ => 68,
  :EQEQ => 69,
  :NEQ => 70,
  :XOR => 71,
  :OR => 72,
  :ANDAND => 73,
  :OROR => 74,
  :QUESTION => 75,
  :MULEQ => 76,
  :DIVEQ => 77,
  :MODEQ => 78,
  :ADDEQ => 79,
  :SUBEQ => 80,
  :LSHIFTEQ => 81,
  :RSHIFTEQ => 82,
  :ANDEQ => 83,
  :XOREQ => 84,
  :OREQ => 85,
  :ID => 86,
  :ICON => 87,
  :FCON => 88,
  :CCON => 89,
  :SCON => 90 }

racc_nt_base = 91

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "COLON",
  "CASE",
  "DEFAULT",
  "LBRACE",
  "RBRACE",
  "SEMICOLON",
  "IF",
  "LPAREN",
  "RPAREN",
  "ELSE",
  "SWITCH",
  "WHILE",
  "DO",
  "FOR",
  "GOTO",
  "CONTINUE",
  "BREAK",
  "RETURN",
  "COMMA",
  "EQ",
  "TYPEDEF",
  "EXTERN",
  "STATIC",
  "AUTO",
  "REGISTER",
  "VOID",
  "CHAR",
  "SHORT",
  "INT",
  "LONG",
  "FLOAT",
  "DOUBLE",
  "SIGNED",
  "UNSIGNED",
  "BOOL",
  "COMPLEX",
  "IMAGINARY",
  "STRUCT",
  "UNION",
  "ENUM",
  "CONST",
  "RESTRICT",
  "VOLATILE",
  "INLINE",
  "LBRACKET",
  "RBRACKET",
  "MUL",
  "ELLIPSIS",
  "TYPENAME",
  "DOT",
  "ARROW",
  "INC",
  "DEC",
  "SIZEOF",
  "AND",
  "ADD",
  "SUB",
  "NOT",
  "BANG",
  "DIV",
  "MOD",
  "LSHIFT",
  "RSHIFT",
  "LT",
  "GT",
  "LEQ",
  "GEQ",
  "EQEQ",
  "NEQ",
  "XOR",
  "OR",
  "ANDAND",
  "OROR",
  "QUESTION",
  "MULEQ",
  "DIVEQ",
  "MODEQ",
  "ADDEQ",
  "SUBEQ",
  "LSHIFTEQ",
  "RSHIFTEQ",
  "ANDEQ",
  "XOREQ",
  "OREQ",
  "ID",
  "ICON",
  "FCON",
  "CCON",
  "SCON",
  "$start",
  "translation_unit",
  "external_declaration",
  "function_definition",
  "declaration",
  "declaration_specifiers",
  "declarator",
  "declaration_list",
  "compound_statement",
  "statement",
  "labeled_statement",
  "expression_statement",
  "selection_statement",
  "iteration_statement",
  "jump_statement",
  "identifier",
  "constant_expression",
  "typedef_name",
  "block_item_list",
  "block_item",
  "expression",
  "init_declarator_list",
  "storage_class_specifier",
  "type_specifier",
  "type_qualifier",
  "function_specifier",
  "init_declarator",
  "initializer",
  "struct_or_union_specifier",
  "enum_specifier",
  "struct_or_union",
  "struct_declaration_list",
  "struct_declaration",
  "specifier_qualifier_list",
  "struct_declarator_list",
  "struct_declarator",
  "enumerator_list",
  "enumerator",
  "enumeration_constant",
  "pointer",
  "direct_declarator",
  "type_qualifier_list",
  "assignment_expression",
  "parameter_type_list",
  "identifier_list",
  "parameter_list",
  "parameter_declaration",
  "abstract_declarator",
  "type_name",
  "direct_abstract_declarator",
  "initializer_list",
  "designation",
  "designator_list",
  "designator",
  "primary_expression",
  "constant",
  "string_literal",
  "postfix_expression",
  "argument_expression_list",
  "argument_expression",
  "unary_expression",
  "unary_operator",
  "cast_expression",
  "multiplicative_expression",
  "additive_expression",
  "shift_expression",
  "relational_expression",
  "equality_expression",
  "and_expression",
  "exclusive_or_expression",
  "inclusive_or_expression",
  "logical_and_expression",
  "logical_or_expression",
  "conditional_expression",
  "assignment_operator" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'cast.y', 32)
  def _reduce_1(val, _values, result)
    result = TranslationUnit.new_at(val[0].pos, NodeChain[val[0]])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 33)
  def _reduce_2(val, _values, result)
    result = val[0]; result.entities << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 37)
  def _reduce_3(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 38)
  def _reduce_4(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 42)
  def _reduce_5(val, _values, result)
    result = make_function_def(val[0][0], val[0][1], val[1], val[2], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 43)
  def _reduce_6(val, _values, result)
    result = make_function_def(val[0][0], val[0][1], val[1], nil   , val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 47)
  def _reduce_7(val, _values, result)
    result = [val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 48)
  def _reduce_8(val, _values, result)
    result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 54)
  def _reduce_9(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 55)
  def _reduce_10(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 56)
  def _reduce_11(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 57)
  def _reduce_12(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 58)
  def _reduce_13(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 59)
  def _reduce_14(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 63)
  def _reduce_15(val, _values, result)
    val[2].labels.unshift(PlainLabel.new_at(val[0].pos, val[0].val)); result = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 64)
  def _reduce_16(val, _values, result)
    val[3].labels.unshift(Case      .new_at(val[0].pos, val[1]    )); result = val[3]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 65)
  def _reduce_17(val, _values, result)
    val[2].labels.unshift(Default   .new_at(val[0].pos            )); result = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 67)
  def _reduce_18(val, _values, result)
    val[2].labels.unshift(PlainLabel.new_at(val[0].pos, val[0].name)); result = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 71)
  def _reduce_19(val, _values, result)
    result = Block.new_at(val[0].pos, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 72)
  def _reduce_20(val, _values, result)
    result = Block.new_at(val[0].pos        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 76)
  def _reduce_21(val, _values, result)
    result = NodeChain[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 77)
  def _reduce_22(val, _values, result)
    result = val[0] << val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 81)
  def _reduce_23(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 82)
  def _reduce_24(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 86)
  def _reduce_25(val, _values, result)
    result = ExpressionStatement.new_at(val[0].pos, val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 87)
  def _reduce_26(val, _values, result)
    result = ExpressionStatement.new_at(val[0].pos        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 91)
  def _reduce_27(val, _values, result)
    result = If    .new_at(val[0].pos, val[2], val[4]        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 92)
  def _reduce_28(val, _values, result)
    result = If    .new_at(val[0].pos, val[2], val[4], val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 93)
  def _reduce_29(val, _values, result)
    result = Switch.new_at(val[0].pos, val[2], val[4]        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 97)
  def _reduce_30(val, _values, result)
    result = While.new_at(val[0].pos, val[2], val[4]              )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 98)
  def _reduce_31(val, _values, result)
    result = While.new_at(val[0].pos, val[4], val[1], :do => true )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 99)
  def _reduce_32(val, _values, result)
    result = For.new_at(val[0].pos, val[2], val[4], val[6], val[8])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 100)
  def _reduce_33(val, _values, result)
    result = For.new_at(val[0].pos, val[2], val[4], nil   , val[7])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 101)
  def _reduce_34(val, _values, result)
    result = For.new_at(val[0].pos, val[2], nil   , val[5], val[7])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 102)
  def _reduce_35(val, _values, result)
    result = For.new_at(val[0].pos, val[2], nil   , nil   , val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 103)
  def _reduce_36(val, _values, result)
    result = For.new_at(val[0].pos, nil   , val[3], val[5], val[7])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 104)
  def _reduce_37(val, _values, result)
    result = For.new_at(val[0].pos, nil   , val[3], nil   , val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 105)
  def _reduce_38(val, _values, result)
    result = For.new_at(val[0].pos, nil   , nil   , val[4], val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 106)
  def _reduce_39(val, _values, result)
    result = For.new_at(val[0].pos, nil   , nil   , nil   , val[5])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 107)
  def _reduce_40(val, _values, result)
    result = For.new_at(val[0].pos, val[2], val[3], val[5], val[7])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 108)
  def _reduce_41(val, _values, result)
    result = For.new_at(val[0].pos, val[2], val[3], nil   , val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 109)
  def _reduce_42(val, _values, result)
    result = For.new_at(val[0].pos, val[2], nil   , val[4], val[6])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 110)
  def _reduce_43(val, _values, result)
    result = For.new_at(val[0].pos, val[2], nil   , nil   , val[5])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 114)
  def _reduce_44(val, _values, result)
    result = Goto    .new_at(val[0].pos, val[1].val)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 115)
  def _reduce_45(val, _values, result)
    result = Continue.new_at(val[0].pos            )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 116)
  def _reduce_46(val, _values, result)
    result = Break   .new_at(val[0].pos            )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 117)
  def _reduce_47(val, _values, result)
    result = Return  .new_at(val[0].pos, val[1]    )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 118)
  def _reduce_48(val, _values, result)
    result = Return  .new_at(val[0].pos            )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 120)
  def _reduce_49(val, _values, result)
    result = Goto    .new_at(val[0].pos, val[1].name)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 126)
  def _reduce_50(val, _values, result)
    result = make_declaration(val[0][0], val[0][1], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 127)
  def _reduce_51(val, _values, result)
    result = make_declaration(val[0][0], val[0][1], NodeArray[])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 131)
  def _reduce_52(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 132)
  def _reduce_53(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 133)
  def _reduce_54(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 134)
  def _reduce_55(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 135)
  def _reduce_56(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 136)
  def _reduce_57(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 137)
  def _reduce_58(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 138)
  def _reduce_59(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 142)
  def _reduce_60(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 143)
  def _reduce_61(val, _values, result)
    result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 147)
  def _reduce_62(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 148)
  def _reduce_63(val, _values, result)
    val[0].init = val[2]; result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 152)
  def _reduce_64(val, _values, result)
    result = [val[0].pos, :typedef ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 153)
  def _reduce_65(val, _values, result)
    result = [val[0].pos, :extern  ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 154)
  def _reduce_66(val, _values, result)
    result = [val[0].pos, :static  ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 155)
  def _reduce_67(val, _values, result)
    result = [val[0].pos, :auto    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 156)
  def _reduce_68(val, _values, result)
    result = [val[0].pos, :register]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 160)
  def _reduce_69(val, _values, result)
    result = [val[0].pos, :void      ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 161)
  def _reduce_70(val, _values, result)
    result = [val[0].pos, :char      ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 162)
  def _reduce_71(val, _values, result)
    result = [val[0].pos, :short     ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 163)
  def _reduce_72(val, _values, result)
    result = [val[0].pos, :int       ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 164)
  def _reduce_73(val, _values, result)
    result = [val[0].pos, :long      ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 165)
  def _reduce_74(val, _values, result)
    result = [val[0].pos, :float     ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 166)
  def _reduce_75(val, _values, result)
    result = [val[0].pos, :double    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 167)
  def _reduce_76(val, _values, result)
    result = [val[0].pos, :signed    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 168)
  def _reduce_77(val, _values, result)
    result = [val[0].pos, :unsigned  ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 169)
  def _reduce_78(val, _values, result)
    result = [val[0].pos, :_Bool     ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 170)
  def _reduce_79(val, _values, result)
    result = [val[0].pos, :_Complex  ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 171)
  def _reduce_80(val, _values, result)
    result = [val[0].pos, :_Imaginary]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 172)
  def _reduce_81(val, _values, result)
    result = [val[0].pos, val[0]    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 173)
  def _reduce_82(val, _values, result)
    result = [val[0].pos, val[0]    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 174)
  def _reduce_83(val, _values, result)
    result = [val[0].pos, val[0]    ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 178)
  def _reduce_84(val, _values, result)
    result = val[0][1].new_at(val[0][0], val[1].val, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 179)
  def _reduce_85(val, _values, result)
    result = val[0][1].new_at(val[0][0], nil       , val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 180)
  def _reduce_86(val, _values, result)
    result = val[0][1].new_at(val[0][0], val[1].val, nil   )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 182)
  def _reduce_87(val, _values, result)
    result = val[0][1].new_at(val[0][0], val[1].name, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 183)
  def _reduce_88(val, _values, result)
    result = val[0][1].new_at(val[0][0], val[1].name, nil   )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 187)
  def _reduce_89(val, _values, result)
    result = [val[0].pos, Struct]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 188)
  def _reduce_90(val, _values, result)
    result = [val[0].pos, Union ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 192)
  def _reduce_91(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 193)
  def _reduce_92(val, _values, result)
    val[0] << val[1]; result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 197)
  def _reduce_93(val, _values, result)
    result = make_declaration(val[0][0], val[0][1], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 201)
  def _reduce_94(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 202)
  def _reduce_95(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 203)
  def _reduce_96(val, _values, result)
    val[1][1] << val[0][1]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 204)
  def _reduce_97(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 208)
  def _reduce_98(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 209)
  def _reduce_99(val, _values, result)
    result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 213)
  def _reduce_100(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 214)
  def _reduce_101(val, _values, result)
    result = val[0]; val[0].num_bits = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 215)
  def _reduce_102(val, _values, result)
    result = Declarator.new_at(val[0].pos, :num_bits => val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 219)
  def _reduce_103(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].val, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 220)
  def _reduce_104(val, _values, result)
    result = Enum.new_at(val[0].pos, nil       , val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 221)
  def _reduce_105(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].val, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 222)
  def _reduce_106(val, _values, result)
    result = Enum.new_at(val[0].pos, nil       , val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 223)
  def _reduce_107(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].val, nil   )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 225)
  def _reduce_108(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].name, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 226)
  def _reduce_109(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].name, val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 227)
  def _reduce_110(val, _values, result)
    result = Enum.new_at(val[0].pos, val[1].name, nil   )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 231)
  def _reduce_111(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 232)
  def _reduce_112(val, _values, result)
    result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 236)
  def _reduce_113(val, _values, result)
    result = Enumerator.new_at(val[0].pos, val[0].val, nil   )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 237)
  def _reduce_114(val, _values, result)
    result = Enumerator.new_at(val[0].pos, val[0].val, val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 241)
  def _reduce_115(val, _values, result)
    result = [val[0].pos, :const   ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 242)
  def _reduce_116(val, _values, result)
    result = [val[0].pos, :restrict]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 243)
  def _reduce_117(val, _values, result)
    result = [val[0].pos, :volatile]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 247)
  def _reduce_118(val, _values, result)
    result = [val[0].pos, :inline]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 251)
  def _reduce_119(val, _values, result)
    result = add_decl_type(val[1], val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 252)
  def _reduce_120(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 256)
  def _reduce_121(val, _values, result)
    result = Declarator.new_at(val[0].pos, nil, val[0].val)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 257)
  def _reduce_122(val, _values, result)
    result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 258)
  def _reduce_123(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 259)
  def _reduce_124(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 260)
  def _reduce_125(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos, nil, val[2]))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 261)
  def _reduce_126(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 262)
  def _reduce_127(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 263)
  def _reduce_128(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 264)
  def _reduce_129(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 265)
  def _reduce_130(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 266)
  def _reduce_131(val, _values, result)
    result = add_decl_type(val[0], Array.new_at(val[0].pos             ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 267)
  def _reduce_132(val, _values, result)
    result = add_decl_type(val[0], Function.new_at(val[0].pos, nil, param_list(*val[2]), :var_args => val[2][1]))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 268)
  def _reduce_133(val, _values, result)
    result = add_decl_type(val[0], Function.new_at(val[0].pos, nil,             val[2]))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 269)
  def _reduce_134(val, _values, result)
    result = add_decl_type(val[0], Function.new_at(val[0].pos                         ))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 273)
  def _reduce_135(val, _values, result)
    result = add_type_quals(Pointer.new_at(val[0].pos), val[1][1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 274)
  def _reduce_136(val, _values, result)
    result =                Pointer.new_at(val[0].pos)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 275)
  def _reduce_137(val, _values, result)
    p      = add_type_quals(Pointer.new_at(val[0].pos), val[1][1]); val[2].direct_type = p; result = val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 276)
  def _reduce_138(val, _values, result)
    p      =                Pointer.new_at(val[0].pos)            ; val[1].direct_type = p; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 280)
  def _reduce_139(val, _values, result)
    result = [val[0][0], [val[0][1]]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 281)
  def _reduce_140(val, _values, result)
    val[0][1] << val[1][1]; result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 285)
  def _reduce_141(val, _values, result)
    result = [val[0], false]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 286)
  def _reduce_142(val, _values, result)
    result = [val[0], true ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 290)
  def _reduce_143(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 291)
  def _reduce_144(val, _values, result)
    result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 295)
  def _reduce_145(val, _values, result)
    ind_type = val[1].indirect_type and ind_type.detach
                                                result = make_parameter(val[0][0], val[0][1], ind_type, val[1].name)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 297)
  def _reduce_146(val, _values, result)
    result = make_parameter(val[0][0], val[0][1], val[1]  , nil        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 298)
  def _reduce_147(val, _values, result)
    result = make_parameter(val[0][0], val[0][1], nil     , nil        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 302)
  def _reduce_148(val, _values, result)
    result = NodeArray[Parameter.new_at(val[0].pos, nil, val[0].val)]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 303)
  def _reduce_149(val, _values, result)
    result = val[0] << Parameter.new_at(val[2].pos, nil, val[2].val)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 307)
  def _reduce_150(val, _values, result)
    val[1].direct_type = make_direct_type(val[0][0], val[0][1]); result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 308)
  def _reduce_151(val, _values, result)
    result             = make_direct_type(val[0][0], val[0][1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 312)
  def _reduce_152(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 313)
  def _reduce_153(val, _values, result)
    val[1].direct_type = val[0]; result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 314)
  def _reduce_154(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 318)
  def _reduce_155(val, _values, result)
    result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 319)
  def _reduce_156(val, _values, result)
    val[0].direct_type = Array.new_at(val[0].pos, nil, val[2]); result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 320)
  def _reduce_157(val, _values, result)
    val[0].direct_type = Array.new_at(val[0].pos, nil, nil   ); result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 321)
  def _reduce_158(val, _values, result)
    result = Array.new_at(val[0].pos, nil, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 322)
  def _reduce_159(val, _values, result)
    result = Array.new_at(val[0].pos             )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 323)
  def _reduce_160(val, _values, result)
    val[0].direct_type = Array.new_at(val[0].pos); result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 324)
  def _reduce_161(val, _values, result)
    result = Array.new_at(val[0].pos)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 325)
  def _reduce_162(val, _values, result)
    val[0].direct_type = Function.new_at(val[0].pos, nil, param_list(*val[2]), val[2][1]); result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 326)
  def _reduce_163(val, _values, result)
    val[0].direct_type = Function.new_at(val[0].pos                                       ); result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 327)
  def _reduce_164(val, _values, result)
    result = Function.new_at(val[0].pos, nil, param_list(*val[1]), val[1][1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 328)
  def _reduce_165(val, _values, result)
    result = Function.new_at(val[0].pos                                     )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 334)
  def _reduce_166(val, _values, result)
    result = CustomType.new_at(val[0].pos, val[0].val)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 338)
  def _reduce_167(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 339)
  def _reduce_168(val, _values, result)
    result = CompoundLiteral.new_at(val[0].pos, nil, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 340)
  def _reduce_169(val, _values, result)
    result = CompoundLiteral.new_at(val[0].pos, nil, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 344)
  def _reduce_170(val, _values, result)
    result = NodeArray[MemberInit.new_at(val[0][0] , val[0][1], val[1])]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 345)
  def _reduce_171(val, _values, result)
    result = NodeArray[MemberInit.new_at(val[0].pos, nil      , val[0])]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 346)
  def _reduce_172(val, _values, result)
    result = val[0] << MemberInit.new_at(val[2][0] , val[2][1], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 347)
  def _reduce_173(val, _values, result)
    result = val[0] << MemberInit.new_at(val[2].pos, nil      , val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 351)
  def _reduce_174(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 355)
  def _reduce_175(val, _values, result)
    result = val[0]; val[0][1] = NodeArray[val[0][1]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 356)
  def _reduce_176(val, _values, result)
    result = val[0]; val[0][1] << val[1][1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 360)
  def _reduce_177(val, _values, result)
    result = [val[1].pos, val[1]                               ]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 361)
  def _reduce_178(val, _values, result)
    result = [val[1].pos, Member.new_at(val[1].pos, val[1].val)]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 367)
  def _reduce_179(val, _values, result)
    result = Variable.new_at(val[0].pos, val[0].val)
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 368)
  def _reduce_180(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 369)
  def _reduce_181(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 371)
  def _reduce_182(val, _values, result)
    result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 372)
  def _reduce_183(val, _values, result)
    block_expressions_enabled? or parse_error val[0].pos, "compound statement found where expression expected"
                                      result = BlockExpression.new(val[1]); result.pos = val[0].pos
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 377)
  def _reduce_184(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 378)
  def _reduce_185(val, _values, result)
    result = Index          .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 379)
  def _reduce_186(val, _values, result)
    result = Call           .new_at(val[0].pos, val[0], val[2]     )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 380)
  def _reduce_187(val, _values, result)
    result = Call           .new_at(val[0].pos, val[0], NodeArray[])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 381)
  def _reduce_188(val, _values, result)
    result = Dot            .new_at(val[0].pos, val[0], Member.new(val[2].val))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 382)
  def _reduce_189(val, _values, result)
    result = Arrow          .new_at(val[0].pos, val[0], Member.new(val[2].val))
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 383)
  def _reduce_190(val, _values, result)
    result = PostInc        .new_at(val[0].pos, val[0]        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 384)
  def _reduce_191(val, _values, result)
    result = PostDec        .new_at(val[0].pos, val[0]        )
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 385)
  def _reduce_192(val, _values, result)
    result = CompoundLiteral.new_at(val[0].pos, val[1], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 386)
  def _reduce_193(val, _values, result)
    result = CompoundLiteral.new_at(val[0].pos, val[1], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 390)
  def _reduce_194(val, _values, result)
    result = NodeArray[val[0]]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 391)
  def _reduce_195(val, _values, result)
    result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 395)
  def _reduce_196(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 396)
  def _reduce_197(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 400)
  def _reduce_198(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 401)
  def _reduce_199(val, _values, result)
    result = PreInc.new_at(val[0].pos, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 402)
  def _reduce_200(val, _values, result)
    result = PreDec.new_at(val[0].pos, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 403)
  def _reduce_201(val, _values, result)
    result = val[0][0].new_at(val[0][1], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 404)
  def _reduce_202(val, _values, result)
    result = Sizeof.new_at(val[0].pos, val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 405)
  def _reduce_203(val, _values, result)
    result = Sizeof.new_at(val[0].pos, val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 409)
  def _reduce_204(val, _values, result)
    result = [Address    , val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 410)
  def _reduce_205(val, _values, result)
    result = [Dereference, val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 411)
  def _reduce_206(val, _values, result)
    result = [Positive   , val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 412)
  def _reduce_207(val, _values, result)
    result = [Negative   , val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 413)
  def _reduce_208(val, _values, result)
    result = [BitNot     , val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 414)
  def _reduce_209(val, _values, result)
    result = [Not        , val[0].pos]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 418)
  def _reduce_210(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 419)
  def _reduce_211(val, _values, result)
    result = Cast.new_at(val[0].pos, val[1], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 423)
  def _reduce_212(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 424)
  def _reduce_213(val, _values, result)
    result = Multiply.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 425)
  def _reduce_214(val, _values, result)
    result = Divide  .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 426)
  def _reduce_215(val, _values, result)
    result = Mod     .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 430)
  def _reduce_216(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 431)
  def _reduce_217(val, _values, result)
    result = Add     .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 432)
  def _reduce_218(val, _values, result)
    result = Subtract.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 436)
  def _reduce_219(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 437)
  def _reduce_220(val, _values, result)
    result = ShiftLeft .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 438)
  def _reduce_221(val, _values, result)
    result = ShiftRight.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 442)
  def _reduce_222(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 443)
  def _reduce_223(val, _values, result)
    result = Less.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 444)
  def _reduce_224(val, _values, result)
    result = More.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 445)
  def _reduce_225(val, _values, result)
    result = LessOrEqual.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 446)
  def _reduce_226(val, _values, result)
    result = MoreOrEqual.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 450)
  def _reduce_227(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 451)
  def _reduce_228(val, _values, result)
    result = Equal   .new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 452)
  def _reduce_229(val, _values, result)
    result = NotEqual.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 456)
  def _reduce_230(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 457)
  def _reduce_231(val, _values, result)
    result = BitAnd.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 461)
  def _reduce_232(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 462)
  def _reduce_233(val, _values, result)
    result = BitXor.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 466)
  def _reduce_234(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 467)
  def _reduce_235(val, _values, result)
    result = BitOr.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 471)
  def _reduce_236(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 472)
  def _reduce_237(val, _values, result)
    result = And.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 476)
  def _reduce_238(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 477)
  def _reduce_239(val, _values, result)
    result = Or.new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 481)
  def _reduce_240(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 482)
  def _reduce_241(val, _values, result)
    result = Conditional.new_at(val[0].pos, val[0], val[2], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 486)
  def _reduce_242(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 487)
  def _reduce_243(val, _values, result)
    result = val[1].new_at(val[0].pos, val[0], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 491)
  def _reduce_244(val, _values, result)
    result =           Assign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 492)
  def _reduce_245(val, _values, result)
    result =   MultiplyAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 493)
  def _reduce_246(val, _values, result)
    result =     DivideAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 494)
  def _reduce_247(val, _values, result)
    result =        ModAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 495)
  def _reduce_248(val, _values, result)
    result =        AddAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 496)
  def _reduce_249(val, _values, result)
    result =   SubtractAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 497)
  def _reduce_250(val, _values, result)
    result =  ShiftLeftAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 498)
  def _reduce_251(val, _values, result)
    result = ShiftRightAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 499)
  def _reduce_252(val, _values, result)
    result =     BitAndAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 500)
  def _reduce_253(val, _values, result)
    result =     BitXorAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 501)
  def _reduce_254(val, _values, result)
    result =      BitOrAssign
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 505)
  def _reduce_255(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 507)
  def _reduce_256(val, _values, result)
        if val[0].is_a? Comma
      if val[2].is_a? Comma
        val[0].exprs.push(*val[2].exprs)
      else
        val[0].exprs << val[2]
      end
      result = val[0]
    else
      if val[2].is_a? Comma
        val[2].exprs.unshift(val[0])
        val[2].pos = val[0].pos
        result = val[2]
      else
        result = Comma.new_at(val[0].pos, NodeArray[val[0], val[2]])
      end
    end

    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 527)
  def _reduce_257(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 542)
  def _reduce_258(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 546)
  def _reduce_259(val, _values, result)
    result = val[0].val; result.pos = val[0].pos
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 547)
  def _reduce_260(val, _values, result)
    result = val[0].val; result.pos = val[0].pos
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 550)
  def _reduce_261(val, _values, result)
    result = val[0].val; result.pos = val[0].pos
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 554)
  def _reduce_262(val, _values, result)
    result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 559)
  def _reduce_263(val, _values, result)
    val[0].val << val[1].val.val; result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'cast.y', 560)
  def _reduce_264(val, _values, result)
     result = val[0].val; result.pos = val[0].pos
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module C

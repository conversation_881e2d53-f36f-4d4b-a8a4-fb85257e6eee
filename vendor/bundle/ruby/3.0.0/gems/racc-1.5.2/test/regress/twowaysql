#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'
module TwoWaySQL
  class Parser < Racc::Parser

module_eval(<<'...end twowaysql.y/module_eval...', 'twowaysql.y', 148)

require 'strscan'

def initialize(opts={})
  opts = {
    :debug => false,
    :preserve_space => true,
    :preserve_comment => false
  }.merge(opts)
  @yydebug = opts[:debug]
  @preserve_space = opts[:preserve_space]
  @preserve_comment = opts[:preserve_comment]
  @num_questions = 0
end


PAREN_EXAMPLE                = '\([^\)]+\)'
BEGIN_BIND_VARIABLE          = '(\/|\#)\*([^\*]+)\*\1'
BIND_VARIABLE_PATTERN        = /\A#{BEGIN_BIND_VARIABLE}\s*/
PAREN_BIND_VARIABLE_PATTERN  = /\A#{BEGIN_BIND_VARIABLE}\s*#{PAREN_EXAMPLE}/
EMBED_VARIABLE_PATTERN       = /\A(\/|\#)\*\$([^\*]+)\*\1\s*/

CONDITIONAL_PATTERN     = /\A(\/|\#)\*(IF)\s+([^\*]+)\s*\*\1/
BEGIN_END_PATTERN       = /\A(\/|\#)\*(BEGIN|END)\s*\*\1/
STRING_LITERAL_PATTERN  = /\A(\'(?:[^\']+|\'\')*\')/   ## quoted string
SPLIT_TOKEN_PATTERN     = /\A(\S+?)(?=\s*(?:(?:\/|\#)\*|-{2,}|\(|\)|\,))/  ## stop on delimiters --,/*,#*,',',(,)
LITERAL_PATTERN         = /\A([^;\s]+)/
SPACES_PATTERN          = /\A(\s+)/
QUESTION_PATTERN        = /\A\?/
COMMA_PATTERN           = /\A\,/
LPAREN_PATTERN          = /\A\(/
RPAREN_PATTERN          = /\A\)/
ACTUAL_COMMENT_PATTERN          = /\A(\/|\#)\*(\s{1,}(?:.*?))\*\1/m  ## start with spaces
SEMICOLON_AT_INPUT_END_PATTERN  = /\A\;\s*\Z/
UNMATCHED_COMMENT_START_PATTERN = /\A(?:(?:\/|\#)\*)/

#TODO: remove trailing spaces for S2Dao compatibility, but this spec sometimes causes SQL bugs...
ELSE_PATTERN            = /\A\-{2,}\s*ELSE\s*/
AND_PATTERN             = /\A(\ *AND)\b/i
OR_PATTERN              = /\A(\ *OR)\b/i


def parse( io )
  @q = []
  io.each_line(nil) do |whole|
    @s = StringScanner.new(whole)
  end
  scan_str

  # @q.push [ false, nil ]
  @q.push [ false, [@s.pos, nil] ]

  ## call racc's private parse method
  do_parse
end


## called by racc
def next_token
  @q.shift
end


def scan_str
  until @s.eos? do
    case
    when @s.scan(AND_PATTERN)
      @q.push [ :AND, [@s.pos, @s[1]] ]
    when @s.scan(OR_PATTERN)
      @q.push [ :OR, [@s.pos, @s[1]] ]
    when @s.scan(SPACES_PATTERN)
      @q.push [ :SPACES, [@s.pos, @s[1]] ]
    when @s.scan(QUESTION_PATTERN)
      @q.push [ :QUESTION, [@s.pos, nil] ]
    when @s.scan(COMMA_PATTERN)
      @q.push [ :COMMA, [@s.pos, ','] ]
    when @s.scan(LPAREN_PATTERN)
      @q.push [ :LPAREN, [@s.pos, '('] ]
    when @s.scan(RPAREN_PATTERN)
      @q.push [ :RPAREN, [@s.pos, ')'] ]
    when @s.scan(ELSE_PATTERN)
      @q.push [ :ELSE, [@s.pos, nil] ]
    when @s.scan(ACTUAL_COMMENT_PATTERN)
      @q.push [ :ACTUAL_COMMENT, [@s.pos, @s[1], @s[2]] ] if @preserve_comment
    when @s.scan(BEGIN_END_PATTERN)
      @q.push [ @s[2].intern, [@s.pos, nil] ]
    when @s.scan(CONDITIONAL_PATTERN)
      @q.push [ @s[2].intern, [@s.pos, @s[3]] ]
    when @s.scan(EMBED_VARIABLE_PATTERN)
      @q.push [ :EMBED_VARIABLE, [@s.pos, @s[2]] ]
    when @s.scan(PAREN_BIND_VARIABLE_PATTERN)
      @q.push [ :PAREN_BIND_VARIABLE, [@s.pos, @s[2]] ]
    when @s.scan(BIND_VARIABLE_PATTERN)
      @q.push [ :BIND_VARIABLE, [@s.pos, @s[2]] ]
    when @s.scan(STRING_LITERAL_PATTERN)
      @q.push [ :STRING_LITERAL, [@s.pos, @s[1]] ]
    when @s.scan(SPLIT_TOKEN_PATTERN)
      @q.push [ :IDENT, [@s.pos, @s[1]] ]
    when @s.scan(UNMATCHED_COMMENT_START_PATTERN)   ## unmatched comment start, '/*','#*'
      raise Racc::ParseError, "unmatched comment. line:[#{line_no(@s.pos)}], str:[#{@s.rest}]"
    when @s.scan(LITERAL_PATTERN)   ## other string token
      @q.push [ :IDENT, [@s.pos, @s[1]] ]
    when @s.scan(SEMICOLON_AT_INPUT_END_PATTERN)
      #drop semicolon at input end
    else
      raise Racc::ParseError, "syntax error at or near line:[#{line_no(@s.pos)}], str:[#{@s.rest}]"
    end
  end
end


## override racc's default on_error method
def on_error(t, v, vstack)
  ## cursor in value-stack is an array of two items,
  ##   that have position value as 0th item. like [731, "ctx[:limit] "]
  cursor = vstack.find do |tokens|
    tokens.size == 2 and tokens[0].kind_of?(Fixnum)
  end
  pos = cursor[0]
  line = line_no(pos)
  rest = @s.string[pos .. -1]
  raise Racc::ParseError, "syntax error at or near line:[#{line}], str:[#{rest}]"
end


def line_no(pos)
  lines = 0
  scanned = @s.string[0..(pos)]
  scanned.each_line { lines += 1 }
  lines
end
...end twowaysql.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
     8,    36,     9,    37,    12,    13,    10,    11,    14,    15,
    16,    17,    18,    19,    22,    23,    24,     8,    38,     9,
     3,    12,    13,    10,    11,    14,    15,    16,    17,    18,
    19,    22,    23,    24,     8,    25,     9,    40,    12,    13,
    10,    11,    14,    15,    16,    17,    18,    19,    22,    23,
    24,     8,    45,     9,    46,    12,    13,    10,    11,    14,
    15,    16,    17,    18,    19,    22,    23,    24,     8,   nil,
     9,   nil,    12,    13,    10,    11,    14,    15,    16,    17,
    18,    19,    22,    23,    24,    35,    33,    34,    31,    32,
    44,    43,    31,    32 ]

racc_action_check = [
     2,    24,     2,    24,     2,     2,     2,     2,     2,     2,
     2,     2,     2,     2,     2,     2,     2,    26,    26,    26,
     1,    26,    26,    26,    26,    26,    26,    26,    26,    26,
    26,    26,    26,    26,    27,     3,    27,    28,    27,    27,
    27,    27,    27,    27,    27,    27,    27,    27,    27,    27,
    27,    41,    37,    41,    39,    41,    41,    41,    41,    41,
    41,    41,    41,    41,    41,    41,    41,    41,    42,   nil,
    42,   nil,    42,    42,    42,    42,    42,    42,    42,    42,
    42,    42,    42,    42,    42,    22,    22,    22,     9,     9,
    34,    34,    40,    40 ]

racc_action_pointer = [
   nil,    20,    -2,    35,   nil,   nil,   nil,   nil,   nil,    82,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    77,   nil,    -7,   nil,    15,    32,    32,   nil,
   nil,   nil,   nil,   nil,    82,   nil,   nil,    44,   nil,    51,
    86,    49,    66,   nil,   nil,   nil,   nil,   nil ]

racc_action_default = [
    -2,   -35,    -1,   -35,    -3,    -4,    -5,    -6,    -2,    -2,
   -16,   -17,   -18,   -19,   -20,   -21,   -22,   -23,   -24,   -25,
   -26,   -27,   -35,   -32,   -35,    48,   -35,   -13,   -10,   -11,
   -12,    -2,    -2,   -28,   -35,   -30,   -33,   -35,    -7,   -35,
    -2,   -14,   -15,   -29,   -31,   -34,    -8,    -9 ]

racc_goto_table = [
     2,     1,    28,    39,   nil,   nil,   nil,   nil,    26,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    41,    42,    47 ]

racc_goto_check = [
     2,     1,     7,     8,   nil,   nil,   nil,   nil,     2,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,     2,     2,     7 ]

racc_goto_pointer = [
   nil,     1,     0,   nil,   nil,   nil,   nil,    -7,   -25,   nil,
   nil,   nil,   nil ]

racc_goto_default = [
   nil,   nil,    27,     4,     5,     6,     7,   nil,   nil,    29,
    30,    20,    21 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 20, :_reduce_1,
  0, 21, :_reduce_2,
  2, 21, :_reduce_3,
  1, 22, :_reduce_none,
  1, 22, :_reduce_none,
  1, 22, :_reduce_none,
  3, 25, :_reduce_7,
  4, 24, :_reduce_8,
  2, 27, :_reduce_9,
  0, 27, :_reduce_10,
  1, 26, :_reduce_none,
  1, 26, :_reduce_none,
  1, 26, :_reduce_none,
  2, 28, :_reduce_14,
  2, 29, :_reduce_15,
  1, 23, :_reduce_16,
  1, 23, :_reduce_17,
  1, 23, :_reduce_18,
  1, 23, :_reduce_19,
  1, 23, :_reduce_20,
  1, 23, :_reduce_21,
  1, 23, :_reduce_22,
  1, 23, :_reduce_23,
  1, 23, :_reduce_24,
  1, 23, :_reduce_25,
  1, 23, :_reduce_none,
  1, 23, :_reduce_none,
  2, 30, :_reduce_28,
  3, 30, :_reduce_29,
  2, 30, :_reduce_30,
  3, 30, :_reduce_31,
  1, 30, :_reduce_32,
  2, 31, :_reduce_33,
  3, 31, :_reduce_34 ]

racc_reduce_n = 35

racc_shift_n = 48

racc_token_table = {
  false => 0,
  :error => 1,
  :BEGIN => 2,
  :END => 3,
  :IF => 4,
  :ELSE => 5,
  :AND => 6,
  :OR => 7,
  :IDENT => 8,
  :STRING_LITERAL => 9,
  :SPACES => 10,
  :COMMA => 11,
  :LPAREN => 12,
  :RPAREN => 13,
  :QUESTION => 14,
  :ACTUAL_COMMENT => 15,
  :BIND_VARIABLE => 16,
  :PAREN_BIND_VARIABLE => 17,
  :EMBED_VARIABLE => 18 }

racc_nt_base = 19

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "BEGIN",
  "END",
  "IF",
  "ELSE",
  "AND",
  "OR",
  "IDENT",
  "STRING_LITERAL",
  "SPACES",
  "COMMA",
  "LPAREN",
  "RPAREN",
  "QUESTION",
  "ACTUAL_COMMENT",
  "BIND_VARIABLE",
  "PAREN_BIND_VARIABLE",
  "EMBED_VARIABLE",
  "$start",
  "sql",
  "stmt_list",
  "stmt",
  "primary",
  "if_stmt",
  "begin_stmt",
  "sub_stmt",
  "else_stmt",
  "and_stmt",
  "or_stmt",
  "bind_var",
  "embed_var" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'twowaysql.y', 20)
  def _reduce_1(val, _values, result)
                      result = RootNode.new( val[0] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 25)
  def _reduce_2(val, _values, result)
                      result = []

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 29)
  def _reduce_3(val, _values, result)
                      result.push val[1]

    result
  end
.,.,

# reduce 4 omitted

# reduce 5 omitted

# reduce 6 omitted

module_eval(<<'.,.,', 'twowaysql.y', 38)
  def _reduce_7(val, _values, result)
                      result = BeginNode.new( val[1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 43)
  def _reduce_8(val, _values, result)
                      result = IfNode.new( val[0][1], val[1], val[2] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 48)
  def _reduce_9(val, _values, result)
                      result = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 52)
  def _reduce_10(val, _values, result)
                      result = nil

    result
  end
.,.,

# reduce 11 omitted

# reduce 12 omitted

# reduce 13 omitted

module_eval(<<'.,.,', 'twowaysql.y', 61)
  def _reduce_14(val, _values, result)
                      result = SubStatementNode.new( val[0][1], val[1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 66)
  def _reduce_15(val, _values, result)
                      result = SubStatementNode.new( val[0][1], val[1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 71)
  def _reduce_16(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 75)
  def _reduce_17(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 79)
  def _reduce_18(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 83)
  def _reduce_19(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 87)
  def _reduce_20(val, _values, result)
                      result = WhiteSpaceNode.new( val[0][1], @preserve_space )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 91)
  def _reduce_21(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 95)
  def _reduce_22(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 99)
  def _reduce_23(val, _values, result)
                      result = LiteralNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 103)
  def _reduce_24(val, _values, result)
                      @num_questions += 1
                  result = QuestionNode.new( @num_questions )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 108)
  def _reduce_25(val, _values, result)
                      result = ActualCommentNode.new( val[0][1] , val[0][2] )

    result
  end
.,.,

# reduce 26 omitted

# reduce 27 omitted

module_eval(<<'.,.,', 'twowaysql.y', 115)
  def _reduce_28(val, _values, result)
                      result = BindVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 119)
  def _reduce_29(val, _values, result)
                      result = BindVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 123)
  def _reduce_30(val, _values, result)
                      result = BindVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 127)
  def _reduce_31(val, _values, result)
                      result = BindVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 131)
  def _reduce_32(val, _values, result)
                      result = ParenBindVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 136)
  def _reduce_33(val, _values, result)
                      result = EmbedVariableNode.new( val[0][1] )

    result
  end
.,.,

module_eval(<<'.,.,', 'twowaysql.y', 140)
  def _reduce_34(val, _values, result)
                      result = EmbedVariableNode.new( val[0][1] )

    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module TwoWaySQL

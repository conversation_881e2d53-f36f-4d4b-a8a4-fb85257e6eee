#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'

require 'strscan'

module EDTF
  class Parser < Racc::Parser

module_eval(<<'...end edtf.y/module_eval...', 'edtf.y', 468)

  @defaults = {
    :level => 2,
    :debug => false
  }.freeze

  class << self; attr_reader :defaults; end

  attr_reader :options

  def initialize(options = {})
    @options = Parser.defaults.merge(options)
  end

  def debug?
    !!(options[:debug] || ENV['DEBUG'])
  end

  def parse(input)
    parse!(input)
  rescue => e
    warn e.message if debug?
    nil
  end

  def parse!(input)
    @yydebug = debug?
    @src = StringScanner.new(input)
    do_parse
  end

  def on_error(tid, value, stack)
    raise ArgumentError,
      "failed to parse date: unexpected '#{value}' at #{stack.inspect}"
  end

  def apply_uncertainty(date, uncertainty, scope = nil)
    uncertainty.each do |u|
      scope.nil? ? date.send(u) : date.send(u, scope)
    end
    date
  end

  alias uoa apply_uncertainty

  def next_token
    case
    when @src.eos?
      nil
    # when @src.scan(/\s+/)
      # ignore whitespace
    when @src.scan(/\(/)
      ['(', @src.matched]
    # when @src.scan(/\)\?~-/)
    #   [:PUA, [:uncertain!, :approximate!]]
    # when @src.scan(/\)\?-/)
    #   [:PUA, [:uncertain!]]
    # when @src.scan(/\)~-/)
    #   [:PUA, [:approximate!]]
    when @src.scan(/\)/)
      [')', @src.matched]
    when @src.scan(/\[/)
      ['[', @src.matched]
    when @src.scan(/\]/)
      [']', @src.matched]
    when @src.scan(/\{/)
      ['{', @src.matched]
    when @src.scan(/\}/)
      ['}', @src.matched]
    when @src.scan(/T/)
      [:T, @src.matched]
    when @src.scan(/Z/)
      [:Z, @src.matched]
    when @src.scan(/\?~/)
      [:UA, [:uncertain!, :approximate!]]
    when @src.scan(/\?/)
      [:UA, [:uncertain!]]
    when @src.scan(/~/)
      [:UA, [:approximate!]]
    when @src.scan(/open/i)
      [:OPEN, :open]
    when @src.scan(/unkn?own/i) # matches 'unkown' typo too
      [:UNKNOWN, :unknown]
    when @src.scan(/u/)
      [:U, @src.matched]
    when @src.scan(/x/i)
      [:X, @src.matched]
    when @src.scan(/y/)
      [:LONGYEAR, @src.matched]
    when @src.scan(/e/)
      [:E, @src.matched]
    when @src.scan(/\+/)
      ['+', @src.matched]
    when @src.scan(/-\(/)
      ['-(', @src.matched]
    when @src.scan(/-/)
      ['-', @src.matched]
    when @src.scan(/:/)
      [':', @src.matched]
    when @src.scan(/\//)
      ['/', @src.matched]
    when @src.scan(/\s*\.\.\s*/)
      [:DOTS, '..']
    when @src.scan(/\s*,\s*/)
      [',', ',']
    when @src.scan(/\^\w+/)
      ['^', @src.matched[1..-1]]
    when @src.scan(/\d/)
      [@src.matched, @src.matched.to_i]
    else @src.scan(/./)
      [:UNMATCHED, @src.rest]
    end
  end


# -*- racc -*-
...end edtf.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
   129,   128,    52,   111,    51,   112,   149,   208,   207,    57,
   -50,    43,    45,    40,    55,    42,    54,    44,    43,    45,
    40,   -48,    42,    53,    44,    64,    58,    46,    47,    48,
    49,    50,   128,    56,    46,    47,    48,    49,    50,   207,
    57,    65,    43,    45,    40,    55,    42,   157,    44,    43,
    45,    40,    55,    42,   214,    44,    92,    58,    46,    47,
    48,    49,    50,    66,    56,    46,    47,    48,    49,    50,
    25,    56,    26,    93,    94,    67,   108,    12,   -65,    43,
    45,    40,   -66,    42,   159,    44,   110,    33,   111,    34,
   112,    95,    36,    25,   141,    46,    47,    48,    49,    50,
    12,    58,    43,    45,    40,   101,    42,   103,    44,   104,
    96,   148,    55,   133,   147,    36,   124,   125,    46,    47,
    48,    49,    50,    87,   165,   111,    12,   112,    43,    45,
    40,    56,    42,   146,    44,   166,   111,   150,   112,   218,
   167,    36,   152,   153,    46,    47,    48,    49,    50,    87,
   108,   111,    12,   112,    43,    45,    40,   188,    42,   186,
    44,   187,   111,   190,   112,   154,   111,    36,   112,   156,
    46,    47,    48,    49,    50,    69,   158,    43,    45,   189,
   191,    42,    12,    44,    43,    45,    40,   200,    42,   201,
    44,   168,   177,    46,    47,    48,    49,    50,   233,   178,
    46,    47,    48,    49,    50,    12,   180,    43,    45,    40,
   111,    42,   112,    44,   232,   234,   111,   240,   112,   239,
    36,   192,   193,    46,    47,    48,    49,    50,    12,   202,
    43,    45,    40,   118,    42,   117,    44,   104,   118,   121,
   117,   209,   104,    36,   121,   210,    46,    47,    48,    49,
    50,    12,   212,    43,    45,    40,   244,    42,   239,    44,
   213,    43,    45,    40,   215,    42,    36,    44,   229,    46,
    47,    48,    49,    50,   180,   180,   236,    46,    47,    48,
    49,    50,    43,    45,    40,   253,    42,   254,    44,    43,
    45,    40,   255,    42,   258,    44,   261,   264,    46,    47,
    48,    49,    50,   124,   125,    46,    47,    48,    49,    50,
    43,    45,    40,   265,    42,   192,    44,    43,    45,   266,
   269,    42,   270,    44,   275,   280,    46,    47,    48,    49,
    50,   284,   285,    46,    47,    48,    49,    50,    43,    45,
    40,   286,    42,   290,    44,    43,    45,   292,   293,    42,
   295,    44,   296,   297,    46,    47,    48,    49,    50,   300,
   301,    46,    47,    48,    49,    50,    43,    45,    40,   180,
    42,   303,    44,    43,    45,    40,   304,    42,   305,    44,
   281,   306,    46,    47,    48,    49,    50,   307,   308,    46,
    47,    48,    49,    50,    43,    45,   175,   311,    42,   312,
    44,    43,    45,    40,   313,    42,   314,    44,   316,   317,
    46,    47,    48,    49,    50,   318,   319,    46,    47,    48,
    49,    50,    43,    45,   nil,   nil,    42,   nil,    44,    43,
    45,   nil,   nil,    42,   nil,    44,   nil,   nil,    46,    47,
    48,    49,    50,   nil,   nil,    46,    47,    48,    49,    50,
   172,   194,   170,   nil,   171,   nil,   173,    43,    45,    40,
   nil,    42,   nil,    44,   nil,   nil,   195,   196,   197,   198,
   199,   nil,   nil,    46,    47,    48,    49,    50,    43,    45,
    40,   nil,    42,   nil,    44,    43,    45,    40,   nil,    42,
   nil,    44,   nil,   nil,    46,    47,    48,    49,    50,   nil,
   nil,    46,    47,    48,    49,    50,    43,    45,    40,   nil,
    42,   nil,    44,    43,    45,   nil,   nil,    42,   nil,    44,
   nil,   nil,    46,    47,    48,    49,    50,   nil,   nil,    46,
    47,    48,    49,    50,    43,    45,    40,   nil,    42,   nil,
    44,    43,    45,    40,   nil,    42,   nil,    44,   nil,   nil,
    46,    47,    48,    49,    50,   nil,   nil,    46,    47,    48,
    49,    50,    43,    45,    40,   nil,    42,   nil,    44,    43,
    45,    40,   nil,    42,   nil,    44,   nil,   nil,    46,    47,
    48,    49,    50,   nil,   nil,    46,    47,    48,    49,    50,
    43,    45,   nil,   nil,    42,   nil,    44,    43,    45,    40,
   nil,    42,   nil,    44,   nil,   nil,    46,    47,    48,    49,
    50,   nil,   nil,    46,    47,    48,    49,    50,    43,    45,
    40,   nil,    42,   nil,    44,    43,    45,   273,   nil,    42,
   nil,    44,   nil,   nil,    46,    47,    48,    49,    50,   nil,
   nil,    46,    47,    48,    49,    50,    43,    45,   274,   nil,
    42,   nil,    44,    43,    45,   276,   nil,    42,   nil,    44,
   nil,   nil,    46,    47,    48,    49,    50,   nil,   nil,    46,
    47,    48,    49,    50,    43,    45,    40,   nil,    42,   nil,
    44,    43,    45,    40,   nil,    42,   nil,    44,   nil,   nil,
    46,    47,    48,    49,    50,   nil,   nil,    46,    47,    48,
    49,    50,    43,    45,    40,   nil,    42,   nil,    44,    43,
    45,    40,   nil,    42,   nil,    44,   nil,   nil,    46,    47,
    48,    49,    50,   nil,   nil,    46,    47,    48,    49,    50,
    43,    45,    40,   nil,    42,   nil,    44,    43,    45,   315,
   nil,    42,   nil,    44,   nil,   nil,    46,    47,    48,    49,
    50,   116,   nil,    46,    47,    48,    49,    50,   118,   250,
   247,   118,   104,   117,   249,   104,   260,   121,   nil,   281,
   nil,   nil,   nil,   nil,   251,   nil,   118,   288,   117,   118,
   104,   117,   121,   104,   118,   121,   117,   118,   104,   117,
   121,   104,   nil,   121,   118,   250,   247,   nil,   104,   nil,
   249,   118,   250,   247,   nil,   104,   nil,   249,   nil,   nil,
   251,   nil,   118,   250,   117,   nil,   104,   251,   249,   118,
   250,   310,   nil,   104,   nil,   249,   nil,   nil,   251,   nil,
   172,   169,   170,   nil,   171,   251,   173,   182,   184,   nil,
   118,   181,   117,   183,   104,   118,   121,   117,   118,   104,
   117,   121,   104,   118,   121,   117,   118,   104,   117,   121,
   104,   118,   121,   117,   nil,   104,   nil,   121,   188,   271,
   186,   118,   187,   117,   272,   104,   nil,   121 ]

racc_action_check = [
    63,    63,     5,    56,     1,    56,    73,   127,   127,    73,
    14,    63,    63,    63,     9,    63,     9,    63,   127,   127,
   127,     5,   127,     5,   127,    16,    73,    63,    63,    63,
    63,    63,   151,     9,   127,   127,   127,   127,   127,   224,
    10,    17,   151,   151,   151,    89,   151,    89,   151,   224,
   224,   224,   134,   224,   134,   224,    37,    10,   151,   151,
   151,   151,   151,    18,    89,   224,   224,   224,   224,   224,
     0,   134,     0,    37,    38,    22,    54,     0,    23,     0,
     0,     0,    24,     0,    91,     0,    54,     0,    54,     0,
    54,    38,     0,    67,    67,     0,     0,     0,     0,     0,
    67,    91,    67,    67,    67,    52,    67,    52,    67,    52,
    51,    72,    72,    66,    72,    67,    59,    60,    67,    67,
    67,    67,    67,    33,    98,    66,    33,    66,    33,    33,
    33,    72,    33,    71,    33,    98,    92,    74,    92,   147,
    98,    33,    77,    78,    33,    33,    33,    33,    33,    34,
   214,   147,    34,   147,    34,    34,    34,   112,    34,   112,
    34,   112,   214,   113,   214,    79,    93,    34,    93,    88,
    34,    34,    34,    34,    34,    26,    90,    26,    26,   113,
   113,    26,    87,    26,    87,    87,    87,   121,    87,   121,
    87,    99,   107,    26,    26,    26,    26,    26,   161,   108,
    87,    87,    87,    87,    87,   153,   109,   153,   153,   153,
   124,   153,   124,   153,   161,   161,   157,   166,   157,   166,
   153,   115,   116,   153,   153,   153,   153,   153,   154,   123,
   154,   154,   154,    58,   154,    58,   154,    58,    94,    58,
    94,   128,    94,   154,    94,   129,   154,   154,   154,   154,
   154,   265,   132,   265,   265,   265,   167,   265,   167,   265,
   133,    12,    12,    12,   144,    12,   265,    12,   158,   265,
   265,   265,   265,   265,   160,   162,   163,    12,    12,    12,
    12,    12,    13,    13,    13,   169,    13,   178,    13,    36,
    36,    36,   189,    36,   202,    36,   213,   218,    13,    13,
    13,    13,    13,   220,   222,    36,    36,    36,    36,    36,
    62,    62,    62,   225,    62,   230,    62,    64,    64,   232,
   236,    64,   238,    64,   245,   253,    62,    62,    62,    62,
    62,   256,   257,    64,    64,    64,    64,    64,    68,    68,
    68,   260,    68,   264,    68,    69,    69,   267,   268,    69,
   271,    69,   273,   274,    68,    68,    68,    68,    68,   280,
   281,    69,    69,    69,    69,    69,    70,    70,    70,   283,
    70,   284,    70,    75,    75,    75,   285,    75,   288,    75,
   290,   292,    70,    70,    70,    70,    70,   293,   295,    75,
    75,    75,    75,    75,   103,   103,   103,   300,   103,   304,
   103,   104,   104,   104,   307,   104,   308,   104,   311,   312,
   103,   103,   103,   103,   103,   313,   316,   104,   104,   104,
   104,   104,   111,   111,   nil,   nil,   111,   nil,   111,   117,
   117,   nil,   nil,   117,   nil,   117,   nil,   nil,   111,   111,
   111,   111,   111,   nil,   nil,   117,   117,   117,   117,   117,
   118,   118,   118,   nil,   118,   nil,   118,   126,   126,   126,
   nil,   126,   nil,   126,   nil,   nil,   118,   118,   118,   118,
   118,   nil,   nil,   126,   126,   126,   126,   126,   130,   130,
   130,   nil,   130,   nil,   130,   143,   143,   143,   nil,   143,
   nil,   143,   nil,   nil,   130,   130,   130,   130,   130,   nil,
   nil,   143,   143,   143,   143,   143,   145,   145,   145,   nil,
   145,   nil,   145,   146,   146,   nil,   nil,   146,   nil,   146,
   nil,   nil,   145,   145,   145,   145,   145,   nil,   nil,   146,
   146,   146,   146,   146,   148,   148,   148,   nil,   148,   nil,
   148,   149,   149,   149,   nil,   149,   nil,   149,   nil,   nil,
   148,   148,   148,   148,   148,   nil,   nil,   149,   149,   149,
   149,   149,   150,   150,   150,   nil,   150,   nil,   150,   205,
   205,   205,   nil,   205,   nil,   205,   nil,   nil,   150,   150,
   150,   150,   150,   nil,   nil,   205,   205,   205,   205,   205,
   215,   215,   nil,   nil,   215,   nil,   215,   216,   216,   216,
   nil,   216,   nil,   216,   nil,   nil,   215,   215,   215,   215,
   215,   nil,   nil,   216,   216,   216,   216,   216,   217,   217,
   217,   nil,   217,   nil,   217,   240,   240,   240,   nil,   240,
   nil,   240,   nil,   nil,   217,   217,   217,   217,   217,   nil,
   nil,   240,   240,   240,   240,   240,   244,   244,   244,   nil,
   244,   nil,   244,   247,   247,   247,   nil,   247,   nil,   247,
   nil,   nil,   244,   244,   244,   244,   244,   nil,   nil,   247,
   247,   247,   247,   247,   249,   249,   249,   nil,   249,   nil,
   249,   250,   250,   250,   nil,   250,   nil,   250,   nil,   nil,
   249,   249,   249,   249,   249,   nil,   nil,   250,   250,   250,
   250,   250,   251,   251,   251,   nil,   251,   nil,   251,   262,
   262,   262,   nil,   262,   nil,   262,   nil,   nil,   251,   251,
   251,   251,   251,   nil,   nil,   262,   262,   262,   262,   262,
   263,   263,   263,   nil,   263,   nil,   263,   310,   310,   310,
   nil,   310,   nil,   310,   nil,   nil,   263,   263,   263,   263,
   263,    57,   nil,   310,   310,   310,   310,   310,   168,   168,
   168,    57,   168,    57,   168,    57,   212,    57,   nil,   254,
   nil,   nil,   nil,   nil,   168,   nil,   212,   261,   212,   254,
   212,   254,   212,   254,    95,   254,    95,   261,    95,   261,
    95,   261,   nil,   261,   270,   270,   270,   nil,   270,   nil,
   270,   275,   275,   275,   nil,   275,   nil,   275,   nil,   nil,
   270,   nil,   296,   296,   296,   nil,   296,   275,   296,   297,
   297,   297,   nil,   297,   nil,   297,   nil,   nil,   296,   nil,
   101,   101,   101,   nil,   101,   297,   101,   110,   110,   nil,
   125,   110,   125,   110,   125,   159,   125,   159,   190,   159,
   190,   159,   190,   191,   190,   191,   233,   191,   233,   191,
   233,   234,   233,   234,   nil,   234,   nil,   234,   239,   239,
   239,   255,   239,   255,   239,   255,   nil,   255 ]

racc_action_pointer = [
    63,     4,   nil,   nil,   nil,     0,   nil,   nil,   nil,     2,
    26,   nil,   245,   266,   -11,   nil,    21,    18,    49,   nil,
   nil,   nil,    54,    78,    82,   nil,   161,   nil,   nil,   nil,
   nil,   nil,   nil,   112,   138,   nil,   273,    42,    60,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   110,    89,   nil,    70,   nil,   -15,   745,   217,   102,
   103,   nil,   294,    -5,   301,   nil,   107,    86,   322,   329,
   350,   129,   100,    -5,   126,   357,   nil,   117,   115,   137,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   168,   142,    33,
   146,    70,   118,   148,   222,   768,   nil,   nil,   121,   176,
   nil,   814,   nil,   378,   385,   nil,   nil,   180,   193,   194,
   821,   406,   141,   149,   nil,   209,   216,   413,   434,   nil,
   nil,   169,   nil,   199,   192,   824,   441,     2,   235,   240,
   462,   nil,   238,   254,    40,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   469,   260,   490,   497,   133,   518,   525,
   546,    26,   nil,   191,   214,   nil,   nil,   198,   256,   829,
   262,   184,   263,   246,   nil,   nil,   199,   238,   742,   270,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   273,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   280,
   832,   837,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   282,   nil,   nil,   553,   nil,   nil,   nil,   nil,
   nil,   nil,   760,   282,   144,   574,   581,   602,   291,   nil,
   289,   nil,   290,   nil,    33,   285,   nil,   nil,   nil,   nil,
   303,   nil,   307,   840,   845,   nil,   308,   nil,   307,   852,
   609,   nil,   nil,   nil,   630,   309,   nil,   637,   nil,   658,
   665,   686,   nil,   307,   763,   855,   301,   302,   nil,   nil,
   335,   771,   693,   714,   329,   237,   nil,   317,   318,   nil,
   778,   335,   nil,   337,   338,   785,   nil,   nil,   nil,   nil,
   341,   354,   nil,   357,   359,   364,   nil,   nil,   372,   nil,
   374,   nil,   369,   375,   nil,   370,   796,   803,   nil,   nil,
   382,   nil,   nil,   nil,   369,   nil,   nil,   374,   388,   nil,
   721,   390,   397,   403,   nil,   nil,   398,   nil,   nil,   nil ]

racc_action_default = [
  -176,  -176,    -1,    -2,    -3,    -4,    -5,    -6,    -7,    -8,
    -9,   -10,  -176,  -176,   -34,   -35,   -36,   -37,   -38,   -39,
   -40,   -41,  -176,   -49,   -51,   -52,  -176,   -64,   -67,   -68,
   -69,   -70,   -71,  -176,  -176,  -107,  -176,  -109,  -110,  -111,
  -128,  -129,  -130,  -131,  -132,  -133,  -134,  -135,  -136,  -137,
  -138,  -176,  -176,   -76,  -176,  -112,  -176,  -176,  -176,    -8,
    -9,   -11,  -176,  -176,  -176,   -72,  -176,  -176,   -55,  -176,
  -170,  -176,    -8,    -9,   -10,  -176,   -38,  -176,   -81,   -86,
   -87,   -88,   -90,   -91,   -92,   -93,   -94,  -176,  -176,  -176,
  -176,  -176,  -176,  -176,  -176,  -176,   320,   -12,   -13,  -176,
   -16,  -176,   -31,  -176,  -176,  -152,   -27,   -29,  -176,  -126,
  -176,  -176,  -176,  -176,   -28,   -30,  -176,  -176,  -176,  -153,
  -160,  -176,  -162,  -176,  -176,  -176,  -176,  -176,  -176,  -176,
   -73,  -174,  -176,  -176,    -8,   -47,   -48,   -49,   -50,   -51,
   -53,   -54,   -58,   -56,  -176,  -171,  -176,  -176,   -98,   -97,
   -96,  -176,   -79,  -176,  -176,   -95,   -80,  -176,  -176,  -176,
  -126,  -176,  -126,  -176,   -14,   -18,  -176,  -176,  -176,  -176,
  -147,  -148,  -149,  -150,  -145,  -151,  -146,  -114,   -44,   -59,
  -127,   -60,   -61,   -62,   -63,  -139,  -140,  -141,  -142,  -176,
  -176,  -176,  -120,   -45,  -154,  -155,  -156,  -157,  -158,  -159,
  -161,  -163,  -176,   -29,   -30,  -176,   -26,   -42,   -77,   -43,
   -78,  -175,  -176,  -176,  -176,  -176,  -172,   -74,  -176,  -101,
  -176,  -100,  -176,   -99,  -176,   -83,   -84,   -85,   -89,  -108,
  -176,  -113,  -176,  -176,  -176,  -117,  -176,   -19,  -176,  -176,
  -176,  -143,   -20,   -21,  -176,  -176,   -32,  -176,  -164,  -176,
  -176,  -176,  -169,  -176,  -176,  -115,  -176,  -176,  -121,  -102,
  -176,  -176,   -75,  -173,   -44,  -176,  -116,  -176,  -176,  -118,
  -176,  -176,  -144,  -176,  -176,  -176,  -168,  -165,  -166,  -167,
  -176,  -176,  -106,  -126,  -176,  -176,  -105,  -103,  -176,   -57,
  -176,   -82,  -176,  -176,   -23,  -176,  -176,  -176,   -15,   -33,
  -176,   -46,  -119,  -122,  -176,  -104,  -124,  -176,  -176,   -25,
  -176,  -176,  -176,  -176,   -24,   -22,  -176,  -123,  -125,   -17 ]

racc_goto_table = [
    70,   179,   130,    13,   228,    11,   248,   115,   123,   226,
   227,   113,   245,     5,    14,     9,    63,    11,    68,    10,
    18,   132,    22,    23,    71,     1,    24,    59,   237,   243,
     2,    60,   309,   309,   241,   241,    75,    75,   131,    77,
    88,     3,     4,    70,   162,   163,     6,   160,   161,    61,
    97,    89,   231,    98,   235,    91,   164,    99,   298,   100,
   242,   143,   102,   299,    15,   126,   127,   144,    16,    17,
    75,   142,    11,   145,   135,   204,   109,   174,   151,   203,
   136,   138,   134,    27,   217,   185,    10,    18,    28,   140,
   137,   174,    11,   139,    29,    30,    31,    32,   225,    90,
   155,   105,    59,   nil,   nil,   nil,    60,   176,   248,   230,
   nil,   nil,   nil,   248,   294,   228,   nil,   nil,   nil,   nil,
   131,   291,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   205,
   206,   nil,   nil,   211,   248,   248,   nil,   nil,   nil,   nil,
   256,   257,   nil,   nil,   nil,   nil,   142,   nil,   216,   nil,
   nil,   nil,   nil,   262,   224,   223,    75,    75,   nil,   nil,
   nil,   nil,   259,   219,   220,   220,   nil,   nil,   221,   222,
   nil,   nil,   nil,   nil,   nil,   302,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   267,   268,   nil,   nil,   nil,   nil,   131,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   282,   283,   nil,   nil,   206,   nil,
   nil,   287,   nil,   nil,   185,   nil,   nil,   nil,   185,   263,
   211,   174,   nil,   nil,   nil,   nil,   nil,   206,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   277,   278,   279,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   211,   289,   nil,    75,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   174 ]

racc_goto_check = [
    43,    45,    52,    23,    58,    11,    66,    26,    26,    57,
    57,    24,    16,     5,    30,     9,    23,    11,    42,    10,
    34,    24,    38,    40,    53,     1,    41,     9,    19,    19,
     2,    10,    22,    22,    25,    25,    23,    23,    43,    54,
    54,     3,     4,    43,    26,    26,     6,    24,    24,     7,
    12,     9,    45,    13,    45,    10,    14,    15,    17,    18,
    20,    42,    28,    29,    31,    23,    23,    53,    32,    33,
    23,    23,    11,    23,    39,    26,    44,    43,    23,    24,
     5,    30,     9,    46,    52,    43,    10,    34,    47,    38,
    40,    43,    11,    41,    48,    49,    50,    51,    56,    61,
     5,    65,     9,   nil,   nil,   nil,    10,    23,    66,    26,
   nil,   nil,   nil,    66,    16,    58,   nil,   nil,   nil,   nil,
    43,    57,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    23,
    23,   nil,   nil,    23,    66,    66,   nil,   nil,   nil,   nil,
    26,    26,   nil,   nil,   nil,   nil,    23,   nil,    23,   nil,
   nil,   nil,   nil,    52,    23,    11,    23,    23,   nil,   nil,
   nil,   nil,    26,     9,     9,     9,   nil,   nil,    10,    10,
   nil,   nil,   nil,   nil,   nil,    45,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,    26,    26,   nil,   nil,   nil,   nil,    43,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    26,    26,   nil,   nil,    23,   nil,
   nil,    26,   nil,   nil,    43,   nil,   nil,   nil,    43,    23,
    23,    43,   nil,   nil,   nil,   nil,   nil,    23,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    23,    23,    23,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    23,    23,   nil,    23,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    43 ]

racc_goto_pointer = [
   nil,    25,    30,    41,    42,    13,    46,    37,   nil,    15,
    19,     5,    -2,     1,   -42,     5,  -156,  -217,     7,  -138,
  -107,   nil,  -264,     3,   -45,  -132,   -50,   nil,    10,  -212,
    14,    64,    68,    69,    20,   nil,   nil,   nil,    22,     7,
    23,    26,    -8,   -26,    22,  -108,    83,    88,    94,    95,
    96,    97,   -62,    -2,     6,   nil,   -55,  -144,  -150,   nil,
   nil,    63,   nil,   nil,   nil,    49,  -162,   nil ]

racc_goto_default = [
   nil,   nil,   nil,   nil,   nil,    84,   nil,     7,     8,    72,
    73,    74,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   238,   252,    62,   107,   106,   nil,   114,   nil,   246,
    86,   nil,   nil,   nil,    76,    19,    20,    21,   nil,   nil,
    85,   nil,   nil,    41,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    78,    79,    80,    81,    82,
    83,    35,    37,    38,    39,   119,   120,   122 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 38, :_reduce_none,
  1, 38, :_reduce_none,
  1, 38, :_reduce_none,
  1, 39, :_reduce_none,
  1, 39, :_reduce_none,
  1, 42, :_reduce_none,
  1, 42, :_reduce_none,
  1, 44, :_reduce_8,
  1, 44, :_reduce_9,
  1, 44, :_reduce_10,
  2, 45, :_reduce_11,
  3, 43, :_reduce_12,
  1, 49, :_reduce_none,
  2, 49, :_reduce_14,
  5, 50, :_reduce_15,
  1, 50, :_reduce_none,
  8, 55, :_reduce_17,
  1, 51, :_reduce_18,
  2, 51, :_reduce_19,
  2, 51, :_reduce_20,
  1, 57, :_reduce_none,
  5, 57, :_reduce_22,
  3, 56, :_reduce_23,
  5, 56, :_reduce_24,
  4, 56, :_reduce_25,
  4, 46, :_reduce_26,
  1, 61, :_reduce_none,
  1, 63, :_reduce_none,
  3, 47, :_reduce_29,
  3, 48, :_reduce_30,
  1, 52, :_reduce_none,
  1, 53, :_reduce_none,
  1, 54, :_reduce_none,
  1, 40, :_reduce_none,
  1, 40, :_reduce_none,
  1, 40, :_reduce_none,
  1, 40, :_reduce_none,
  1, 67, :_reduce_38,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  4, 71, :_reduce_42,
  4, 71, :_reduce_43,
  4, 72, :_reduce_44,
  4, 73, :_reduce_45,
  7, 74, :_reduce_46,
  3, 68, :_reduce_47,
  1, 75, :_reduce_none,
  1, 75, :_reduce_none,
  1, 75, :_reduce_none,
  1, 75, :_reduce_none,
  1, 75, :_reduce_none,
  1, 76, :_reduce_none,
  1, 76, :_reduce_none,
  2, 69, :_reduce_55,
  3, 69, :_reduce_56,
  5, 79, :_reduce_57,
  2, 79, :_reduce_58,
  4, 70, :_reduce_59,
  2, 81, :_reduce_60,
  2, 81, :_reduce_61,
  2, 81, :_reduce_62,
  2, 81, :_reduce_63,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  1, 41, :_reduce_none,
  2, 83, :_reduce_72,
  3, 88, :_reduce_73,
  4, 88, :_reduce_74,
  5, 88, :_reduce_75,
  2, 87, :_reduce_76,
  4, 86, :_reduce_77,
  4, 86, :_reduce_78,
  3, 84, :_reduce_79,
  3, 85, :_reduce_80,
  1, 91, :_reduce_81,
  5, 91, :_reduce_82,
  3, 91, :_reduce_83,
  3, 91, :_reduce_84,
  3, 91, :_reduce_85,
  1, 91, :_reduce_86,
  1, 91, :_reduce_87,
  1, 93, :_reduce_88,
  3, 93, :_reduce_89,
  1, 95, :_reduce_none,
  1, 95, :_reduce_none,
  1, 96, :_reduce_none,
  1, 96, :_reduce_none,
  1, 96, :_reduce_none,
  2, 92, :_reduce_95,
  2, 94, :_reduce_96,
  2, 94, :_reduce_97,
  2, 94, :_reduce_98,
  3, 97, :_reduce_99,
  3, 97, :_reduce_100,
  3, 97, :_reduce_101,
  5, 78, :_reduce_102,
  6, 78, :_reduce_103,
  7, 78, :_reduce_104,
  6, 78, :_reduce_105,
  6, 78, :_reduce_106,
  1, 77, :_reduce_none,
  4, 77, :_reduce_108,
  1, 98, :_reduce_109,
  1, 98, :_reduce_110,
  1, 98, :_reduce_111,
  2, 99, :_reduce_112,
  4, 100, :_reduce_113,
  4, 100, :_reduce_114,
  5, 100, :_reduce_115,
  5, 100, :_reduce_116,
  4, 101, :_reduce_117,
  5, 101, :_reduce_118,
  7, 101, :_reduce_119,
  4, 101, :_reduce_120,
  5, 101, :_reduce_121,
  7, 101, :_reduce_122,
  9, 101, :_reduce_123,
  7, 101, :_reduce_124,
  9, 101, :_reduce_125,
  0, 82, :_reduce_126,
  1, 82, :_reduce_none,
  1, 60, :_reduce_128,
  1, 60, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  1, 80, :_reduce_none,
  2, 62, :_reduce_139,
  2, 62, :_reduce_140,
  2, 62, :_reduce_141,
  2, 62, :_reduce_142,
  1, 58, :_reduce_none,
  2, 58, :_reduce_144,
  2, 102, :_reduce_145,
  2, 102, :_reduce_146,
  2, 102, :_reduce_147,
  2, 102, :_reduce_148,
  2, 102, :_reduce_149,
  2, 102, :_reduce_150,
  2, 65, :_reduce_none,
  1, 65, :_reduce_none,
  1, 103, :_reduce_none,
  2, 103, :_reduce_154,
  2, 103, :_reduce_155,
  2, 103, :_reduce_156,
  2, 103, :_reduce_157,
  2, 103, :_reduce_158,
  2, 103, :_reduce_159,
  1, 104, :_reduce_none,
  2, 104, :_reduce_161,
  1, 64, :_reduce_none,
  2, 64, :_reduce_163,
  1, 59, :_reduce_none,
  2, 59, :_reduce_165,
  2, 59, :_reduce_166,
  2, 59, :_reduce_167,
  2, 66, :_reduce_none,
  1, 66, :_reduce_none,
  1, 90, :_reduce_170,
  2, 90, :_reduce_171,
  3, 90, :_reduce_172,
  4, 90, :_reduce_173,
  1, 89, :_reduce_174,
  2, 89, :_reduce_175 ]

racc_reduce_n = 176

racc_shift_n = 320

racc_token_table = {
  false => 0,
  :error => 1,
  :T => 2,
  :Z => 3,
  :E => 4,
  :X => 5,
  :U => 6,
  :UNKNOWN => 7,
  :OPEN => 8,
  :LONGYEAR => 9,
  :UNMATCHED => 10,
  :DOTS => 11,
  :UA => 12,
  :PUA => 13,
  "-" => 14,
  ":" => 15,
  "2" => 16,
  "4" => 17,
  "0" => 18,
  "+" => 19,
  "1" => 20,
  "/" => 21,
  "3" => 22,
  "^" => 23,
  "[" => 24,
  "]" => 25,
  "{" => 26,
  "}" => 27,
  "," => 28,
  "(" => 29,
  ")" => 30,
  "-(" => 31,
  "5" => 32,
  "6" => 33,
  "7" => 34,
  "8" => 35,
  "9" => 36 }

racc_nt_base = 37

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "T",
  "Z",
  "E",
  "X",
  "U",
  "UNKNOWN",
  "OPEN",
  "LONGYEAR",
  "UNMATCHED",
  "DOTS",
  "UA",
  "PUA",
  "\"-\"",
  "\":\"",
  "\"2\"",
  "\"4\"",
  "\"0\"",
  "\"+\"",
  "\"1\"",
  "\"/\"",
  "\"3\"",
  "\"^\"",
  "\"[\"",
  "\"]\"",
  "\"{\"",
  "\"}\"",
  "\",\"",
  "\"(\"",
  "\")\"",
  "\"-(\"",
  "\"5\"",
  "\"6\"",
  "\"7\"",
  "\"8\"",
  "\"9\"",
  "$start",
  "edtf",
  "level_0_expression",
  "level_1_expression",
  "level_2_expression",
  "date",
  "date_time",
  "positive_date",
  "negative_date",
  "year",
  "year_month",
  "year_month_day",
  "time",
  "base_time",
  "zone_offset",
  "hour",
  "minute",
  "second",
  "midnight",
  "zone_offset_hour",
  "positive_zone_offset",
  "d01_13",
  "d01_59",
  "digit",
  "month",
  "d01_12",
  "day",
  "d01_31",
  "d00_23",
  "d00_59",
  "unspecified",
  "level_1_interval",
  "long_year_simple",
  "season",
  "unspecified_year",
  "unspecified_month",
  "unspecified_day",
  "unspecified_day_and_month",
  "level_1_start",
  "level_1_end",
  "partial_uncertain_or_approximate",
  "partial_unspecified",
  "long_year",
  "positive_digit",
  "season_number",
  "ua",
  "season_qualified",
  "choice_list",
  "inclusive_list",
  "masked_precision",
  "date_and_calendar",
  "long_year_scientific",
  "integer",
  "int1_4",
  "list",
  "earlier",
  "list_elements",
  "later",
  "list_element",
  "atomic",
  "consecutives",
  "pua_base",
  "pua_year",
  "pua_year_month",
  "pua_year_month_day",
  "d01_23",
  "d01_29",
  "d01_30" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

# reduce 2 omitted

# reduce 3 omitted

# reduce 4 omitted

# reduce 5 omitted

# reduce 6 omitted

# reduce 7 omitted

module_eval(<<'.,.,', 'edtf.y', 54)
  def _reduce_8(val, _values, result)
     result = Date.new(val[0]).year_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 55)
  def _reduce_9(val, _values, result)
     result = Date.new(*val.flatten).month_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 56)
  def _reduce_10(val, _values, result)
     result = Date.new(*val.flatten).day_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 59)
  def _reduce_11(val, _values, result)
     result = -val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 63)
  def _reduce_12(val, _values, result)
        result = DateTime.new(val[0].year, val[0].month, val[0].day, *val[2])
    result.skip_timezone = (val[2].length == 3)

    result
  end
.,.,

# reduce 13 omitted

module_eval(<<'.,.,', 'edtf.y', 68)
  def _reduce_14(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 70)
  def _reduce_15(val, _values, result)
     result = val.values_at(0, 2, 4)
    result
  end
.,.,

# reduce 16 omitted

module_eval(<<'.,.,', 'edtf.y', 73)
  def _reduce_17(val, _values, result)
     result = [24, 0, 0]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 75)
  def _reduce_18(val, _values, result)
     result = 0
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 76)
  def _reduce_19(val, _values, result)
     result = -1 * val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 77)
  def _reduce_20(val, _values, result)
     result = val[1]
    result
  end
.,.,

# reduce 21 omitted

module_eval(<<'.,.,', 'edtf.y', 81)
  def _reduce_22(val, _values, result)
     result = 0
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 85)
  def _reduce_23(val, _values, result)
     result = Rational(val[0] * 60 + val[2], 1440)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 86)
  def _reduce_24(val, _values, result)
     result = Rational(840, 1440)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 87)
  def _reduce_25(val, _values, result)
     result = Rational(val[3], 1440)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 91)
  def _reduce_26(val, _values, result)
        result = val.zip([1000,100,10,1]).reduce(0) { |s,(a,b)| s += a * b }

    result
  end
.,.,

# reduce 27 omitted

# reduce 28 omitted

module_eval(<<'.,.,', 'edtf.y', 97)
  def _reduce_29(val, _values, result)
     result = [val[0], val[2]]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 104)
  def _reduce_30(val, _values, result)
        result = val[0] << val[2]
    if result[2] > 31 || (result[2] > 30 && [2,4,6,9,11].include?(result[1])) || (result[2] > 29 && result[1] == 2)
      raise ArgumentError, "invalid date (invalid days #{result[2]} for month #{result[1]})"
    end

    result
  end
.,.,

# reduce 31 omitted

# reduce 32 omitted

# reduce 33 omitted

# reduce 34 omitted

# reduce 35 omitted

# reduce 36 omitted

# reduce 37 omitted

module_eval(<<'.,.,', 'edtf.y', 127)
  def _reduce_38(val, _values, result)
                    result = Date.new(val[0][0]).year_precision!
                result.unspecified.year[2,2] = val[0][1]

    result
  end
.,.,

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

module_eval(<<'.,.,', 'edtf.y', 138)
  def _reduce_42(val, _values, result)
          result = [val[0,3].zip([1000,100,10]).reduce(0) { |s,(a,b)| s += a * b }, [false,true]]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 142)
  def _reduce_43(val, _values, result)
          result = [val[0,2].zip([1000,100]).reduce(0) { |s,(a,b)| s += a * b }, [true, true]]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 146)
  def _reduce_44(val, _values, result)
        result = Date.new(val[0]).unspecified!(:month)
    result.precision = :month

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 151)
  def _reduce_45(val, _values, result)
        result = Date.new(*val[0]).unspecified!(:day)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 155)
  def _reduce_46(val, _values, result)
        result = Date.new(val[0]).unspecified!([:day,:month])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 160)
  def _reduce_47(val, _values, result)
        result = Interval.new(val[0], val[2])

    result
  end
.,.,

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

# reduce 53 omitted

# reduce 54 omitted

module_eval(<<'.,.,', 'edtf.y', 171)
  def _reduce_55(val, _values, result)
          result = Date.new(val[1])
      result.precision = :year

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 176)
  def _reduce_56(val, _values, result)
          result = Date.new(-1 * val[2])
      result.precision = :year

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 183)
  def _reduce_57(val, _values, result)
          result = val.zip([10000,1000,100,10,1]).reduce(0) { |s,(a,b)| s += a * b }

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 185)
  def _reduce_58(val, _values, result)
     result = 10 * val[0] + val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 190)
  def _reduce_59(val, _values, result)
        result = Season.new(val[0], val[2])
    val[3].each { |ua| result.send(ua) }

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 194)
  def _reduce_60(val, _values, result)
     result = 21
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 195)
  def _reduce_61(val, _values, result)
     result = 22
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 196)
  def _reduce_62(val, _values, result)
     result = 23
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 197)
  def _reduce_63(val, _values, result)
     result = 24
    result
  end
.,.,

# reduce 64 omitted

# reduce 65 omitted

# reduce 66 omitted

# reduce 67 omitted

# reduce 68 omitted

# reduce 69 omitted

# reduce 70 omitted

# reduce 71 omitted

module_eval(<<'.,.,', 'edtf.y', 215)
  def _reduce_72(val, _values, result)
     result = val[0]; result.qualifier = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 221)
  def _reduce_73(val, _values, result)
          result = Date.new(val[0].year * 10 ** val[2]).year_precision!

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 225)
  def _reduce_74(val, _values, result)
          result = Date.new(val[1] * 10 ** val[3]).year_precision!

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 229)
  def _reduce_75(val, _values, result)
          result = Date.new(-1 * val[2] * 10 ** val[4]).year_precision!

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 234)
  def _reduce_76(val, _values, result)
     result = val[0]; result.calendar = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 240)
  def _reduce_77(val, _values, result)
          d = val[0,3].zip([1000,100,10]).reduce(0) { |s,(a,b)| s += a * b }
      result = EDTF::Decade.new(d)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 245)
  def _reduce_78(val, _values, result)
          d = val[0,2].zip([1000,100]).reduce(0) { |s,(a,b)| s += a * b }
      result = EDTF::Century.new(d)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 251)
  def _reduce_79(val, _values, result)
     result = val[1].choice!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 253)
  def _reduce_80(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 255)
  def _reduce_81(val, _values, result)
     result = EDTF::Set.new(val[0]).earlier!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 256)
  def _reduce_82(val, _values, result)
     result = EDTF::Set.new([val[0]] + val[2] + [val[4]]).earlier!.later!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 257)
  def _reduce_83(val, _values, result)
     result = EDTF::Set.new([val[0]] + val[2]).earlier!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 258)
  def _reduce_84(val, _values, result)
     result = EDTF::Set.new([val[0]] + [val[2]]).earlier!.later!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 259)
  def _reduce_85(val, _values, result)
     result = EDTF::Set.new(val[0] + [val[2]]).later!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 260)
  def _reduce_86(val, _values, result)
     result = EDTF::Set.new(*val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 261)
  def _reduce_87(val, _values, result)
     result = EDTF::Set.new(val[0]).later!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 264)
  def _reduce_88(val, _values, result)
     result = [val[0]].flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 265)
  def _reduce_89(val, _values, result)
     result = val[0] + [val[2]].flatten
    result
  end
.,.,

# reduce 90 omitted

# reduce 91 omitted

# reduce 92 omitted

# reduce 93 omitted

# reduce 94 omitted

module_eval(<<'.,.,', 'edtf.y', 277)
  def _reduce_95(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 279)
  def _reduce_96(val, _values, result)
     result = Date.new(*val[0]).year_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 280)
  def _reduce_97(val, _values, result)
     result = Date.new(*val[0]).month_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 281)
  def _reduce_98(val, _values, result)
     result = Date.new(val[0]).year_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 284)
  def _reduce_99(val, _values, result)
     result = (Date.new(val[0]).day_precision! .. Date.new(val[2]).day_precision!)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 285)
  def _reduce_100(val, _values, result)
     result = (Date.new(val[0]).month_precision! .. Date.new(val[2]).month_precision!)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 286)
  def _reduce_101(val, _values, result)
     result = (Date.new(val[0]).year_precision! .. Date.new(val[2]).year_precision!)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 292)
  def _reduce_102(val, _values, result)
          result = Date.new(val[0][0], val[2], val[4])
      result.unspecified.year[2,2] = val[0][1]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 297)
  def _reduce_103(val, _values, result)
          result = Date.new(val[0][0], 1, val[5])
      result.unspecified.year[2,2] = val[0][1]
      result.unspecified!(:month)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 303)
  def _reduce_104(val, _values, result)
          result = Date.new(val[0][0], 1, 1)
      result.unspecified.year[2,2] = val[0][1]
      result.unspecified!([:month, :day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 309)
  def _reduce_105(val, _values, result)
          result = Date.new(val[0][0], val[2], 1)
      result.unspecified.year[2,2] = val[0][1]
      result.unspecified!(:day)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 315)
  def _reduce_106(val, _values, result)
          result = Date.new(val[0], 1, val[5])
      result.unspecified!(:month)

    result
  end
.,.,

# reduce 107 omitted

module_eval(<<'.,.,', 'edtf.y', 322)
  def _reduce_108(val, _values, result)
     result = uoa(val[1], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 325)
  def _reduce_109(val, _values, result)
     result = val[0].year_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 326)
  def _reduce_110(val, _values, result)
     result = val[0][0].month_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 327)
  def _reduce_111(val, _values, result)
     result = val[0].day_precision!
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 329)
  def _reduce_112(val, _values, result)
     result = uoa(Date.new(val[0]), val[1], :year)
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 333)
  def _reduce_113(val, _values, result)
          result = [uoa(val[0].change(:month => val[2]), val[3], [:month, :year])]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 336)
  def _reduce_114(val, _values, result)
            result = [uoa(Date.new(val[0], val[2]), val[3], [:year, :month])]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 339)
  def _reduce_115(val, _values, result)
            result = [uoa(Date.new(val[0], val[2]), val[4], [:month]), true]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 342)
  def _reduce_116(val, _values, result)
            result = [uoa(val[0].change(:month => val[2]), val[4], [:month]), true]

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 348)
  def _reduce_117(val, _values, result)
          result = uoa(val[0][0].change(:day => val[2]), val[3], val[0][1] ? [:day] : nil)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 351)
  def _reduce_118(val, _values, result)
            result = uoa(val[0][0].change(:day => val[2]), val[4], [:day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 354)
  def _reduce_119(val, _values, result)
            result = uoa(uoa(Date.new(val[0], val[2], val[5]), val[4], :month), val[6], :day)

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 357)
  def _reduce_120(val, _values, result)
            result = uoa(Date.new(val[0][0], val[0][1], val[2]), val[3])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 360)
  def _reduce_121(val, _values, result)
            result = uoa(Date.new(val[0][0], val[0][1], val[2]), val[4], [:day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 363)
  def _reduce_122(val, _values, result)
            result = uoa(Date.new(val[0], val[2], val[4]), val[6], [:month, :day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 366)
  def _reduce_123(val, _values, result)
            result = Date.new(val[0], val[2], val[4])
        result = uoa(result, val[6], [:day])
        result = uoa(result, val[8], [:month, :day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 371)
  def _reduce_124(val, _values, result)
            result = val[0].change(:month => val[2], :day => val[4])
        result = uoa(result, val[6], [:month, :day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 375)
  def _reduce_125(val, _values, result)
            result = val[0].change(:month => val[2], :day => val[4])
        result = uoa(result, val[6], [:day])
        result = uoa(result, val[8], [:month, :day])

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 386)
  def _reduce_126(val, _values, result)
     result = []
    result
  end
.,.,

# reduce 127 omitted

module_eval(<<'.,.,', 'edtf.y', 390)
  def _reduce_128(val, _values, result)
     result = 0
    result
  end
.,.,

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

# reduce 136 omitted

# reduce 137 omitted

# reduce 138 omitted

module_eval(<<'.,.,', 'edtf.y', 396)
  def _reduce_139(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 397)
  def _reduce_140(val, _values, result)
     result = 10
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 398)
  def _reduce_141(val, _values, result)
     result = 11
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 399)
  def _reduce_142(val, _values, result)
     result = 12
    result
  end
.,.,

# reduce 143 omitted

module_eval(<<'.,.,', 'edtf.y', 403)
  def _reduce_144(val, _values, result)
     result = 13
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 406)
  def _reduce_145(val, _values, result)
     result = val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 407)
  def _reduce_146(val, _values, result)
     result = 10 + val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 408)
  def _reduce_147(val, _values, result)
     result = 20
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 409)
  def _reduce_148(val, _values, result)
     result = 21
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 410)
  def _reduce_149(val, _values, result)
     result = 22
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 411)
  def _reduce_150(val, _values, result)
     result = 23
    result
  end
.,.,

# reduce 151 omitted

# reduce 152 omitted

# reduce 153 omitted

module_eval(<<'.,.,', 'edtf.y', 419)
  def _reduce_154(val, _values, result)
     result = 24
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 420)
  def _reduce_155(val, _values, result)
     result = 25
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 421)
  def _reduce_156(val, _values, result)
     result = 26
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 422)
  def _reduce_157(val, _values, result)
     result = 27
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 423)
  def _reduce_158(val, _values, result)
     result = 28
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 424)
  def _reduce_159(val, _values, result)
     result = 29
    result
  end
.,.,

# reduce 160 omitted

module_eval(<<'.,.,', 'edtf.y', 428)
  def _reduce_161(val, _values, result)
     result = 30
    result
  end
.,.,

# reduce 162 omitted

module_eval(<<'.,.,', 'edtf.y', 432)
  def _reduce_163(val, _values, result)
     result = 31
    result
  end
.,.,

# reduce 164 omitted

module_eval(<<'.,.,', 'edtf.y', 436)
  def _reduce_165(val, _values, result)
     result = 30 + val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 437)
  def _reduce_166(val, _values, result)
     result = 40 + val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 438)
  def _reduce_167(val, _values, result)
     result = 50 + val[1]
    result
  end
.,.,

# reduce 168 omitted

# reduce 169 omitted

module_eval(<<'.,.,', 'edtf.y', 445)
  def _reduce_170(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 446)
  def _reduce_171(val, _values, result)
     result = 10 * val[0] + val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 449)
  def _reduce_172(val, _values, result)
               result = val.zip([100,10,1]).reduce(0) { |s,(a,b)| s += a * b }

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 453)
  def _reduce_173(val, _values, result)
               result = val.zip([1000,100,10,1]).reduce(0) { |s,(a,b)| s += a * b }

    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 457)
  def _reduce_174(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'edtf.y', 458)
  def _reduce_175(val, _values, result)
     result = 10 * val[0] + val[1]
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

  end   # class Parser
end   # module EDTF

#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'


require 'nasl/parser/tree'

require 'nasl/parser/argument'
require 'nasl/parser/array'
require 'nasl/parser/assigment'
require 'nasl/parser/block'
require 'nasl/parser/break'
require 'nasl/parser/call'
require 'nasl/parser/comment'
require 'nasl/parser/continue'
require 'nasl/parser/decrement'
require 'nasl/parser/empty'
require 'nasl/parser/export'
require 'nasl/parser/expression'
require 'nasl/parser/for'
require 'nasl/parser/foreach'
require 'nasl/parser/function'
require 'nasl/parser/global'
require 'nasl/parser/identifier'
require 'nasl/parser/if'
require 'nasl/parser/import'
require 'nasl/parser/include'
require 'nasl/parser/increment'
require 'nasl/parser/integer'
require 'nasl/parser/ip'
require 'nasl/parser/key_value_pair'
require 'nasl/parser/list'
require 'nasl/parser/local'
require 'nasl/parser/lvalue'
require 'nasl/parser/parameter'
require 'nasl/parser/reference'
require 'nasl/parser/repeat'
require 'nasl/parser/repetition'
require 'nasl/parser/return'
require 'nasl/parser/string'
require 'nasl/parser/undefined'
require 'nasl/parser/while'

module Nasl
  class Grammar < Racc::Parser

module_eval(<<'...end nasl.y/module_eval...', 'nasl.y', 582)

def n(cls, *args)
  begin
    Nasl.const_get(cls).new(@tree, *args)
  rescue
    puts "An exception occurred during the creation of a #{cls} instance."
    puts
    puts "The arguments passed to the constructor were:"
    puts args
    puts
    puts @tok.last.context
    puts
    raise
  end
end

def c(*args)
  n(:Comment, *args)
  args[1]
end

def on_error(type, value, stack)
  raise ParseException, "The language's grammar does not permit #{value.name} to appear here", value.context
end

def next_token
  @tok = @tkz.get_token

  if @first && @tok.first == :COMMENT
    n(:Comment, @tok.last)
    @tok = @tkz.get_token
  end
  @first = false

  return @tok
end

def parse(env, code, path)
  @first = true
  @tree = Tree.new(env)
  @tkz = Tokenizer.new(code, path)
  @tree.concat(do_parse)
end

...end nasl.y/module_eval...
##### State transition tables begin ###

clist = [
'144,143,161,162,163,164,165,166,157,158,159,160,153,152,151,154,155',
'156,145,146,147,149,150,82,54,111,148,81,218,83,55,51,50,56,54,54,80',
'11,78,65,55,55,53,63,54,54,66,95,102,103,55,55,53,53,64,54,218,269,67',
'94,96,55,53,53,97,98,99,100,101,102,103,104,82,53,267,217,81,130,83',
'131,51,50,130,54,131,80,130,54,131,55,51,50,68,55,54,72,95,147,149,150',
'55,53,73,148,54,53,102,103,74,96,55,109,53,97,98,99,100,101,102,103',
'104,82,53,112,114,81,54,83,133,51,50,134,55,136,80,137,54,138,141,51',
'50,167,55,54,53,95,147,149,150,55,10,11,148,54,53,172,37,37,96,55,185',
'53,97,98,99,100,101,102,103,104,82,53,192,195,81,225,83,226,51,50,148',
'54,148,80,179,148,253,55,254,255,54,54,54,256,95,257,55,55,55,53,97',
'98,99,100,101,102,103,96,53,53,53,97,98,99,100,101,102,103,104,82,54',
'258,259,81,260,83,55,51,50,262,54,266,80,268,270,43,55,273,53,54,274',
'54,275,95,148,55,148,55,53,97,98,99,100,101,102,103,96,53,148,53,97',
'98,99,100,101,102,103,104,82,148,276,43,81,300,83,301,51,50,306,,,80',
'154,155,156,145,146,147,149,150,54,,95,148,,,55,,145,146,147,149,150',
',,96,148,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',145,146,147,149,150,,,54,148,95,,,,55,,,145,146,147,149,150,94,96,184',
'148,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,97,98',
'99,100,101,,,,54,,95,,,,55,97,98,99,100,101,,,,96,,,53,97,98,99,100',
'101,102,103,104,82,,,,81,,83,,51,50,,,,80,97,98,99,100,101,,,,54,,95',
',,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51',
'50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100,101,102',
'103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,',
'53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54',
',95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83',
',51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102',
'103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,',
'53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54',
',95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83',
',51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102',
'103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,',
'53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54',
',95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83',
',51,50,,,,80,208,,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100',
'101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,',
',,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,',
',,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82',
',,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,,53,97,98,99',
'100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,',
',,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80',
',,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100,101,102,103,104',
'82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97',
'98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,',
',,55,,,,,,,,94,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51',
'50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100,101,102',
'103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,,96,,',
'53,97,98,99,100,101,102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54',
',95,,,,55,,,,,,,,,96,,,53,97,98,99,100,101,102,103,104,82,,,,81,,83',
',51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,94,96,,,53,97,98,99,100,101',
'102,103,104,82,,,,81,,83,,51,50,,,,80,,,,,,,,,54,,95,,,,55,,,,,,,,94',
'96,,,53,97,98,99,100,101,102,103,104,115,116,117,118,119,120,123,122',
'121,115,116,117,118,119,120,123,122,121,153,152,151,154,155,156,145',
'146,147,149,150,,,,148,,,,126,125,,,,124,,,,126,125,,,,124,144,143,161',
'162,163,164,165,166,157,158,159,160,153,152,151,154,155,156,145,146',
'147,149,150,,,,148,,,,,,,,142,144,143,161,162,163,164,165,166,157,158',
'159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148,,,,,,,,221',
'144,143,161,162,163,164,165,166,157,158,159,160,153,152,151,154,155',
'156,145,146,147,149,150,,,,148,,,,,,,,289,144,143,161,162,163,164,165',
'166,157,158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148',
',,,,,,,297,144,143,161,162,163,164,165,166,157,158,159,160,153,152,151',
'154,155,156,145,146,147,149,150,,,,148,,,,,,,251,144,143,161,162,163',
'164,165,166,157,158,159,160,153,152,151,154,155,156,145,146,147,149',
'150,,,,148,,,,,,,263,144,143,161,162,163,164,165,166,157,158,159,160',
'153,152,151,154,155,156,145,146,147,149,150,,,,148,,,,,,,265,144,143',
'161,162,163,164,165,166,157,158,159,160,153,152,151,154,155,156,145',
'146,147,149,150,,,,148,,,,,,,286,144,143,161,162,163,164,165,166,157',
'158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148,51,50',
'4,10,11,,299,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50',
'4,10,11,,53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50',
'105,,,,53,36,32,34,37,39,40,41,54,42,43,107,44,45,55,46,,47,,48,51,50',
'105,,,,53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105',
',,,53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,',
',,53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,,',
',53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,,,',
'53,36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,,,,53',
'36,32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,,,,53,36',
'32,34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,51,50,105,,,,53,36,32',
'34,37,39,40,41,54,42,43,,44,45,55,46,,47,,48,,,,,,,53,144,143,161,162',
'163,164,165,166,157,158,159,160,153,152,151,154,155,156,145,146,147',
'149,150,,,,148,144,143,161,162,163,164,165,166,157,158,159,160,153,152',
'151,154,155,156,145,146,147,149,150,,,,148,144,143,161,162,163,164,165',
'166,157,158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148',
'144,143,161,162,163,164,165,166,157,158,159,160,153,152,151,154,155',
'156,145,146,147,149,150,,,,148,144,143,161,162,163,164,165,166,157,158',
'159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148,144,143,161',
'162,163,164,165,166,157,158,159,160,153,152,151,154,155,156,145,146',
'147,149,150,,,,148,144,143,161,162,163,164,165,166,157,158,159,160,153',
'152,151,154,155,156,145,146,147,149,150,,,,148,144,143,161,162,163,164',
'165,166,157,158,159,160,153,152,151,154,155,156,145,146,147,149,150',
',,,148,144,143,161,162,163,164,165,166,157,158,159,160,153,152,151,154',
'155,156,145,146,147,149,150,,,,148,144,143,161,162,163,164,165,166,157',
'158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148,144,143',
'161,162,163,164,165,166,157,158,159,160,153,152,151,154,155,156,145',
'146,147,149,150,,,,148,144,143,161,162,163,164,165,166,157,158,159,160',
'153,152,151,154,155,156,145,146,147,149,150,,,,148,144,143,161,162,163',
'164,165,166,157,158,159,160,153,152,151,154,155,156,145,146,147,149',
'150,,,,148,144,143,161,162,163,164,165,166,157,158,159,160,153,152,151',
'154,155,156,145,146,147,149,150,,,,148,144,143,161,162,163,164,165,166',
'157,158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148,144',
'143,161,162,163,164,165,166,157,158,159,160,153,152,151,154,155,156',
'145,146,147,149,150,,,,148,143,161,162,163,164,165,166,157,158,159,160',
'153,152,151,154,155,156,145,146,147,149,150,,,,148,161,162,163,164,165',
'166,157,158,159,160,153,152,151,154,155,156,145,146,147,149,150,,,,148',
'153,152,151,154,155,156,145,146,147,149,150,,,,148,153,152,151,154,155',
'156,145,146,147,149,150,,,,148,153,152,151,154,155,156,145,146,147,149',
'150,,,,148,153,152,151,154,155,156,145,146,147,149,150,,,,148,153,152',
'151,154,155,156,145,146,147,149,150,,,,148,153,152,151,154,155,156,145',
'146,147,149,150,,,,148,153,152,151,154,155,156,145,146,147,149,150,',
',,148,153,152,151,154,155,156,145,146,147,149,150,,,,148,153,152,151',
'154,155,156,145,146,147,149,150,,,,148,152,151,154,155,156,145,146,147',
'149,150,,,,148,151,154,155,156,145,146,147,149,150,,,,148' ]
        racc_action_table = arr = ::Array.new(4010, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

clist = [
'213,213,213,213,213,213,213,213,213,213,213,213,213,213,213,213,213',
'213,213,213,213,213,213,42,11,45,213,42,273,42,11,42,42,1,45,37,42,10',
'42,33,45,37,11,31,41,42,33,42,73,73,41,42,45,37,32,273,134,213,34,42',
'42,273,41,42,42,42,42,42,42,42,42,42,66,273,210,134,66,210,66,210,66',
'66,52,134,52,66,132,50,132,134,109,109,35,50,66,38,66,229,229,229,66',
'134,39,229,109,50,74,74,40,66,109,44,66,66,66,66,66,66,66,66,66,80,109',
'46,48,80,51,80,56,80,80,62,51,69,80,70,94,71,75,297,297,77,94,80,51',
'80,230,230,230,80,4,4,230,297,94,90,105,4,80,297,106,80,80,80,80,80',
'80,80,80,80,81,297,110,113,81,139,81,140,81,81,169,95,170,81,95,171',
'174,95,175,176,111,131,81,177,81,178,111,131,81,95,95,95,95,95,95,95',
'95,81,111,131,81,81,81,81,81,81,81,81,81,82,138,182,183,82,187,82,138',
'82,82,193,256,207,82,212,216,217,256,220,138,218,225,82,226,82,231,218',
'232,82,256,256,256,256,256,256,256,256,82,218,233,82,82,82,82,82,82',
'82,82,82,83,234,252,270,83,288,83,296,83,83,302,,,83,235,235,235,235',
'235,235,235,235,83,,83,235,,,83,,238,238,238,238,238,,,83,238,,83,83',
'83,83,83,83,83,83,83,96,,,,96,,96,,96,96,,,,96,,239,239,239,239,239',
',,96,239,96,,,,96,,,240,240,240,240,240,96,96,96,240,96,96,96,96,96',
'96,96,96,96,112,,,,112,,112,,112,112,,,,112,172,172,172,172,172,,,,112',
',112,,,,112,276,276,276,276,276,,,,112,,,112,112,112,112,112,112,112',
'112,112,114,,,,114,,114,,114,114,,,,114,301,301,301,301,301,,,,114,',
'114,,,,114,,,,,,,,,114,,,114,114,114,114,114,114,114,114,114,115,,,',
'115,,115,,115,115,,,,115,,,,,,,,,115,,115,,,,115,,,,,,,,115,115,,,115',
'115,115,115,115,115,115,115,115,116,,,,116,,116,,116,116,,,,116,,,,',
',,,,116,,116,,,,116,,,,,,,,,116,,,116,116,116,116,116,116,116,116,116',
'117,,,,117,,117,,117,117,,,,117,,,,,,,,,117,,117,,,,117,,,,,,,,,117',
',,117,117,117,117,117,117,117,117,117,118,,,,118,,118,,118,118,,,,118',
',,,,,,,,118,,118,,,,118,,,,,,,,,118,,,118,118,118,118,118,118,118,118',
'118,119,,,,119,,119,,119,119,,,,119,,,,,,,,,119,,119,,,,119,,,,,,,,',
'119,,,119,119,119,119,119,119,119,119,119,120,,,,120,,120,,120,120,',
',,120,,,,,,,,,120,,120,,,,120,,,,,,,,,120,,,120,120,120,120,120,120',
'120,120,120,121,,,,121,,121,,121,121,,,,121,,,,,,,,,121,,121,,,,121',
',,,,,,,,121,,,121,121,121,121,121,121,121,121,121,122,,,,122,,122,,122',
'122,,,,122,,,,,,,,,122,,122,,,,122,,,,,,,,,122,,,122,122,122,122,122',
'122,122,122,122,123,,,,123,,123,,123,123,,,,123,,,,,,,,,123,,123,,,',
'123,,,,,,,,,123,,,123,123,123,123,123,123,123,123,123,124,,,,124,,124',
',124,124,,,,124,124,,,,,,,,124,,124,,,,124,,,,,,,,124,124,,,124,124',
'124,124,124,124,124,124,124,130,,,,130,,130,,130,130,,,,130,,,,,,,,',
'130,,130,,,,130,,,,,,,,,130,,,130,130,130,130,130,130,130,130,130,137',
',,,137,,137,,137,137,,,,137,,,,,,,,,137,,137,,,,137,,,,,,,,137,137,',
',137,137,137,137,137,137,137,137,137,143,,,,143,,143,,143,143,,,,143',
',,,,,,,,143,,143,,,,143,,,,,,,,,143,,,143,143,143,143,143,143,143,143',
'143,144,,,,144,,144,,144,144,,,,144,,,,,,,,,144,,144,,,,144,,,,,,,,',
'144,,,144,144,144,144,144,144,144,144,144,145,,,,145,,145,,145,145,',
',,145,,,,,,,,,145,,145,,,,145,,,,,,,,,145,,,145,145,145,145,145,145',
'145,145,145,146,,,,146,,146,,146,146,,,,146,,,,,,,,,146,,146,,,,146',
',,,,,,,,146,,,146,146,146,146,146,146,146,146,146,147,,,,147,,147,,147',
'147,,,,147,,,,,,,,,147,,147,,,,147,,,,,,,,,147,,,147,147,147,147,147',
'147,147,147,147,148,,,,148,,148,,148,148,,,,148,,,,,,,,,148,,148,,,',
'148,,,,,,,,,148,,,148,148,148,148,148,148,148,148,148,149,,,,149,,149',
',149,149,,,,149,,,,,,,,,149,,149,,,,149,,,,,,,,,149,,,149,149,149,149',
'149,149,149,149,149,150,,,,150,,150,,150,150,,,,150,,,,,,,,,150,,150',
',,,150,,,,,,,,,150,,,150,150,150,150,150,150,150,150,150,151,,,,151',
',151,,151,151,,,,151,,,,,,,,,151,,151,,,,151,,,,,,,,,151,,,151,151,151',
'151,151,151,151,151,151,152,,,,152,,152,,152,152,,,,152,,,,,,,,,152',
',152,,,,152,,,,,,,,,152,,,152,152,152,152,152,152,152,152,152,153,,',
',153,,153,,153,153,,,,153,,,,,,,,,153,,153,,,,153,,,,,,,,,153,,,153',
'153,153,153,153,153,153,153,153,154,,,,154,,154,,154,154,,,,154,,,,',
',,,,154,,154,,,,154,,,,,,,,,154,,,154,154,154,154,154,154,154,154,154',
'155,,,,155,,155,,155,155,,,,155,,,,,,,,,155,,155,,,,155,,,,,,,,,155',
',,155,155,155,155,155,155,155,155,155,156,,,,156,,156,,156,156,,,,156',
',,,,,,,,156,,156,,,,156,,,,,,,,,156,,,156,156,156,156,156,156,156,156',
'156,157,,,,157,,157,,157,157,,,,157,,,,,,,,,157,,157,,,,157,,,,,,,,',
'157,,,157,157,157,157,157,157,157,157,157,158,,,,158,,158,,158,158,',
',,158,,,,,,,,,158,,158,,,,158,,,,,,,,,158,,,158,158,158,158,158,158',
'158,158,158,159,,,,159,,159,,159,159,,,,159,,,,,,,,,159,,159,,,,159',
',,,,,,,,159,,,159,159,159,159,159,159,159,159,159,160,,,,160,,160,,160',
'160,,,,160,,,,,,,,,160,,160,,,,160,,,,,,,,,160,,,160,160,160,160,160',
'160,160,160,160,161,,,,161,,161,,161,161,,,,161,,,,,,,,,161,,161,,,',
'161,,,,,,,,,161,,,161,161,161,161,161,161,161,161,161,162,,,,162,,162',
',162,162,,,,162,,,,,,,,,162,,162,,,,162,,,,,,,,,162,,,162,162,162,162',
'162,162,162,162,162,163,,,,163,,163,,163,163,,,,163,,,,,,,,,163,,163',
',,,163,,,,,,,,,163,,,163,163,163,163,163,163,163,163,163,164,,,,164',
',164,,164,164,,,,164,,,,,,,,,164,,164,,,,164,,,,,,,,,164,,,164,164,164',
'164,164,164,164,164,164,165,,,,165,,165,,165,165,,,,165,,,,,,,,,165',
',165,,,,165,,,,,,,,,165,,,165,165,165,165,165,165,165,165,165,166,,',
',166,,166,,166,166,,,,166,,,,,,,,,166,,166,,,,166,,,,,,,,,166,,,166',
'166,166,166,166,166,166,166,166,192,,,,192,,192,,192,192,,,,192,,,,',
',,,,192,,192,,,,192,,,,,,,,,192,,,192,192,192,192,192,192,192,192,192',
'195,,,,195,,195,,195,195,,,,195,,,,,,,,,195,,195,,,,195,,,,,,,,,195',
',,195,195,195,195,195,195,195,195,195,253,,,,253,,253,,253,253,,,,253',
',,,,,,,,253,,253,,,,253,,,,,,,,253,253,,,253,253,253,253,253,253,253',
'253,253,254,,,,254,,254,,254,254,,,,254,,,,,,,,,254,,254,,,,254,,,,',
',,,254,254,,,254,254,254,254,254,254,254,254,254,255,,,,255,,255,,255',
'255,,,,255,,,,,,,,,255,,255,,,,255,,,,,,,,255,255,,,255,255,255,255',
'255,255,255,255,255,258,,,,258,,258,,258,258,,,,258,,,,,,,,,258,,258',
',,,258,,,,,,,,258,258,,,258,258,258,258,258,258,258,258,258,260,,,,260',
',260,,260,260,,,,260,,,,,,,,,260,,260,,,,260,,,,,,,,,260,,,260,260,260',
'260,260,260,260,260,260,262,,,,262,,262,,262,262,,,,262,,,,,,,,,262',
',262,,,,262,,,,,,,,,262,,,262,262,262,262,262,262,262,262,262,267,,',
',267,,267,,267,267,,,,267,,,,,,,,,267,,267,,,,267,,,,,,,,267,267,,,267',
'267,267,267,267,267,267,267,267,268,,,,268,,268,,268,268,,,,268,,,,',
',,,,268,,268,,,,268,,,,,,,,268,268,,,268,268,268,268,268,268,268,268',
'268,49,49,49,49,49,49,49,49,49,79,79,79,79,79,79,79,79,79,241,241,241',
'241,241,241,241,241,241,241,241,,,,241,,,,49,49,,,,49,,,,79,79,,,,79',
'76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76,76',
',,,76,,,,,,,,76,135,135,135,135,135,135,135,135,135,135,135,135,135',
'135,135,135,135,135,135,135,135,135,135,,,,135,,,,,,,,135,264,264,264',
'264,264,264,264,264,264,264,264,264,264,264,264,264,264,264,264,264',
'264,264,264,,,,264,,,,,,,,264,285,285,285,285,285,285,285,285,285,285',
'285,285,285,285,285,285,285,285,285,285,285,285,285,,,,285,,,,,,,,285',
'168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168',
'168,168,168,168,168,168,,,,168,,,,,,,168,194,194,194,194,194,194,194',
'194,194,194,194,194,194,194,194,194,194,194,194,194,194,194,194,,,,194',
',,,,,,194,196,196,196,196,196,196,196,196,196,196,196,196,196,196,196',
'196,196,196,196,196,196,196,196,,,,196,,,,,,,196,261,261,261,261,261',
'261,261,261,261,261,261,261,261,261,261,261,261,261,261,261,261,261',
'261,,,,261,,,,,,,261,287,287,287,287,287,287,287,287,287,287,287,287',
'287,287,287,287,287,287,287,287,287,287,287,,,,287,0,0,0,0,0,,287,0',
'0,0,0,0,0,0,0,0,0,,0,0,0,0,,0,,0,3,3,3,3,3,,0,3,3,3,3,3,3,3,3,3,3,,3',
'3,3,3,,3,,3,43,43,43,,,,3,43,43,43,43,43,43,43,43,43,43,43,43,43,43',
'43,,43,,43,47,47,47,,,,43,47,47,47,47,47,47,47,47,47,47,,47,47,47,47',
',47,,47,108,108,108,,,,47,108,108,108,108,108,108,108,108,108,108,,108',
'108,108,108,,108,,108,263,263,263,,,,108,263,263,263,263,263,263,263',
'263,263,263,,263,263,263,263,,263,,263,265,265,265,,,,263,265,265,265',
'265,265,265,265,265,265,265,,265,265,265,265,,265,,265,286,286,286,',
',,265,286,286,286,286,286,286,286,286,286,286,,286,286,286,286,,286',
',286,299,299,299,,,,286,299,299,299,299,299,299,299,299,299,299,,299',
'299,299,299,,299,,299,300,300,300,,,,299,300,300,300,300,300,300,300',
'300,300,300,,300,300,300,300,,300,,300,306,306,306,,,,300,306,306,306',
'306,306,306,306,306,306,306,,306,306,306,306,,306,,306,,,,,,,306,180',
'180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180',
'180,180,180,180,180,,,,180,197,197,197,197,197,197,197,197,197,197,197',
'197,197,197,197,197,197,197,197,197,197,197,197,,,,197,199,199,199,199',
'199,199,199,199,199,199,199,199,199,199,199,199,199,199,199,199,199',
'199,199,,,,199,200,200,200,200,200,200,200,200,200,200,200,200,200,200',
'200,200,200,200,200,200,200,200,200,,,,200,201,201,201,201,201,201,201',
'201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,201,,,,201',
'202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202,202',
'202,202,202,202,202,202,,,,202,203,203,203,203,203,203,203,203,203,203',
'203,203,203,203,203,203,203,203,203,203,203,203,203,,,,203,204,204,204',
'204,204,204,204,204,204,204,204,204,204,204,204,204,204,204,204,204',
'204,204,204,,,,204,205,205,205,205,205,205,205,205,205,205,205,205,205',
'205,205,205,205,205,205,205,205,205,205,,,,205,206,206,206,206,206,206',
'206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206,206',
',,,206,209,209,209,209,209,209,209,209,209,209,209,209,209,209,209,209',
'209,209,209,209,209,209,209,,,,209,222,222,222,222,222,222,222,222,222',
'222,222,222,222,222,222,222,222,222,222,222,222,222,222,,,,222,277,277',
'277,277,277,277,277,277,277,277,277,277,277,277,277,277,277,277,277',
'277,277,277,277,,,,277,279,279,279,279,279,279,279,279,279,279,279,279',
'279,279,279,279,279,279,279,279,279,279,279,,,,279,281,281,281,281,281',
'281,281,281,281,281,281,281,281,281,281,281,281,281,281,281,281,281',
'281,,,,281,291,291,291,291,291,291,291,291,291,291,291,291,291,291,291',
'291,291,291,291,291,291,291,291,,,,291,228,228,228,228,228,228,228,228',
'228,228,228,228,228,228,228,228,228,228,228,228,228,228,,,,228,227,227',
'227,227,227,227,227,227,227,227,227,227,227,227,227,227,227,227,227',
'227,227,,,,227,242,242,242,242,242,242,242,242,242,242,242,,,,242,243',
'243,243,243,243,243,243,243,243,243,243,,,,243,244,244,244,244,244,244',
'244,244,244,244,244,,,,244,245,245,245,245,245,245,245,245,245,245,245',
',,,245,246,246,246,246,246,246,246,246,246,246,246,,,,246,247,247,247',
'247,247,247,247,247,247,247,247,,,,247,248,248,248,248,248,248,248,248',
'248,248,248,,,,248,249,249,249,249,249,249,249,249,249,249,249,,,,249',
'250,250,250,250,250,250,250,250,250,250,250,,,,250,237,237,237,237,237',
'237,237,237,237,237,,,,237,236,236,236,236,236,236,236,236,236,,,,236' ]
        racc_action_check = arr = ::Array.new(4010, nil)
        idx = 0
        clist.each do |str|
          str.split(',', -1).each do |i|
            arr[idx] = i.to_i unless i.empty?
            idx += 1
          end
        end

racc_action_pointer = [
  3034,    33,   nil,  3060,   109,   nil,   nil,   nil,   nil,   nil,
    -5,   -28,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    -2,     9,    -6,    13,    47,   nil,   -17,    50,    59,
    65,    -8,    -7,  3086,    68,   -18,    80,  3112,    81,  2716,
    35,    74,    15,   nil,   nil,   nil,   128,   nil,   nil,   nil,
   nil,   nil,    88,   nil,   nil,   nil,    42,   nil,   nil,    88,
   133,    72,   nil,   -28,    30,    93,  2758,    96,   nil,  2725,
    91,   140,   189,   238,   nil,   nil,   nil,   nil,   nil,   nil,
    86,   nil,   nil,   nil,    84,   129,   287,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   108,   105,   nil,  3138,    52,
   129,   138,   336,   111,   385,   434,   483,   532,   581,   630,
   679,   728,   777,   826,   875,   nil,   nil,   nil,   nil,   nil,
   924,   139,    19,   nil,    31,  2793,   nil,   973,   168,   131,
   133,   nil,   nil,  1022,  1071,  1120,  1169,  1218,  1267,  1316,
  1365,  1414,  1463,  1512,  1561,  1610,  1659,  1708,  1757,  1806,
  1855,  1904,  1953,  2002,  2051,  2100,  2149,   nil,  2898,   143,
   145,   148,   309,   nil,   122,   124,   125,   128,   140,   nil,
  3354,   nil,   156,   154,   nil,   nil,   nil,   179,   nil,   nil,
   nil,   nil,  2198,   171,  2932,  2247,  2966,  3381,   nil,  3408,
  3435,  3462,  3489,  3516,  3543,  3570,  3597,   187,   nil,  3624,
    10,   nil,   168,   -11,   nil,   nil,   190,   181,   187,   nil,
   172,   nil,  3651,   nil,   nil,   195,   197,  3810,  3785,    66,
   115,   207,   209,   221,   232,   256,  3972,  3959,   269,   303,
   319,  2713,  3825,  3840,  3855,  3870,  3885,  3900,  3915,  3930,
  3945,   nil,   201,  2296,  2345,  2394,   178,   nil,  2443,   nil,
  2492,  3000,  2541,  3164,  2828,  3190,   nil,  2590,  2639,   nil,
   217,   nil,   nil,     3,   nil,   nil,   324,  3678,   nil,  3705,
   nil,  3732,   nil,   nil,   nil,  2863,  3216,  3034,   213,   nil,
   nil,  3759,   nil,   nil,   nil,   nil,   206,   101,   nil,  3242,
  3268,   358,   234,   nil,   nil,   nil,  3294,   nil ]

racc_action_default = [
    -2,  -172,    -1,    -4,  -172,    -6,    -8,    -9,   -10,   -11,
  -172,  -172,   -15,   -16,   -17,   -18,   -19,   -20,   -22,   -23,
   -24,   -25,   -26,   -27,   -28,   -29,   -30,   -31,   -32,   -33,
   -34,  -172,  -172,  -172,  -172,  -172,   -40,  -172,  -172,  -172,
  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,
  -172,  -172,  -128,  -160,  -161,  -162,  -172,    -3,    -5,    -7,
   -21,   -12,  -172,   -35,   -36,   -37,  -172,   -38,   -39,  -172,
  -157,  -159,   -42,  -172,  -172,  -172,  -172,  -172,   -49,  -108,
  -172,  -172,  -172,  -172,   -93,   -94,  -105,  -106,  -107,  -109,
  -110,  -111,  -112,  -113,  -172,  -172,  -172,  -163,  -164,  -165,
  -166,  -167,  -169,  -170,  -171,  -172,  -172,   -51,  -154,  -138,
  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,
  -172,  -172,  -172,  -172,  -172,   -72,   -74,   -71,   -73,  -127,
  -172,  -172,  -142,   308,  -172,  -172,   -41,  -172,  -172,  -172,
  -172,   -45,   -47,  -172,  -172,  -172,  -172,  -172,  -172,  -172,
  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,  -172,
  -172,  -172,  -172,  -172,  -172,  -172,  -172,   -48,  -172,   -77,
   -81,   -82,  -172,  -129,  -172,  -172,  -172,  -126,  -172,  -133,
  -143,  -144,  -146,  -172,  -148,   -50,  -153,  -172,  -134,  -135,
  -136,  -137,  -172,  -172,  -172,  -172,  -172,   -59,   -60,   -61,
   -62,   -63,   -64,   -65,   -66,   -67,   -68,  -172,   -70,  -116,
  -128,  -117,  -131,  -172,  -140,  -141,  -172,  -172,  -172,  -150,
  -152,   -46,  -155,  -156,  -158,  -172,  -172,   -76,   -78,   -79,
   -80,   -83,   -84,   -85,   -86,   -87,   -88,   -89,   -90,   -91,
   -92,   -95,   -96,   -97,   -98,   -99,  -100,  -101,  -102,  -103,
  -104,   -75,  -172,  -172,  -172,  -172,  -125,  -132,  -172,  -147,
  -172,  -172,  -172,  -172,  -172,  -172,   -69,  -172,  -172,  -139,
  -172,   -14,  -149,  -172,   -43,   -44,  -172,  -118,  -121,  -119,
  -122,  -120,  -123,  -124,  -145,  -172,  -172,  -172,   -55,   -57,
   -58,  -114,  -115,  -130,   -13,  -151,  -172,  -138,   -53,  -172,
  -172,  -172,  -172,   -54,   -56,  -168,  -172,   -52 ]

racc_goto_table = [
    31,   187,    62,    31,   175,    33,    77,   183,    33,   178,
    35,   139,   140,    35,   207,    38,   216,   106,    38,    60,
    69,     1,   271,    59,    75,    58,   215,    49,    70,    61,
    49,   108,    70,   174,     2,   113,   110,    57,   nil,   nil,
   nil,   nil,   nil,    31,   nil,   nil,   nil,    31,    33,   nil,
   nil,   nil,    33,    35,   nil,   nil,   nil,    35,    38,   nil,
   nil,   nil,    38,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    49,   nil,   nil,   nil,    49,   294,   nil,   127,   128,   198,
   nil,   252,   186,   nil,   nil,   173,   176,   nil,   211,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   108,   nil,   nil,   nil,
   nil,   223,   193,   nil,   nil,   nil,   nil,   nil,    31,   188,
   nil,   nil,   nil,    33,   189,   210,   nil,   nil,    35,   190,
    60,   224,   214,    38,   191,   219,   nil,   nil,   nil,    70,
   nil,   nil,   nil,   nil,   nil,    49,    49,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   295,   nil,   nil,   293,   nil,
   nil,   nil,   nil,   nil,   nil,   175,   nil,   nil,   nil,   284,
   283,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   296,   nil,   nil,   nil,   302,
   nil,   nil,   nil,   nil,   174,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   272,
   305,   nil,   nil,   nil,   nil,   nil,   nil,   278,   280,   282,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   292,   211,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   176,   nil,   nil,
   nil,   288,   nil,   290,   nil,   nil,   nil,   nil,   nil,   210,
   nil,   nil,   nil,    31,   219,    31,   nil,   nil,    33,   nil,
    33,   nil,   nil,    35,   298,    35,   nil,   nil,    38,    76,
    38,   nil,   nil,   nil,   nil,   nil,    31,   303,   304,   nil,
    49,    33,    49,   nil,   307,   nil,    35,   188,   nil,    31,
    31,    38,   189,   135,    33,    33,    31,   190,   nil,    35,
    35,    33,   191,    49,    38,    38,    35,   168,   169,   170,
   171,    38,   nil,   nil,    49,   nil,    49,    49,   nil,   nil,
   nil,   nil,   nil,    49,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   194,
   nil,   196,   197,   199,   200,   201,   202,   203,   204,   205,
   206,   209,   nil,   nil,   nil,   nil,   nil,   213,   nil,   nil,
   nil,   nil,   nil,   nil,   222,   nil,   nil,   nil,   nil,   nil,
   227,   228,   229,   230,   231,   232,   233,   234,   235,   236,
   237,   238,   239,   240,   241,   242,   243,   244,   245,   246,
   247,   248,   249,   250,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   261,
   nil,   nil,   264,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   277,   279,   281,   nil,   nil,   nil,   nil,   285,   nil,   287,
   nil,   nil,   nil,   nil,   291,   209 ]

racc_goto_check = [
    30,    39,     9,    30,    43,    31,    37,    53,    31,    49,
    32,    35,    35,    32,    41,    34,    10,    38,    34,    18,
    33,     1,    11,     5,    33,     4,    50,    40,     9,     5,
    40,     6,     9,    35,     2,     6,     9,     2,   nil,   nil,
   nil,   nil,   nil,    30,   nil,   nil,   nil,    30,    31,   nil,
   nil,   nil,    31,    32,   nil,   nil,   nil,    32,    34,   nil,
   nil,   nil,    34,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    40,   nil,   nil,   nil,    40,    11,   nil,    40,    40,    37,
   nil,    43,    38,   nil,   nil,     9,     9,   nil,    37,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,     6,   nil,   nil,   nil,
   nil,    37,     9,   nil,   nil,   nil,   nil,   nil,    30,    30,
   nil,   nil,   nil,    31,    31,     9,   nil,   nil,    32,    32,
    18,    33,     9,    34,    34,     9,   nil,   nil,   nil,     9,
   nil,   nil,   nil,   nil,   nil,    40,    40,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    10,   nil,   nil,    41,   nil,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,    53,
    49,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,    43,   nil,   nil,   nil,    39,
   nil,   nil,   nil,   nil,    35,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,
    43,   nil,   nil,   nil,   nil,   nil,   nil,    37,    37,    37,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    37,    37,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,     9,   nil,   nil,
   nil,     6,   nil,     6,   nil,   nil,   nil,   nil,   nil,     9,
   nil,   nil,   nil,    30,     9,    30,   nil,   nil,    31,   nil,
    31,   nil,   nil,    32,     6,    32,   nil,   nil,    34,    36,
    34,   nil,   nil,   nil,   nil,   nil,    30,     6,     6,   nil,
    40,    31,    40,   nil,     6,   nil,    32,    30,   nil,    30,
    30,    34,    31,    36,    31,    31,    30,    32,   nil,    32,
    32,    31,    34,    40,    34,    34,    32,    36,    36,    36,
    36,    34,   nil,   nil,    40,   nil,    40,    40,   nil,   nil,
   nil,   nil,   nil,    40,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,
   nil,    36,    36,    36,    36,    36,    36,    36,    36,    36,
    36,    36,   nil,   nil,   nil,   nil,   nil,    36,   nil,   nil,
   nil,   nil,   nil,   nil,    36,   nil,   nil,   nil,   nil,   nil,
    36,    36,    36,    36,    36,    36,    36,    36,    36,    36,
    36,    36,    36,    36,    36,    36,    36,    36,    36,    36,
    36,    36,    36,    36,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    36,
   nil,   nil,    36,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    36,    36,    36,   nil,   nil,   nil,   nil,    36,   nil,    36,
   nil,   nil,   nil,   nil,    36,    36 ]

racc_goto_pointer = [
   nil,    21,    34,   nil,    21,    19,   -12,   nil,   nil,    -9,
  -118,  -195,   nil,   nil,   nil,   nil,   nil,   nil,    15,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
     0,     5,    10,   -17,    15,   -62,   237,   -36,   -26,  -108,
    27,  -110,   nil,   -91,   nil,   nil,   nil,   nil,   nil,   -86,
  -106,   nil,   nil,   -89,   nil,   nil ]

racc_goto_default = [
   nil,   nil,   nil,     3,     5,     6,     7,     8,     9,    52,
   nil,    25,    12,    13,    14,    15,    16,    17,    18,    19,
    20,    21,    22,    23,    24,    26,    27,    28,    29,    30,
    86,    88,    85,   nil,    84,    87,   180,   181,   nil,   nil,
    79,   nil,    89,    90,    91,    92,    93,   212,   177,   nil,
   129,   132,   182,   nil,   220,    71 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 80, :_reduce_1,
  0, 80, :_reduce_2,
  2, 81, :_reduce_3,
  1, 81, :_reduce_4,
  2, 82, :_reduce_5,
  1, 82, :_reduce_6,
  2, 82, :_reduce_7,
  1, 82, :_reduce_8,
  1, 82, :_reduce_9,
  1, 85, :_reduce_10,
  1, 85, :_reduce_11,
  2, 83, :_reduce_12,
  6, 84, :_reduce_13,
  5, 84, :_reduce_14,
  1, 86, :_reduce_15,
  1, 86, :_reduce_16,
  1, 86, :_reduce_17,
  1, 86, :_reduce_18,
  1, 86, :_reduce_19,
  1, 86, :_reduce_20,
  2, 86, :_reduce_21,
  1, 86, :_reduce_22,
  1, 86, :_reduce_23,
  1, 86, :_reduce_24,
  1, 86, :_reduce_25,
  1, 86, :_reduce_26,
  1, 86, :_reduce_27,
  1, 86, :_reduce_28,
  1, 87, :_reduce_29,
  1, 87, :_reduce_30,
  1, 87, :_reduce_31,
  1, 87, :_reduce_32,
  1, 87, :_reduce_33,
  1, 87, :_reduce_34,
  2, 91, :_reduce_35,
  2, 92, :_reduce_36,
  2, 93, :_reduce_37,
  2, 94, :_reduce_38,
  2, 95, :_reduce_39,
  1, 96, :_reduce_40,
  3, 97, :_reduce_41,
  2, 100, :_reduce_42,
  5, 98, :_reduce_43,
  5, 99, :_reduce_44,
  3, 101, :_reduce_45,
  4, 102, :_reduce_46,
  3, 103, :_reduce_47,
  3, 103, :_reduce_48,
  2, 103, :_reduce_49,
  3, 90, :_reduce_50,
  2, 90, :_reduce_51,
  9, 104, :_reduce_52,
  6, 105, :_reduce_53,
  7, 105, :_reduce_54,
  5, 106, :_reduce_55,
  7, 106, :_reduce_56,
  5, 107, :_reduce_57,
  5, 108, :_reduce_58,
  3, 109, :_reduce_59,
  3, 109, :_reduce_60,
  3, 109, :_reduce_61,
  3, 109, :_reduce_62,
  3, 109, :_reduce_63,
  3, 109, :_reduce_64,
  3, 109, :_reduce_65,
  3, 109, :_reduce_66,
  3, 109, :_reduce_67,
  3, 109, :_reduce_68,
  4, 110, :_reduce_69,
  3, 110, :_reduce_70,
  2, 111, :_reduce_71,
  2, 111, :_reduce_72,
  2, 113, :_reduce_73,
  2, 113, :_reduce_74,
  3, 115, :_reduce_75,
  3, 115, :_reduce_76,
  2, 115, :_reduce_77,
  3, 115, :_reduce_78,
  3, 115, :_reduce_79,
  3, 115, :_reduce_80,
  2, 115, :_reduce_81,
  2, 115, :_reduce_82,
  3, 115, :_reduce_83,
  3, 115, :_reduce_84,
  3, 115, :_reduce_85,
  3, 115, :_reduce_86,
  3, 115, :_reduce_87,
  3, 115, :_reduce_88,
  3, 115, :_reduce_89,
  3, 115, :_reduce_90,
  3, 115, :_reduce_91,
  3, 115, :_reduce_92,
  1, 115, :_reduce_93,
  1, 115, :_reduce_94,
  3, 115, :_reduce_95,
  3, 115, :_reduce_96,
  3, 115, :_reduce_97,
  3, 115, :_reduce_98,
  3, 115, :_reduce_99,
  3, 115, :_reduce_100,
  3, 115, :_reduce_101,
  3, 115, :_reduce_102,
  3, 115, :_reduce_103,
  3, 115, :_reduce_104,
  1, 115, :_reduce_105,
  1, 115, :_reduce_106,
  1, 115, :_reduce_107,
  1, 115, :_reduce_108,
  1, 115, :_reduce_109,
  1, 115, :_reduce_110,
  1, 115, :_reduce_111,
  1, 115, :_reduce_112,
  1, 115, :_reduce_113,
  3, 126, :_reduce_114,
  3, 126, :_reduce_115,
  1, 126, :_reduce_116,
  1, 126, :_reduce_117,
  3, 127, :_reduce_118,
  3, 127, :_reduce_119,
  3, 127, :_reduce_120,
  3, 127, :_reduce_121,
  3, 127, :_reduce_122,
  3, 127, :_reduce_123,
  3, 128, :_reduce_124,
  2, 128, :_reduce_125,
  1, 128, :_reduce_126,
  2, 119, :_reduce_127,
  1, 119, :_reduce_128,
  2, 116, :_reduce_129,
  3, 120, :_reduce_130,
  1, 120, :_reduce_131,
  3, 125, :_reduce_132,
  2, 125, :_reduce_133,
  1, 118, :_reduce_134,
  1, 118, :_reduce_135,
  1, 118, :_reduce_136,
  1, 118, :_reduce_137,
  0, 118, :_reduce_138,
  3, 130, :_reduce_139,
  2, 130, :_reduce_140,
  2, 129, :_reduce_141,
  1, 129, :_reduce_142,
  1, 131, :_reduce_143,
  1, 131, :_reduce_144,
  3, 132, :_reduce_145,
  1, 132, :_reduce_146,
  3, 124, :_reduce_147,
  2, 124, :_reduce_148,
  2, 133, :_reduce_149,
  1, 133, :_reduce_150,
  3, 89, :_reduce_151,
  1, 89, :_reduce_152,
  2, 117, :_reduce_153,
  1, 117, :_reduce_154,
  3, 134, :_reduce_155,
  3, 134, :_reduce_156,
  1, 134, :_reduce_157,
  3, 112, :_reduce_158,
  1, 112, :_reduce_159,
  1, 88, :_reduce_160,
  1, 88, :_reduce_161,
  1, 88, :_reduce_162,
  1, 122, :_reduce_163,
  1, 122, :_reduce_164,
  1, 122, :_reduce_165,
  1, 122, :_reduce_166,
  1, 122, :_reduce_167,
  7, 121, :_reduce_168,
  1, 114, :_reduce_169,
  1, 114, :_reduce_170,
  1, 123, :_reduce_171 ]

racc_reduce_n = 172

racc_shift_n = 308

racc_token_table = {
  false => 0,
  :error => 1,
  :ASS_EQ => 2,
  :ADD_EQ => 3,
  :SUB_EQ => 4,
  :MUL_EQ => 5,
  :DIV_EQ => 6,
  :MOD_EQ => 7,
  :SLL_EQ => 8,
  :SRA_EQ => 9,
  :SRL_EQ => 10,
  :OR => 11,
  :AND => 12,
  :CMP_LT => 13,
  :CMP_GT => 14,
  :CMP_EQ => 15,
  :CMP_NE => 16,
  :CMP_GE => 17,
  :CMP_LE => 18,
  :SUBSTR_EQ => 19,
  :SUBSTR_NE => 20,
  :REGEX_EQ => 21,
  :REGEX_NE => 22,
  :BIT_OR => 23,
  :BIT_XOR => 24,
  :AMPERSAND => 25,
  :BIT_SRA => 26,
  :BIT_SRL => 27,
  :BIT_SLL => 28,
  :ADD => 29,
  :SUB => 30,
  :MUL => 31,
  :DIV => 32,
  :MOD => 33,
  :NOT => 34,
  :UMINUS => 35,
  :BIT_NOT => 36,
  :EXP => 37,
  :INCR => 38,
  :DECR => 39,
  :COMMENT => 40,
  :EXPORT => 41,
  :FUNCTION => 42,
  :LPAREN => 43,
  :RPAREN => 44,
  :SEMICOLON => 45,
  :BREAK => 46,
  :CONTINUE => 47,
  :GLOBAL => 48,
  :IMPORT => 49,
  :INCLUDE => 50,
  :LOCAL => 51,
  :REP => 52,
  :RETURN => 53,
  :LBRACE => 54,
  :RBRACE => 55,
  :FOR => 56,
  :FOREACH => 57,
  :IN => 58,
  :IF => 59,
  :ELSE => 60,
  :REPEAT => 61,
  :UNTIL => 62,
  :WHILE => 63,
  :COLON => 64,
  :COMMA => 65,
  :AT_SIGN => 66,
  :LBRACK => 67,
  :RBRACK => 68,
  :PERIOD => 69,
  :IDENT => 70,
  :INT_DEC => 71,
  :INT_HEX => 72,
  :INT_OCT => 73,
  :FALSE => 74,
  :TRUE => 75,
  :DATA => 76,
  :STRING => 77,
  :UNDEF => 78 }

racc_nt_base = 79

racc_use_result_var = false

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "ASS_EQ",
  "ADD_EQ",
  "SUB_EQ",
  "MUL_EQ",
  "DIV_EQ",
  "MOD_EQ",
  "SLL_EQ",
  "SRA_EQ",
  "SRL_EQ",
  "OR",
  "AND",
  "CMP_LT",
  "CMP_GT",
  "CMP_EQ",
  "CMP_NE",
  "CMP_GE",
  "CMP_LE",
  "SUBSTR_EQ",
  "SUBSTR_NE",
  "REGEX_EQ",
  "REGEX_NE",
  "BIT_OR",
  "BIT_XOR",
  "AMPERSAND",
  "BIT_SRA",
  "BIT_SRL",
  "BIT_SLL",
  "ADD",
  "SUB",
  "MUL",
  "DIV",
  "MOD",
  "NOT",
  "UMINUS",
  "BIT_NOT",
  "EXP",
  "INCR",
  "DECR",
  "COMMENT",
  "EXPORT",
  "FUNCTION",
  "LPAREN",
  "RPAREN",
  "SEMICOLON",
  "BREAK",
  "CONTINUE",
  "GLOBAL",
  "IMPORT",
  "INCLUDE",
  "LOCAL",
  "REP",
  "RETURN",
  "LBRACE",
  "RBRACE",
  "FOR",
  "FOREACH",
  "IN",
  "IF",
  "ELSE",
  "REPEAT",
  "UNTIL",
  "WHILE",
  "COLON",
  "COMMA",
  "AT_SIGN",
  "LBRACK",
  "RBRACK",
  "PERIOD",
  "IDENT",
  "INT_DEC",
  "INT_HEX",
  "INT_OCT",
  "FALSE",
  "TRUE",
  "DATA",
  "STRING",
  "UNDEF",
  "$start",
  "start",
  "roots",
  "root",
  "export",
  "function",
  "statement",
  "simple",
  "compound",
  "ident",
  "params",
  "block",
  "assign",
  "break",
  "call",
  "continue",
  "decr",
  "empty",
  "global",
  "import",
  "include",
  "incr",
  "local",
  "rep",
  "return",
  "for",
  "foreach",
  "if",
  "repeat",
  "while",
  "assign_exp",
  "call_exp",
  "decr_exp",
  "var_decls",
  "incr_exp",
  "string",
  "expr",
  "ref",
  "statements",
  "field",
  "lval",
  "args",
  "ip",
  "int",
  "undef",
  "list_expr",
  "array_expr",
  "arg",
  "kv_pair",
  "kv_pairs",
  "indexes",
  "index",
  "list_elem",
  "list_elems",
  "param",
  "var_decl" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'nasl.y', 61)
  def _reduce_1(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 63)
  def _reduce_2(val, _values)
     []
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 67)
  def _reduce_3(val, _values)
     [val[0]] + val[1]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 69)
  def _reduce_4(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 73)
  def _reduce_5(val, _values)
     c(*val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 75)
  def _reduce_6(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 77)
  def _reduce_7(val, _values)
     c(*val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 79)
  def _reduce_8(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 81)
  def _reduce_9(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 85)
  def _reduce_10(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 87)
  def _reduce_11(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 95)
  def _reduce_12(val, _values)
     n(:Export, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 99)
  def _reduce_13(val, _values)
     n(:Function, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 101)
  def _reduce_14(val, _values)
     n(:Function, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 105)
  def _reduce_15(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 107)
  def _reduce_16(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 109)
  def _reduce_17(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 111)
  def _reduce_18(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 113)
  def _reduce_19(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 115)
  def _reduce_20(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 117)
  def _reduce_21(val, _values)
     c(*val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 119)
  def _reduce_22(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 121)
  def _reduce_23(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 123)
  def _reduce_24(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 125)
  def _reduce_25(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 127)
  def _reduce_26(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 129)
  def _reduce_27(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 131)
  def _reduce_28(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 135)
  def _reduce_29(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 137)
  def _reduce_30(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 139)
  def _reduce_31(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 141)
  def _reduce_32(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 143)
  def _reduce_33(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 145)
  def _reduce_34(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 153)
  def _reduce_35(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 157)
  def _reduce_36(val, _values)
     n(:Break, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 161)
  def _reduce_37(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 165)
  def _reduce_38(val, _values)
     n(:Continue, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 169)
  def _reduce_39(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 173)
  def _reduce_40(val, _values)
     n(:Empty, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 177)
  def _reduce_41(val, _values)
     n(:Global, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 181)
  def _reduce_42(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 185)
  def _reduce_43(val, _values)
     n(:Import, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 189)
  def _reduce_44(val, _values)
     n(:Include, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 193)
  def _reduce_45(val, _values)
     n(:Local, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 197)
  def _reduce_46(val, _values)
     n(:Repetition, *val[0..-1])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 201)
  def _reduce_47(val, _values)
     n(:Return, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 203)
  def _reduce_48(val, _values)
     n(:Return, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 205)
  def _reduce_49(val, _values)
     n(:Return, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 213)
  def _reduce_50(val, _values)
     n(:Block, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 215)
  def _reduce_51(val, _values)
     n(:Block, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 219)
  def _reduce_52(val, _values)
     n(:For, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 223)
  def _reduce_53(val, _values)
     n(:Foreach, val[0], val[1], val[3], val[5])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 225)
  def _reduce_54(val, _values)
     n(:Foreach, val[0], val[2], val[4], val[6])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 229)
  def _reduce_55(val, _values)
     n(:If, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 231)
  def _reduce_56(val, _values)
     n(:If, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 235)
  def _reduce_57(val, _values)
     n(:Repeat, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 239)
  def _reduce_58(val, _values)
     n(:While, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 247)
  def _reduce_59(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 249)
  def _reduce_60(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 251)
  def _reduce_61(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 253)
  def _reduce_62(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 255)
  def _reduce_63(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 257)
  def _reduce_64(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 259)
  def _reduce_65(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 261)
  def _reduce_66(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 263)
  def _reduce_67(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 265)
  def _reduce_68(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 269)
  def _reduce_69(val, _values)
     n(:Call, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 271)
  def _reduce_70(val, _values)
     n(:Call, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 275)
  def _reduce_71(val, _values)
     n(:Decrement, val[0])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 277)
  def _reduce_72(val, _values)
     n(:Decrement, val[0])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 281)
  def _reduce_73(val, _values)
     n(:Increment, val[0])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 283)
  def _reduce_74(val, _values)
     n(:Increment, val[0])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 287)
  def _reduce_75(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 289)
  def _reduce_76(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 291)
  def _reduce_77(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 293)
  def _reduce_78(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 295)
  def _reduce_79(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 297)
  def _reduce_80(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 299)
  def _reduce_81(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 301)
  def _reduce_82(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 303)
  def _reduce_83(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 305)
  def _reduce_84(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 307)
  def _reduce_85(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 309)
  def _reduce_86(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 311)
  def _reduce_87(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 313)
  def _reduce_88(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 315)
  def _reduce_89(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 317)
  def _reduce_90(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 319)
  def _reduce_91(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 321)
  def _reduce_92(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 323)
  def _reduce_93(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 325)
  def _reduce_94(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 327)
  def _reduce_95(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 329)
  def _reduce_96(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 331)
  def _reduce_97(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 333)
  def _reduce_98(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 335)
  def _reduce_99(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 337)
  def _reduce_100(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 339)
  def _reduce_101(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 341)
  def _reduce_102(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 343)
  def _reduce_103(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 345)
  def _reduce_104(val, _values)
     n(:Expression, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 347)
  def _reduce_105(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 349)
  def _reduce_106(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 351)
  def _reduce_107(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 353)
  def _reduce_108(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 355)
  def _reduce_109(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 357)
  def _reduce_110(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 359)
  def _reduce_111(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 361)
  def _reduce_112(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 363)
  def _reduce_113(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 371)
  def _reduce_114(val, _values)
     n(:Argument, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 373)
  def _reduce_115(val, _values)
     n(:Argument, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 375)
  def _reduce_116(val, _values)
     n(:Argument, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 377)
  def _reduce_117(val, _values)
     n(:Argument, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 381)
  def _reduce_118(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 383)
  def _reduce_119(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 385)
  def _reduce_120(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 387)
  def _reduce_121(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 389)
  def _reduce_122(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 391)
  def _reduce_123(val, _values)
     n(:KeyValuePair, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 395)
  def _reduce_124(val, _values)
     [val[0]] + val[2]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 397)
  def _reduce_125(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 399)
  def _reduce_126(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 403)
  def _reduce_127(val, _values)
     n(:Lvalue, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 405)
  def _reduce_128(val, _values)
     n(:Lvalue, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 409)
  def _reduce_129(val, _values)
     n(:Reference, val[1])
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 417)
  def _reduce_130(val, _values)
     [val[0]] + val[2]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 419)
  def _reduce_131(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 423)
  def _reduce_132(val, _values)
     n(:Array, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 425)
  def _reduce_133(val, _values)
     n(:Array, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 429)
  def _reduce_134(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 431)
  def _reduce_135(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 433)
  def _reduce_136(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 435)
  def _reduce_137(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 437)
  def _reduce_138(val, _values)
     nil
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 441)
  def _reduce_139(val, _values)
     val[1]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 443)
  def _reduce_140(val, _values)
     val[1]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 447)
  def _reduce_141(val, _values)
     [val[0]] + val[1]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 449)
  def _reduce_142(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 453)
  def _reduce_143(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 455)
  def _reduce_144(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 459)
  def _reduce_145(val, _values)
     [val[0]] + val[2]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 461)
  def _reduce_146(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 465)
  def _reduce_147(val, _values)
     n(:List, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 467)
  def _reduce_148(val, _values)
     n(:List, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 471)
  def _reduce_149(val, _values)
     n(:Parameter, val[1], 'reference')
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 473)
  def _reduce_150(val, _values)
     n(:Parameter, val[0], 'value')
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 477)
  def _reduce_151(val, _values)
     [val[0]] + val[2]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 479)
  def _reduce_152(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 483)
  def _reduce_153(val, _values)
     [val[0]] + val[1]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 485)
  def _reduce_154(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 489)
  def _reduce_155(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 491)
  def _reduce_156(val, _values)
     n(:Assignment, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 493)
  def _reduce_157(val, _values)
     val[0]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 497)
  def _reduce_158(val, _values)
     [val[0]] + val[2]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 499)
  def _reduce_159(val, _values)
     [val[0]]
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 507)
  def _reduce_160(val, _values)
     n(:Identifier, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 509)
  def _reduce_161(val, _values)
     n(:Identifier, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 511)
  def _reduce_162(val, _values)
     n(:Identifier, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 515)
  def _reduce_163(val, _values)
     n(:Integer, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 517)
  def _reduce_164(val, _values)
     n(:Integer, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 519)
  def _reduce_165(val, _values)
     n(:Integer, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 521)
  def _reduce_166(val, _values)
     n(:Integer, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 523)
  def _reduce_167(val, _values)
     n(:Integer, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 527)
  def _reduce_168(val, _values)
     n(:Ip, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 530)
  def _reduce_169(val, _values)
     n(:String, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 532)
  def _reduce_170(val, _values)
     n(:String, *val)
  end
.,.,

module_eval(<<'.,.,', 'nasl.y', 536)
  def _reduce_171(val, _values)
     n(:Undefined, *val)
  end
.,.,

def _reduce_none(val, _values)
  val[0]
end

  end   # class Grammar
end   # module Nasl



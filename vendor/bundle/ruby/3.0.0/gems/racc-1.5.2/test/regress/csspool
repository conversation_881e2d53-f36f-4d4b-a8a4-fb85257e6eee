#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.5.0
# from Racc grammar file "".
#

require 'racc/parser.rb'
module CSSPool
  module CSS
    class Parser < Racc::Parser

module_eval(<<'...end csspool.y/module_eval...', 'csspool.y', 670)

def numeric thing
  thing = thing.gsub(/[^\d.]/, '')
  Integer(thing) rescue Float(thing)
end

def interpret_identifier s
  interpret_escapes s
end

def interpret_uri s
  interpret_escapes s.match(/^url\((.*)\)$/mui)[1].strip.match(/^(['"]?)((?:\\.|.)*)\1$/mu)[2]
end

def interpret_string_no_quote s
  interpret_escapes s.match(/^(.*)\)$/mu)[1].strip.match(/^(['"]?)((?:\\.|.)*)\1$/mu)[2]
end

def interpret_string s
  interpret_escapes s.match(/^(['"])((?:\\.|.)*)\1$/mu)[2]
end

def interpret_escapes s
  token_exp = /\\(?:([0-9a-fA-F]{1,6}(?:\r\n|\s)?)|(.))/mu
  return s.gsub(token_exp) do |escape_sequence|
    if !$1.nil?
      code = $1.chomp.to_i 16
      code = 0xFFFD if code > 0x10FFFF
      next [code].pack('U')
    end
    next '' if $2 == "\n"
    next $2
  end
end

# override racc's on_error so we can have context in our error messages
def on_error(t, val, vstack)
  errcontext = (@ss.pre_match[-10..-1] || @ss.pre_match) +
                @ss.matched + @ss.post_match[0..9]
  line_number = @ss.pre_match.lines.count
  raise ParseError, sprintf("parse error on value %s (%s) " +
                            "on line %s around \"%s\"",
                            val.inspect, token_to_str(t) || '?',
                            line_number, errcontext)
end

def before_pos(val)
  # don't include leading whitespace
  return current_pos - val.last.length + val.last[/\A\s*/].size
end

def after_pos(val)
  # don't include trailing whitespace
  return current_pos - val.last[/\s*\z/].size
end

# charpos will work with multibyte strings but is not available until ruby 2
def current_pos
  @ss.respond_to?('charpos') ? @ss.charpos : @ss.pos
end
...end csspool.y/module_eval...
##### State transition tables begin ###

racc_action_table = [
     9,    10,   137,   129,    37,    31,    55,   139,   130,    39,
    45,    47,    45,    47,   123,     9,    10,   103,     3,    37,
    31,    98,   229,   103,    39,    45,    47,   230,   124,   125,
   224,    20,   113,    56,    37,    31,    23,   114,   104,    39,
    45,    47,   245,    27,   104,    11,    20,   126,    48,    25,
    48,    23,   242,    29,   138,   244,    38,    46,    27,    46,
    11,   108,   113,    48,    25,     9,    10,   114,    29,    37,
    31,    38,    46,   223,    39,    45,    47,    49,    48,   115,
     9,    10,   108,   113,    37,    31,    38,    46,   114,    39,
    45,    47,    55,   339,   243,   155,    20,   108,   113,    37,
    31,    23,   107,   114,    39,    45,    47,   231,    27,   115,
    11,    20,   232,    48,    25,    55,    23,    59,    29,    56,
   344,    38,    46,    27,   340,    11,    20,    53,    48,    25,
   115,    23,   345,    29,    37,    31,    38,    46,    27,    39,
    45,    47,    56,    48,    25,   115,   -28,   105,    29,    37,
    31,    38,    46,   116,    39,    45,    47,   250,    75,   120,
   251,    20,   108,   113,    37,    31,    23,   118,   114,    39,
    45,    47,   121,    27,    74,    73,    20,   313,    48,    25,
   314,    23,   128,    29,   108,   113,    38,    46,    27,   215,
   114,    20,   131,    48,    25,    75,    23,   136,    29,    37,
    64,    38,    46,    27,    39,    45,    47,    92,    48,    25,
   115,    74,    73,    29,    37,    31,    38,    46,    77,    39,
    45,    47,   148,    94,   103,   156,    20,   -89,    83,    37,
    31,    23,   115,    87,    39,    45,    47,   160,    27,    85,
   153,    20,   151,    48,    25,   104,    23,    84,    29,   157,
   158,    38,    46,    27,   163,   252,    20,   151,    48,    25,
   201,    23,   164,    29,    37,    31,    38,    46,    27,    39,
    45,    47,   203,    48,    25,   188,   202,    59,    29,    37,
   165,    38,    46,   187,    39,    45,    47,   201,   204,   166,
   184,    83,   288,   198,   287,   190,   197,   192,   191,   193,
   194,   195,    85,   202,    37,    45,    47,    37,    48,    39,
    45,    47,    39,    45,    47,   167,    38,    46,   168,    83,
   170,   113,   210,    48,   181,   186,   114,   185,   196,    37,
    85,    38,    46,    92,    39,    45,    47,   203,    84,   150,
   152,   151,   289,    48,   290,   292,   163,   291,    48,    94,
   -33,    48,    46,   204,   169,   199,    38,    46,    37,    38,
    46,   200,   -33,    39,    45,    47,    92,   188,   115,    59,
   294,   258,   293,    48,   296,   187,   295,   298,   260,   297,
   212,    38,    46,    83,   216,   198,   259,   190,   197,   192,
   191,   193,   194,   195,    85,    45,    47,    45,    47,    45,
    47,   217,    48,   218,   219,   188,   108,    59,   285,   108,
    38,    46,   170,   187,   225,   226,   181,   186,   233,   185,
   196,    83,   129,   198,   234,   190,   197,   192,   191,   193,
   194,   195,    85,    48,   163,    48,   255,    48,   256,   155,
   263,   169,    46,   188,    46,    59,    46,   264,   168,   265,
   170,   187,   266,    92,   181,   186,    92,   185,   196,    83,
    92,   198,    92,   190,   197,   192,   191,   193,   194,   195,
    85,   198,   278,   190,   197,   192,   191,   193,   194,   195,
   279,   188,   281,    59,   241,   235,   236,   237,   170,   187,
   286,   229,   181,   186,   231,   185,   196,    83,   163,   198,
   300,   190,   197,   192,   191,   193,   194,   195,    85,   301,
   302,   238,   239,   240,   303,   306,   307,   255,   141,   188,
    75,    59,   322,   163,   185,   168,   170,   187,   312,   317,
   181,   186,   143,   185,   196,    83,   319,   198,   323,   190,
   197,   192,   191,   193,   194,   195,    85,   324,   325,   145,
   326,   327,   328,   329,   330,   331,   144,   188,   146,    59,
   147,   332,   142,   333,   170,   187,   334,   306,   181,   186,
   163,   185,   196,    83,   338,   198,   163,   190,   197,   192,
   191,   193,   194,   195,    85,   346,   319,   319,   163,   351,
   306,   163,   319,   357,   359,   188,   nil,    59,   nil,   nil,
   nil,   nil,   170,   187,   nil,   nil,   181,   186,   nil,   185,
   196,    83,   nil,   198,   nil,   190,   197,   192,   191,   193,
   194,   195,    85,   272,   nil,    83,   nil,   198,   nil,   190,
   197,   192,   191,   193,   194,   195,   nil,   nil,   nil,   nil,
   170,   nil,   nil,   nil,   181,   186,   nil,   185,   196,   272,
   nil,    83,   nil,   198,   nil,   190,   197,   192,   191,   193,
   194,   195,   272,   nil,    83,   nil,   198,   nil,   190,   197,
   192,   191,   193,   194,   195,   272,   nil,    83,   nil,   198,
   nil,   190,   197,   192,   191,   193,   194,   195,   272,   nil,
    83,   nil,   198,   nil,   190,   197,   192,   191,   193,   194,
   195,   272,   nil,    83,   nil,   198,   nil,   190,   197,   192,
   191,   193,   194,   195 ]

racc_action_check = [
     2,     2,    47,    38,     2,     2,    10,    47,    38,     2,
     2,     2,    35,    35,    34,     5,     5,    26,     1,     5,
     5,    26,   128,   217,     5,     5,     5,   128,    34,    34,
   112,     2,   210,    10,    31,    31,     2,   210,    26,    31,
    31,    31,   143,     2,   217,     2,     5,    34,     2,     2,
    35,     5,   142,     2,    47,   143,     2,     2,     5,    35,
     5,   110,   110,     5,     5,     6,     6,   110,     5,     6,
     6,     5,     5,   112,     6,     6,     6,     3,    31,   210,
     7,     7,   221,   221,     7,     7,    31,    31,   221,     7,
     7,     7,    58,   309,   142,    58,     6,    28,    28,    12,
    12,     6,    28,    28,    12,    12,    12,   131,     6,   110,
     6,     7,   131,     6,     6,    11,     7,    11,     6,    58,
   315,     6,     6,     7,   309,     7,    12,     9,     7,     7,
   221,    12,   315,     7,    13,    13,     7,     7,    12,    13,
    13,    13,    11,    12,    12,    28,    20,    27,    12,    14,
    14,    12,    12,    29,    14,    14,    14,   149,    20,    32,
   149,    13,    30,    30,    15,    15,    13,    30,    30,    15,
    15,    15,    33,    13,    20,    20,    14,   269,    13,    13,
   269,    14,    37,    13,   100,   100,    13,    13,    14,   100,
   100,    15,    39,    14,    14,   157,    15,    46,    14,    19,
    19,    14,    14,    15,    19,    19,    19,    25,    15,    15,
    30,   157,   157,    15,    21,    21,    15,    15,    21,    21,
    21,    21,    53,    25,    99,    67,    19,    99,    22,    24,
    24,    19,   100,    24,    24,    24,    24,    71,    19,    22,
    57,    21,    57,    19,    19,    99,    21,    22,    19,    70,
    70,    19,    19,    21,    75,   154,    24,   154,    21,    21,
    90,    24,    76,    21,    64,    64,    21,    21,    24,    64,
    64,    64,    91,    24,    24,    83,    90,    83,    24,   121,
    78,    24,    24,    83,   121,   121,   121,   206,    91,    79,
    83,    83,   235,    83,   235,    83,    83,    83,    83,    83,
    83,    83,    83,   206,   122,    41,    41,   144,    64,   122,
   122,   122,   144,   144,   144,    80,    64,    64,    81,   166,
    83,    92,    92,   121,    83,    83,    92,    83,    83,   146,
   166,   121,   121,    92,   146,   146,   146,   207,   166,    54,
    54,    54,   236,    41,   236,   237,   270,   237,   122,    92,
   270,   144,    41,   207,    82,    86,   122,   122,   302,   144,
   144,    88,   270,   302,   302,   302,    94,   171,    92,   171,
   238,   171,   238,   146,   239,   171,   239,   240,   171,   240,
    97,   146,   146,   171,   101,   171,   171,   171,   171,   171,
   171,   171,   171,   171,   171,    42,    42,    43,    43,    44,
    44,   102,   302,   105,   106,   223,   108,   223,   223,   109,
   302,   302,   171,   223,   114,   117,   171,   171,   137,   171,
   171,   223,   138,   223,   139,   223,   223,   223,   223,   223,
   223,   223,   223,    42,   147,    43,   161,    44,   162,   172,
   175,   176,    42,   261,    43,   261,    44,   177,   179,   183,
   223,   261,   185,   201,   223,   223,   202,   223,   223,   261,
   203,   261,   204,   261,   261,   261,   261,   261,   261,   261,
   261,   189,   208,   189,   189,   189,   189,   189,   189,   189,
   209,   285,   214,   285,   140,   140,   140,   140,   261,   285,
   224,   233,   261,   261,   234,   261,   261,   285,   243,   285,
   245,   285,   285,   285,   285,   285,   285,   285,   285,   246,
   247,   140,   140,   140,   248,   249,   251,   254,    48,   286,
   255,   286,   286,   256,   266,   267,   285,   286,   268,   280,
   285,   285,    48,   285,   285,   286,   284,   286,   287,   286,
   286,   286,   286,   286,   286,   286,   286,   288,   289,    48,
   290,   291,   292,   293,   294,   295,    48,   322,    48,   322,
    48,   296,    48,   297,   286,   322,   298,   299,   286,   286,
   304,   286,   286,   322,   306,   322,   312,   322,   322,   322,
   322,   322,   322,   322,   322,   316,   320,   321,   335,   337,
   338,   340,   349,   350,   358,   353,   nil,   353,   nil,   nil,
   nil,   nil,   322,   353,   nil,   nil,   322,   322,   nil,   322,
   322,   353,   nil,   353,   nil,   353,   353,   353,   353,   353,
   353,   353,   353,   186,   nil,   186,   nil,   186,   nil,   186,
   186,   186,   186,   186,   186,   186,   nil,   nil,   nil,   nil,
   353,   nil,   nil,   nil,   353,   353,   nil,   353,   353,   272,
   nil,   272,   nil,   272,   nil,   272,   272,   272,   272,   272,
   272,   272,   313,   nil,   313,   nil,   313,   nil,   313,   313,
   313,   313,   313,   313,   313,   314,   nil,   314,   nil,   314,
   nil,   314,   314,   314,   314,   314,   314,   314,   344,   nil,
   344,   nil,   344,   nil,   344,   344,   344,   344,   344,   344,
   344,   345,   nil,   345,   nil,   345,   nil,   345,   345,   345,
   345,   345,   345,   345 ]

racc_action_pointer = [
   nil,    18,    -2,    77,   nil,    13,    63,    78,   nil,   123,
     2,   111,    93,   128,   143,   158,   nil,   nil,   nil,   193,
   140,   208,   208,   nil,   223,   189,    11,   141,    92,   144,
   157,    28,   150,   164,     7,     0,   nil,   124,    -3,   134,
   nil,   293,   383,   385,   387,   nil,   191,    -4,   512,   nil,
   nil,   nil,   nil,   217,   334,   nil,   nil,   235,    88,   nil,
   nil,   nil,   nil,   nil,   258,   nil,   nil,   215,   nil,   nil,
   241,   231,   nil,   nil,   nil,   247,   252,   nil,   271,   281,
   308,   311,   347,   271,   nil,   nil,   345,   nil,   352,   nil,
   224,   236,   315,   nil,   348,   nil,   nil,   370,   nil,   218,
   179,   375,   393,   nil,   nil,   394,   394,   nil,   401,   404,
    56,   nil,    23,   nil,   408,   nil,   nil,   405,   nil,   nil,
   nil,   273,   298,   nil,   nil,   nil,   nil,   nil,    16,   nil,
   nil,   101,   nil,   nil,   nil,   nil,   nil,   360,   416,   366,
   470,   nil,    46,    36,   301,   nil,   323,   427,   nil,   152,
   nil,   nil,   nil,   nil,   250,   nil,   nil,   177,   nil,   nil,
   nil,   400,   432,   nil,   nil,   nil,   299,   nil,   nil,   nil,
   nil,   363,   432,   nil,   nil,   433,   434,   440,   nil,   441,
   nil,   nil,   nil,   430,   nil,   444,   605,   nil,   nil,   449,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   435,   438,   442,   444,   nil,   251,   301,   453,   461,
    26,   nil,   nil,   nil,   472,   nil,   nil,    17,   nil,   nil,
   nil,    77,   nil,   401,   440,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   485,   488,   288,   338,   341,   366,   370,
   373,   nil,   nil,   491,   nil,   481,   490,   502,   495,   509,
   nil,   510,   nil,   nil,   481,   502,   516,   nil,   nil,   nil,
   nil,   439,   nil,   nil,   nil,   nil,   468,   518,   509,   155,
   339,   nil,   631,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   510,   nil,   nil,   nil,   504,   477,   515,   524,   533,   534,
   536,   537,   538,   539,   540,   541,   547,   549,   552,   561,
   nil,   nil,   352,   nil,   563,   nil,   566,   nil,   nil,    74,
   nil,   nil,   569,   644,   657,   109,   566,   nil,   nil,   nil,
   554,   555,   553,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   581,   nil,   570,   584,   nil,
   584,   nil,   nil,   nil,   670,   683,   nil,   nil,   nil,   560,
   574,   nil,   nil,   591,   nil,   nil,   nil,   nil,   575,   nil ]

racc_action_default = [
    -1,  -229,   -10,  -229,    -2,    -6,    -7,    -8,    -9,  -229,
  -229,  -229,   -41,   -42,   -43,   -44,   -45,   -46,   -47,   -33,
   -23,  -229,  -229,   -55,  -229,  -229,   -89,  -229,  -229,  -229,
  -229,  -229,  -229,  -103,  -105,  -111,  -112,  -115,  -229,  -120,
  -119,  -124,  -125,  -126,  -127,  -132,  -229,  -229,  -229,   360,
    -3,    -4,    -5,  -229,  -229,   -15,   -16,  -229,  -229,  -228,
   -37,   -38,   -39,   -40,   -32,   -48,   -49,  -229,   -99,   -21,
  -229,  -229,   -35,   -26,   -27,   -33,  -229,   -53,  -229,   -57,
   -58,   -59,   -60,  -229,  -198,  -214,  -229,   -62,  -229,   -64,
   -65,   -66,  -229,   -71,  -229,   -73,   -74,  -229,   -82,   -85,
  -229,  -229,   -91,   -92,   -93,  -229,  -229,   -95,  -160,  -165,
  -166,  -167,  -229,  -174,  -229,  -176,   -96,  -229,   -98,  -100,
  -101,  -229,  -229,  -106,  -107,  -108,  -109,  -110,  -229,  -117,
  -121,  -229,  -128,  -129,  -130,  -131,  -133,  -115,  -229,  -229,
  -229,  -147,  -229,  -229,  -229,  -152,  -229,   -33,   -11,  -229,
   -13,   -14,   -20,   -18,  -229,  -227,   -50,   -28,   -51,   -35,
   -29,   -25,  -229,   -32,   -52,   -54,  -229,  -197,  -194,  -213,
   -36,  -182,  -183,  -184,  -185,  -186,  -187,  -188,  -189,  -190,
  -191,  -192,  -193,  -229,  -196,  -200,  -229,  -212,  -216,  -229,
  -218,  -219,  -220,  -221,  -222,  -223,  -224,  -225,  -226,   -61,
   -63,  -229,  -229,  -229,  -229,   -67,   -68,   -69,  -229,  -229,
  -229,   -72,   -81,   -84,  -229,   -87,   -88,   -89,   -83,   -94,
  -161,  -164,  -163,  -229,  -229,  -175,   -97,  -102,  -104,  -116,
  -123,  -118,  -122,  -229,  -229,  -229,  -229,  -229,  -229,  -229,
  -229,  -146,  -148,   -33,  -149,  -229,  -229,  -114,  -229,  -156,
   -12,  -229,   -17,   -22,   -24,  -229,   -33,   -56,  -177,  -178,
  -179,  -229,  -181,  -215,  -211,  -195,  -229,  -209,  -229,  -202,
  -205,  -208,  -229,  -217,   -76,   -78,   -75,   -77,   -70,   -79,
  -229,   -86,   -90,  -162,  -173,  -229,  -229,  -229,  -229,  -229,
  -229,  -229,  -229,  -229,  -229,  -229,  -229,  -229,  -229,  -156,
  -150,  -151,  -229,  -153,   -33,  -157,  -158,   -19,   -34,  -229,
  -180,  -199,   -33,  -229,  -229,  -229,  -229,   -80,  -168,  -172,
  -173,  -173,  -229,  -134,  -135,  -136,  -137,  -138,  -139,  -140,
  -141,  -142,  -143,  -144,  -145,   -33,  -113,  -229,  -229,   -30,
   -33,  -201,  -203,  -204,  -229,  -229,  -210,  -169,  -170,  -173,
  -229,  -154,  -159,  -229,  -206,  -207,  -171,  -155,  -229,   -31 ]

racc_goto_table = [
    81,   248,   183,    68,   106,    91,   117,   271,    78,   246,
   273,   247,    82,    69,   209,   161,    97,    89,   268,   304,
    90,   119,    54,    57,   220,   221,   318,    60,    61,    62,
    63,   354,   355,     1,    65,   127,    76,     2,   149,    86,
    58,   132,   133,   134,   135,     4,    70,   159,    50,    51,
    52,   308,    67,    66,   119,    88,   208,   282,   227,   162,
   228,   122,   347,   348,   140,   352,   261,   311,   nil,   335,
   154,   nil,   207,   nil,   211,   nil,   214,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   205,   nil,   222,   206,   nil,   213,
   262,   356,   nil,   271,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   254,   nil,   316,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   249,   280,   nil,   271,   271,   nil,   nil,   nil,   nil,
   nil,   nil,   284,   nil,    81,   342,   343,   nil,   nil,   nil,
   253,   nil,   257,   nil,   nil,   nil,    82,   336,   nil,   nil,
   nil,   nil,   nil,   nil,   267,   271,   271,   247,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   310,   274,   275,   276,   277,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   283,   nil,   nil,
   nil,   nil,   nil,   nil,   320,   321,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   299,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   309,   349,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   267,   nil,   nil,   nil,   315,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   358,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   337,   nil,
   nil,   267,   267,   nil,   nil,   nil,   341,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   350,
   nil,   nil,   267,   267,   353 ]

racc_goto_check = [
    35,    62,    18,    17,    51,    41,    51,    77,    32,    58,
    77,    58,    36,    12,    46,    15,    48,    39,    82,    68,
    40,    55,     8,     8,    70,    70,    73,     7,     7,     7,
     7,    84,    84,     1,     7,    61,     7,     3,     9,     7,
    10,    61,    61,    61,    61,     2,    11,    14,     2,     2,
     2,    16,    27,    28,    55,    38,    42,    52,    56,    17,
    57,    59,    73,    73,    63,    69,    74,    81,   nil,    68,
     8,   nil,    41,   nil,    41,   nil,    51,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,    39,   nil,    51,    40,   nil,    48,
    18,    73,   nil,    77,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    15,   nil,    82,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,    17,    46,   nil,    77,    77,   nil,   nil,   nil,   nil,
   nil,   nil,    18,   nil,    35,    82,    82,   nil,   nil,   nil,
    12,   nil,    32,   nil,   nil,   nil,    36,    62,   nil,   nil,
   nil,   nil,   nil,   nil,    35,    77,    77,    58,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    18,    41,    41,    41,    41,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    51,   nil,   nil,
   nil,   nil,   nil,   nil,    18,    18,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    17,    18,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
    35,   nil,   nil,   nil,    17,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,    18,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,   nil,
   nil,    35,    35,   nil,   nil,   nil,    17,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,    17,
   nil,   nil,    35,    35,    17 ]

racc_goto_pointer = [
   nil,    33,    43,    37,   nil,   nil,   nil,    15,    12,   -16,
    29,    26,    -7,   nil,   -24,   -57,  -204,   -16,   -81,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,    33,    34,   nil,
   nil,   nil,   -14,   nil,   nil,   -22,   -10,   nil,    30,    -8,
    -5,   -20,   -36,   nil,   nil,   nil,   -78,   nil,   -10,   nil,
   nil,   -24,  -160,   nil,   nil,   -10,   -63,   -62,  -135,    27,
   nil,     0,  -145,    17,   nil,   nil,   nil,   nil,  -230,  -273,
   -84,   nil,   nil,  -258,  -105,   nil,   nil,  -179,   nil,   nil,
   nil,  -199,  -168,   nil,  -313,   nil ]

racc_goto_default = [
   nil,   nil,   nil,   nil,     5,     6,     7,     8,   nil,   nil,
   172,   nil,   nil,    71,   nil,   nil,    72,   nil,   nil,   180,
    12,    13,    14,    15,    16,    17,    18,   nil,   nil,    19,
    21,    22,   nil,    79,    80,   179,   176,    24,   nil,   nil,
   nil,   nil,   nil,    93,    95,    96,   111,    26,   nil,    99,
   100,   nil,   101,   102,    28,    30,    32,    33,    34,   nil,
    35,    36,   nil,    40,    41,    42,    43,    44,   nil,   305,
   110,   109,   112,   nil,   nil,   171,   173,   174,   175,   177,
   178,   182,   nil,   269,   270,   189 ]

racc_reduce_table = [
  0, 0, :racc_error,
  0, 63, :_reduce_1,
  2, 61, :_reduce_2,
  2, 62, :_reduce_none,
  2, 62, :_reduce_none,
  2, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  0, 62, :_reduce_none,
  3, 64, :_reduce_11,
  4, 65, :_reduce_12,
  3, 65, :_reduce_13,
  2, 68, :_reduce_none,
  1, 68, :_reduce_15,
  1, 68, :_reduce_16,
  4, 66, :_reduce_17,
  3, 66, :_reduce_18,
  3, 69, :_reduce_19,
  1, 69, :_reduce_20,
  1, 71, :_reduce_21,
  3, 71, :_reduce_22,
  0, 71, :_reduce_23,
  3, 72, :_reduce_24,
  2, 72, :_reduce_25,
  1, 73, :_reduce_26,
  1, 73, :_reduce_27,
  0, 73, :_reduce_28,
  1, 74, :_reduce_29,
  5, 76, :_reduce_30,
  8, 76, :_reduce_31,
  1, 77, :_reduce_32,
  0, 77, :_reduce_33,
  3, 75, :_reduce_34,
  0, 75, :_reduce_35,
  1, 79, :_reduce_36,
  2, 67, :_reduce_none,
  2, 67, :_reduce_none,
  2, 67, :_reduce_none,
  2, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 67, :_reduce_none,
  1, 81, :_reduce_none,
  1, 81, :_reduce_none,
  1, 81, :_reduce_none,
  1, 87, :_reduce_none,
  1, 87, :_reduce_none,
  3, 84, :_reduce_50,
  3, 89, :_reduce_51,
  3, 85, :_reduce_52,
  2, 85, :_reduce_53,
  3, 90, :_reduce_54,
  1, 91, :_reduce_55,
  3, 92, :_reduce_56,
  1, 92, :_reduce_57,
  1, 93, :_reduce_none,
  1, 93, :_reduce_none,
  1, 93, :_reduce_none,
  3, 86, :_reduce_61,
  2, 86, :_reduce_62,
  3, 97, :_reduce_63,
  1, 98, :_reduce_64,
  1, 98, :_reduce_65,
  1, 98, :_reduce_66,
  1, 102, :_reduce_67,
  1, 102, :_reduce_68,
  1, 102, :_reduce_69,
  3, 101, :_reduce_70,
  1, 101, :_reduce_71,
  2, 99, :_reduce_72,
  1, 100, :_reduce_none,
  1, 100, :_reduce_none,
  3, 104, :_reduce_75,
  3, 104, :_reduce_76,
  3, 105, :_reduce_77,
  3, 105, :_reduce_78,
  3, 103, :_reduce_79,
  4, 103, :_reduce_80,
  3, 82, :_reduce_none,
  2, 82, :_reduce_none,
  3, 107, :_reduce_83,
  2, 108, :_reduce_none,
  1, 108, :_reduce_none,
  3, 109, :_reduce_86,
  2, 109, :_reduce_87,
  2, 110, :_reduce_88,
  0, 112, :_reduce_none,
  3, 112, :_reduce_90,
  1, 112, :_reduce_none,
  1, 113, :_reduce_none,
  1, 113, :_reduce_93,
  3, 83, :_reduce_94,
  2, 83, :_reduce_95,
  2, 114, :_reduce_96,
  3, 80, :_reduce_97,
  2, 80, :_reduce_98,
  1, 88, :_reduce_99,
  2, 115, :_reduce_100,
  2, 115, :_reduce_101,
  3, 116, :_reduce_102,
  1, 116, :_reduce_103,
  3, 117, :_reduce_104,
  1, 117, :_reduce_none,
  1, 119, :_reduce_106,
  1, 119, :_reduce_107,
  1, 119, :_reduce_108,
  1, 119, :_reduce_109,
  2, 118, :_reduce_110,
  1, 118, :_reduce_111,
  1, 118, :_reduce_112,
  3, 122, :_reduce_113,
  1, 122, :_reduce_none,
  1, 123, :_reduce_115,
  3, 123, :_reduce_116,
  2, 123, :_reduce_117,
  3, 123, :_reduce_118,
  1, 120, :_reduce_119,
  1, 120, :_reduce_120,
  2, 120, :_reduce_121,
  3, 120, :_reduce_122,
  3, 120, :_reduce_123,
  1, 121, :_reduce_124,
  1, 121, :_reduce_125,
  1, 121, :_reduce_126,
  1, 121, :_reduce_127,
  2, 121, :_reduce_128,
  2, 121, :_reduce_129,
  2, 121, :_reduce_130,
  2, 121, :_reduce_131,
  1, 124, :_reduce_132,
  2, 125, :_reduce_133,
  5, 126, :_reduce_134,
  5, 126, :_reduce_135,
  5, 126, :_reduce_136,
  5, 126, :_reduce_137,
  5, 126, :_reduce_138,
  5, 126, :_reduce_139,
  5, 126, :_reduce_140,
  5, 126, :_reduce_141,
  5, 126, :_reduce_142,
  5, 126, :_reduce_143,
  5, 126, :_reduce_144,
  5, 126, :_reduce_145,
  3, 126, :_reduce_146,
  2, 127, :_reduce_147,
  3, 127, :_reduce_148,
  3, 127, :_reduce_149,
  4, 127, :_reduce_150,
  4, 127, :_reduce_151,
  2, 127, :_reduce_152,
  4, 127, :_reduce_153,
  6, 127, :_reduce_154,
  7, 127, :_reduce_155,
  0, 128, :_reduce_none,
  1, 128, :_reduce_none,
  1, 129, :_reduce_none,
  3, 129, :_reduce_none,
  1, 130, :_reduce_none,
  2, 130, :_reduce_none,
  3, 111, :_reduce_none,
  2, 111, :_reduce_none,
  2, 111, :_reduce_none,
  1, 111, :_reduce_none,
  1, 111, :_reduce_none,
  1, 131, :_reduce_167,
  4, 106, :_reduce_168,
  5, 106, :_reduce_169,
  5, 106, :_reduce_170,
  6, 106, :_reduce_171,
  1, 133, :_reduce_172,
  0, 133, :_reduce_173,
  1, 132, :_reduce_174,
  2, 132, :_reduce_175,
  1, 132, :_reduce_176,
  1, 134, :_reduce_none,
  1, 134, :_reduce_none,
  1, 134, :_reduce_none,
  3, 78, :_reduce_180,
  2, 78, :_reduce_181,
  1, 78, :_reduce_182,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  1, 135, :_reduce_none,
  2, 95, :_reduce_194,
  3, 95, :_reduce_195,
  2, 95, :_reduce_196,
  2, 94, :_reduce_197,
  1, 94, :_reduce_198,
  3, 141, :_reduce_none,
  1, 141, :_reduce_none,
  4, 140, :_reduce_201,
  1, 142, :_reduce_none,
  3, 142, :_reduce_203,
  3, 142, :_reduce_204,
  1, 143, :_reduce_none,
  4, 143, :_reduce_206,
  4, 143, :_reduce_207,
  1, 144, :_reduce_208,
  1, 144, :_reduce_209,
  3, 144, :_reduce_210,
  2, 139, :_reduce_211,
  1, 139, :_reduce_212,
  2, 96, :_reduce_213,
  1, 96, :_reduce_214,
  2, 138, :_reduce_215,
  1, 138, :_reduce_216,
  2, 137, :_reduce_217,
  1, 137, :_reduce_218,
  1, 137, :_reduce_219,
  1, 137, :_reduce_220,
  1, 137, :_reduce_221,
  1, 137, :_reduce_222,
  1, 137, :_reduce_223,
  1, 136, :_reduce_224,
  1, 145, :_reduce_225,
  1, 145, :_reduce_226,
  2, 70, :_reduce_227,
  1, 70, :_reduce_228 ]

racc_reduce_n = 229

racc_shift_n = 360

racc_token_table = {
  false => 0,
  :error => 1,
  :CHARSET_SYM => 2,
  :IMPORT_SYM => 3,
  :STRING => 4,
  :SEMI => 5,
  :IDENT => 6,
  :S => 7,
  :COMMA => 8,
  :LBRACE => 9,
  :RBRACE => 10,
  :STAR => 11,
  :HASH => 12,
  :LSQUARE => 13,
  :RSQUARE => 14,
  :EQUAL => 15,
  :INCLUDES => 16,
  :DASHMATCH => 17,
  :LPAREN => 18,
  :RPAREN => 19,
  :FUNCTION => 20,
  :GREATER => 21,
  :PLUS => 22,
  :SLASH => 23,
  :NUMBER => 24,
  :MINUS => 25,
  :LENGTH => 26,
  :PERCENTAGE => 27,
  :ANGLE => 28,
  :TIME => 29,
  :FREQ => 30,
  :URI => 31,
  :IMPORTANT_SYM => 32,
  :MEDIA_SYM => 33,
  :NOT => 34,
  :ONLY => 35,
  :AND => 36,
  :NTH_PSEUDO_CLASS => 37,
  :DOCUMENT_QUERY_SYM => 38,
  :FUNCTION_NO_QUOTE => 39,
  :TILDE => 40,
  :PREFIXMATCH => 41,
  :SUFFIXMATCH => 42,
  :SUBSTRINGMATCH => 43,
  :NOT_PSEUDO_CLASS => 44,
  :KEYFRAMES_SYM => 45,
  :MATCHES_PSEUDO_CLASS => 46,
  :NAMESPACE_SYM => 47,
  :MOZ_PSEUDO_ELEMENT => 48,
  :RESOLUTION => 49,
  :COLON => 50,
  :SUPPORTS_SYM => 51,
  :OR => 52,
  :VARIABLE_NAME => 53,
  :CALC_SYM => 54,
  :FONTFACE_SYM => 55,
  :UNICODE_RANGE => 56,
  :RATIO => 57,
  "|" => 58,
  "." => 59 }

racc_nt_base = 60

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]

Racc_token_to_s_table = [
  "$end",
  "error",
  "CHARSET_SYM",
  "IMPORT_SYM",
  "STRING",
  "SEMI",
  "IDENT",
  "S",
  "COMMA",
  "LBRACE",
  "RBRACE",
  "STAR",
  "HASH",
  "LSQUARE",
  "RSQUARE",
  "EQUAL",
  "INCLUDES",
  "DASHMATCH",
  "LPAREN",
  "RPAREN",
  "FUNCTION",
  "GREATER",
  "PLUS",
  "SLASH",
  "NUMBER",
  "MINUS",
  "LENGTH",
  "PERCENTAGE",
  "ANGLE",
  "TIME",
  "FREQ",
  "URI",
  "IMPORTANT_SYM",
  "MEDIA_SYM",
  "NOT",
  "ONLY",
  "AND",
  "NTH_PSEUDO_CLASS",
  "DOCUMENT_QUERY_SYM",
  "FUNCTION_NO_QUOTE",
  "TILDE",
  "PREFIXMATCH",
  "SUFFIXMATCH",
  "SUBSTRINGMATCH",
  "NOT_PSEUDO_CLASS",
  "KEYFRAMES_SYM",
  "MATCHES_PSEUDO_CLASS",
  "NAMESPACE_SYM",
  "MOZ_PSEUDO_ELEMENT",
  "RESOLUTION",
  "COLON",
  "SUPPORTS_SYM",
  "OR",
  "VARIABLE_NAME",
  "CALC_SYM",
  "FONTFACE_SYM",
  "UNICODE_RANGE",
  "RATIO",
  "\"|\"",
  "\".\"",
  "$start",
  "document",
  "stylesheet",
  "@1",
  "charset",
  "import",
  "namespace",
  "body",
  "import_location",
  "medium",
  "ident",
  "media_query_list",
  "media_query",
  "optional_only_or_not",
  "media_type",
  "optional_and_exprs",
  "media_expr",
  "optional_space",
  "expr",
  "resolution",
  "ruleset",
  "conditional_rule",
  "keyframes_rule",
  "fontface_rule",
  "media",
  "document_query",
  "supports",
  "body_in_media",
  "empty_ruleset",
  "start_media",
  "start_document_query",
  "start_document_query_pos",
  "url_match_fns",
  "url_match_fn",
  "function_no_quote",
  "function",
  "uri",
  "start_supports",
  "supports_condition_root",
  "supports_negation",
  "supports_conjunction_or_disjunction",
  "supports_condition_in_parens",
  "supports_condition",
  "supports_declaration_condition",
  "supports_conjunction",
  "supports_disjunction",
  "declaration_internal",
  "start_keyframes_rule",
  "keyframes_blocks",
  "keyframes_block",
  "start_keyframes_block",
  "declarations",
  "keyframes_selectors",
  "keyframes_selector",
  "start_fontface_rule",
  "start_selector",
  "selectors",
  "selector",
  "simple_selector",
  "combinator",
  "element_name",
  "hcap",
  "simple_selectors",
  "ident_with_namespace",
  "hash",
  "class",
  "attrib",
  "pseudo",
  "any_number_of_idents",
  "multiple_idents",
  "one_or_more_semis",
  "declaration",
  "property",
  "prio",
  "operator",
  "term",
  "ratio",
  "numeric",
  "string",
  "hexcolor",
  "calc",
  "uranges",
  "calc_sum",
  "calc_product",
  "calc_value",
  "unary_operator" ]

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

module_eval(<<'.,.,', 'csspool.y', 26)
  def _reduce_1(val, _values, result)
     @handler.start_document
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 28)
  def _reduce_2(val, _values, result)
     @handler.end_document
    result
  end
.,.,

# reduce 3 omitted

# reduce 4 omitted

# reduce 5 omitted

# reduce 6 omitted

# reduce 7 omitted

# reduce 8 omitted

# reduce 9 omitted

# reduce 10 omitted

module_eval(<<'.,.,', 'csspool.y', 41)
  def _reduce_11(val, _values, result)
     @handler.charset interpret_string(val[1]), {}
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 45)
  def _reduce_12(val, _values, result)
            @handler.import_style val[2], val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 48)
  def _reduce_13(val, _values, result)
            @handler.import_style [], val[1]

    result
  end
.,.,

# reduce 14 omitted

module_eval(<<'.,.,', 'csspool.y', 53)
  def _reduce_15(val, _values, result)
     result = Terms::String.new interpret_string val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 54)
  def _reduce_16(val, _values, result)
     result = Terms::URI.new interpret_uri val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 58)
  def _reduce_17(val, _values, result)
            @handler.namespace val[1], val[2]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 61)
  def _reduce_18(val, _values, result)
            @handler.namespace nil, val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 66)
  def _reduce_19(val, _values, result)
            result = val[0] << MediaType.new(val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 69)
  def _reduce_20(val, _values, result)
            result = [MediaType.new(val[0])]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 73)
  def _reduce_21(val, _values, result)
     result = MediaQueryList.new([ val[0] ])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 74)
  def _reduce_22(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 75)
  def _reduce_23(val, _values, result)
     result = MediaQueryList.new
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 78)
  def _reduce_24(val, _values, result)
     result = MediaQuery.new(val[0], val[1], val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 79)
  def _reduce_25(val, _values, result)
     result = MediaQuery.new(nil, val[0], val[1])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 82)
  def _reduce_26(val, _values, result)
     result = :only
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 83)
  def _reduce_27(val, _values, result)
     result = :not
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 84)
  def _reduce_28(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 87)
  def _reduce_29(val, _values, result)
     result = MediaType.new(val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 90)
  def _reduce_30(val, _values, result)
     result = MediaType.new(val[2])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 91)
  def _reduce_31(val, _values, result)
     result = MediaFeature.new(val[2], val[6][0])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 94)
  def _reduce_32(val, _values, result)
     result = val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 95)
  def _reduce_33(val, _values, result)
     result = nil
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 98)
  def _reduce_34(val, _values, result)
     result = val[0] << val[2]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 99)
  def _reduce_35(val, _values, result)
     result = []
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 103)
  def _reduce_36(val, _values, result)
            unit = val.first.gsub(/[\s\d.]/, '')
        number = numeric(val.first)
        result = Terms::Resolution.new(number, unit)

    result
  end
.,.,

# reduce 37 omitted

# reduce 38 omitted

# reduce 39 omitted

# reduce 40 omitted

# reduce 41 omitted

# reduce 42 omitted

# reduce 43 omitted

# reduce 44 omitted

# reduce 45 omitted

# reduce 46 omitted

# reduce 47 omitted

# reduce 48 omitted

# reduce 49 omitted

module_eval(<<'.,.,', 'csspool.y', 128)
  def _reduce_50(val, _values, result)
     @handler.end_media val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 132)
  def _reduce_51(val, _values, result)
            result = val[1]
        @handler.start_media result

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 137)
  def _reduce_52(val, _values, result)
     @handler.end_document_query(before_pos(val), after_pos(val))
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 138)
  def _reduce_53(val, _values, result)
     @handler.end_document_query(before_pos(val), after_pos(val))
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 142)
  def _reduce_54(val, _values, result)
            @handler.start_document_query(val[1], after_pos(val))

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 147)
  def _reduce_55(val, _values, result)
            @handler.node_start_pos = before_pos(val)

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 152)
  def _reduce_56(val, _values, result)
            result = [val[0], val[2]].flatten

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 155)
  def _reduce_57(val, _values, result)
            result = val

    result
  end
.,.,

# reduce 58 omitted

# reduce 59 omitted

# reduce 60 omitted

module_eval(<<'.,.,', 'csspool.y', 164)
  def _reduce_61(val, _values, result)
     @handler.end_supports
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 165)
  def _reduce_62(val, _values, result)
     @handler.end_supports
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 169)
  def _reduce_63(val, _values, result)
            @handler.start_supports val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 173)
  def _reduce_64(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 174)
  def _reduce_65(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 175)
  def _reduce_66(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 178)
  def _reduce_67(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 179)
  def _reduce_68(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 180)
  def _reduce_69(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 183)
  def _reduce_70(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 184)
  def _reduce_71(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 187)
  def _reduce_72(val, _values, result)
     result = val.join('')
    result
  end
.,.,

# reduce 73 omitted

# reduce 74 omitted

module_eval(<<'.,.,', 'csspool.y', 194)
  def _reduce_75(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 195)
  def _reduce_76(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 198)
  def _reduce_77(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 199)
  def _reduce_78(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 202)
  def _reduce_79(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 203)
  def _reduce_80(val, _values, result)
     result = val.join('')
    result
  end
.,.,

# reduce 81 omitted

# reduce 82 omitted

module_eval(<<'.,.,', 'csspool.y', 211)
  def _reduce_83(val, _values, result)
            @handler.start_keyframes_rule val[1]

    result
  end
.,.,

# reduce 84 omitted

# reduce 85 omitted

module_eval(<<'.,.,', 'csspool.y', 219)
  def _reduce_86(val, _values, result)
     @handler.end_keyframes_block
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 220)
  def _reduce_87(val, _values, result)
     @handler.end_keyframes_block
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 224)
  def _reduce_88(val, _values, result)
            @handler.start_keyframes_block val[0]

    result
  end
.,.,

# reduce 89 omitted

module_eval(<<'.,.,', 'csspool.y', 229)
  def _reduce_90(val, _values, result)
             result = val[0] + ', ' + val[2]

    result
  end
.,.,

# reduce 91 omitted

# reduce 92 omitted

module_eval(<<'.,.,', 'csspool.y', 235)
  def _reduce_93(val, _values, result)
     result = val[0].strip
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 238)
  def _reduce_94(val, _values, result)
     @handler.end_fontface_rule
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 239)
  def _reduce_95(val, _values, result)
     @handler.end_fontface_rule
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 243)
  def _reduce_96(val, _values, result)
            @handler.start_fontface_rule

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 248)
  def _reduce_97(val, _values, result)
            @handler.end_selector val.first

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 251)
  def _reduce_98(val, _values, result)
            @handler.end_selector val.first

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 256)
  def _reduce_99(val, _values, result)
            start = @handler.start_selector([])
        @handler.end_selector(start)

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 261)
  def _reduce_100(val, _values, result)
     result = val.last
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 263)
  def _reduce_101(val, _values, result)
            @handler.start_selector val.first

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 269)
  def _reduce_102(val, _values, result)
            sel = Selector.new(val.first, {})
        result = [sel].concat(val[2])

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 274)
  def _reduce_103(val, _values, result)
            result = [Selector.new(val.first, {})]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 280)
  def _reduce_104(val, _values, result)
            val.flatten!
        val[2].combinator = val.delete_at 1
        result = val

    result
  end
.,.,

# reduce 105 omitted

module_eval(<<'.,.,', 'csspool.y', 287)
  def _reduce_106(val, _values, result)
     result = :s
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 288)
  def _reduce_107(val, _values, result)
     result = :>
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 289)
  def _reduce_108(val, _values, result)
     result = :+
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 290)
  def _reduce_109(val, _values, result)
     result = :~
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 294)
  def _reduce_110(val, _values, result)
            selector = val.first
        selector.additional_selectors = val.last
        result = [selector]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 298)
  def _reduce_111(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 301)
  def _reduce_112(val, _values, result)
            ss = Selectors::Simple.new nil, nil
        ss.additional_selectors = val.flatten
        result = [ss]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 307)
  def _reduce_113(val, _values, result)
     result = [val[0], val[2]].flatten
    result
  end
.,.,

# reduce 114 omitted

module_eval(<<'.,.,', 'csspool.y', 311)
  def _reduce_115(val, _values, result)
     result = [interpret_identifier(val[0]), nil]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 312)
  def _reduce_116(val, _values, result)
     result = [interpret_identifier(val[2]), interpret_identifier(val[0])]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 313)
  def _reduce_117(val, _values, result)
     result = [interpret_identifier(val[1]), nil]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 314)
  def _reduce_118(val, _values, result)
     result = [interpret_identifier(val[2]), '*']
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 317)
  def _reduce_119(val, _values, result)
     result = Selectors::Type.new val.first[0], nil, val.first[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 318)
  def _reduce_120(val, _values, result)
     result = Selectors::Universal.new val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 319)
  def _reduce_121(val, _values, result)
     result = Selectors::Universal.new val[1]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 320)
  def _reduce_122(val, _values, result)
     result = Selectors::Universal.new val[2], nil, val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 321)
  def _reduce_123(val, _values, result)
     result = Selectors::Universal.new val[2], nil, interpret_identifier(val[0])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 324)
  def _reduce_124(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 325)
  def _reduce_125(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 326)
  def _reduce_126(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 327)
  def _reduce_127(val, _values, result)
     result = val
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 328)
  def _reduce_128(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 329)
  def _reduce_129(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 330)
  def _reduce_130(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 331)
  def _reduce_131(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 335)
  def _reduce_132(val, _values, result)
            result = Selectors::Id.new interpret_identifier val.first.sub(/^#/, '')

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 339)
  def _reduce_133(val, _values, result)
            result = Selectors::Class.new interpret_identifier val.last

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 344)
  def _reduce_134(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::EQUALS,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 352)
  def _reduce_135(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::EQUALS,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 360)
  def _reduce_136(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::INCLUDES,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 368)
  def _reduce_137(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::INCLUDES,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 376)
  def _reduce_138(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::DASHMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 384)
  def _reduce_139(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::DASHMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 392)
  def _reduce_140(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::PREFIXMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 400)
  def _reduce_141(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::PREFIXMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 408)
  def _reduce_142(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::SUFFIXMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 416)
  def _reduce_143(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::SUFFIXMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 424)
  def _reduce_144(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_identifier(val[3]),
          Selectors::Attribute::SUBSTRINGMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 432)
  def _reduce_145(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          interpret_string(val[3]),
          Selectors::Attribute::SUBSTRINGMATCH,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 440)
  def _reduce_146(val, _values, result)
            result = Selectors::Attribute.new(
          val[1][0],
          nil,
          Selectors::Attribute::SET,
          val[1][1]
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 450)
  def _reduce_147(val, _values, result)
            result = Selectors::pseudo interpret_identifier(val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 453)
  def _reduce_148(val, _values, result)
            result = Selectors::PseudoElement.new(
          interpret_identifier(val[2])
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 458)
  def _reduce_149(val, _values, result)
            result = Selectors::PseudoClass.new(
          interpret_identifier(val[1].sub(/\($/, '')),
          ''
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 464)
  def _reduce_150(val, _values, result)
            result = Selectors::PseudoClass.new(
          interpret_identifier(val[1].sub(/\($/, '')),
          interpret_identifier(val[2])
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 470)
  def _reduce_151(val, _values, result)
            result = Selectors::PseudoClass.new(
          'not',
          val[2].first.to_s
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 476)
  def _reduce_152(val, _values, result)
            result = Selectors::PseudoClass.new(
          interpret_identifier(val[1].sub(/\(.*/, '')),
          interpret_identifier(val[1].sub(/.*\(/, '').sub(/\).*/, ''))
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 482)
  def _reduce_153(val, _values, result)
            result = Selectors::PseudoClass.new(
          val[1].split('(').first.strip,
          val[2].join(', ')
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 488)
  def _reduce_154(val, _values, result)
            result = Selectors::PseudoElement.new(
          interpret_identifier(val[1].sub(/\($/, ''))
        )

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 493)
  def _reduce_155(val, _values, result)
            result = Selectors::PseudoElement.new(
          interpret_identifier(val[2].sub(/\($/, ''))
        )

    result
  end
.,.,

# reduce 156 omitted

# reduce 157 omitted

# reduce 158 omitted

# reduce 159 omitted

# reduce 160 omitted

# reduce 161 omitted

# reduce 162 omitted

# reduce 163 omitted

# reduce 164 omitted

# reduce 165 omitted

# reduce 166 omitted

module_eval(<<'.,.,', 'csspool.y', 519)
  def _reduce_167(val, _values, result)
     @handler.property val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 523)
  def _reduce_168(val, _values, result)
     result = Declaration.new(val.first, val[2], val[3])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 525)
  def _reduce_169(val, _values, result)
     result = Declaration.new(val.first, val[3], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 527)
  def _reduce_170(val, _values, result)
     result = Declaration.new(val.first, val[3], val[4])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 529)
  def _reduce_171(val, _values, result)
     result = Declaration.new(val.first, val[4], val[5])
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 532)
  def _reduce_172(val, _values, result)
     result = true
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 533)
  def _reduce_173(val, _values, result)
     result = false
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 536)
  def _reduce_174(val, _values, result)
     result = interpret_identifier val[0]
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 537)
  def _reduce_175(val, _values, result)
     result = interpret_identifier val.join
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 538)
  def _reduce_176(val, _values, result)
     result = interpret_identifier val[0]
    result
  end
.,.,

# reduce 177 omitted

# reduce 178 omitted

# reduce 179 omitted

module_eval(<<'.,.,', 'csspool.y', 547)
  def _reduce_180(val, _values, result)
            result = [val.first, val.last].flatten
        val.last.first.operator = val[1]

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 550)
  def _reduce_181(val, _values, result)
     result = val.flatten
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 551)
  def _reduce_182(val, _values, result)
     result = val
    result
  end
.,.,

# reduce 183 omitted

# reduce 184 omitted

# reduce 185 omitted

# reduce 186 omitted

# reduce 187 omitted

# reduce 188 omitted

# reduce 189 omitted

# reduce 190 omitted

# reduce 191 omitted

# reduce 192 omitted

# reduce 193 omitted

module_eval(<<'.,.,', 'csspool.y', 567)
  def _reduce_194(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 569)
  def _reduce_195(val, _values, result)
            name = interpret_identifier val.first.sub(/\($/, '')
        if name == 'rgb'
          result = Terms::Rgb.new(*val[1])
        else
          result = Terms::Function.new name, val[1]
        end

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 577)
  def _reduce_196(val, _values, result)
            name = interpret_identifier val.first.sub(/\($/, '')
        result = Terms::Function.new name

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 582)
  def _reduce_197(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 584)
  def _reduce_198(val, _values, result)
            parts = val.first.split('(')
        name = interpret_identifier parts.first
        result = Terms::Function.new(name, [Terms::String.new(interpret_string_no_quote(parts.last))])

    result
  end
.,.,

# reduce 199 omitted

# reduce 200 omitted

module_eval(<<'.,.,', 'csspool.y', 595)
  def _reduce_201(val, _values, result)
           result = Terms::Math.new(val.first.split('(').first, val[1])

    result
  end
.,.,

# reduce 202 omitted

module_eval(<<'.,.,', 'csspool.y', 601)
  def _reduce_203(val, _values, result)
     val.insert(1, ' '); result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 602)
  def _reduce_204(val, _values, result)
     val.insert(1, ' '); result = val.join('')
    result
  end
.,.,

# reduce 205 omitted

module_eval(<<'.,.,', 'csspool.y', 606)
  def _reduce_206(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 607)
  def _reduce_207(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 610)
  def _reduce_208(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 611)
  def _reduce_209(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 612)
  def _reduce_210(val, _values, result)
     result = val.join('')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 615)
  def _reduce_211(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 616)
  def _reduce_212(val, _values, result)
     result = Terms::Hash.new val.first.sub(/^#/, '')
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 619)
  def _reduce_213(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 620)
  def _reduce_214(val, _values, result)
     result = Terms::URI.new interpret_uri val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 623)
  def _reduce_215(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 624)
  def _reduce_216(val, _values, result)
     result = Terms::String.new interpret_string val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 628)
  def _reduce_217(val, _values, result)
            result = val[1]
        val[1].unary_operator = val.first

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 632)
  def _reduce_218(val, _values, result)
            result = Terms::Number.new numeric val.first

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 635)
  def _reduce_219(val, _values, result)
            result = Terms::Number.new numeric(val.first), nil, '%'

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 638)
  def _reduce_220(val, _values, result)
            unit    = val.first.gsub(/[\s\d.]/, '')
        result = Terms::Number.new numeric(val.first), nil, unit

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 642)
  def _reduce_221(val, _values, result)
            unit    = val.first.gsub(/[\s\d.]/, '')
        result = Terms::Number.new numeric(val.first), nil, unit

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 646)
  def _reduce_222(val, _values, result)
            unit    = val.first.gsub(/[\s\d.]/, '')
        result = Terms::Number.new numeric(val.first), nil, unit

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 650)
  def _reduce_223(val, _values, result)
            unit    = val.first.gsub(/[\s\d.]/, '')
        result = Terms::Number.new numeric(val.first), nil, unit

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 656)
  def _reduce_224(val, _values, result)
            result = Terms::Ratio.new(val[0], val[1])

    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 660)
  def _reduce_225(val, _values, result)
     result = :minus
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 661)
  def _reduce_226(val, _values, result)
     result = :plus
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 664)
  def _reduce_227(val, _values, result)
     result = val.first
    result
  end
.,.,

module_eval(<<'.,.,', 'csspool.y', 665)
  def _reduce_228(val, _values, result)
     result = Terms::Ident.new interpret_identifier val.first
    result
  end
.,.,

def _reduce_none(val, _values, result)
  val[0]
end

    end   # class Parser
  end   # module CSS
end   # module CSSPool

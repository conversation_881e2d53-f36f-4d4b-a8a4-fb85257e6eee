= NEWS

=== 1.4.6

* バグの修正

  * bin/racc -g オプションを -t に改名
  * racc/compiler.rb を削除
  * '|' が meta rules によって許可
  * Ruby 1.8.7 互換性を修正
  * Ruby 1.9 互換性を修正

=== 1.4.5 (2005-11-21)
  * [FEATURE CHANGE] --no-extensions オプションを削除
  * [fix] racc パッケージのみで -E を使えるように修正
  * [fix] --no-omit-actions が動作していなかったのを修正
  * setup.rb 3.4.1.

=== 1.4.4 (2003-10-12)
  * Ruby 1.8.0 に対応するリリース。本体に変更はなし
  * -all パッケージに strscan, amstd の同梱するのをやめた
  * setup.rb 3.2.1

=== 1.4.3 (2002-11-14)
  * [fix] ruby 1.8 の警告を消した

=== 1.4.2 (2002-01-29)
  * [new] 新しいオプション --no-extentions

=== 1.4.1 (2001-12-02)
  * amstd 非依存になった (ただし -all パッケージへバンドルは継続)
  * y2racc racc2y を 1.4 対応にした

=== 1.4.0 (2001-11-30)
  * ランタイムを Ruby の CVS に入れたのにあわせてマイナーバージョンアップ
  * RaccParser, RaccScanner → GrammarFileParser, GrammarFileScanner
  * ハズい typo を修正 (grammer → grammar)

=== 1.3.12 (2001-11-22)
  * インストーラのバグを修正 (thanks Tanaka Akira)
  * アクション中の正規表現や % 文字列、グローバル変数の検出を向上させた

=== 1.3.11 (2001-08-28)
  * アクション中の $' $` $/ などを正しくスキャン

=== 1.3.10 (2001-08-12)
  * cparse.c のプロトタイプ違いを直した

=== 1.3.9 (2001-04-07)
  * Ruby 1.4 に(再び)対応した

=== 1.3.8 (2001-03-17)
  * パースエラーの時に記号名も出力するようにした
  * Racc::Parser#token_to_s

=== 1.3.7 (2001-02-04)
  * サンプルを増やした

=== 1.3.6 (2001-01-22)
  * cparse がスタティックリンクされても動くようにした

=== 1.3.5 (2001-01-18)
  * % 文字列のスキャンがバグってた
  * 新しい命令 expect

=== 1.3.4 (2001-01-11)
  * cparse: シンボルのタイプチェックを入れた
  * cparse: depend を消した
  * cparse: rb_iterate 中の GC で落ちるバグを修正

=== 1.3.3 (2000-12-25)
  * ジェネレータに致命的なバグ。1.3.1 から混入 (format.rb)
  * racc --runtime-version

=== 1.3.2 (2000-12-21)
  * -E が失敗するのを直した
  * 再度 strscan を同梱 (y2racc/racc2y に必要)

=== 1.3.1 (2000-12-17)
  * 正規表現の繰り返し指定の上限を動的に決定する (RE_DUP_MAX)
  * パースルーチンが常に Ruby 版になっていた (消し忘れ)

=== 1.3.0 (2000-11-30)
  * スキャナから yield でトークンを渡せるようになった

=== 1.2.6 (2000-11-28)
  * class M::C を許した

=== 1.2.5 (2000-11-20)
  * オプションに大変動。非互換オプションは -h -f -p -i -n -c -A
  * ロングオプションをサポート
  * y2racc, racc2y はデフォルトでアクションを残すようにした

=== 1.2.4 (2000-09-13)
  * インストーラとドキュメントを更新

=== 1.2.3 (2000-08-14)
  * 使われない規則と非終端記号を出力 (強力版)
  * S/R conflict の時 nonassoc で解決するならばエラー

=== 1.2.2 (2000-08-12)
  * 内部の変更

=== 1.2.1 (2000-08-05)
  * yacc との変換コマンド racc2y・y2racc を添付

=== 1.2.0 (2000-08-02)
  * 先読みアルゴリズムを bison のものに変更

=== 1.1.6 (2000-07-25)
  * 新たなキーワード options とその引数 no_result_var

=== 1.1.5 (2000-07-21)
  * [重要] token を convert に変更
  * 「新たな」キーワード token (終端記号の宣言)

=== 1.1.4 (2000-07-13)
  * サンプルがバグってた

=== 1.1.3 (2000-06-30)
  * 空アクションの呼び出しを省略しないようにするオプション -a

=== 1.1.2 (2000-06-29)
  * スキャナで strscan を使わないようにした
  * ScanError -&gt; Racc::ScanError, ParseError -&gt; Racc::ParseError
  * エラーメッセージを強化

=== 1.1.1 (2000-06-15)
  * requireミス (thanks Toshさん)
  * -v をつけるとconflictが報告されなくなっていた

=== 1.1.0 (2000-06-12)
  * 新しい 状態遷移表生成アルゴリズム

=== 1.0.4 (2000-06-04)
  * S/R conflict がおきると .output 出力で落ちるバグ修正 (Tosh さんの報告)
  * 使われない非終端記号・規則を表示

=== 1.0.3 (2000-06-03)
  * filter -&gt; collect!

=== 1.0.2 (2000-05-16)
  * インストーラをアップデート

=== 1.0.1 (2000-05-12)
  * state.rb:  先読みルーチンをちょっとだけ高速化 && 追加デバッグ
  * コードを整理した。著作権表示全体を全部のファイルにつけた。
  * amstd アップデート (1.7.0)

=== 1.0.0 (2000-05-06)
  * バージョン 1.0

=== 0.14.6 (2000-05-05)
  * デバッグ出力を詳細にした

=== 0.14.5 (2000-05-01)
  * インストーラを ruby 1.4.4 系の新しいパスに対応させた

=== 0.14.4 (2000-04-09)
  * パーサの定数を削減(Racc_arg にまとめた)
  * state 生成を微妙に高速化(コアを文字列に変換)

=== 0.14.3 (2000-04-04)
  * cparse の SYM2ID と ID2SYM のチェックを分離 (thanks 小松さん)

=== 0.14.2 (2000-04-03)
  * 一行目の class がパースエラーになっていた (thanks 和田さん)
  * 新しいフラグ racc -V

=== 0.14.1 (2000-03-31)

=== 0.14.0 (2000-03-21)
  * 高速テーブルを実装
  * 一時的にファイル名/行番号の変換をやめた(Rubyのバグのため。)

=== 0.13.1 (2000-03-21)
  * --version --copyright などがうまく働いてなかった (thanks ふなばさん)

=== 0.13.0 (2000-03-20)
  * yyerror/yyerrok/yyaccept を実装

=== 0.12.2 (2000-03-19)
  * -E フラグがバグってた (thanks ふなばさん)

=== 0.12.1 (2000-03-16)
  * デフォルトアクションの決め方をちょっと修正(元に戻しただけ)

=== 0.12.0 (2000-03-15)
  * 完全な LALR を実装したら遅くなったので SLR も併用するようにした。効果絶大。

=== 0.11.3 (2000-03-09)
  * 状態遷移表生成のバグの修正がまだ甘かった。さらに別のバグもあるようだ。

=== 0.11.2 (2000-03-09)
  * cparse が Symbol に対応できてなかった

=== 0.11.1 (2000-03-08)
  * ruby 1.5 の Symbol に対応
  * strscan を最新に

=== 0.11.0 (2000-02-19)
  * 例外のとき、元のファイルの行番号が出るようにした

=== 0.10.9 (2000-01-19)
  * セットアップ方法など細かな変更

=== 0.10.8 (2000-01-03)
  * 忘れてしまったけどたしかインストーラ関係の修正
  * (1/17 repacked) ドキュメントの追加と修正

=== 0.10.7 (2000-01-03)
  * setup.rb compile.rb amstd/inst などのバグ修正

=== 0.10.6 (1999-12-24)
  * racc -e ruby でデフォルトパスを使用
  * 空のアクションの呼びだしは省略するようにした

=== 0.10.5 (1999-12-21)
  * 埋めこみアクションの実装がすさまじくバグってた
  * setup.rb が inst.rb の変化に追従してなかった
  * calc.y calc2.y を 0.10 用に修正

=== 0.10.4 (1999-12-19)
  * エラー回復モードを実装
  * racc -E で単体で動作するパーサを生成
  * Racc は class から module になった

=== 0.10.3 (1999-12-01)
  * 埋めこみアクションをサポート
  * .output の出力内容にバグがあったのを修正

=== 0.10.2 (1999-11-27)
  * ドキュメントの訂正と更新
  * libracc.rb を分割

=== 0.10.1 (1999-11-19)
  * C でランタイムを書きなおした
  * next_token が false を返したらもう読みこまない
  * アクションがトークンによらず決まるときは next_token を呼ばない
  * $end 廃止
  * LALRactionTable

=== 0.10.0 (1999-11-06)
  * next_* を next_token に一本化、peep_token 廃止
  * @__debug__ -&lt; @yydebug など変数名を大幅変更
  * 文法ファイルの構造が class...rule...end に変わった
  * コアのコードを一新、高速化
  * strscan を併合
  * ライブラリを racc/ ディレクトリに移動

=== 0.9.5 (1999-10-03)
  * 0.9.4 の変更がすごくバグってた
  * $end が通らなかったのを修正
  * __show_stack__ の引数が違ってた

=== 0.9.4 (1999-09-??)
  * Parser::Reporter をなくしてメソッドに戻した
  * d.format.rb を再編成

=== 0.9.3 (1999-09-03)
  * racc.rb -> racc

=== 0.9.2 (1999-06-26)
  * strscan使用

=== 0.9.1 (1999-06-08)
  * アクション中の正規表現に対応 ( /= にも注意だ)
  * アクション中の # コメントに対応

=== 0.9.0 (1999-06-03)
  * アクションを { } 形式にした
  * ユーザーコードを '----' を使う形式にした

=== 0.8.11 (?)
  * -g の出力をわかりやすくした

=== 0.8.10 (?)
  * アクションからreturnできるようにした

=== 0.8.9 (1999-03-21)
  * -g + @__debug__をつかったデバッグメッセージ操作
  * エラー発生時のバグを修正
  * TOKEN_TO_S_TABLEを付加するようにした

=== 0.8.8 (1999-03-20)
  * 100倍程度の高速化
  * defaultトークンを加えた
  * デバッグ用ソースを出力するオプション-gをくわえた
  * user_initializeを廃止し、普通にinitializeを使えるようにした
  * parse_initialize/finalize,parseメソッドを廃止
  * next_token,next_value,peep_tokenのデフォルトを廃止
  * %precと同等の機能を加えた

=== 0.8.7 (1999-03-01)
  * 内部構造が大幅に変化
  * マニュアルがHTMLになった

=== 0.8.0 (1999-01-16)
  * 文法がブロック型に変化

=== 0.5.0 (1999-01-07)
  * 演算子優先順位が実装されたようだ
  * スタート規則が実装されたようだ
  * トークン値の置換が実装されたようだ(後に致命的なバグ発見)

=== 0.1.0 (1999-01-01)
  * とにかく動くようになった

= Racc

* http://i.loveruby.net/en/projects/racc/
* http://racc.rubyforge.org/

== DESCRIPTION:

  Racc is a LALR(1) parser generator.
  It is written in Ruby itself, and generates Ruby program.

  NOTE: Ruby 1.8.x comes with Racc runtime module.  You
  can run your parsers generated by racc 1.4.x out of the
  box.


== Requirement

  *  Ruby 1.8.x or later.

== Installation

  gem install:

    $ gem install racc

  setup.rb install:

  Type this in the top directory of the extracted archive:

    $ ruby setup.rb config
    $ ruby setup.rb setup
   ($ su)
    # ruby setup.rb install

  You can install Racc into your favorite directory by giving
  options to setup.rb. e.g.

    $ ruby setup.rb config --prefix=/usr

  For details, try "ruby setup.rb --help".


  If you don't have C Compiler
  ----------------------------

  You can install Racc without C compilers. Type following
  command in config phase.

    $ ruby setup.rb config --without-ext


== Testing Racc

  Racc comes with simple calculator. To compile this, on shell:

      $ racc -o calc calc.y

  This process costs few seconds (or less). Then type:

      $ ruby calc

  ... Does it works?
  For details of Racc, see HTML documents placed under 'doc.en/'
  and sample grammer files under 'sample/'.


== License

  Racc is distributed under the same terms of ruby.
  (see the file COPYING). Note that you do NOT need to follow
  ruby license for your own parser (racc outputs).
  You can distribute those files under any licenses you want.


== Bug Reports

  Any kind of bug reports are welcome.
  If you find a bug of Racc, please email me. Your grammer file,
  debug output genereted by "racc -g", are helpful.


                                                      Minero Aoki
                                              <EMAIL>
                                            http://i.loveruby.net

# -*- coding: binary -*-
# frozen_string_literal: true
# Generated from freedesktop.org.xml
class MimeMagic
  # @private
  # :nodoc:
  EXTENSIONS = {
    '123' => 'application/vnd.lotus-1-2-3',
    '32x' => 'application/x-genesis-32x-rom',
    '3ds' => 'image/x-3ds',
    '3g2' => 'video/3gpp2',
    '3ga' => 'video/3gpp',
    '3gp' => 'video/3gpp',
    '3gp2' => 'video/3gpp2',
    '3gpp' => 'video/3gpp',
    '3gpp2' => 'video/3gpp2',
    '602' => 'application/x-t602',
    '669' => 'audio/x-mod',
    '7z' => 'application/x-7z-compressed',
    'a' => 'application/x-archive',
    'a26' => 'application/x-atari-2600-rom',
    'a78' => 'application/x-atari-7800-rom',
    'aa' => 'audio/x-pn-audibleaudio',
    'aac' => 'audio/aac',
    'aax' => 'audio/x-pn-audibleaudio',
    'abw' => 'application/x-abiword',
    'abw.crashed' => 'application/x-abiword',
    'abw.gz' => 'application/x-abiword',
    'ac3' => 'audio/ac3',
    'ace' => 'application/x-ace',
    'adb' => 'text/x-adasrc',
    'adf' => 'application/x-amiga-disk-format',
    'ads' => 'text/x-adasrc',
    'adts' => 'audio/aac',
    'afm' => 'application/x-font-afm',
    'ag' => 'image/x-applix-graphics',
    'agb' => 'application/x-gba-rom',
    'ai' => 'application/illustrator',
    'aif' => 'audio/x-aiff',
    'aifc' => 'audio/x-aifc',
    'aiff' => 'audio/x-aiff',
    'aiffc' => 'audio/x-aifc',
    'al' => 'application/x-perl',
    'alz' => 'application/x-alz',
    'amr' => 'audio/AMR',
    'amz' => 'audio/x-amzxml',
    'ani' => 'application/x-navi-animation',
    'anx' => 'application/annodex',
    'ape' => 'audio/x-ape',
    'apk' => 'application/vnd.android.package-archive',
    'appimage' => 'application/x-iso9660-appimage',
    'ar' => 'application/x-archive',
    'arj' => 'application/x-arj',
    'arw' => 'image/x-sony-arw',
    'as' => 'application/x-applix-spreadsheet',
    'asc' => 'application/pgp-encrypted',
    'asf' => 'application/vnd.ms-asf',
    'asp' => 'application/x-asp',
    'ass' => 'text/x-ssa',
    'asx' => 'audio/x-ms-asx',
    'atom' => 'application/atom+xml',
    'au' => 'audio/basic',
    'automount' => 'text/x-systemd-unit',
    'avf' => 'video/x-msvideo',
    'avi' => 'video/x-msvideo',
    'aw' => 'application/x-applix-word',
    'awb' => 'audio/AMR-WB',
    'awk' => 'application/x-awk',
    'axa' => 'audio/annodex',
    'axv' => 'video/annodex',
    'bak' => 'application/x-trash',
    'bcpio' => 'application/x-bcpio',
    'bdf' => 'application/x-font-bdf',
    'bdm' => 'video/mp2t',
    'bdmv' => 'video/mp2t',
    'bib' => 'text/x-bibtex',
    'bin' => 'application/x-sega-cd-rom',
    'blend' => 'application/x-blender',
    'blender' => 'application/x-blender',
    'bmp' => 'image/bmp',
    'bsdiff' => 'application/x-bsdiff',
    'bz' => 'application/x-bzip',
    'bz2' => 'application/x-bzip',
    'c' => 'text/x-c++src',
    'c++' => 'text/x-c++src',
    'cab' => 'application/vnd.ms-cab-compressed',
    'cap' => 'application/vnd.tcpdump.pcap',
    'cb7' => 'application/x-cb7',
    'cbl' => 'text/x-cobol',
    'cbr' => 'application/vnd.comicbook-rar',
    'cbt' => 'application/x-cbt',
    'cbz' => 'application/vnd.comicbook+zip',
    'cc' => 'text/x-c++src',
    'ccmx' => 'application/x-ccmx',
    'cdf' => 'application/x-netcdf',
    'cdr' => 'application/vnd.corel-draw',
    'cer' => 'application/pkix-cert',
    'cert' => 'application/x-x509-ca-cert',
    'cgb' => 'application/x-gameboy-color-rom',
    'cgm' => 'image/cgm',
    'chm' => 'application/vnd.ms-htmlhelp',
    'chrt' => 'application/x-kchart',
    'cl' => 'text/x-opencl-src',
    'class' => 'application/x-java',
    'clpi' => 'video/mp2t',
    'cls' => 'text/x-tex',
    'cmake' => 'text/x-cmake',
    'cob' => 'text/x-cobol',
    'coffee' => 'application/vnd.coffeescript',
    'cpi' => 'video/mp2t',
    'cpio' => 'application/x-cpio',
    'cpio.gz' => 'application/x-cpio-compressed',
    'cpp' => 'text/x-c++src',
    'cr2' => 'image/x-canon-cr2',
    'crdownload' => 'application/x-partial-download',
    'crl' => 'application/pkix-crl',
    'crt' => 'application/x-x509-ca-cert',
    'crw' => 'image/x-canon-crw',
    'cs' => 'text/x-csharp',
    'csh' => 'application/x-csh',
    'css' => 'text/css',
    'csv' => 'text/csv',
    'csvs' => 'text/csv-schema',
    'cue' => 'application/x-cue',
    'cur' => 'image/x-win-bitmap',
    'cxx' => 'text/x-c++src',
    'd' => 'text/x-dsrc',
    'dar' => 'application/x-dar',
    'dbf' => 'application/x-dbf',
    'dbk' => 'application/x-docbook+xml',
    'dc' => 'application/x-dc-rom',
    'dcl' => 'text/x-dcl',
    'dcm' => 'application/dicom',
    'dcr' => 'image/x-kodak-dcr',
    'dds' => 'image/x-dds',
    'deb' => 'application/vnd.debian.binary-package',
    'der' => 'application/x-x509-ca-cert',
    'desktop' => 'application/x-desktop',
    'device' => 'text/x-systemd-unit',
    'di' => 'text/x-dsrc',
    'dia' => 'application/x-dia-diagram',
    'dib' => 'image/bmp',
    'diff' => 'text/x-patch',
    'divx' => 'video/x-msvideo',
    'djv' => 'image/vnd.djvu',
    'djvu' => 'image/vnd.djvu',
    'dmg' => 'application/x-apple-diskimage',
    'dmp' => 'application/vnd.tcpdump.pcap',
    'dng' => 'image/x-adobe-dng',
    'doc' => 'application/msword',
    'docbook' => 'application/x-docbook+xml',
    'docm' => 'application/vnd.ms-word.document.macroEnabled.12',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'dot' => 'application/msword-template',
    'dotm' => 'application/vnd.ms-word.template.macroEnabled.12',
    'dotx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    'dsl' => 'text/x-dsl',
    'dtd' => 'application/xml-dtd',
    'dts' => 'audio/vnd.dts',
    'dtshd' => 'audio/vnd.dts.hd',
    'dtx' => 'text/x-tex',
    'dv' => 'video/dv',
    'dvi' => 'application/x-dvi',
    'dvi.bz2' => 'application/x-bzdvi',
    'dvi.gz' => 'application/x-gzdvi',
    'dwg' => 'image/vnd.dwg',
    'dxf' => 'image/vnd.dxf',
    'e' => 'text/x-eiffel',
    'egon' => 'application/x-egon',
    'eif' => 'text/x-eiffel',
    'el' => 'text/x-emacs-lisp',
    'emf' => 'image/emf',
    'eml' => 'message/rfc822',
    'emp' => 'application/vnd.emusic-emusic_package',
    'ent' => 'application/xml-external-parsed-entity',
    'eps' => 'image/x-eps',
    'eps.bz2' => 'image/x-bzeps',
    'eps.gz' => 'image/x-gzeps',
    'epsf' => 'image/x-eps',
    'epsf.bz2' => 'image/x-bzeps',
    'epsf.gz' => 'image/x-gzeps',
    'epsi' => 'image/x-eps',
    'epsi.bz2' => 'image/x-bzeps',
    'epsi.gz' => 'image/x-gzeps',
    'epub' => 'application/epub+zip',
    'erl' => 'text/x-erlang',
    'es' => 'application/ecmascript',
    'etheme' => 'application/x-e-theme',
    'etx' => 'text/x-setext',
    'exe' => 'application/x-ms-dos-executable',
    'exr' => 'image/x-exr',
    'ez' => 'application/andrew-inset',
    'f' => 'text/x-fortran',
    'f4a' => 'audio/mp4',
    'f4b' => 'audio/x-m4b',
    'f4v' => 'video/mp4',
    'f90' => 'text/x-fortran',
    'f95' => 'text/x-fortran',
    'fb2' => 'application/x-fictionbook+xml',
    'fb2.zip' => 'application/x-zip-compressed-fb2',
    'fd' => 'application/x-raw-floppy-disk-image',
    'fds' => 'application/x-fds-disk',
    'feature' => 'text/x-gherkin',
    'fig' => 'image/x-xfig',
    'fits' => 'image/fits',
    'fl' => 'application/x-fluid',
    'flac' => 'audio/flac',
    'flatpak' => 'application/vnd.flatpak',
    'flatpakref' => 'application/vnd.flatpak.ref',
    'flatpakrepo' => 'application/vnd.flatpak.repo',
    'flc' => 'video/x-flic',
    'fli' => 'video/x-flic',
    'flv' => 'video/x-flv',
    'flw' => 'application/x-kivio',
    'fm' => 'application/vnd.framemaker',
    'fo' => 'text/x-xslfo',
    'fodg' => 'application/vnd.oasis.opendocument.graphics-flat-xml',
    'fodp' => 'application/vnd.oasis.opendocument.presentation-flat-xml',
    'fods' => 'application/vnd.oasis.opendocument.spreadsheet-flat-xml',
    'fodt' => 'application/vnd.oasis.opendocument.text-flat-xml',
    'for' => 'text/x-fortran',
    'fxm' => 'video/x-javafx',
    'g3' => 'image/fax-g3',
    'gb' => 'application/x-gameboy-rom',
    'gba' => 'application/x-gba-rom',
    'gbc' => 'application/x-gameboy-color-rom',
    'gbr' => 'image/x-gimp-gbr',
    'gcode' => 'text/x.gcode',
    'gcrd' => 'text/vcard',
    'ged' => 'application/x-gedcom',
    'gedcom' => 'application/x-gedcom',
    'gem' => 'application/x-tar',
    'gen' => 'application/x-genesis-rom',
    'geo.json' => 'application/geo+json',
    'geojson' => 'application/geo+json',
    'gf' => 'application/x-tex-gf',
    'gg' => 'application/x-gamegear-rom',
    'gif' => 'image/gif',
    'gih' => 'image/x-gimp-gih',
    'glade' => 'application/x-glade',
    'gml' => 'application/gml+xml',
    'gmo' => 'application/x-gettext-translation',
    'gnc' => 'application/x-gnucash',
    'gnd' => 'application/gnunet-directory',
    'gnucash' => 'application/x-gnucash',
    'gnumeric' => 'application/x-gnumeric',
    'gnuplot' => 'application/x-gnuplot',
    'go' => 'text/x-go',
    'gp' => 'application/x-gnuplot',
    'gpg' => 'application/pgp-encrypted',
    'gplt' => 'application/x-gnuplot',
    'gpx' => 'application/gpx+xml',
    'gra' => 'application/x-graphite',
    'gs' => 'text/x-genie',
    'gsf' => 'application/x-font-type1',
    'gsm' => 'audio/x-gsm',
    'gtar' => 'application/x-tar',
    'gv' => 'text/vnd.graphviz',
    'gvp' => 'text/x-google-video-pointer',
    'gz' => 'application/gzip',
    'h' => 'text/x-chdr',
    'h++' => 'text/x-c++hdr',
    'h4' => 'application/x-hdf',
    'h5' => 'application/x-hdf',
    'hdf' => 'application/x-hdf',
    'hdf4' => 'application/x-hdf',
    'hdf5' => 'application/x-hdf',
    'heic' => 'image/heif',
    'heif' => 'image/heif',
    'hfe' => 'application/x-hfe-floppy-image',
    'hh' => 'text/x-c++hdr',
    'hlp' => 'application/winhlp',
    'hp' => 'text/x-c++hdr',
    'hpgl' => 'application/vnd.hp-hpgl',
    'hpp' => 'text/x-c++hdr',
    'hs' => 'text/x-haskell',
    'htm' => 'text/html',
    'html' => 'text/html',
    'hwp' => 'application/x-hwp',
    'hwt' => 'application/x-hwt',
    'hxx' => 'text/x-c++hdr',
    'ica' => 'application/x-ica',
    'icb' => 'image/x-tga',
    'icc' => 'application/vnd.iccprofile',
    'icm' => 'application/vnd.iccprofile',
    'icns' => 'image/x-icns',
    'ico' => 'image/vnd.microsoft.icon',
    'ics' => 'text/calendar',
    'idl' => 'text/x-idl',
    'ief' => 'image/ief',
    'iff' => 'image/x-ilbm',
    'iges' => 'model/iges',
    'igs' => 'model/iges',
    'ilbm' => 'image/x-ilbm',
    'ime' => 'text/x-iMelody',
    'img' => 'application/x-raw-disk-image',
    'img.xz' => 'application/x-raw-disk-image-xz-compressed',
    'imy' => 'text/x-iMelody',
    'ins' => 'text/x-tex',
    'iptables' => 'text/x-iptables',
    'ipynb' => 'application/x-ipynb+json',
    'iso' => 'application/x-cd-image',
    'iso9660' => 'application/x-cd-image',
    'it' => 'audio/x-it',
    'it87' => 'application/x-it87',
    'j2c' => 'image/x-jp2-codestream',
    'j2k' => 'image/x-jp2-codestream',
    'jad' => 'text/vnd.sun.j2me.app-descriptor',
    'jar' => 'application/x-java-archive',
    'java' => 'text/x-java',
    'jceks' => 'application/x-java-jce-keystore',
    'jks' => 'application/x-java-keystore',
    'jng' => 'image/x-jng',
    'jnlp' => 'application/x-java-jnlp-file',
    'jp2' => 'image/jp2',
    'jpc' => 'image/x-jp2-codestream',
    'jpe' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'jpf' => 'image/jpx',
    'jpg' => 'image/jpeg',
    'jpg2' => 'image/jp2',
    'jpgm' => 'image/jpm',
    'jpm' => 'image/jpm',
    'jpr' => 'application/x-jbuilder-project',
    'jpx' => 'application/x-jbuilder-project',
    'jrd' => 'application/jrd+json',
    'js' => 'application/javascript',
    'jsm' => 'application/javascript',
    'json' => 'application/json',
    'json-patch' => 'application/json-patch+json',
    'jsonld' => 'application/ld+json',
    'k25' => 'image/x-kodak-k25',
    'k7' => 'application/x-thomson-cassette',
    'kar' => 'audio/midi',
    'karbon' => 'application/x-karbon',
    'kdc' => 'image/x-kodak-kdc',
    'kdelnk' => 'application/x-desktop',
    'kexi' => 'application/x-kexiproject-sqlite2',
    'kexic' => 'application/x-kexi-connectiondata',
    'kexis' => 'application/x-kexiproject-shortcut',
    'key' => 'application/x-iwork-keynote-sffkey',
    'kfo' => 'application/x-kformula',
    'kil' => 'application/x-killustrator',
    'kino' => 'application/smil+xml',
    'kml' => 'application/vnd.google-earth.kml+xml',
    'kmz' => 'application/vnd.google-earth.kmz',
    'kon' => 'application/x-kontour',
    'kpm' => 'application/x-kpovmodeler',
    'kpr' => 'application/x-kpresenter',
    'kpt' => 'application/x-kpresenter',
    'kra' => 'application/x-krita',
    'ks' => 'application/x-java-keystore',
    'ksp' => 'application/x-kspread',
    'ktx' => 'image/ktx',
    'kud' => 'application/x-kugar',
    'kwd' => 'application/x-kword',
    'kwt' => 'application/x-kword',
    'la' => 'application/x-shared-library-la',
    'latex' => 'text/x-tex',
    'lbm' => 'image/x-ilbm',
    'ldif' => 'text/x-ldif',
    'lha' => 'application/x-lha',
    'lhs' => 'text/x-literate-haskell',
    'lhz' => 'application/x-lhz',
    'lnx' => 'application/x-atari-lynx-rom',
    'loas' => 'audio/usac',
    'log' => 'text/x-log',
    'lrv' => 'video/mp4',
    'lrz' => 'application/x-lrzip',
    'ltx' => 'text/x-tex',
    'lua' => 'text/x-lua',
    'lwo' => 'image/x-lwo',
    'lwob' => 'image/x-lwo',
    'lwp' => 'application/vnd.lotus-wordpro',
    'lws' => 'image/x-lws',
    'ly' => 'text/x-lilypond',
    'lyx' => 'application/x-lyx',
    'lz' => 'application/x-lzip',
    'lz4' => 'application/x-lz4',
    'lzh' => 'application/x-lha',
    'lzma' => 'application/x-lzma',
    'lzo' => 'application/x-lzop',
    'm' => 'text/x-objcsrc',
    'm15' => 'audio/x-mod',
    'm1u' => 'video/vnd.mpegurl',
    'm2t' => 'video/mp2t',
    'm2ts' => 'video/mp2t',
    'm3u' => 'audio/x-mpegurl',
    'm3u8' => 'audio/x-mpegurl',
    'm4' => 'application/x-m4',
    'm4a' => 'audio/mp4',
    'm4b' => 'audio/x-m4b',
    'm4r' => 'audio/x-m4r',
    'm4u' => 'video/vnd.mpegurl',
    'm4v' => 'video/mp4',
    'm7' => 'application/x-thomson-cartridge-memo7',
    'mab' => 'application/x-markaby',
    'mak' => 'text/x-makefile',
    'man' => 'application/x-troff-man',
    'manifest' => 'text/cache-manifest',
    'markdown' => 'text/markdown',
    'mbox' => 'application/mbox',
    'md' => 'text/markdown',
    'mdb' => 'application/vnd.ms-access',
    'mdi' => 'image/vnd.ms-modi',
    'mdx' => 'application/x-genesis-32x-rom',
    'me' => 'text/x-troff-me',
    'med' => 'audio/x-mod',
    'meta4' => 'application/metalink4+xml',
    'metalink' => 'application/metalink+xml',
    'mgp' => 'application/x-magicpoint',
    'mht' => 'application/x-mimearchive',
    'mhtml' => 'application/x-mimearchive',
    'mid' => 'audio/midi',
    'midi' => 'audio/midi',
    'mif' => 'application/x-mif',
    'minipsf' => 'audio/x-minipsf',
    'mj2' => 'video/mj2',
    'mjp2' => 'video/mj2',
    'mjpeg' => 'video/x-mjpeg',
    'mjpg' => 'video/x-mjpeg',
    'mjs' => 'application/javascript',
    'mk' => 'text/x-makefile',
    'mk3d' => 'video/x-matroska-3d',
    'mka' => 'audio/x-matroska',
    'mkd' => 'text/markdown',
    'mkv' => 'video/x-matroska',
    'ml' => 'text/x-ocaml',
    'mli' => 'text/x-ocaml',
    'mm' => 'text/x-troff-mm',
    'mmf' => 'application/x-smaf',
    'mml' => 'application/mathml+xml',
    'mng' => 'video/x-mng',
    'mo' => 'application/x-gettext-translation',
    'mo3' => 'audio/x-mo3',
    'mobi' => 'application/x-mobipocket-ebook',
    'moc' => 'text/x-moc',
    'mod' => 'audio/x-mod',
    'mof' => 'text/x-mof',
    'moov' => 'video/quicktime',
    'mount' => 'text/x-systemd-unit',
    'mov' => 'video/quicktime',
    'movie' => 'video/x-sgi-movie',
    'mp+' => 'audio/x-musepack',
    'mp2' => 'audio/mp2',
    'mp3' => 'audio/mpeg',
    'mp4' => 'video/mp4',
    'mpc' => 'audio/x-musepack',
    'mpe' => 'video/mpeg',
    'mpeg' => 'video/mpeg',
    'mpg' => 'video/mpeg',
    'mpga' => 'audio/mpeg',
    'mpl' => 'video/mp2t',
    'mpls' => 'video/mp2t',
    'mpp' => 'audio/x-musepack',
    'mrl' => 'text/x-mrml',
    'mrml' => 'text/x-mrml',
    'mrw' => 'image/x-minolta-mrw',
    'ms' => 'text/x-troff-ms',
    'msi' => 'application/x-msi',
    'msod' => 'image/x-msod',
    'msx' => 'application/x-msx-rom',
    'mtm' => 'audio/x-mod',
    'mts' => 'video/mp2t',
    'mup' => 'text/x-mup',
    'mxf' => 'application/mxf',
    'mxu' => 'video/vnd.mpegurl',
    'n64' => 'application/x-n64-rom',
    'nb' => 'application/mathematica',
    'nc' => 'application/x-netcdf',
    'nds' => 'application/x-nintendo-ds-rom',
    'nef' => 'image/x-nikon-nef',
    'nes' => 'application/x-nes-rom',
    'nez' => 'application/x-nes-rom',
    'nfo' => 'text/x-nfo',
    'ngc' => 'application/x-neo-geo-pocket-color-rom',
    'ngp' => 'application/x-neo-geo-pocket-rom',
    'not' => 'text/x-mup',
    'nsc' => 'application/x-netshow-channel',
    'nsv' => 'video/x-nsv',
    'nzb' => 'application/x-nzb',
    'o' => 'application/x-object',
    'obj' => 'application/x-tgif',
    'ocl' => 'text/x-ocl',
    'oda' => 'application/oda',
    'odb' => 'application/vnd.oasis.opendocument.database',
    'odc' => 'application/vnd.oasis.opendocument.chart',
    'odf' => 'application/vnd.oasis.opendocument.formula',
    'odg' => 'application/vnd.oasis.opendocument.graphics',
    'odi' => 'application/vnd.oasis.opendocument.image',
    'odm' => 'application/vnd.oasis.opendocument.text-master',
    'odp' => 'application/vnd.oasis.opendocument.presentation',
    'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
    'odt' => 'application/vnd.oasis.opendocument.text',
    'oga' => 'audio/ogg',
    'ogg' => 'audio/ogg',
    'ogm' => 'video/x-ogm+ogg',
    'ogv' => 'video/ogg',
    'ogx' => 'application/ogg',
    'old' => 'application/x-trash',
    'oleo' => 'application/x-oleo',
    'ooc' => 'text/x-ooc',
    'opml' => 'text/x-opml+xml',
    'oprc' => 'application/vnd.palm',
    'opus' => 'audio/ogg',
    'ora' => 'image/openraster',
    'orf' => 'image/x-olympus-orf',
    'otc' => 'application/vnd.oasis.opendocument.chart-template',
    'otf' => 'application/vnd.oasis.opendocument.formula-template',
    'otg' => 'application/vnd.oasis.opendocument.graphics-template',
    'oth' => 'application/vnd.oasis.opendocument.text-web',
    'otp' => 'application/vnd.oasis.opendocument.presentation-template',
    'ots' => 'application/vnd.oasis.opendocument.spreadsheet-template',
    'ott' => 'application/vnd.oasis.opendocument.text-template',
    'owl' => 'application/rdf+xml',
    'owx' => 'application/owl+xml',
    'oxps' => 'application/oxps',
    'oxt' => 'application/vnd.openofficeorg.extension',
    'p' => 'text/x-pascal',
    'p10' => 'application/pkcs10',
    'p12' => 'application/pkcs12',
    'p65' => 'application/x-pagemaker',
    'p7b' => 'application/x-pkcs7-certificates',
    'p7c' => 'application/pkcs7-mime',
    'p7m' => 'application/pkcs7-mime',
    'p7s' => 'application/pkcs7-signature',
    'p8' => 'application/pkcs8',
    'p8e' => 'application/pkcs8-encrypted',
    'pack' => 'application/x-java-pack200',
    'pak' => 'application/x-pak',
    'par2' => 'application/x-par2',
    'part' => 'application/x-partial-download',
    'pas' => 'text/x-pascal',
    'pat' => 'image/x-gimp-pat',
    'patch' => 'text/x-patch',
    'path' => 'text/x-systemd-unit',
    'pbm' => 'image/x-portable-bitmap',
    'pcap' => 'application/vnd.tcpdump.pcap',
    'pcd' => 'image/x-photo-cd',
    'pce' => 'application/x-pc-engine-rom',
    'pcf' => 'application/x-font-pcf',
    'pcf.gz' => 'application/x-font-pcf',
    'pcf.z' => 'application/x-font-pcf',
    'pcl' => 'application/vnd.hp-pcl',
    'pct' => 'image/x-pict',
    'pcx' => 'image/vnd.zbrush.pcx',
    'pdb' => 'application/x-aportisdoc',
    'pdc' => 'application/x-aportisdoc',
    'pdf' => 'application/pdf',
    'pdf.bz2' => 'application/x-bzpdf',
    'pdf.gz' => 'application/x-gzpdf',
    'pdf.lz' => 'application/x-lzpdf',
    'pdf.xz' => 'application/x-xzpdf',
    'pef' => 'image/x-pentax-pef',
    'pem' => 'application/x-x509-ca-cert',
    'perl' => 'application/x-perl',
    'pfa' => 'application/x-font-type1',
    'pfb' => 'application/x-font-type1',
    'pfx' => 'application/pkcs12',
    'pgm' => 'image/x-portable-graymap',
    'pgn' => 'application/vnd.chess-pgn',
    'pgp' => 'application/pgp-encrypted',
    'php' => 'application/x-php',
    'php3' => 'application/x-php',
    'php4' => 'application/x-php',
    'php5' => 'application/x-php',
    'phps' => 'application/x-php',
    'pict' => 'image/x-pict',
    'pict1' => 'image/x-pict',
    'pict2' => 'image/x-pict',
    'pk' => 'application/x-tex-pk',
    'pkg' => 'application/x-xar',
    'pkipath' => 'application/pkix-pkipath',
    'pkr' => 'application/pgp-keys',
    'pl' => 'application/x-perl',
    'pla' => 'audio/x-iriver-pla',
    'pln' => 'application/x-planperfect',
    'pls' => 'audio/x-scpls',
    'pm' => 'application/x-perl',
    'pm6' => 'application/x-pagemaker',
    'pmd' => 'application/x-pagemaker',
    'png' => 'image/png',
    'pnm' => 'image/x-portable-anymap',
    'pntg' => 'image/x-macpaint',
    'po' => 'text/x-gettext-translation',
    'pod' => 'application/x-perl',
    'por' => 'application/x-spss-por',
    'pot' => 'application/vnd.ms-powerpoint',
    'potm' => 'application/vnd.ms-powerpoint.template.macroEnabled.12',
    'potx' => 'application/vnd.openxmlformats-officedocument.presentationml.template',
    'ppam' => 'application/vnd.ms-powerpoint.addin.macroEnabled.12',
    'ppm' => 'image/x-portable-pixmap',
    'pps' => 'application/vnd.ms-powerpoint',
    'ppsm' => 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
    'ppsx' => 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptm' => 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'ppz' => 'application/vnd.ms-powerpoint',
    'pqa' => 'application/vnd.palm',
    'prc' => 'application/x-mobipocket-ebook',
    'ps' => 'application/postscript',
    'ps.bz2' => 'application/x-bzpostscript',
    'ps.gz' => 'application/x-gzpostscript',
    'psd' => 'image/vnd.adobe.photoshop',
    'psf' => 'application/x-font-linux-psf',
    'psf.gz' => 'application/x-gz-font-linux-psf',
    'psflib' => 'audio/x-psflib',
    'psid' => 'audio/prs.sid',
    'psw' => 'application/x-pocket-word',
    'pub' => 'application/vnd.ms-publisher',
    'pw' => 'application/x-pw',
    'py' => 'text/x-python3',
    'py3' => 'text/x-python3',
    'py3x' => 'text/x-python3',
    'pyc' => 'application/x-python-bytecode',
    'pyo' => 'application/x-python-bytecode',
    'pyx' => 'text/x-python',
    'qd' => 'application/x-raw-floppy-disk-image',
    'qif' => 'application/x-qw',
    'qml' => 'text/x-qml',
    'qmlproject' => 'text/x-qml',
    'qmltypes' => 'text/x-qml',
    'qp' => 'application/x-qpress',
    'qt' => 'video/quicktime',
    'qti' => 'application/x-qtiplot',
    'qti.gz' => 'application/x-qtiplot',
    'qtif' => 'image/x-quicktime',
    'qtl' => 'application/x-quicktime-media-link',
    'qtvr' => 'video/quicktime',
    'ra' => 'audio/vnd.rn-realaudio',
    'raf' => 'image/x-fuji-raf',
    'ram' => 'application/ram',
    'raml' => 'application/raml+yaml',
    'rar' => 'application/vnd.rar',
    'ras' => 'image/x-cmu-raster',
    'raw' => 'image/x-panasonic-rw',
    'raw-disk-image' => 'application/x-raw-disk-image',
    'raw-disk-image.xz' => 'application/x-raw-disk-image-xz-compressed',
    'rax' => 'audio/vnd.rn-realaudio',
    'rb' => 'application/x-ruby',
    'rdf' => 'application/rdf+xml',
    'rdfs' => 'application/rdf+xml',
    'reg' => 'text/x-ms-regedit',
    'rej' => 'text/x-reject',
    'rgb' => 'image/x-rgb',
    'rle' => 'image/rle',
    'rm' => 'application/vnd.rn-realmedia',
    'rmj' => 'application/vnd.rn-realmedia',
    'rmm' => 'application/vnd.rn-realmedia',
    'rms' => 'application/vnd.rn-realmedia',
    'rmvb' => 'application/vnd.rn-realmedia',
    'rmx' => 'application/vnd.rn-realmedia',
    'rnc' => 'application/relax-ng-compact-syntax',
    'rng' => 'application/xml',
    'roff' => 'text/troff',
    'rp' => 'image/vnd.rn-realpix',
    'rpm' => 'application/x-rpm',
    'rs' => 'text/rust',
    'rss' => 'application/rss+xml',
    'rt' => 'text/vnd.rn-realtext',
    'rtf' => 'application/rtf',
    'rtx' => 'text/richtext',
    'rv' => 'video/vnd.rn-realvideo',
    'rvx' => 'video/vnd.rn-realvideo',
    'rw2' => 'image/x-panasonic-rw2',
    's3m' => 'audio/x-s3m',
    'sam' => 'application/x-amipro',
    'sami' => 'application/x-sami',
    'sap' => 'application/x-thomson-sap-image',
    'sass' => 'text/x-sass',
    'sav' => 'application/x-spss-sav',
    'scala' => 'text/x-scala',
    'scm' => 'text/x-scheme',
    'scope' => 'text/x-systemd-unit',
    'scss' => 'text/x-scss',
    'sda' => 'application/vnd.stardivision.draw',
    'sdc' => 'application/vnd.stardivision.calc',
    'sdd' => 'application/vnd.stardivision.impress',
    'sdp' => 'application/vnd.stardivision.impress',
    'sds' => 'application/vnd.stardivision.chart',
    'sdw' => 'application/vnd.stardivision.writer',
    'service' => 'text/x-dbus-service',
    'sfc' => 'application/vnd.nintendo.snes.rom',
    'sg' => 'application/x-sg1000-rom',
    'sgb' => 'application/x-gameboy-rom',
    'sgf' => 'application/x-go-sgf',
    'sgi' => 'image/x-sgi',
    'sgl' => 'application/vnd.stardivision.writer',
    'sgm' => 'text/sgml',
    'sgml' => 'text/sgml',
    'sh' => 'application/x-shellscript',
    'shape' => 'application/x-dia-shape',
    'shar' => 'application/x-shar',
    'shn' => 'application/x-shorten',
    'siag' => 'application/x-siag',
    'sid' => 'audio/prs.sid',
    'sig' => 'application/pgp-signature',
    'sik' => 'application/x-trash',
    'sis' => 'application/vnd.symbian.install',
    'sisx' => 'x-epoc/x-sisx-app',
    'sit' => 'application/x-stuffit',
    'siv' => 'application/sieve',
    'sk' => 'image/x-skencil',
    'sk1' => 'image/x-skencil',
    'skr' => 'application/pgp-keys',
    'sldm' => 'application/vnd.ms-powerpoint.slide.macroEnabled.12',
    'sldx' => 'application/vnd.openxmlformats-officedocument.presentationml.slide',
    'slice' => 'text/x-systemd-unit',
    'slk' => 'text/spreadsheet',
    'smaf' => 'application/x-smaf',
    'smc' => 'application/vnd.nintendo.snes.rom',
    'smd' => 'application/vnd.stardivision.mail',
    'smf' => 'application/vnd.stardivision.math',
    'smi' => 'application/smil+xml',
    'smil' => 'application/smil+xml',
    'sml' => 'application/smil+xml',
    'sms' => 'application/x-sms-rom',
    'snap' => 'application/vnd.snap',
    'snd' => 'audio/basic',
    'so' => 'application/x-sharedlib',
    'socket' => 'text/x-systemd-unit',
    'spc' => 'application/x-pkcs7-certificates',
    'spd' => 'application/x-font-speedo',
    'spec' => 'text/x-rpm-spec',
    'spl' => 'application/vnd.adobe.flash.movie',
    'spm' => 'application/x-source-rpm',
    'spx' => 'audio/x-speex',
    'sql' => 'application/sql',
    'sqlite2' => 'application/x-sqlite2',
    'sqlite3' => 'application/vnd.sqlite3',
    'sqsh' => 'application/vnd.squashfs',
    'sr2' => 'image/x-sony-sr2',
    'src' => 'application/x-wais-source',
    'src.rpm' => 'application/x-source-rpm',
    'srf' => 'image/x-sony-srf',
    'srt' => 'application/x-subrip',
    'ss' => 'text/x-scheme',
    'ssa' => 'text/x-ssa',
    'stc' => 'application/vnd.sun.xml.calc.template',
    'std' => 'application/vnd.sun.xml.draw.template',
    'sti' => 'application/vnd.sun.xml.impress.template',
    'stl' => 'model/stl',
    'stm' => 'audio/x-stm',
    'stw' => 'application/vnd.sun.xml.writer.template',
    'sty' => 'text/x-tex',
    'sub' => 'text/x-microdvd',
    'sun' => 'image/x-sun-raster',
    'sv' => 'text/x-svsrc',
    'sv4cpio' => 'application/x-sv4cpio',
    'sv4crc' => 'application/x-sv4crc',
    'svg' => 'image/svg+xml',
    'svgz' => 'image/svg+xml-compressed',
    'svh' => 'text/x-svhdr',
    'swap' => 'text/x-systemd-unit',
    'swf' => 'application/vnd.adobe.flash.movie',
    'swm' => 'application/x-ms-wim',
    'sxc' => 'application/vnd.sun.xml.calc',
    'sxd' => 'application/vnd.sun.xml.draw',
    'sxg' => 'application/vnd.sun.xml.writer.global',
    'sxi' => 'application/vnd.sun.xml.impress',
    'sxm' => 'application/vnd.sun.xml.math',
    'sxw' => 'application/vnd.sun.xml.writer',
    'sylk' => 'text/spreadsheet',
    't' => 'application/x-perl',
    't2t' => 'text/x-txt2tags',
    'tar' => 'application/x-tar',
    'tar.bz' => 'application/x-bzip-compressed-tar',
    'tar.bz2' => 'application/x-bzip-compressed-tar',
    'tar.gz' => 'application/x-compressed-tar',
    'tar.lrz' => 'application/x-lrzip-compressed-tar',
    'tar.lz' => 'application/x-lzip-compressed-tar',
    'tar.lz4' => 'application/x-lz4-compressed-tar',
    'tar.lzma' => 'application/x-lzma-compressed-tar',
    'tar.lzo' => 'application/x-tzo',
    'tar.xz' => 'application/x-xz-compressed-tar',
    'tar.z' => 'application/x-tarz',
    'target' => 'text/x-systemd-unit',
    'taz' => 'application/x-tarz',
    'tb2' => 'application/x-bzip-compressed-tar',
    'tbz' => 'application/x-bzip-compressed-tar',
    'tbz2' => 'application/x-bzip-compressed-tar',
    'tcl' => 'text/x-tcl',
    'tex' => 'text/x-tex',
    'texi' => 'text/x-texinfo',
    'texinfo' => 'text/x-texinfo',
    'tga' => 'image/x-tga',
    'tgz' => 'application/x-compressed-tar',
    'theme' => 'application/x-theme',
    'themepack' => 'application/x-windows-themepack',
    'tif' => 'image/tiff',
    'tiff' => 'image/tiff',
    'timer' => 'text/x-systemd-unit',
    'tk' => 'text/x-tcl',
    'tlrz' => 'application/x-lrzip-compressed-tar',
    'tlz' => 'application/x-lzma-compressed-tar',
    'tnef' => 'application/vnd.ms-tnef',
    'tnf' => 'application/vnd.ms-tnef',
    'toc' => 'application/x-cdrdao-toc',
    'torrent' => 'application/x-bittorrent',
    'tpic' => 'image/x-tga',
    'tr' => 'text/troff',
    'trig' => 'application/trig',
    'ts' => 'text/vnd.qt.linguist',
    'tsv' => 'text/tab-separated-values',
    'tta' => 'audio/x-tta',
    'ttc' => 'font/collection',
    'ttf' => 'font/ttf',
    'ttl' => 'text/turtle',
    'ttx' => 'application/x-font-ttx',
    'twig' => 'text/x-twig',
    'txt' => 'text/plain',
    'txz' => 'application/x-xz-compressed-tar',
    'tzo' => 'application/x-tzo',
    'udeb' => 'application/vnd.debian.binary-package',
    'ufraw' => 'application/x-ufraw',
    'ui' => 'application/x-designer',
    'uil' => 'text/x-uil',
    'ult' => 'audio/x-mod',
    'unf' => 'application/x-nes-rom',
    'uni' => 'audio/x-mod',
    'unif' => 'application/x-nes-rom',
    'url' => 'application/x-mswinurl',
    'ustar' => 'application/x-ustar',
    'uue' => 'text/x-uuencode',
    'v' => 'text/x-verilog',
    'v64' => 'application/x-n64-rom',
    'vala' => 'text/x-vala',
    'vapi' => 'text/x-vala',
    'vb' => 'application/x-virtual-boy-rom',
    'vcard' => 'text/vcard',
    'vcf' => 'text/vcard',
    'vcs' => 'text/calendar',
    'vct' => 'text/vcard',
    'vda' => 'image/x-tga',
    'vhd' => 'text/x-vhdl',
    'vhdl' => 'text/x-vhdl',
    'viv' => 'video/vnd.vivo',
    'vivo' => 'video/vnd.vivo',
    'vlc' => 'audio/x-mpegurl',
    'vob' => 'video/mpeg',
    'voc' => 'audio/x-voc',
    'vor' => 'application/vnd.stardivision.writer',
    'vrm' => 'model/vrml',
    'vrml' => 'model/vrml',
    'vsd' => 'application/vnd.visio',
    'vsdm' => 'application/vnd.ms-visio.drawing.macroEnabled.main+xml',
    'vsdx' => 'application/vnd.ms-visio.drawing.main+xml',
    'vss' => 'application/vnd.visio',
    'vssm' => 'application/vnd.ms-visio.stencil.macroEnabled.main+xml',
    'vssx' => 'application/vnd.ms-visio.stencil.main+xml',
    'vst' => 'application/vnd.visio',
    'vstm' => 'application/vnd.ms-visio.template.macroEnabled.main+xml',
    'vstx' => 'application/vnd.ms-visio.template.main+xml',
    'vsw' => 'application/vnd.visio',
    'vtt' => 'text/vtt',
    'wad' => 'application/x-wii-wad',
    'wav' => 'audio/x-wav',
    'wax' => 'audio/x-ms-asx',
    'wb1' => 'application/x-quattropro',
    'wb2' => 'application/x-quattropro',
    'wb3' => 'application/x-quattropro',
    'wbmp' => 'image/vnd.wap.wbmp',
    'wcm' => 'application/vnd.ms-works',
    'wdb' => 'application/vnd.ms-works',
    'webm' => 'video/webm',
    'webp' => 'image/webp',
    'wim' => 'application/x-ms-wim',
    'wk1' => 'application/vnd.lotus-1-2-3',
    'wk3' => 'application/vnd.lotus-1-2-3',
    'wk4' => 'application/vnd.lotus-1-2-3',
    'wkdownload' => 'application/x-partial-download',
    'wks' => 'application/vnd.lotus-1-2-3',
    'wma' => 'audio/x-ms-wma',
    'wmf' => 'image/wmf',
    'wml' => 'text/vnd.wap.wml',
    'wmls' => 'text/vnd.wap.wmlscript',
    'wmv' => 'video/x-ms-wmv',
    'wmx' => 'audio/x-ms-asx',
    'woff' => 'font/woff',
    'woff2' => 'font/woff',
    'wp' => 'application/vnd.wordperfect',
    'wp4' => 'application/vnd.wordperfect',
    'wp5' => 'application/vnd.wordperfect',
    'wp6' => 'application/vnd.wordperfect',
    'wpd' => 'application/vnd.wordperfect',
    'wpg' => 'application/x-wpg',
    'wpl' => 'application/vnd.ms-wpl',
    'wpp' => 'application/vnd.wordperfect',
    'wps' => 'application/vnd.ms-works',
    'wri' => 'application/x-mswrite',
    'wrl' => 'model/vrml',
    'ws' => 'application/x-wonderswan-rom',
    'wsc' => 'application/x-wonderswan-color-rom',
    'wsgi' => 'text/x-python',
    'wv' => 'audio/x-wavpack',
    'wvc' => 'audio/x-wavpack-correction',
    'wvp' => 'audio/x-wavpack',
    'wvx' => 'audio/x-ms-asx',
    'wwf' => 'application/x-wwf',
    'x3f' => 'image/x-sigma-x3f',
    'xac' => 'application/x-gnucash',
    'xar' => 'application/x-xar',
    'xbel' => 'application/x-xbel',
    'xbl' => 'application/xml',
    'xbm' => 'image/x-xbitmap',
    'xcf' => 'image/x-xcf',
    'xcf.bz2' => 'image/x-compressed-xcf',
    'xcf.gz' => 'image/x-compressed-xcf',
    'xdgapp' => 'application/vnd.flatpak',
    'xhe' => 'audio/usac',
    'xht' => 'application/xhtml+xml',
    'xhtml' => 'application/xhtml+xml',
    'xi' => 'audio/x-xi',
    'xla' => 'application/vnd.ms-excel',
    'xlam' => 'application/vnd.ms-excel.addin.macroEnabled.12',
    'xlc' => 'application/vnd.ms-excel',
    'xld' => 'application/vnd.ms-excel',
    'xlf' => 'application/xliff+xml',
    'xliff' => 'application/xliff+xml',
    'xll' => 'application/vnd.ms-excel',
    'xlm' => 'application/vnd.ms-excel',
    'xlr' => 'application/vnd.ms-works',
    'xls' => 'application/vnd.ms-excel',
    'xlsb' => 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
    'xlsm' => 'application/vnd.ms-excel.sheet.macroEnabled.12',
    'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xlt' => 'application/vnd.ms-excel',
    'xltm' => 'application/vnd.ms-excel.template.macroEnabled.12',
    'xltx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    'xlw' => 'application/vnd.ms-excel',
    'xm' => 'audio/x-xm',
    'xmf' => 'audio/x-xmf',
    'xmi' => 'text/x-xmi',
    'xml' => 'application/xml',
    'xpi' => 'application/x-xpinstall',
    'xpm' => 'image/x-xpixmap',
    'xps' => 'application/oxps',
    'xsd' => 'application/xml',
    'xsl' => 'application/xslt+xml',
    'xslfo' => 'text/x-xslfo',
    'xslt' => 'application/xslt+xml',
    'xspf' => 'application/xspf+xml',
    'xul' => 'application/vnd.mozilla.xul+xml',
    'xwd' => 'image/x-xwindowdump',
    'xz' => 'application/x-xz',
    'yaml' => 'application/x-yaml',
    'yml' => 'application/x-yaml',
    'yt' => 'application/vnd.youtube.yt',
    'z' => 'application/x-compress',
    'z64' => 'application/x-n64-rom',
    'zabw' => 'application/x-abiword',
    'zip' => 'application/zip',
    'zoo' => 'application/x-zoo',
    'zsav' => 'application/x-spss-sav',
    'zz' => 'application/zlib',
  }
  # @private
  # :nodoc:
  TYPES = {
    'application/andrew-inset' => [%w(ez), %w(), 'ATK inset'],
    'application/annodex' => [%w(anx), %w(), 'Annodex exchange format'],
    'application/atom+xml' => [%w(atom), %w(application/xml), 'Atom syndication feed'],
    'application/dicom' => [%w(dcm), %w(), 'DICOM image'],
    'application/ecmascript' => [%w(es), %w(application/x-executable text/plain), 'ECMAScript program'],
    'application/epub+zip' => [%w(epub), %w(application/zip), 'electronic book document'],
    'application/geo+json' => [%w(geojson geo.json), %w(application/json), 'GeoJSON geospatial data'],
    'application/gml+xml' => [%w(gml), %w(application/xml), 'GML document'],
    'application/gnunet-directory' => [%w(gnd), %w(), 'GNUnet search file'],
    'application/gpx+xml' => [%w(gpx), %w(application/xml), 'GPX geographic data'],
    'application/gzip' => [%w(gz), %w(), 'Gzip archive'],
    'application/illustrator' => [%w(ai), %w(), 'Adobe Illustrator document'],
    'application/javascript' => [%w(js jsm mjs), %w(application/ecmascript), 'JavaScript program'],
    'application/jrd+json' => [%w(jrd), %w(application/json), 'JRD document'],
    'application/json' => [%w(json), %w(application/javascript), 'JSON document'],
    'application/json-patch+json' => [%w(json-patch), %w(application/json), 'JSON patch'],
    'application/ld+json' => [%w(jsonld), %w(application/json), 'JSON-LD document'],
    'application/mathematica' => [%w(nb), %w(text/plain), 'Mathematica Notebook'],
    'application/mathml+xml' => [%w(mml), %w(application/xml), 'MathML document'],
    'application/mbox' => [%w(mbox), %w(text/plain), 'mailbox file'],
    'application/metalink+xml' => [%w(metalink), %w(application/xml), 'Metalink file'],
    'application/metalink4+xml' => [%w(meta4), %w(application/xml), 'Metalink file'],
    'application/msword' => [%w(doc), %w(application/x-ole-storage), 'Word document'],
    'application/msword-template' => [%w(dot), %w(application/msword), 'Word template'],
    'application/mxf' => [%w(mxf), %w(), 'MXF video'],
    'application/oda' => [%w(oda), %w(), 'ODA document'],
    'application/ogg' => [%w(ogx), %w(), 'Ogg multimedia file'],
    'application/owl+xml' => [%w(owx), %w(application/xml), 'OWL XML file'],
    'application/oxps' => [%w(oxps xps), %w(application/zip), 'XPS document'],
    'application/pdf' => [%w(pdf), %w(), 'PDF document'],
    'application/pgp-encrypted' => [%w(pgp gpg asc), %w(text/plain), 'PGP/MIME-encrypted message header'],
    'application/pgp-keys' => [%w(skr pkr asc pgp gpg), %w(text/plain), 'PGP keys'],
    'application/pgp-signature' => [%w(asc sig pgp gpg), %w(text/plain), 'detached OpenPGP signature'],
    'application/pkcs10' => [%w(p10), %w(), 'PKCS#10 certification request'],
    'application/pkcs12' => [%w(p12 pfx), %w(), 'PKCS#12 certificate bundle'],
    'application/pkcs7-mime' => [%w(p7c p7m), %w(), 'PKCS#7 Message or Certificate'],
    'application/pkcs7-signature' => [%w(p7s), %w(text/plain), 'detached S/MIME signature'],
    'application/pkcs8' => [%w(p8), %w(), 'PKCS#8 private key'],
    'application/pkcs8-encrypted' => [%w(p8e), %w(), 'PKCS#8 private key (encrypted)'],
    'application/pkix-cert' => [%w(cer), %w(), 'X.509 certificate'],
    'application/pkix-crl' => [%w(crl), %w(), 'Certificate revocation list'],
    'application/pkix-pkipath' => [%w(pkipath), %w(), 'PkiPath certification path'],
    'application/postscript' => [%w(ps), %w(text/plain), 'PS document'],
    'application/ram' => [%w(ram), %w(), 'RealMedia Metafile'],
    'application/raml+yaml' => [%w(raml), %w(application/x-yaml), 'RAML document'],
    'application/rdf+xml' => [%w(rdf rdfs owl), %w(application/xml), 'RDF file'],
    'application/relax-ng-compact-syntax' => [%w(rnc), %w(text/plain), 'RELAX NG XML schema'],
    'application/rss+xml' => [%w(rss), %w(application/xml), 'RSS summary'],
    'application/rtf' => [%w(rtf), %w(text/plain), 'RTF document'],
    'application/sdp' => [%w(sdp), %w(text/plain), 'SDP multicast stream file'],
    'application/sieve' => [%w(siv), %w(application/xml), 'Sieve mail filter script'],
    'application/smil+xml' => [%w(smil smi sml kino), %w(application/xml), 'SMIL document'],
    'application/sql' => [%w(sql), %w(text/plain), 'SQL code'],
    'application/trig' => [%w(trig), %w(text/plain), 'TriG RDF document'],
    'application/vnd.adobe.flash.movie' => [%w(swf spl), %w(), 'Shockwave Flash file'],
    'application/vnd.android.package-archive' => [%w(apk), %w(application/x-java-archive), 'Android package'],
    'application/vnd.appimage' => [%w(appimage), %w(application/vnd.squashfs application/x-executable), 'AppImage application bundle'],
    'application/vnd.apple.mpegurl' => [%w(m3u m3u8), %w(text/plain), 'HTTP Live Streaming playlist'],
    'application/vnd.chess-pgn' => [%w(pgn), %w(text/plain), 'PGN chess game notation'],
    'application/vnd.coffeescript' => [%w(coffee), %w(text/plain), 'CoffeeScript document'],
    'application/vnd.comicbook+zip' => [%w(cbz), %w(application/zip), 'comic book archive'],
    'application/vnd.comicbook-rar' => [%w(cbr), %w(application/vnd.rar), 'comic book archive'],
    'application/vnd.corel-draw' => [%w(cdr), %w(), 'Corel Draw drawing'],
    'application/vnd.debian.binary-package' => [%w(deb udeb), %w(), 'Debian package'],
    'application/vnd.emusic-emusic_package' => [%w(emp), %w(), 'eMusic download package'],
    'application/vnd.flatpak' => [%w(flatpak xdgapp), %w(), 'Flatpak application bundle'],
    'application/vnd.flatpak.ref' => [%w(flatpakref), %w(text/plain), 'Flatpak repository reference'],
    'application/vnd.flatpak.repo' => [%w(flatpakrepo), %w(text/plain), 'Flatpak repository description'],
    'application/vnd.framemaker' => [%w(fm), %w(), 'Adobe FrameMaker document'],
    'application/vnd.google-earth.kml+xml' => [%w(kml), %w(application/xml), 'KML geographic data'],
    'application/vnd.google-earth.kmz' => [%w(kmz), %w(application/zip), 'KML geographic compressed data'],
    'application/vnd.hp-hpgl' => [%w(hpgl), %w(), 'HPGL file'],
    'application/vnd.hp-pcl' => [%w(pcl), %w(), 'PCL file'],
    'application/vnd.iccprofile' => [%w(icc icm), %w(), 'ICC profile'],
    'application/vnd.lotus-1-2-3' => [%w(123 wk1 wk3 wk4 wks), %w(), 'Lotus 1-2-3 spreadsheet'],
    'application/vnd.lotus-wordpro' => [%w(lwp), %w(), 'Lotus Word Pro'],
    'application/vnd.mozilla.xul+xml' => [%w(xul), %w(application/xml), 'XUL interface document'],
    'application/vnd.ms-access' => [%w(mdb), %w(), 'JET database'],
    'application/vnd.ms-asf' => [%w(asf), %w(), 'ASF video'],
    'application/vnd.ms-cab-compressed' => [%w(cab), %w(), 'Microsoft Cabinet archive'],
    'application/vnd.ms-excel' => [%w(xls xlc xll xlm xlw xla xlt xld), %w(), 'Excel spreadsheet'],
    'application/vnd.ms-excel.addin.macroEnabled.12' => [%w(xlam), %w(application/vnd.openxmlformats-officedocument.spreadsheetml.sheet), 'Excel add-in'],
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12' => [%w(xlsb), %w(application/vnd.openxmlformats-officedocument.spreadsheetml.sheet), 'Excel 2007 binary spreadsheet'],
    'application/vnd.ms-excel.sheet.macroEnabled.12' => [%w(xlsm), %w(application/vnd.openxmlformats-officedocument.spreadsheetml.sheet), 'Excel spreadsheet'],
    'application/vnd.ms-excel.template.macroEnabled.12' => [%w(xltm), %w(application/vnd.openxmlformats-officedocument.spreadsheetml.template), 'Excel spreadsheet template'],
    'application/vnd.ms-htmlhelp' => [%w(chm), %w(), 'CHM document'],
    'application/vnd.ms-powerpoint' => [%w(ppz ppt pps pot), %w(), 'PowerPoint presentation'],
    'application/vnd.ms-powerpoint.addin.macroEnabled.12' => [%w(ppam), %w(), 'PowerPoint add-in'],
    'application/vnd.ms-powerpoint.presentation.macroEnabled.12' => [%w(pptm), %w(application/vnd.openxmlformats-officedocument.presentationml.presentation), 'PowerPoint presentation'],
    'application/vnd.ms-powerpoint.slide.macroEnabled.12' => [%w(sldm), %w(application/vnd.openxmlformats-officedocument.presentationml.slide), 'PowerPoint slide'],
    'application/vnd.ms-powerpoint.slideshow.macroEnabled.12' => [%w(ppsm), %w(application/vnd.openxmlformats-officedocument.presentationml.slideshow), 'PowerPoint presentation'],
    'application/vnd.ms-powerpoint.template.macroEnabled.12' => [%w(potm), %w(application/vnd.openxmlformats-officedocument.presentationml.template), 'PowerPoint presentation template'],
    'application/vnd.ms-publisher' => [%w(pub), %w(application/x-ole-storage), 'Microsoft Publisher document'],
    'application/vnd.ms-tnef' => [%w(tnef tnf), %w(), 'TNEF message'],
    'application/vnd.ms-visio.drawing.macroEnabled.main+xml' => [%w(vsdm), %w(application/zip), 'Office Open XML Visio Drawing'],
    'application/vnd.ms-visio.drawing.main+xml' => [%w(vsdx), %w(application/zip), 'Office Open XML Visio Drawing'],
    'application/vnd.ms-visio.stencil.macroEnabled.main+xml' => [%w(vssm), %w(application/zip), 'Office Open XML Visio Stencil'],
    'application/vnd.ms-visio.stencil.main+xml' => [%w(vssx), %w(application/zip), 'Office Open XML Visio Stencil'],
    'application/vnd.ms-visio.template.macroEnabled.main+xml' => [%w(vstm), %w(application/zip), 'Office Open XML Visio Template'],
    'application/vnd.ms-visio.template.main+xml' => [%w(vstx), %w(application/zip), 'Office Open XML Visio Template'],
    'application/vnd.ms-word.document.macroEnabled.12' => [%w(docm), %w(application/vnd.openxmlformats-officedocument.wordprocessingml.document), 'Word document'],
    'application/vnd.ms-word.template.macroEnabled.12' => [%w(dotm), %w(application/vnd.openxmlformats-officedocument.wordprocessingml.template), 'Word document template'],
    'application/vnd.ms-works' => [%w(wcm wdb wks wps xlr), %w(application/x-ole-storage), 'Microsoft Works document'],
    'application/vnd.ms-wpl' => [%w(wpl), %w(), 'WPL playlist'],
    'application/vnd.nintendo.snes.rom' => [%w(sfc smc), %w(), 'Super NES ROM'],
    'application/vnd.oasis.opendocument.chart' => [%w(odc), %w(application/zip), 'ODC chart'],
    'application/vnd.oasis.opendocument.chart-template' => [%w(otc), %w(application/zip), 'ODC template'],
    'application/vnd.oasis.opendocument.database' => [%w(odb), %w(application/zip), 'ODB database'],
    'application/vnd.oasis.opendocument.formula' => [%w(odf), %w(application/zip), 'ODF formula'],
    'application/vnd.oasis.opendocument.formula-template' => [%w(otf), %w(application/zip), 'ODF template'],
    'application/vnd.oasis.opendocument.graphics' => [%w(odg), %w(application/zip), 'ODG drawing'],
    'application/vnd.oasis.opendocument.graphics-flat-xml' => [%w(fodg), %w(application/xml), 'ODG drawing (Flat XML)'],
    'application/vnd.oasis.opendocument.graphics-template' => [%w(otg), %w(application/zip), 'ODG template'],
    'application/vnd.oasis.opendocument.image' => [%w(odi), %w(application/zip), 'ODI image'],
    'application/vnd.oasis.opendocument.presentation' => [%w(odp), %w(application/zip), 'ODP presentation'],
    'application/vnd.oasis.opendocument.presentation-flat-xml' => [%w(fodp), %w(application/xml), 'ODP presentation (Flat XML)'],
    'application/vnd.oasis.opendocument.presentation-template' => [%w(otp), %w(application/zip), 'ODP template'],
    'application/vnd.oasis.opendocument.spreadsheet' => [%w(ods), %w(application/zip), 'ODS spreadsheet'],
    'application/vnd.oasis.opendocument.spreadsheet-flat-xml' => [%w(fods), %w(application/xml), 'ODS spreadsheet (Flat XML)'],
    'application/vnd.oasis.opendocument.spreadsheet-template' => [%w(ots), %w(application/zip), 'ODS template'],
    'application/vnd.oasis.opendocument.text' => [%w(odt), %w(application/zip), 'ODT document'],
    'application/vnd.oasis.opendocument.text-flat-xml' => [%w(fodt), %w(application/xml), 'ODT document (Flat XML)'],
    'application/vnd.oasis.opendocument.text-master' => [%w(odm), %w(application/zip), 'ODM document'],
    'application/vnd.oasis.opendocument.text-template' => [%w(ott), %w(application/zip), 'ODT template'],
    'application/vnd.oasis.opendocument.text-web' => [%w(oth), %w(application/zip), 'OTH template'],
    'application/vnd.openofficeorg.extension' => [%w(oxt), %w(application/zip), 'OpenOffice.org extension'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation' => [%w(pptx), %w(application/zip), 'PowerPoint 2007 presentation'],
    'application/vnd.openxmlformats-officedocument.presentationml.slide' => [%w(sldx), %w(application/zip), 'PowerPoint 2007 slide'],
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow' => [%w(ppsx), %w(application/zip), 'PowerPoint 2007 show'],
    'application/vnd.openxmlformats-officedocument.presentationml.template' => [%w(potx), %w(application/zip), 'PowerPoint 2007 presentation template'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => [%w(xlsx), %w(application/zip), 'Excel 2007 spreadsheet'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template' => [%w(xltx), %w(application/zip), 'Excel 2007 spreadsheet template'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => [%w(docx), %w(application/zip), 'Word 2007 document'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template' => [%w(dotx), %w(application/zip), 'Word 2007 document template'],
    'application/vnd.palm' => [%w(prc pdb pqa oprc), %w(), 'Palm OS database'],
    'application/vnd.rar' => [%w(rar), %w(), 'RAR archive'],
    'application/vnd.rn-realmedia' => [%w(rm rmj rmm rms rmx rmvb), %w(), 'RealMedia document'],
    'application/vnd.snap' => [%w(snap), %w(application/vnd.squashfs), 'Snap package'],
    'application/vnd.sqlite3' => [%w(sqlite3), %w(), 'SQLite3 database'],
    'application/vnd.squashfs' => [%w(sqsh), %w(), 'Squashfs filesystem'],
    'application/vnd.stardivision.calc' => [%w(sdc), %w(), 'StarCalc spreadsheet'],
    'application/vnd.stardivision.chart' => [%w(sds), %w(), 'StarChart chart'],
    'application/vnd.stardivision.draw' => [%w(sda), %w(), 'StarDraw drawing'],
    'application/vnd.stardivision.impress' => [%w(sdd sdp), %w(), 'StarImpress presentation'],
    'application/vnd.stardivision.mail' => [%w(smd), %w(), 'StarMail email'],
    'application/vnd.stardivision.math' => [%w(smf), %w(), 'StarMath formula'],
    'application/vnd.stardivision.writer' => [%w(sdw vor sgl), %w(), 'StarWriter document'],
    'application/vnd.sun.xml.calc' => [%w(sxc), %w(application/zip), 'OpenOffice Calc spreadsheet'],
    'application/vnd.sun.xml.calc.template' => [%w(stc), %w(application/zip), 'OpenOffice Calc template'],
    'application/vnd.sun.xml.draw' => [%w(sxd), %w(application/zip), 'OpenOffice Draw drawing'],
    'application/vnd.sun.xml.draw.template' => [%w(std), %w(application/zip), 'OpenOffice Draw template'],
    'application/vnd.sun.xml.impress' => [%w(sxi), %w(application/zip), 'OpenOffice Impress presentation'],
    'application/vnd.sun.xml.impress.template' => [%w(sti), %w(application/zip), 'OpenOffice Impress template'],
    'application/vnd.sun.xml.math' => [%w(sxm), %w(application/zip), 'OpenOffice Math formula'],
    'application/vnd.sun.xml.writer' => [%w(sxw), %w(application/zip), 'OpenOffice Writer document'],
    'application/vnd.sun.xml.writer.global' => [%w(sxg), %w(application/zip), 'OpenOffice Writer global document'],
    'application/vnd.sun.xml.writer.template' => [%w(stw), %w(application/zip), 'OpenOffice Writer template'],
    'application/vnd.symbian.install' => [%w(sis), %w(), 'SIS package'],
    'application/vnd.tcpdump.pcap' => [%w(pcap cap dmp), %w(), 'Network Packet Capture'],
    'application/vnd.visio' => [%w(vsd vst vsw vss), %w(application/x-ole-storage), 'Microsoft Visio document'],
    'application/vnd.wordperfect' => [%w(wp wp4 wp5 wp6 wpd wpp), %w(), 'WordPerfect document'],
    'application/vnd.youtube.yt' => [%w(yt), %w(application/zip), 'YouTube Media Archive'],
    'application/winhlp' => [%w(hlp), %w(), 'WinHelp help file'],
    'application/x-7z-compressed' => [%w(7z), %w(), '7-zip archive'],
    'application/x-abiword' => [%w(abw abw.crashed abw.gz zabw), %w(application/xml), 'AbiWord document'],
    'application/x-ace' => [%w(ace), %w(), 'ACE archive'],
    'application/x-alz' => [%w(alz), %w(), 'Alzip archive'],
    'application/x-amiga-disk-format' => [%w(adf), %w(), 'Amiga disk image'],
    'application/x-amipro' => [%w(sam), %w(), 'Lotus AmiPro document'],
    'application/x-aportisdoc' => [%w(pdb pdc), %w(application/vnd.palm), 'AportisDoc document'],
    'application/x-apple-diskimage' => [%w(dmg), %w(), 'Apple disk image'],
    'application/x-applix-spreadsheet' => [%w(as), %w(), 'Applix Spreadsheets spreadsheet'],
    'application/x-applix-word' => [%w(aw), %w(), 'Applix Words document'],
    'application/x-archive' => [%w(a ar), %w(), 'AR archive'],
    'application/x-arj' => [%w(arj), %w(), 'ARJ archive'],
    'application/x-asp' => [%w(asp), %w(text/plain), 'ASP page'],
    'application/x-atari-2600-rom' => [%w(a26), %w(), 'Atari 2600'],
    'application/x-atari-7800-rom' => [%w(a78), %w(), 'Atari 7800'],
    'application/x-atari-lynx-rom' => [%w(lnx), %w(), 'Atari Lynx'],
    'application/x-awk' => [%w(awk), %w(application/x-executable text/plain), 'AWK script'],
    'application/x-bcpio' => [%w(bcpio), %w(), 'BCPIO document'],
    'application/x-bittorrent' => [%w(torrent), %w(), 'BitTorrent seed file'],
    'application/x-blender' => [%w(blender blend blend), %w(), 'Blender scene'],
    'application/x-bsdiff' => [%w(bsdiff), %w(), 'binary differences between files'],
    'application/x-bzdvi' => [%w(dvi.bz2), %w(application/x-bzip), 'TeX DVI document (bzip-compressed)'],
    'application/x-bzip' => [%w(bz2 bz), %w(), 'Bzip archive'],
    'application/x-bzip-compressed-tar' => [%w(tar.bz2 tar.bz tbz2 tbz tb2), %w(application/x-bzip), 'Tar archive (bzip-compressed)'],
    'application/x-bzpdf' => [%w(pdf.bz2), %w(application/x-bzip), 'PDF document (bzip-compressed)'],
    'application/x-bzpostscript' => [%w(ps.bz2), %w(application/x-bzip), 'PostScript document (bzip-compressed)'],
    'application/x-cb7' => [%w(cb7), %w(application/x-7z-compressed), 'comic book archive'],
    'application/x-cbt' => [%w(cbt), %w(application/x-tar), 'comic book archive'],
    'application/x-ccmx' => [%w(ccmx), %w(text/plain), 'CCMX color correction file'],
    'application/x-cd-image' => [%w(iso iso9660), %w(application/x-raw-disk-image), 'raw CD image'],
    'application/x-cdrdao-toc' => [%w(toc), %w(text/plain), 'CD Table Of Contents'],
    'application/x-cisco-vpn-settings' => [%w(pcf), %w(), 'Cisco VPN Settings'],
    'application/x-compress' => [%w(z), %w(), 'UNIX-compressed file'],
    'application/x-compressed-tar' => [%w(tar.gz tgz), %w(application/gzip), 'Tar archive (gzip-compressed)'],
    'application/x-cpio' => [%w(cpio), %w(), 'CPIO archive'],
    'application/x-cpio-compressed' => [%w(cpio.gz), %w(application/gzip), 'CPIO archive (gzip-compressed)'],
    'application/x-csh' => [%w(csh), %w(application/x-shellscript text/plain), 'C shell script'],
    'application/x-cue' => [%w(cue), %w(text/plain), 'CD image cuesheet'],
    'application/x-dar' => [%w(dar), %w(), 'DAR archive'],
    'application/x-dbf' => [%w(dbf), %w(), 'Xbase document'],
    'application/x-dc-rom' => [%w(dc), %w(), 'Dreamcast GD-ROM'],
    'application/x-designer' => [%w(ui), %w(application/xml), 'Qt Designer file'],
    'application/x-desktop' => [%w(desktop kdelnk), %w(text/plain), 'desktop configuration file'],
    'application/x-dia-diagram' => [%w(dia), %w(application/xml), 'Dia diagram'],
    'application/x-dia-shape' => [%w(shape), %w(application/xml), 'Dia shape'],
    'application/x-docbook+xml' => [%w(dbk docbook), %w(application/xml), 'DocBook document'],
    'application/x-doom-wad' => [%w(wad), %w(), 'Doom WAD'],
    'application/x-dvi' => [%w(dvi), %w(), 'TeX DVI document'],
    'application/x-e-theme' => [%w(etheme), %w(), 'Enlightenment theme'],
    'application/x-egon' => [%w(egon), %w(), 'Egon Animator animation'],
    'application/x-fds-disk' => [%w(fds), %w(), 'Nintendo FDS disk image'],
    'application/x-fictionbook+xml' => [%w(fb2), %w(application/xml), 'FictionBook document'],
    'application/x-fluid' => [%w(fl), %w(text/plain), 'FLTK Fluid file'],
    'application/x-font-afm' => [%w(afm), %w(), 'Adobe font metrics'],
    'application/x-font-bdf' => [%w(bdf), %w(), 'BDF font'],
    'application/x-font-linux-psf' => [%w(psf), %w(), 'Linux PSF console font'],
    'application/x-font-pcf' => [%w(pcf pcf.z pcf.gz), %w(), 'PCF font'],
    'application/x-font-speedo' => [%w(spd), %w(), 'Speedo font'],
    'application/x-font-ttx' => [%w(ttx), %w(application/xml), 'TrueType XML font'],
    'application/x-font-type1' => [%w(pfa pfb gsf), %w(application/postscript), 'PostScript type-1 font'],
    'application/x-gameboy-color-rom' => [%w(gbc cgb), %w(), 'Game Boy Color ROM'],
    'application/x-gameboy-rom' => [%w(gb sgb), %w(), 'Game Boy ROM'],
    'application/x-gamecube-rom' => [%w(iso), %w(), 'GameCube disc image'],
    'application/x-gamegear-rom' => [%w(gg), %w(), 'Game Gear ROM'],
    'application/x-gba-rom' => [%w(gba agb), %w(), 'Game Boy Advance ROM'],
    'application/x-gedcom' => [%w(ged gedcom), %w(), 'GEDCOM family history'],
    'application/x-genesis-32x-rom' => [%w(32x mdx), %w(), 'Genesis 32X ROM'],
    'application/x-genesis-rom' => [%w(gen smd), %w(), 'Genesis ROM'],
    'application/x-gettext-translation' => [%w(gmo mo), %w(), 'translated messages (machine-readable)'],
    'application/x-glade' => [%w(glade), %w(application/xml), 'Glade project'],
    'application/x-gnucash' => [%w(gnucash gnc xac), %w(), 'GnuCash financial data'],
    'application/x-gnumeric' => [%w(gnumeric), %w(), 'Gnumeric spreadsheet'],
    'application/x-gnuplot' => [%w(gp gplt gnuplot), %w(text/plain), 'Gnuplot document'],
    'application/x-go-sgf' => [%w(sgf), %w(text/plain), 'SGF record'],
    'application/x-graphite' => [%w(gra), %w(), 'Graphite scientific graph'],
    'application/x-gtk-builder' => [%w(ui), %w(application/xml), 'GTK+ Builder'],
    'application/x-gz-font-linux-psf' => [%w(psf.gz), %w(application/gzip), 'Linux PSF console font (gzip-compressed)'],
    'application/x-gzdvi' => [%w(dvi.gz), %w(application/gzip), 'TeX DVI document (gzip-compressed)'],
    'application/x-gzpdf' => [%w(pdf.gz), %w(application/gzip), 'PDF document (gzip-compressed)'],
    'application/x-gzpostscript' => [%w(ps.gz), %w(application/gzip), 'PostScript document (gzip-compressed)'],
    'application/x-hdf' => [%w(hdf hdf4 h4 hdf5 h5), %w(), 'HDF document'],
    'application/x-hfe-floppy-image' => [%w(hfe), %w(), 'HFE floppy disk image'],
    'application/x-hwp' => [%w(hwp), %w(), 'Haansoft Hangul document'],
    'application/x-hwt' => [%w(hwt), %w(), 'Haansoft Hangul document template'],
    'application/x-ica' => [%w(ica), %w(text/plain), 'Citrix ICA settings file'],
    'application/x-ipynb+json' => [%w(ipynb), %w(application/json), 'Jupyter Notebook'],
    'application/x-iso9660-appimage' => [%w(appimage), %w(application/x-executable application/x-iso9660-image), 'AppImage application bundle'],
    'application/x-it87' => [%w(it87), %w(text/plain), 'IT 8.7 color calibration file'],
    'application/x-iwork-keynote-sffkey' => [%w(key), %w(application/zip), 'Apple Keynote 5 presentation'],
    'application/x-java' => [%w(class), %w(), 'Java class'],
    'application/x-java-archive' => [%w(jar), %w(application/zip), 'Java archive'],
    'application/x-java-jce-keystore' => [%w(jceks), %w(), 'Java JCE keystore'],
    'application/x-java-jnlp-file' => [%w(jnlp), %w(application/xml), 'JNLP file'],
    'application/x-java-keystore' => [%w(jks ks), %w(), 'Java keystore'],
    'application/x-java-pack200' => [%w(pack), %w(), 'Pack200 Java archive'],
    'application/x-jbuilder-project' => [%w(jpr jpx), %w(), 'JBuilder project'],
    'application/x-karbon' => [%w(karbon), %w(), 'Karbon14 drawing'],
    'application/x-kchart' => [%w(chrt), %w(), 'KChart chart'],
    'application/x-kexi-connectiondata' => [%w(kexic), %w(), 'Kexi settings for database server connection'],
    'application/x-kexiproject-shortcut' => [%w(kexis), %w(), 'shortcut to Kexi project on database server'],
    'application/x-kexiproject-sqlite2' => [%w(kexi), %w(application/x-sqlite2), 'Kexi database file-based project'],
    'application/x-kexiproject-sqlite3' => [%w(kexi), %w(application/x-sqlite3), 'Kexi database file-based project'],
    'application/x-kformula' => [%w(kfo), %w(), 'KFormula formula'],
    'application/x-killustrator' => [%w(kil), %w(), 'KIllustrator drawing'],
    'application/x-kivio' => [%w(flw), %w(), 'Kivio flowchart'],
    'application/x-kontour' => [%w(kon), %w(), 'Kontour drawing'],
    'application/x-kpovmodeler' => [%w(kpm), %w(), 'KPovModeler scene'],
    'application/x-kpresenter' => [%w(kpr kpt), %w(), 'KPresenter presentation'],
    'application/x-krita' => [%w(kra), %w(), 'Krita document'],
    'application/x-kspread' => [%w(ksp), %w(), 'KSpread spreadsheet'],
    'application/x-kugar' => [%w(kud), %w(), 'Kugar document'],
    'application/x-kword' => [%w(kwd kwt), %w(), 'KWord document'],
    'application/x-lha' => [%w(lha lzh), %w(), 'LHA archive'],
    'application/x-lhz' => [%w(lhz), %w(), 'LHZ archive'],
    'application/x-lrzip' => [%w(lrz), %w(), 'Lrzip archive'],
    'application/x-lrzip-compressed-tar' => [%w(tar.lrz tlrz), %w(application/x-lrzip), 'Tar archive (lrzip-compressed)'],
    'application/x-lyx' => [%w(lyx), %w(text/plain), 'LyX document'],
    'application/x-lz4' => [%w(lz4), %w(), 'LZ4 archive'],
    'application/x-lz4-compressed-tar' => [%w(tar.lz4), %w(application/x-lz4), 'Tar archive (LZ4-compressed)'],
    'application/x-lzip' => [%w(lz), %w(), 'Lzip archive'],
    'application/x-lzip-compressed-tar' => [%w(tar.lz), %w(application/x-lzip), 'Tar archive (lzip-compressed)'],
    'application/x-lzma' => [%w(lzma), %w(), 'LZMA archive'],
    'application/x-lzma-compressed-tar' => [%w(tar.lzma tlz), %w(application/x-lzma), 'Tar archive (LZMA-compressed)'],
    'application/x-lzop' => [%w(lzo), %w(), 'LZO archive'],
    'application/x-lzpdf' => [%w(pdf.lz), %w(application/x-lzip), 'PDF document (lzip-compressed)'],
    'application/x-m4' => [%w(m4), %w(text/plain), 'M4 macro'],
    'application/x-magicpoint' => [%w(mgp), %w(text/plain), 'MagicPoint presentation'],
    'application/x-markaby' => [%w(mab), %w(application/x-ruby), 'Markaby script'],
    'application/x-mif' => [%w(mif), %w(), 'Adobe FrameMaker MIF document'],
    'application/x-mimearchive' => [%w(mhtml mht), %w(multipart/related), 'MHTML web archive'],
    'application/x-mobipocket-ebook' => [%w(mobi prc), %w(application/vnd.palm), 'Mobipocket e-book'],
    'application/x-ms-dos-executable' => [%w(exe), %w(), 'DOS/Windows executable'],
    'application/x-ms-wim' => [%w(wim swm), %w(), 'WIM disk Image'],
    'application/x-msi' => [%w(msi), %w(application/x-ole-storage), 'Windows Installer package'],
    'application/x-mswinurl' => [%w(url), %w(), 'Internet shortcut'],
    'application/x-mswrite' => [%w(wri), %w(), 'WRI document'],
    'application/x-msx-rom' => [%w(msx), %w(), 'MSX ROM'],
    'application/x-n64-rom' => [%w(n64 z64 v64), %w(), 'Nintendo64 ROM'],
    'application/x-navi-animation' => [%w(ani), %w(), 'Windows animated cursor'],
    'application/x-neo-geo-pocket-color-rom' => [%w(ngc), %w(), 'Neo-Geo Pocket Color ROM'],
    'application/x-neo-geo-pocket-rom' => [%w(ngp), %w(), 'Neo-Geo Pocket ROM'],
    'application/x-nes-rom' => [%w(nes nez unf unif), %w(), 'NES ROM'],
    'application/x-netcdf' => [%w(cdf nc), %w(), 'Unidata NetCDF document'],
    'application/x-netshow-channel' => [%w(nsc), %w(application/vnd.ms-asf), 'Windows Media Station file'],
    'application/x-nintendo-ds-rom' => [%w(nds), %w(), 'Nintendo DS ROM'],
    'application/x-nzb' => [%w(nzb), %w(application/xml), 'NewzBin usenet index'],
    'application/x-object' => [%w(o), %w(), 'object code'],
    'application/x-oleo' => [%w(oleo), %w(), 'GNU Oleo spreadsheet'],
    'application/x-pagemaker' => [%w(p65 pm pm6 pmd), %w(application/x-ole-storage), 'Adobe PageMaker'],
    'application/x-pak' => [%w(pak), %w(), 'PAK archive'],
    'application/x-par2' => [%w(par2 par2), %w(), 'Parchive archive'],
    'application/x-partial-download' => [%w(wkdownload crdownload part), %w(), 'Partially downloaded file'],
    'application/x-pc-engine-rom' => [%w(pce), %w(), 'PC Engine ROM'],
    'application/x-perl' => [%w(pl pl pm al perl pod t), %w(application/x-executable text/plain), 'Perl script'],
    'application/x-php' => [%w(php php3 php4 php5 phps), %w(text/plain), 'PHP script'],
    'application/x-pkcs7-certificates' => [%w(p7b spc), %w(), 'PKCS#7 certificate bundle'],
    'application/x-planperfect' => [%w(pln), %w(), 'PlanPerfect spreadsheet'],
    'application/x-pocket-word' => [%w(psw), %w(), 'Pocket Word document'],
    'application/x-pw' => [%w(pw), %w(), 'Pathetic Writer document'],
    'application/x-python-bytecode' => [%w(pyc pyo), %w(), 'Python bytecode'],
    'application/x-qpress' => [%w(qp), %w(), 'Qpress archive'],
    'application/x-qtiplot' => [%w(qti qti.gz), %w(text/plain), 'QtiPlot document'],
    'application/x-quattropro' => [%w(wb1 wb2 wb3), %w(), 'Quattro Pro spreadsheet'],
    'application/x-quicktime-media-link' => [%w(qtl), %w(video/quicktime), 'QuickTime metalink playlist'],
    'application/x-qw' => [%w(qif), %w(), 'Quicken document'],
    'application/x-raw-disk-image' => [%w(raw-disk-image img), %w(), 'Raw disk image'],
    'application/x-raw-disk-image-xz-compressed' => [%w(raw-disk-image.xz img.xz), %w(application/x-xz), 'Raw disk image (XZ-compressed)'],
    'application/x-raw-floppy-disk-image' => [%w(fd qd), %w(application/x-raw-disk-image), 'Floppy disk image'],
    'application/x-rpm' => [%w(rpm), %w(), 'RPM package'],
    'application/x-ruby' => [%w(rb), %w(application/x-executable text/plain), 'Ruby script'],
    'application/x-sami' => [%w(smi sami), %w(text/plain), 'SAMI subtitles'],
    'application/x-saturn-rom' => [%w(bin iso), %w(), 'Sega Saturn disc image'],
    'application/x-sega-cd-rom' => [%w(bin iso), %w(), 'Sega CD disc image'],
    'application/x-sg1000-rom' => [%w(sg), %w(), 'SG-1000 ROM'],
    'application/x-shar' => [%w(shar), %w(), 'shell archive'],
    'application/x-shared-library-la' => [%w(la), %w(text/plain), 'libtool shared library'],
    'application/x-sharedlib' => [%w(so), %w(), 'shared library'],
    'application/x-shellscript' => [%w(sh), %w(application/x-executable text/plain), 'shell script'],
    'application/x-shorten' => [%w(shn), %w(), 'Shorten audio'],
    'application/x-siag' => [%w(siag), %w(), 'Siag spreadsheet'],
    'application/x-smaf' => [%w(mmf smaf), %w(), 'SMAF audio'],
    'application/x-sms-rom' => [%w(sms), %w(), 'Master System ROM'],
    'application/x-source-rpm' => [%w(src.rpm spm), %w(application/x-rpm), 'Source RPM package'],
    'application/x-spss-por' => [%w(por), %w(), 'SPSS Portable Data File'],
    'application/x-spss-sav' => [%w(sav zsav), %w(), 'SPSS Data File'],
    'application/x-sqlite2' => [%w(sqlite2), %w(), 'SQLite2 database'],
    'application/x-stuffit' => [%w(sit), %w(), 'StuffIt archive'],
    'application/x-subrip' => [%w(srt), %w(text/plain), 'SubRip subtitles'],
    'application/x-sv4cpio' => [%w(sv4cpio), %w(), 'SV4 CPIO archive'],
    'application/x-sv4crc' => [%w(sv4crc), %w(), 'SV4 CPIO archive (with CRC)'],
    'application/x-t602' => [%w(602), %w(), 'T602 document'],
    'application/x-tar' => [%w(tar gtar gem), %w(), 'Tar archive'],
    'application/x-tarz' => [%w(tar.z taz), %w(application/x-compress), 'Tar archive (compressed)'],
    'application/x-tex-gf' => [%w(gf), %w(), 'generic font file'],
    'application/x-tex-pk' => [%w(pk), %w(), 'packed font file'],
    'application/x-tgif' => [%w(obj), %w(), 'TGIF document'],
    'application/x-theme' => [%w(theme), %w(application/x-desktop), 'theme'],
    'application/x-thomson-cartridge-memo7' => [%w(m7), %w(), "Thomson M\303\251mo7 cartridge"],
    'application/x-thomson-cassette' => [%w(k7), %w(), 'Thomson cassette'],
    'application/x-thomson-sap-image' => [%w(sap), %w(), 'SAP Thomson floppy disk image'],
    'application/x-trash' => [%w(bak old sik), %w(), 'backup file'],
    'application/x-troff-man' => [%w(man), %w(text/plain), 'Manpage manual document'],
    'application/x-tzo' => [%w(tar.lzo tzo), %w(application/x-lzop), 'Tar archive (LZO-compressed)'],
    'application/x-ufraw' => [%w(ufraw), %w(application/xml), 'UFRaw ID image'],
    'application/x-ustar' => [%w(ustar), %w(), 'Ustar archive'],
    'application/x-virtual-boy-rom' => [%w(vb), %w(), 'Virtual Boy ROM'],
    'application/x-wais-source' => [%w(src), %w(text/plain), 'WAIS source code'],
    'application/x-wii-rom' => [%w(iso), %w(), 'Wii disc image'],
    'application/x-wii-wad' => [%w(wad), %w(), 'WiiWare bundle'],
    'application/x-windows-themepack' => [%w(themepack), %w(application/vnd.ms-cab-compressed), 'Microsoft Windows theme pack'],
    'application/x-wonderswan-color-rom' => [%w(wsc), %w(), 'Bandai WonderSwan Color ROM'],
    'application/x-wonderswan-rom' => [%w(ws), %w(), 'Bandai WonderSwan ROM'],
    'application/x-wpg' => [%w(wpg), %w(), 'WordPerfect/Drawperfect image'],
    'application/x-wwf' => [%w(wwf), %w(application/pdf), 'WWF document'],
    'application/x-x509-ca-cert' => [%w(der crt cert pem), %w(), 'DER/PEM/Netscape-encoded X.509 certificate'],
    'application/x-xar' => [%w(xar pkg), %w(), 'XAR archive'],
    'application/x-xbel' => [%w(xbel), %w(application/xml), 'XBEL bookmarks'],
    'application/x-xpinstall' => [%w(xpi), %w(application/zip), 'XPInstall installer module'],
    'application/x-xz' => [%w(xz), %w(), 'XZ archive'],
    'application/x-xz-compressed-tar' => [%w(tar.xz txz), %w(application/x-xz), 'Tar archive (XZ-compressed)'],
    'application/x-xzpdf' => [%w(pdf.xz), %w(application/x-xz), 'PDF document (XZ-compressed)'],
    'application/x-yaml' => [%w(yaml yml), %w(text/plain), 'YAML document'],
    'application/x-zip-compressed-fb2' => [%w(fb2.zip), %w(application/zip), 'Compressed FictionBook document'],
    'application/x-zoo' => [%w(zoo), %w(), 'Zoo archive'],
    'application/xhtml+xml' => [%w(xhtml xht), %w(application/xml), 'XHTML page'],
    'application/xliff+xml' => [%w(xlf xliff), %w(application/xml), 'XLIFF translation file'],
    'application/xml' => [%w(xml xbl xsd rng), %w(text/plain), 'XML document'],
    'application/xml-dtd' => [%w(dtd), %w(text/plain), 'DTD file'],
    'application/xml-external-parsed-entity' => [%w(ent), %w(application/xml), 'XML entities document'],
    'application/xslt+xml' => [%w(xsl xslt), %w(application/xml), 'XSLT stylesheet'],
    'application/xspf+xml' => [%w(xspf), %w(application/xml), 'XSPF playlist'],
    'application/zip' => [%w(zip), %w(), 'Zip archive'],
    'application/zlib' => [%w(zz), %w(), 'Zlib archive'],
    'audio/AMR' => [%w(amr), %w(), 'AMR audio'],
    'audio/AMR-WB' => [%w(awb), %w(), 'AMR-WB audio'],
    'audio/aac' => [%w(aac adts ass), %w(), 'AAC audio'],
    'audio/ac3' => [%w(ac3), %w(), 'Dolby Digital audio'],
    'audio/annodex' => [%w(axa), %w(application/annodex), 'Annodex Audio'],
    'audio/basic' => [%w(au snd), %w(), 'ULAW (Sun) audio'],
    'audio/flac' => [%w(flac), %w(), 'FLAC audio'],
    'audio/midi' => [%w(mid midi kar), %w(), 'MIDI audio'],
    'audio/mp2' => [%w(mp2), %w(), 'MP2 audio'],
    'audio/mp4' => [%w(m4a f4a), %w(), 'MPEG-4 audio'],
    'audio/mpeg' => [%w(mp3 mpga), %w(), 'MP3 audio'],
    'audio/ogg' => [%w(oga ogg opus), %w(application/ogg), 'Ogg Audio'],
    'audio/prs.sid' => [%w(sid psid), %w(), 'Commodore 64 audio'],
    'audio/usac' => [%w(loas xhe), %w(), 'USAC audio'],
    'audio/vnd.dts' => [%w(dts), %w(), 'DTS audio'],
    'audio/vnd.dts.hd' => [%w(dtshd), %w(audio/vnd.dts), 'DTSHD audio'],
    'audio/vnd.rn-realaudio' => [%w(ra rax), %w(), 'RealAudio document'],
    'audio/x-aifc' => [%w(aifc aiffc), %w(application/x-iff), 'AIFC audio'],
    'audio/x-aiff' => [%w(aiff aif), %w(application/x-iff), 'AIFF/Amiga/Mac audio'],
    'audio/x-amzxml' => [%w(amz), %w(), 'AmazonMP3 download file'],
    'audio/x-ape' => [%w(ape), %w(), "Monkey's audio"],
    'audio/x-flac+ogg' => [%w(oga ogg), %w(audio/ogg), 'Ogg FLAC audio'],
    'audio/x-gsm' => [%w(gsm), %w(), 'GSM 06.10 audio'],
    'audio/x-iriver-pla' => [%w(pla), %w(), 'iRiver Playlist'],
    'audio/x-it' => [%w(it), %w(), 'Impulse Tracker audio'],
    'audio/x-m4b' => [%w(m4b f4b), %w(audio/mp4), 'MPEG-4 audio book'],
    'audio/x-m4r' => [%w(m4r), %w(video/mp4), 'MPEG-4 Ringtone'],
    'audio/x-matroska' => [%w(mka), %w(application/x-matroska), 'Matroska audio'],
    'audio/x-minipsf' => [%w(minipsf), %w(audio/x-psf), 'MiniPSF audio'],
    'audio/x-mo3' => [%w(mo3), %w(), 'compressed Tracker audio'],
    'audio/x-mod' => [%w(mod ult uni m15 mtm 669 med), %w(), 'Amiga SoundTracker audio'],
    'audio/x-mpegurl' => [%w(m3u m3u8 vlc), %w(text/plain), 'MP3 audio (streamed)'],
    'audio/x-ms-asx' => [%w(asx wax wvx wmx), %w(), 'Microsoft ASX playlist'],
    'audio/x-ms-wma' => [%w(wma), %w(application/vnd.ms-asf), 'Windows Media audio'],
    'audio/x-musepack' => [%w(mpc mpp mp+), %w(), 'Musepack audio'],
    'audio/x-opus+ogg' => [%w(opus), %w(audio/ogg), 'Opus audio'],
    'audio/x-pn-audibleaudio' => [%w(aa aax), %w(), 'Audible.Com audio'],
    'audio/x-psf' => [%w(psf), %w(), 'PSF audio'],
    'audio/x-psflib' => [%w(psflib), %w(audio/x-psf), 'PSFlib audio library'],
    'audio/x-s3m' => [%w(s3m), %w(), 'Scream Tracker 3 audio'],
    'audio/x-scpls' => [%w(pls), %w(), 'MP3 ShoutCast playlist'],
    'audio/x-speex' => [%w(spx), %w(), 'Speex audio'],
    'audio/x-speex+ogg' => [%w(oga ogg), %w(audio/ogg), 'Ogg Speex audio'],
    'audio/x-stm' => [%w(stm), %w(), 'Scream Tracker audio'],
    'audio/x-tta' => [%w(tta), %w(), 'TrueAudio audio'],
    'audio/x-voc' => [%w(voc), %w(), 'VOC audio'],
    'audio/x-vorbis+ogg' => [%w(oga ogg), %w(audio/ogg), 'Ogg Vorbis audio'],
    'audio/x-wav' => [%w(wav), %w(), 'WAV audio'],
    'audio/x-wavpack' => [%w(wv wvp), %w(), 'WavPack audio'],
    'audio/x-wavpack-correction' => [%w(wvc), %w(), 'WavPack audio correction file'],
    'audio/x-xi' => [%w(xi), %w(), 'Scream Tracker instrument'],
    'audio/x-xm' => [%w(xm), %w(), 'FastTracker II audio'],
    'audio/x-xmf' => [%w(xmf), %w(), 'XMF audio'],
    'font/collection' => [%w(ttc), %w(), 'Font collection'],
    'font/otf' => [%w(otf), %w(font/ttf), 'OpenType font'],
    'font/ttf' => [%w(ttf), %w(), 'TrueType font'],
    'font/woff' => [%w(woff2), %w(), 'WOFF2 font'],
    'image/bmp' => [%w(bmp dib), %w(), 'Windows BMP image'],
    'image/cgm' => [%w(cgm), %w(), 'Computer Graphics Metafile'],
    'image/emf' => [%w(emf), %w(), 'EMF image'],
    'image/fax-g3' => [%w(g3), %w(), 'CCITT G3 fax'],
    'image/fits' => [%w(fits), %w(), 'FITS document'],
    'image/gif' => [%w(gif), %w(), 'GIF image'],
    'image/heif' => [%w(heic heif), %w(), 'HEIF image'],
    'image/ief' => [%w(ief), %w(), 'IEF image'],
    'image/jp2' => [%w(jp2 jpg2), %w(), 'JPEG-2000 JP2 image'],
    'image/jpeg' => [%w(jpeg jpg jpe), %w(), 'JPEG image'],
    'image/jpm' => [%w(jpm jpgm), %w(), 'JPEG-2000 JPM image'],
    'image/jpx' => [%w(jpf jpx), %w(), 'JPEG-2000 JPX image'],
    'image/ktx' => [%w(ktx), %w(), 'Khronos texture image'],
    'image/openraster' => [%w(ora), %w(application/zip), 'OpenRaster archiving image'],
    'image/png' => [%w(png), %w(), 'PNG image'],
    'image/rle' => [%w(rle), %w(), 'Run Length Encoded bitmap image'],
    'image/svg+xml' => [%w(svg), %w(application/xml), 'SVG image'],
    'image/svg+xml-compressed' => [%w(svgz), %w(application/gzip), 'compressed SVG image'],
    'image/tiff' => [%w(tif tiff), %w(), 'TIFF image'],
    'image/vnd.adobe.photoshop' => [%w(psd), %w(), 'Photoshop image'],
    'image/vnd.djvu' => [%w(djvu djv), %w(), 'DjVu image'],
    'image/vnd.djvu+multipage' => [%w(djvu djv), %w(image/vnd.djvu), 'DjVu document'],
    'image/vnd.dwg' => [%w(dwg), %w(), 'AutoCAD image'],
    'image/vnd.dxf' => [%w(dxf), %w(), 'DXF vector image'],
    'image/vnd.microsoft.icon' => [%w(ico), %w(), 'Windows icon'],
    'image/vnd.ms-modi' => [%w(mdi), %w(), 'Microsoft Document Imaging format'],
    'image/vnd.rn-realpix' => [%w(rp), %w(), 'RealPix document'],
    'image/vnd.wap.wbmp' => [%w(wbmp), %w(), 'WBMP image'],
    'image/vnd.zbrush.pcx' => [%w(pcx), %w(), 'PCX image'],
    'image/webp' => [%w(webp), %w(), 'WebP image'],
    'image/wmf' => [%w(wmf), %w(), 'WMF image'],
    'image/x-3ds' => [%w(3ds), %w(), '3D Studio image'],
    'image/x-adobe-dng' => [%w(dng), %w(image/tiff image/x-dcraw), 'Adobe DNG negative'],
    'image/x-applix-graphics' => [%w(ag), %w(), 'Applix Graphics image'],
    'image/x-bzeps' => [%w(eps.bz2 epsi.bz2 epsf.bz2), %w(application/x-bzip), 'EPS image (bzip-compressed)'],
    'image/x-canon-cr2' => [%w(cr2), %w(image/tiff image/x-dcraw), 'Canon CR2 raw image'],
    'image/x-canon-crw' => [%w(crw), %w(image/x-dcraw), 'Canon CRW raw image'],
    'image/x-cmu-raster' => [%w(ras), %w(), 'CMU raster image'],
    'image/x-compressed-xcf' => [%w(xcf.gz xcf.bz2), %w(), 'compressed GIMP image'],
    'image/x-dds' => [%w(dds), %w(), 'DirectDraw surface'],
    'image/x-eps' => [%w(eps epsi epsf), %w(application/postscript), 'EPS image'],
    'image/x-exr' => [%w(exr), %w(), 'EXR image'],
    'image/x-fuji-raf' => [%w(raf), %w(image/x-dcraw), 'Fuji RAF raw image'],
    'image/x-gimp-gbr' => [%w(gbr), %w(), 'GIMP brush'],
    'image/x-gimp-gih' => [%w(gih), %w(), 'GIMP brush pipe'],
    'image/x-gimp-pat' => [%w(pat), %w(), 'GIMP pattern'],
    'image/x-gzeps' => [%w(eps.gz epsi.gz epsf.gz), %w(application/gzip), 'EPS image (gzip-compressed)'],
    'image/x-icns' => [%w(icns), %w(), 'MacOS X icon'],
    'image/x-ilbm' => [%w(iff ilbm lbm), %w(application/x-iff), 'ILBM image'],
    'image/x-jng' => [%w(jng), %w(), 'JNG image'],
    'image/x-jp2-codestream' => [%w(j2c j2k jpc), %w(), 'JPEG-2000 codestream'],
    'image/x-kodak-dcr' => [%w(dcr), %w(image/tiff image/x-dcraw), 'Kodak DCR raw image'],
    'image/x-kodak-k25' => [%w(k25), %w(image/tiff image/x-dcraw), 'Kodak K25 raw image'],
    'image/x-kodak-kdc' => [%w(kdc), %w(image/tiff image/x-dcraw), 'Kodak KDC raw image'],
    'image/x-lwo' => [%w(lwo lwob), %w(), 'LightWave object'],
    'image/x-lws' => [%w(lws), %w(), 'LightWave scene'],
    'image/x-macpaint' => [%w(pntg), %w(), 'MacPaint Bitmap image'],
    'image/x-minolta-mrw' => [%w(mrw), %w(image/x-dcraw), 'Minolta MRW raw image'],
    'image/x-msod' => [%w(msod), %w(), 'Office drawing'],
    'image/x-nikon-nef' => [%w(nef), %w(image/tiff image/x-dcraw), 'Nikon NEF raw image'],
    'image/x-olympus-orf' => [%w(orf), %w(image/x-dcraw), 'Olympus ORF raw image'],
    'image/x-panasonic-rw' => [%w(raw), %w(image/x-dcraw), 'Panasonic raw image'],
    'image/x-panasonic-rw2' => [%w(rw2), %w(image/x-dcraw), 'Panasonic raw2 image'],
    'image/x-pentax-pef' => [%w(pef), %w(image/tiff image/x-dcraw), 'Pentax PEF raw image'],
    'image/x-photo-cd' => [%w(pcd), %w(), 'PCD image'],
    'image/x-pict' => [%w(pct pict pict1 pict2), %w(), 'Macintosh Quickdraw/PICT drawing'],
    'image/x-portable-anymap' => [%w(pnm), %w(), 'PNM image'],
    'image/x-portable-bitmap' => [%w(pbm), %w(image/x-portable-anymap), 'PBM image'],
    'image/x-portable-graymap' => [%w(pgm), %w(image/x-portable-anymap), 'PGM image'],
    'image/x-portable-pixmap' => [%w(ppm), %w(image/x-portable-anymap), 'PPM image'],
    'image/x-quicktime' => [%w(qtif qif), %w(), 'QuickTime image'],
    'image/x-rgb' => [%w(rgb), %w(), 'RGB image'],
    'image/x-sgi' => [%w(sgi), %w(), 'SGI image'],
    'image/x-sigma-x3f' => [%w(x3f), %w(image/x-dcraw), 'Sigma X3F raw image'],
    'image/x-skencil' => [%w(sk sk1), %w(), 'Skencil document'],
    'image/x-sony-arw' => [%w(arw), %w(image/tiff image/x-dcraw), 'Sony ARW raw image'],
    'image/x-sony-sr2' => [%w(sr2), %w(image/tiff image/x-dcraw), 'Sony SR2 raw image'],
    'image/x-sony-srf' => [%w(srf), %w(image/tiff image/x-dcraw), 'Sony SRF raw image'],
    'image/x-sun-raster' => [%w(sun), %w(), 'Sun raster image'],
    'image/x-tga' => [%w(tga icb tpic vda vst), %w(), 'TGA image'],
    'image/x-win-bitmap' => [%w(cur), %w(), 'Windows cursor'],
    'image/x-xbitmap' => [%w(xbm), %w(), 'XBM image'],
    'image/x-xcf' => [%w(xcf), %w(), 'GIMP image'],
    'image/x-xfig' => [%w(fig), %w(), 'XFig image'],
    'image/x-xpixmap' => [%w(xpm), %w(), 'XPM image'],
    'image/x-xwindowdump' => [%w(xwd), %w(), 'X window image'],
    'message/rfc822' => [%w(eml), %w(text/plain), 'email message'],
    'model/iges' => [%w(igs iges), %w(text/plain), 'IGES document'],
    'model/stl' => [%w(stl), %w(), 'STL 3D model'],
    'model/vrml' => [%w(vrm vrml wrl), %w(text/plain), 'VRML document'],
    'text/cache-manifest' => [%w(manifest), %w(text/plain), 'Web application cache manifest'],
    'text/calendar' => [%w(vcs ics), %w(text/plain), 'VCS/ICS calendar'],
    'text/css' => [%w(css), %w(text/plain), 'CSS stylesheet'],
    'text/csv' => [%w(csv), %w(text/plain), 'CSV document'],
    'text/csv-schema' => [%w(csvs), %w(text/plain), 'CSV Schema document'],
    'text/html' => [%w(html htm), %w(text/plain), 'HTML document'],
    'text/markdown' => [%w(md mkd markdown), %w(text/plain), 'Markdown document'],
    'text/plain' => [%w(txt asc), %w(), 'plain text document'],
    'text/richtext' => [%w(rtx), %w(text/plain), 'rich text document'],
    'text/rust' => [%w(rs), %w(text/plain), 'Rust source code'],
    'text/sgml' => [%w(sgml sgm), %w(text/plain), 'SGML document'],
    'text/spreadsheet' => [%w(sylk slk), %w(text/plain), 'spreadsheet interchange document'],
    'text/tab-separated-values' => [%w(tsv), %w(text/plain), 'TSV document'],
    'text/troff' => [%w(tr roff t), %w(text/plain), 'Troff document'],
    'text/turtle' => [%w(ttl), %w(text/plain), 'Turtle document'],
    'text/vcard' => [%w(vcard vcf vct gcrd), %w(text/plain), 'electronic business card'],
    'text/vnd.graphviz' => [%w(gv dot), %w(), 'Graphviz DOT graph'],
    'text/vnd.qt.linguist' => [%w(ts), %w(application/xml), 'message catalog'],
    'text/vnd.rn-realtext' => [%w(rt), %w(), 'RealText document'],
    'text/vnd.sun.j2me.app-descriptor' => [%w(jad), %w(), 'JAD document'],
    'text/vnd.wap.wml' => [%w(wml), %w(application/xml), 'WML document'],
    'text/vnd.wap.wmlscript' => [%w(wmls), %w(), 'WMLScript program'],
    'text/vtt' => [%w(vtt), %w(text/plain), 'WebVTT subtitles'],
    'text/x-adasrc' => [%w(adb ads), %w(text/plain), 'Ada source code'],
    'text/x-bibtex' => [%w(bib), %w(text/plain), 'BibTeX document'],
    'text/x-c++hdr' => [%w(hh hp hpp h++ hxx), %w(text/x-chdr), 'C++ header'],
    'text/x-c++src' => [%w(cpp cxx cc c c++), %w(text/x-csrc), 'C++ source code'],
    'text/x-chdr' => [%w(h), %w(text/x-csrc), 'C header'],
    'text/x-cmake' => [%w(cmake), %w(text/plain), 'CMake source code'],
    'text/x-cobol' => [%w(cbl cob), %w(text/plain), 'COBOL source file'],
    'text/x-csharp' => [%w(cs), %w(text/x-csrc), 'C# source code'],
    'text/x-csrc' => [%w(c), %w(text/plain), 'C source code'],
    'text/x-dbus-service' => [%w(service), %w(text/plain), 'D-Bus service file'],
    'text/x-dcl' => [%w(dcl), %w(text/plain), 'DCL script'],
    'text/x-dsl' => [%w(dsl), %w(text/plain), 'DSSSL document'],
    'text/x-dsrc' => [%w(d di), %w(text/x-csrc), 'D source code'],
    'text/x-eiffel' => [%w(e eif), %w(text/plain), 'Eiffel source code'],
    'text/x-emacs-lisp' => [%w(el), %w(text/plain), 'Emacs Lisp source code'],
    'text/x-erlang' => [%w(erl), %w(text/plain), 'Erlang source code'],
    'text/x-fortran' => [%w(f f90 f95 for), %w(text/plain), 'Fortran source code'],
    'text/x-genie' => [%w(gs), %w(text/plain), 'Genie source code'],
    'text/x-gettext-translation' => [%w(po), %w(text/plain), 'translation file'],
    'text/x-gettext-translation-template' => [%w(pot), %w(text/plain), 'translation template'],
    'text/x-gherkin' => [%w(feature), %w(text/plain), 'feature specification in Gherkin format'],
    'text/x-go' => [%w(go), %w(text/plain), 'Go source code'],
    'text/x-google-video-pointer' => [%w(gvp), %w(), 'Google Video Pointer'],
    'text/x-haskell' => [%w(hs), %w(text/plain), 'Haskell source code'],
    'text/x-iMelody' => [%w(imy ime), %w(), 'iMelody ringtone'],
    'text/x-idl' => [%w(idl), %w(text/plain), 'IDL document'],
    'text/x-iptables' => [%w(iptables), %w(text/plain), 'iptables configuration file'],
    'text/x-java' => [%w(java), %w(text/x-csrc), 'Java source code'],
    'text/x-ldif' => [%w(ldif), %w(text/plain), 'LDIF address book'],
    'text/x-lilypond' => [%w(ly), %w(text/plain), 'Lilypond music sheet'],
    'text/x-literate-haskell' => [%w(lhs), %w(text/plain), 'LHS source code'],
    'text/x-log' => [%w(log), %w(text/plain), 'application log'],
    'text/x-lua' => [%w(lua), %w(application/x-executable text/plain), 'Lua script'],
    'text/x-makefile' => [%w(mk mak), %w(text/plain), 'Makefile'],
    'text/x-matlab' => [%w(m), %w(text/plain), 'MATLAB script/function'],
    'text/x-microdvd' => [%w(sub), %w(text/plain), 'MicroDVD subtitles'],
    'text/x-moc' => [%w(moc), %w(text/plain), 'Qt MOC file'],
    'text/x-modelica' => [%w(mo), %w(text/plain), 'Modelica model'],
    'text/x-mof' => [%w(mof), %w(text/x-csrc), 'Managed Object Format'],
    'text/x-mpsub' => [%w(sub), %w(text/plain), 'MPSub subtitles'],
    'text/x-mrml' => [%w(mrml mrl), %w(), 'MRML playlist'],
    'text/x-ms-regedit' => [%w(reg), %w(text/plain), 'Windows Registry extract'],
    'text/x-mup' => [%w(mup not), %w(text/plain), 'Mup publication'],
    'text/x-nfo' => [%w(nfo), %w(text/x-readme), 'NFO document'],
    'text/x-objcsrc' => [%w(m), %w(text/x-csrc), 'Objective-C source code'],
    'text/x-ocaml' => [%w(ml mli), %w(), 'OCaml source code'],
    'text/x-ocl' => [%w(ocl), %w(text/plain), 'OCL file'],
    'text/x-ooc' => [%w(ooc), %w(text/x-csrc), 'OOC source code'],
    'text/x-opencl-src' => [%w(cl), %w(text/x-csrc), 'OpenCL source code'],
    'text/x-opml+xml' => [%w(opml), %w(application/xml), 'OPML syndication feed'],
    'text/x-pascal' => [%w(p pas), %w(text/plain), 'Pascal source code'],
    'text/x-patch' => [%w(diff patch), %w(text/plain), 'differences between files'],
    'text/x-python' => [%w(py pyx wsgi), %w(application/x-executable text/plain), 'Python script'],
    'text/x-python3' => [%w(py py3 py3x), %w(text/x-python), 'Python 3 script'],
    'text/x-qml' => [%w(qml qmltypes qmlproject), %w(), 'Qt Markup Language file'],
    'text/x-reject' => [%w(rej), %w(text/plain), 'rejected patch'],
    'text/x-rpm-spec' => [%w(spec), %w(text/plain), 'RPM spec file'],
    'text/x-sass' => [%w(sass), %w(text/plain), 'Sass CSS pre-processor file'],
    'text/x-scala' => [%w(scala), %w(text/plain), 'Scala source code'],
    'text/x-scheme' => [%w(scm ss), %w(text/plain), 'Scheme source code'],
    'text/x-scss' => [%w(scss), %w(text/plain), 'Sass CSS pre-processor file'],
    'text/x-setext' => [%w(etx), %w(text/plain), 'Setext document'],
    'text/x-ssa' => [%w(ssa ass), %w(text/plain), 'SSA subtitles'],
    'text/x-subviewer' => [%w(sub), %w(text/plain), 'SubViewer subtitles'],
    'text/x-svhdr' => [%w(svh), %w(text/x-verilog), 'SystemVerilog header'],
    'text/x-svsrc' => [%w(sv), %w(text/x-verilog), 'SystemVerilog source code'],
    'text/x-systemd-unit' => [%w(automount device mount path scope service slice socket swap target timer), %w(text/plain), 'systemd unit file'],
    'text/x-tcl' => [%w(tcl tk), %w(text/plain), 'Tcl script'],
    'text/x-tex' => [%w(tex ltx sty cls dtx ins latex), %w(text/plain), 'TeX document'],
    'text/x-texinfo' => [%w(texi texinfo), %w(text/plain), 'TeXInfo document'],
    'text/x-troff-me' => [%w(me), %w(text/plain), 'Troff ME input document'],
    'text/x-troff-mm' => [%w(mm), %w(text/plain), 'Troff MM input document'],
    'text/x-troff-ms' => [%w(ms), %w(text/plain), 'Troff MS input document'],
    'text/x-twig' => [%w(twig), %w(text/plain), 'Twig template'],
    'text/x-txt2tags' => [%w(t2t), %w(text/plain), 'txt2tags document'],
    'text/x-uil' => [%w(uil), %w(text/plain), 'X-Motif UIL table'],
    'text/x-uuencode' => [%w(uue), %w(text/plain), 'uuencoded file'],
    'text/x-vala' => [%w(vala vapi), %w(text/x-csrc), 'Vala source code'],
    'text/x-verilog' => [%w(v), %w(text/plain), 'Verilog source code'],
    'text/x-vhdl' => [%w(vhd vhdl), %w(text/plain), 'VHDL source code'],
    'text/x-xmi' => [%w(xmi), %w(application/xml), 'XMI file'],
    'text/x-xslfo' => [%w(fo xslfo), %w(application/xml), 'XSL FO file'],
    'text/x.gcode' => [%w(gcode), %w(text/plain), 'G-code file'],
    'video/3gpp' => [%w(3gp 3gpp 3ga), %w(video/mp4), '3GPP multimedia file'],
    'video/3gpp2' => [%w(3g2 3gp2 3gpp2), %w(video/mp4), '3GPP2 multimedia file'],
    'video/annodex' => [%w(axv), %w(application/annodex), 'Annodex Video'],
    'video/dv' => [%w(dv), %w(), 'DV video'],
    'video/mj2' => [%w(mj2 mjp2), %w(), 'JPEG-2000 MJ2 video'],
    'video/mp2t' => [%w(m2t m2ts ts mts cpi clpi mpl mpls bdm bdmv), %w(), 'MPEG-2 transport stream'],
    'video/mp4' => [%w(mp4 m4v f4v lrv), %w(), 'MPEG-4 video'],
    'video/mpeg' => [%w(mpeg mpg mp2 mpe vob), %w(), 'MPEG video'],
    'video/ogg' => [%w(ogv ogg), %w(application/ogg), 'Ogg Video'],
    'video/quicktime' => [%w(qt mov moov qtvr), %w(), 'QuickTime video'],
    'video/vnd.mpegurl' => [%w(m1u m4u mxu), %w(text/plain), 'MPEG video (streamed)'],
    'video/vnd.rn-realvideo' => [%w(rv rvx), %w(), 'RealVideo document'],
    'video/vnd.vivo' => [%w(viv vivo), %w(), 'Vivo video'],
    'video/webm' => [%w(webm), %w(), 'WebM video'],
    'video/x-flic' => [%w(fli flc), %w(), 'FLIC animation'],
    'video/x-flv' => [%w(flv), %w(), 'Flash video'],
    'video/x-javafx' => [%w(fxm), %w(video/x-flv), 'JavaFX video'],
    'video/x-matroska' => [%w(mkv), %w(application/x-matroska), 'Matroska video'],
    'video/x-matroska-3d' => [%w(mk3d), %w(application/x-matroska), 'Matroska 3D video'],
    'video/x-mjpeg' => [%w(mjpeg mjpg), %w(image/jpeg), 'MJPEG video stream'],
    'video/x-mng' => [%w(mng), %w(), 'MNG animation'],
    'video/x-ms-wmv' => [%w(wmv), %w(application/vnd.ms-asf), 'Windows Media video'],
    'video/x-msvideo' => [%w(avi avf divx), %w(), 'AVI video'],
    'video/x-nsv' => [%w(nsv), %w(), 'NullSoft video'],
    'video/x-ogm+ogg' => [%w(ogm), %w(video/ogg), 'OGM video'],
    'video/x-sgi-movie' => [%w(movie), %w(), 'SGI video'],
    'video/x-theora+ogg' => [%w(ogg), %w(video/ogg), 'Ogg Theora video'],
    'x-epoc/x-sisx-app' => [%w(sisx), %w(), 'SISX package'],
  }
  # @private
  # :nodoc:
  MAGIC = [
    ['image/jpeg', [[0, "\377\330\377"], [0, "\377\330"]]],
    ['image/png', [[0, "\211PNG"]]],
    ['image/gif', [[0, 'GIF8']]],
    ['image/tiff', [[0, "MM\000*"], [0, "II*\000"]]],
    ['image/bmp', [[0, 'BM', [[14, "\f"], [14, '@'], [14, '(']]]]],
    ['image/vnd.adobe.photoshop', []],
    ['image/webp', [[0, 'RIFF', [[8, 'WEBP']]]]],
    ['image/svg+xml', [[0..256, '<!DOCTYPE svg'], [0..256, '<svg']]],
    ['video/x-msvideo', [[0, 'RIFF', [[8, 'AVI ']]], [0, 'AVF0', [[8, 'AVI ']]]]],
    ['video/mp4', [[4, 'ftypisom'], [4, 'ftypmp42'], [4, 'ftypMSNV'], [4, 'ftypM4V '], [4, 'ftypf4v ']]],
    ['video/quicktime', [[12, 'mdat'], [4, 'mdat'], [4, 'moov'], [4, 'ftypqt']]],
    ['video/mpeg', [[0, "G?\377\020"], [0, "\000\000\001\263"], [0, "\000\000\001\272"]]],
    ['video/ogg', [[0, 'OggS']]],
    ['video/webm', [[0, "\032E\337\243", [[5..65, "B\202", [[8..75, 'webm']]]]]]],
    ['video/x-flv', [[0, 'FLV']]],
    ['audio/mpeg', [[0, "\377\373"], [0, 'ID3']]],
    ['audio/x-wav', [[8, 'WAVE'], [8, 'WAV ']]],
    ['audio/aac', [[0, 'ADIF']]],
    ['audio/flac', [[0, 'fLaC']]],
    ['audio/mp4', [[4, 'ftypM4A']]],
    ['audio/ogg', [[0, 'OggS']]],
    ['application/pdf', [[0..1024, '%PDF-']]],
    ['application/msword', [[0, "1\276\000\000"], [0, 'PO^Q`'], [0, "\3767\000#"], [0, "\333\245-\000\000\000"], [2112, 'MSWordDoc'], [2108, 'MSWordDoc'], [2112, 'Microsoft Word document data'], [546, 'bjbj'], [546, 'jbjb']]],
    ['application/vnd.ms-excel', [[2080, 'Microsoft Excel 5.0 Worksheet']]],
    ['application/vnd.stardivision.writer', [[2089, 'StarWriter']]],
    ['application/x-docbook+xml', [[0, '<?xml', [[0..100, '-//OASIS//DTD DocBook XML'], [0..100, '-//KDE//DTD DocBook XML']]]]],
    ['image/x-eps', [[0, '%!', [[15, 'EPS']]], [0, "\004%!", [[16, 'EPS']]], [0, "\305\320\323\306"]]],
    ['application/prs.plucker', [[60, 'DataPlkr']]],
    ['application/vnd.corel-draw', []],
    ['application/x-fictionbook+xml', [[0..256, '<FictionBook']]],
    ['application/x-mobipocket-ebook', [[60, 'BOOKMOBI']]],
    ['application/x-mozilla-bookmarks', [[0..64, '<!DOCTYPE NETSCAPE-Bookmark-file-1>']]],
    ['application/x-nzb', [[0..256, '<nzb']]],
    ['application/x-pak', [[0, 'PACK']]],
    ['application/x-php', [[0..64, '<?php']]],
    ['application/xliff+xml', [[0..256, '<xliff']]],
    ['audio/x-flac+ogg', [[0, 'OggS', [[28, 'fLaC']]], [0, 'OggS', [[28, "\177FLAC"]]]]],
    ['audio/x-opus+ogg', [[0, 'OggS', [[28, 'OpusHead']]]]],
    ['audio/x-speex+ogg', [[0, 'OggS', [[28, 'Speex  ']]]]],
    ['audio/x-vorbis+ogg', [[0, 'OggS', [[28, "\001vorbis"]]]]],
    ['image/ktx', [[0, "\253KTX", [[4, " 11\273", [[8, "\r\n\032\n"]]]]]]],
    ['image/vnd.djvu', [[0, 'AT&TFORM', [[12, 'DJVU']]], [0, 'FORM', [[8, 'DJVU']]]]],
    ['image/vnd.djvu+multipage', [[0, 'AT&TFORM', [[12, 'DJVM']]], [0, 'FORM', [[8, 'DJVM']]]]],
    ['image/x-kodak-kdc', [[242, 'EASTMAN KODAK COMPANY']]],
    ['image/x-niff', [[0, 'IIN1']]],
    ['text/x-qml', [[2..16, '/bin/env qml'], [0..3000, 'import Qt', [[9..3009, '{']]], [0..3000, 'import Qml', [[9..3009, '{']]]]],
    ['video/x-ogm+ogg', [[0, 'OggS', [[29, 'video']]]]],
    ['video/x-theora+ogg', [[0, 'OggS', [[28, "\200theora"]]]]],
    ['application/atom+xml', [[0..256, '<feed ']]],
    ['application/epub+zip', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/epub+zip'], [43, 'application/epub+zip']]]]]]],
    ['application/rss+xml', [[0..256, '<rss '], [0..256, '<RSS ']]],
    ['application/vnd.apple.mpegurl', [[0, '#EXTM3U', [[0..128, '#EXT-X-TARGETDURATION'], [0..128, '#EXT-X-STREAM-INF']]]]],
    ['application/vnd.oasis.opendocument.chart', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.chart']]]]]]],
    ['application/vnd.oasis.opendocument.chart-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.chart-template']]]]]]],
    ['application/vnd.oasis.opendocument.database', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.base']]]]]]],
    ['application/vnd.oasis.opendocument.formula', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.formula']]]]]]],
    ['application/vnd.oasis.opendocument.formula-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.formula-template']]]]]]],
    ['application/vnd.oasis.opendocument.graphics', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.graphics']]]]]]],
    ['application/vnd.oasis.opendocument.graphics-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.graphics-template']]]]]]],
    ['application/vnd.oasis.opendocument.image', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.image']]]]]]],
    ['application/vnd.oasis.opendocument.presentation', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.presentation']]]]]]],
    ['application/vnd.oasis.opendocument.presentation-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.presentation-template']]]]]]],
    ['application/vnd.oasis.opendocument.spreadsheet', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.spreadsheet']]]]]]],
    ['application/vnd.oasis.opendocument.spreadsheet-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.spreadsheet-template']]]]]]],
    ['application/vnd.oasis.opendocument.text', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.text']]]]]]],
    ['application/vnd.oasis.opendocument.text-master', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.text-master']]]]]]],
    ['application/vnd.oasis.opendocument.text-template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.text-template']]]]]]],
    ['application/vnd.oasis.opendocument.text-web', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.oasis.opendocument.text-web']]]]]]],
    ['application/vnd.sun.xml.calc', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.calc']]]]]]],
    ['application/vnd.sun.xml.calc.template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.calc']]]]]]],
    ['application/vnd.sun.xml.draw', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.draw']]]]]]],
    ['application/vnd.sun.xml.draw.template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.draw']]]]]]],
    ['application/vnd.sun.xml.impress', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.impress']]]]]]],
    ['application/vnd.sun.xml.impress.template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.impress']]]]]]],
    ['application/vnd.sun.xml.math', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.math']]]]]]],
    ['application/vnd.sun.xml.writer', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.writer']]]]]]],
    ['application/vnd.sun.xml.writer.global', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.writer']]]]]]],
    ['application/vnd.sun.xml.writer.template', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'application/vnd.sun.xml.writer']]]]]]],
    ['application/x-iwork-keynote-sffkey', [[0, "PK\003\004", [[30, 'index.apxl']]]]],
    ['application/x-zip-compressed-fb2', [[0, "PK\003\004", [[30..256, '.fb2']]]]],
    ['image/openraster', [[0, "PK\003\004", [[30, 'mimetype', [[38, 'image/openraster']]]]]]],
    ['text/x-opml+xml', [[0..256, '<opml ']]],
    ['application/vnd.ms-cab-compressed', [[0, "MSCF\000\000\000\000"]]],
    ['application/vnd.ms-wpl', [[0..256, '<?wpl']]],
    ['application/vnd.rar', [[0, 'Rar!']]],
    ['application/x-7z-compressed', [[0, "7z\274\257'\034"]]],
    ['application/x-ace', [[7, '**ACE**']]],
    ['application/x-arc', []],
    ['application/x-cpio', [[0, "\307q"], [0, '070701'], [0, '070702'], [0, "q\307"]]],
    ['application/x-font-type1', [[0, 'LWFN'], [65, 'LWFN'], [0, '%!PS-AdobeFont-1.'], [6, '%!PS-AdobeFont-1.'], [0, '%!FontType1-1.'], [6, '%!FontType1-1.']]],
    ['application/x-java-pack200', [[0, "\312\376\320\r"]]],
    ['application/x-karbon', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-karbon\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-karbon']]]]]]],
    ['application/x-kchart', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kchart\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kchart']]]]]]],
    ['application/x-kformula', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kformula\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kformula']]]]]]],
    ['application/x-killustrator', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-killustrator\004\006"]]]]]]],
    ['application/x-kivio', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kivio\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kivio']]]]]]],
    ['application/x-kontour', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kontour\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kontour']]]]]]],
    ['application/x-kpresenter', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kpresenter\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kpresenter']]]]]]],
    ['application/x-krita', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-krita\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-krita']]]]]]],
    ['application/x-kspread', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kspread\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kspread']]]]]]],
    ['application/x-kword', [[0, "\037\213", [[10, 'KOffice', [[18, "application/x-kword\004\006"]]]]], [0, "PK\003\004", [[30, 'mimetype', [[38, 'application/x-kword']]]]]]],
    ['application/x-lha', [[2, '-lh -'], [2, '-lh0-'], [2, '-lh1-'], [2, '-lh2-'], [2, '-lh3-'], [2, '-lh4-'], [2, '-lh5-'], [2, '-lh40-'], [2, '-lhd-'], [2, '-lz4-'], [2, '-lz5-'], [2, '-lzs-']]],
    ['application/x-lrzip', [[0, 'LRZI']]],
    ['application/x-lz4', [[0, "\004\"M\030"], [0, "\002!L\030"]]],
    ['application/x-lzip', [[0, 'LZIP']]],
    ['application/x-lzop', [[0, "\211LZO\000\r\n\032\n"]]],
    ['application/x-par2', [[0, 'PAR2']]],
    ['application/x-qpress', [[0, 'qpress10']]],
    ['application/x-quicktime-media-link', [[0, '<?xml', [[0..64, '<?quicktime']]], [0, 'RTSPtext'], [0, 'rtsptext'], [0, 'SMILtext']]],
    ['application/x-sega-cd-rom', [[0, 'SEGADISCSYSTEM', [[256, 'SEGA']]], [16, 'SEGADISCSYSTEM', [[272, 'SEGA']]]]],
    ['application/x-stuffit', [[0, 'StuffIt '], [0, 'SIT!']]],
    ['application/x-tar', [[257, "ustar\000"], [257, "ustar  \000"]]],
    ['application/x-xar', [[0, 'xar!']]],
    ['application/x-xz', [[0, "\3757zXZ\000"]]],
    ['application/x-zoo', [[20, "\334\247\304\375"]]],
    ['application/xhtml+xml', [[0..256, '//W3C//DTD XHTML '], [0..256, 'http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd'], [0..256, "<html xmlns=\"http://www.w3.org/1999/xhtml"], [0..256, "<HTML xmlns=\"http://www.w3.org/1999/xhtml"]]],
    ['application/zip', [[0, "PK\003\004"]]],
    ['audio/vnd.dts.hd', [[0..18725, 'dX %']]],
    ['text/x-python3', [[0, '#!/bin/python3'], [0, '#! /bin/python3'], [0, "eval \"exec /bin/python3"], [0, '#!/usr/bin/python3'], [0, '#! /usr/bin/python3'], [0, "eval \"exec /usr/bin/python3"], [0, '#!/usr/local/bin/python3'], [0, '#! /usr/local/bin/python3'], [0, "eval \"exec /usr/local/bin/python3"], [2..16, '/bin/env python3']]],
    ['text/x-txt2tags', [[0, '%!postproc'], [0, '%!encoding']]],
    ['application/smil+xml', [[0..256, '<smil']]],
    ['audio/x-ms-asx', [[0, 'ASF '], [0..64, '<ASX'], [0..64, '<asx'], [0..64, '<Asx']]],
    ['application/annodex', [[0, 'OggS', [[28, "fishead\000", [[56..512, "CMML\000\000\000\000"]]]]]]],
    ['application/dicom', [[128, 'DICM']]],
    ['application/gnunet-directory', [[0, "\211GND\r\n\032\n"]]],
    ['application/gzip', [[0, "\037\213"]]],
    ['application/javascript', [[0, '#!/bin/gjs'], [0, '#! /bin/gjs'], [0, "eval \"exec /bin/gjs"], [0, '#!/usr/bin/gjs'], [0, '#! /usr/bin/gjs'], [0, "eval \"exec /usr/bin/gjs"], [0, '#!/usr/local/bin/gjs'], [0, '#! /usr/local/bin/gjs'], [0, "eval \"exec /usr/local/bin/gjs"], [2..16, '/bin/env gjs']]],
    ['application/mac-binhex40', [[11, 'must be converted with BinHex']]],
    ['application/mathematica', [[0, '(************** Content-type: application/mathematica'], [100..256, 'This notebook can be used on any computer system with Mathematica'], [10..256, 'This is a Mathematica Notebook file.  It contains ASCII text']]],
    ['application/metalink+xml', [[0..256, "<metalink version=\"3.0\""]]],
    ['application/metalink4+xml', [[0..256, "<metalink xmlns=\"urn"]]],
    ['application/mxf', [[0..256, "\006\016+4\002\005\001\001\r\001\002\001\001\002"]]],
    ['application/ogg', [[0, 'OggS']]],
    ['application/owl+xml', [[0..256, '<Ontology']]],
    ['application/pgp-encrypted', [[0, '-----BEGIN PGP MESSAGE-----']]],
    ['application/pgp-keys', [[0, '-----BEGIN PGP PUBLIC KEY BLOCK-----'], [0, '-----BEGIN PGP PRIVATE KEY BLOCK-----'], [0, "\225\001"], [0, "\225\000"], [0, "\231\000"], [0, "\231\001"]]],
    ['application/pgp-signature', [[0, '-----BEGIN PGP SIGNATURE-----']]],
    ['application/postscript', [[0, "\004%!"], [0, '%!']]],
    ['application/raml+yaml', [[0, '#%RAML ']]],
    ['application/rtf', [[0, "{\\rtf"]]],
    ['application/sdp', [[0, 'v=', [[0..256, 's=']]]]],
    ['application/vnd.adobe.flash.movie', [[0, 'FWS'], [0, 'CWS']]],
    ['application/vnd.appimage', [[1, 'ELF', [[8, 'A', [[9, 'I', [[10, "\002"]]]]]]]]],
    ['application/vnd.chess-pgn', [[0, '[Event ']]],
    ['application/vnd.debian.binary-package', [[0, '!<arch>', [[8, 'debian']]]]],
    ['application/vnd.emusic-emusic_package', [[0, 'nF7YLao']]],
    ['application/vnd.flatpak', [[0, "xdg-app\000\001\000\211\345"], [0, "flatpak\000\001\000\211\345"]]],
    ['application/vnd.flatpak.ref', [[0..256, '[Flatpak Ref]']]],
    ['application/vnd.flatpak.repo', [[0..256, '[Flatpak Repo]']]],
    ['application/vnd.framemaker', [[0, '<MakerFile'], [0, '<MIFFile'], [0, '<MakerDictionary'], [0, '<MakerScreenFon'], [0, '<MML'], [0, '<Book'], [0, '<Maker']]],
    ['application/vnd.iccprofile', [[36, 'acsp']]],
    ['application/vnd.lotus-1-2-3', [[0, "\000\000\002\000\006\004\006\000\b\000\000\000\000\000"]]],
    ['application/vnd.lotus-wordpro', [[0, 'WordPro']]],
    ['application/vnd.ms-access', [[0, "\000\001\000\000Standard Jet DB"]]],
    ['application/vnd.ms-asf', [[0, "0&\262u"], [0, '[Reference]']]],
    ['application/vnd.ms-tnef', [[0, "x\237>\""]]],
    ['application/vnd.rn-realmedia', [[0, '.RMF']]],
    ['application/vnd.sqlite3', [[0, 'SQLite format 3']]],
    ['application/vnd.squashfs', [[0, 'sqsh'], [0, 'hsqs']]],
    ['application/vnd.symbian.install', [[8, "\031\004\000\020"]]],
    ['application/vnd.tcpdump.pcap', [[0, "\324\303\262\241"], [0, "\241\262\303\324"]]],
    ['application/vnd.wordperfect', [[1, 'WPC']]],
    ['application/winhlp', [[0, "?_\003\000"]]],
    ['application/x-abiword', [[0..256, '<abiword'], [0..256, '<!DOCTYPE abiword']]],
    ['application/x-alz', [[0, 'ALZ']]],
    ['application/x-amiga-disk-format', [[0, "DOS\000"]]],
    ['application/x-aportisdoc', [[60, 'TEXtREAd'], [60, 'TEXtTlDc']]],
    ['application/x-applix-spreadsheet', [[0, '*BEGIN SPREADSHEETS'], [0, '*BEGIN', [[7, 'SPREADSHEETS']]]]],
    ['application/x-applix-word', [[0, '*BEGIN', [[7, 'WORDS']]]]],
    ['application/x-arj', [[0, "`\352"]]],
    ['application/x-atari-7800-rom', [[1, 'ATARI7800']]],
    ['application/x-atari-lynx-rom', [[0, 'LYNX']]],
    ['application/x-awk', [[0, '#!/bin/gawk'], [0, '#! /bin/gawk'], [0, '#!/usr/bin/gawk'], [0, '#! /usr/bin/gawk'], [0, '#!/usr/local/bin/gawk'], [0, '#! /usr/local/bin/gawk'], [0, '#!/bin/awk'], [0, '#! /bin/awk'], [0, '#!/usr/bin/awk'], [0, '#! /usr/bin/awk']]],
    ['application/x-bittorrent', [[0, 'd8:announce']]],
    ['application/x-blender', [[0, 'BLENDER']]],
    ['application/x-bsdiff', [[0, 'BSDIFF40'], [0, 'BSDIFN40']]],
    ['application/x-bzip', [[0, 'BZh']]],
    ['application/x-ccmx', [[0, 'CCMX']]],
    ['application/x-cdrdao-toc', [[0, "CD_ROM\n"], [0, "CD_DA\n"], [0, "CD_ROM_XA\n"], [0, 'CD_TEXT '], [0, "CATALOG \"", [[22, "\""]]]]],
    ['application/x-cisco-vpn-settings', [[0, '[main]', [[0..256, 'AuthType=']]]]],
    ['application/x-compress', [[0, "\037\235"]]],
    ['application/x-core', [[0, "\177ELF", [[5, "\001", [[16, "\004\000"]]]]], [0, "\177ELF", [[5, "\002", [[16, "\000\004"]]]]], [0, "Core\001"], [0, "Core\002"]]],
    ['application/x-csh', [[2..16, '/bin/tcsh'], [2..16, '/bin/csh'], [2..16, '/bin/env csh'], [2..16, '/bin/env tcsh']]],
    ['application/x-dar', [[0, "\000\000\000{"]]],
    ['application/x-designer', [[0..256, '<ui '], [0..256, '<UI ']]],
    ['application/x-desktop', [[0..32, '[Desktop Entry]'], [0, '[Desktop Action'], [0, '[KDE Desktop Entry]'], [0, '# Config File'], [0, '# KDE Config File']]],
    ['application/x-dia-diagram', [[5..100, '<dia:']]],
    ['application/x-dia-shape', [[5..100, '<shape']]],
    ['application/x-doom-wad', [[0, 'IWAD'], [0, 'PWAD']]],
    ['application/x-dvi', [[0, "\367\002"]]],
    ['application/x-fds-disk', [[1, '*NINTENDO-HVC*']]],
    ['application/x-fluid', [[0, '# data file for the Fltk']]],
    ['application/x-font-bdf', [[0, 'STARTFONT ']]],
    ['application/x-font-dos', [[0, "\377FON"], [7, "\000EGA"], [7, "\000VID"]]],
    ['application/x-font-framemaker', [[0, '<MakerScreenFont']]],
    ['application/x-font-libgrx', [[0, "\024\002Y\031"]]],
    ['application/x-font-linux-psf', [[0, "6\004"]]],
    ['application/x-font-pcf', [[0, "\001fcp"]]],
    ['application/x-font-speedo', [[0, "D1.0\r"]]],
    ['application/x-font-sunos-news', [[0, 'StartFont'], [0, "\023z)"], [8, "\023z+"]]],
    ['application/x-font-tex', [[0, "\367\203"], [0, "\367Y"], [0, "\367\312"]]],
    ['application/x-font-tex-tfm', [[2, "\000\021"], [2, "\000\022"]]],
    ['application/x-font-ttx', [[0..256, "<ttFont sfntVersion=\"\\000\\001\\000\\000\" ttLibVersion=\""]]],
    ['application/x-font-vfont', [[0, 'FONT']]],
    ['application/x-gameboy-color-rom', [[260, "\316\355ff\314\r\000\v\003s\000\203\000\f\000\r\000\b"]]],
    ['application/x-gameboy-rom', [[260, "\316\355ff\314\r\000\v\003s\000\203\000\f\000\r\000\b\021\037\210\211\000\016"]]],
    ['application/x-gamecube-rom', [[28, "\3023\237="]]],
    ['application/x-gdbm', [[0, "\023W\232\316"], [0, "\316\232W\023"], [0, 'GDBM']]],
    ['application/x-gedcom', [[0, '0 HEAD']]],
    ['application/x-genesis-32x-rom', [[256, 'SEGA 32X']]],
    ['application/x-genesis-rom', [[256, 'SEGA GENESIS'], [256, 'SEGA MEGA DRIVE'], [640, 'EAGN'], [640, 'EAMG']]],
    ['application/x-gettext-translation', [[0, "\336\022\004\225"], [0, "\225\004\022\336"]]],
    ['application/x-glade', [[0..256, '<glade-interface']]],
    ['application/x-gnumeric', [[0..64, 'gmr:Workbook'], [0..64, 'gnm:Workbook']]],
    ['application/x-go-sgf', [[0, '(;FF[3]'], [0, '(;FF[4]']]],
    ['application/x-gtk-builder', [[0..256, '<interface']]],
    ['application/x-gtktalog', [[4, 'gtktalog ']]],
    ['application/x-hdf', [[0, "\211HDF\r\n\032\n"], [0, "\016\003\023\001"]]],
    ['application/x-hfe-floppy-image', [[0, 'HXCPICFE']]],
    ['application/x-hwp', [[0, 'HWP Document File']]],
    ['application/x-ipod-firmware', [[0, 'S T O P']]],
    ['application/x-ipynb+json', [[0, '{', [[1..256, "\"cells\":"]]]]],
    ['application/x-iso9660-appimage', [[1, 'ELF', [[8, 'A', [[9, 'I', [[10, "\001"]]]]]]]]],
    ['application/x-it87', [[0, 'IT8.7']]],
    ['application/x-java', [[0, "\312\376\272\276"]]],
    ['application/x-java-jce-keystore', [[0, "\316\316\316\316"]]],
    ['application/x-java-jnlp-file', [[0..256, '<jnlp']]],
    ['application/x-java-keystore', [[0, "\376\355\376\355"]]],
    ['application/x-kspread-crypt', [[0, "\r\032'\002"]]],
    ['application/x-ksysv-package', [[4, 'KSysV', [[15, "\001"]]]]],
    ['application/x-kword-crypt', [[0, "\r\032'\001"]]],
    ['application/x-lyx', [[0, '#LyX']]],
    ['application/x-macbinary', [[102, 'mBIN']]],
    ['application/x-matroska', [[0, "\032E\337\243", [[5..65, "B\202", [[8..75, 'matroska']]]]]]],
    ['application/x-ms-dos-executable', [[0, 'MZ']]],
    ['application/x-ms-wim', [[0, "MSWIM\000\000\000"]]],
    ['application/x-mswinurl', [[1, 'InternetShortcut'], [1, 'DEFAULT', [[11, 'BASEURL=']]]]],
    ['application/x-n64-rom', [[0, "\2007\022@"], [0, "7\200@\022"], [0, "@\0227\200"]]],
    ['application/x-nautilus-link', [[0..32, '<nautilus_object nautilus_link']]],
    ['application/x-navi-animation', [[0, 'RIFF', [[8, 'ACON']]]]],
    ['application/x-neo-geo-pocket-color-rom', [[35, "\020", [[0, 'COPYRIGHT BY SNK CORPORATION'], [0, ' LICENSED BY SNK CORPORATION']]]]],
    ['application/x-neo-geo-pocket-rom', [[35, "\000", [[0, 'COPYRIGHT BY SNK CORPORATION'], [0, ' LICENSED BY SNK CORPORATION']]]]],
    ['application/x-netshow-channel', [[0, '[Address]']]],
    ['application/x-object', [[0, "\177ELF", [[5, "\001", [[16, "\001\000"]]]]], [0, "\177ELF", [[5, "\002", [[16, "\000\001"]]]]]]],
    ['application/x-ole-storage', [[0, "\320\317\021\340\241\261\032\341"], [0, "\320\317\021\340"]]],
    ['application/x-oleo', [[31, 'Oleo']]],
    ['application/x-pef-executable', [[0, 'Joy!']]],
    ['application/x-perl', [[0, "eval \"exec /usr/local/bin/perl"], [2..16, '/bin/perl'], [2..16, '/bin/env perl'], [0..256, 'use strict'], [0..256, 'use warnings'], [0..256, 'use diagnostics'], [0..256, 'use Test::'], [0..256, 'BEGIN {']]],
    ['application/x-pocket-word', [[0, "{\\pwi"]]],
    ['application/x-python-bytecode', [[0, "\231N\r\n"]]],
    ['application/x-qtiplot', [[0, 'QtiPlot']]],
    ['application/x-rpm', [[0, "\355\253\356\333"]]],
    ['application/x-ruby', [[2..16, '/bin/env ruby'], [2..16, '/bin/ruby']]],
    ['application/x-sami', [[0..256, '<SAMI>']]],
    ['application/x-saturn-rom', [[0, 'SEGA SEGASATURN'], [16, 'SEGA SEGASATURN']]],
    ['application/x-sc', [[38, 'Spreadsheet']]],
    ['application/x-sega-pico-rom', [[256, 'SEGA PICO']]],
    ['application/x-sharedlib', [[0, "\177ELF", [[5, "\001", [[16, "\003\000"]]]]], [0, "\177ELF", [[5, "\002", [[16, "\000\003"]]]]], [0, "\203\001"]]],
    ['application/x-shellscript', [[10, '# This is a shell archive'], [2..16, '/bin/bash'], [2..16, '/bin/nawk'], [2..16, '/bin/zsh'], [2..16, '/bin/sh'], [2..16, '/bin/ksh'], [2..16, '/bin/dash'], [2..16, '/bin/env sh'], [2..16, '/bin/env bash'], [2..16, '/bin/env zsh'], [2..16, '/bin/env ksh']]],
    ['application/x-shorten', [[0, 'ajkg']]],
    ['application/x-smaf', [[0, 'MMMD']]],
    ['application/x-spss-por', [[40, 'ASCII SPSS PORT FILE']]],
    ['application/x-spss-sav', [[0, '$FL2'], [0, '$FL3']]],
    ['application/x-sqlite2', [[0, '** This file contains an SQLite']]],
    ['application/x-subrip', [[0, '1', [[0..256, ' --> ']]]]],
    ['application/x-t602', [[0, '@CT 0'], [0, '@CT 1'], [0, '@CT 2']]],
    ['application/x-tgif', [[0, '%TGIF']]],
    ['application/x-thomson-sap-image', [[1, "SYSTEME D'ARCHIVAGE PUKALL S.A.P. (c) Alexandre PUKALL Avril 1998"]]],
    ['application/x-wii-rom', [[24, "]\034\236\243"], [0, 'WBFS'], [0, "WII\001DISC"]]],
    ['application/x-wii-wad', [[4, "Is\000\000"], [4, "ib\000\000"], [4, "Bk\000\000"]]],
    ['application/x-xbel', [[0..256, '<!DOCTYPE xbel']]],
    ['application/x-yaml', [[0, '%YAML']]],
    ['application/xslt+xml', [[0..256, '<xsl:stylesheet']]],
    ['application/xspf+xml', [[0..64, "<playlist version=\"1"], [0..64, "<playlist version='1"]]],
    ['audio/AMR', [[0, "#!AMR\n"], [0, "#!AMR_MC1.0\n"]]],
    ['audio/AMR-WB', [[0, "#!AMR-WB\n"], [0, "#!AMR-WB_MC1.0\n"]]],
    ['audio/ac3', [[0, "\vw"]]],
    ['audio/annodex', [[0, 'OggS', [[28, "fishead\000", [[56..512, "CMML\000\000\000\000"]]]]]]],
    ['audio/midi', [[0, 'MThd']]],
    ['audio/prs.sid', [[0, 'PSID']]],
    ['audio/vnd.dts', [[0, "\177\376\200\001"], [0, "\200\001\177\376"], [0, "\037\377\350\000"], [0, "\350\000\037\377"]]],
    ['audio/x-adpcm', [[0, '.snd', [[12, "\000\000\000\027"]]], [0, ".sd\000", [[12, "\001\000\000\000"], [12, "\002\000\000\000"], [12, "\003\000\000\000"], [12, "\004\000\000\000"], [12, "\005\000\000\000"], [12, "\006\000\000\000"], [12, "\a\000\000\000"], [12, "\027\000\000\000"]]]]],
    ['audio/x-aifc', [[8, 'AIFC']]],
    ['audio/x-aiff', [[8, 'AIFF'], [8, '8SVX']]],
    ['audio/x-ape', [[0, 'MAC ']]],
    ['audio/x-iriver-pla', [[4, 'iriver UMS PLA']]],
    ['audio/x-it', [[0, 'IMPM']]],
    ['audio/x-m4b', [[4, 'ftypM4B']]],
    ['audio/x-mo3', [[0, 'MO3']]],
    ['audio/x-mpegurl', [[0, '#EXTM3U']]],
    ['audio/x-musepack', [[0, 'MP+']]],
    ['audio/x-pn-audibleaudio', [[4, "W\220u6"], [8, 'aax ']]],
    ['audio/x-psf', [[0, 'PSF']]],
    ['audio/x-s3m', [[44, 'SCRM']]],
    ['audio/x-scpls', [[0, '[playlist]'], [0, '[Playlist]'], [0, '[PLAYLIST]']]],
    ['audio/x-speex', [[0, 'Speex']]],
    ['audio/x-stm', [[20, "!Scream!\032"], [20, "!SCREAM!\032"], [20, "BMOD2STM\032"]]],
    ['audio/x-tta', [[0, 'TTA1']]],
    ['audio/x-wavpack', [[0, 'wvpk']]],
    ['audio/x-wavpack-correction', [[0, 'wvpk']]],
    ['audio/x-xi', [[0, 'Extended Instrument:']]],
    ['audio/x-xm', [[0, 'Extended Module:']]],
    ['audio/x-xmf', [[0, 'XMF_'], [0, "XMF_2.00\000\000\000\002"]]],
    ['font/otf', [[0, 'OTTO']]],
    ['font/ttf', [[0, 'FFIL'], [65, 'FFIL'], [0, "\000\001\000\000\000"]]],
    ['font/woff', [[0, 'wOFF']]],
    ['font/woff', [[0, 'wOF2']]],
    ['image/dpx', [[0, 'SDPX']]],
    ['image/emf', [[0, "\001\000\000\000", [[40, ' EMF', [[44, "\000\000\001\000", [[58, "\000\000"]]]]]]]]],
    ['image/fits', [[0, 'SIMPLE  =']]],
    ['image/jp2', []],
    ['image/jpm', []],
    ['image/jpx', []],
    ['image/vnd.dxf', [[0..64, "\nHEADER\n"], [0..64, "\r\nHEADER\r\n"]]],
    ['image/vnd.microsoft.icon', [[0, "\000\000\001\000", [[5, "\000"]]]]],
    ['image/vnd.ms-modi', [[0, "EP*\000"]]],
    ['image/vnd.zbrush.pcx', [[0, "\n", [[1, "\000"], [1, "\002"], [1, "\003"], [1, "\005"]]]]],
    ['image/wmf', [[0, "\327\315\306\232", [[22, "\001\000", [[24, "\t\000"]]]]], [0, "\001\000", [[2, "\t\000"]]]]],
    ['image/x-applix-graphics', [[0, '*BEGIN', [[7, 'GRAPHICS']]]]],
    ['image/x-canon-crw', [[0, "II\032\000\000\000HEAPCCDR"]]],
    ['image/x-dds', [[0, 'DDS']]],
    ['image/x-dib', [[0, "(\000\000\000"]]],
    ['image/x-exr', [[0, "v/1\001"]]],
    ['image/x-fpx', [[0, 'FPix']]],
    ['image/x-fuji-raf', [[0, 'FUJIFILMCCD-RAW ']]],
    ['image/x-gimp-gbr', [[20, 'GIMP']]],
    ['image/x-gimp-pat', [[20, 'GPAT']]],
    ['image/x-icns', [[0, 'icns']]],
    ['image/x-ilbm', [[8, 'ILBM'], [8, 'PBM ']]],
    ['image/x-jp2-codestream', [[0, "\377O\377Q"]]],
    ['image/x-minolta-mrw', [[0, "\000MRM"]]],
    ['image/x-olympus-orf', [[0, "IIRO\b\000\000\000"]]],
    ['image/x-panasonic-rw', [[0, "IIU\000\b\000\000\000"]]],
    ['image/x-panasonic-rw2', [[0, "IIU\000\030\000\000\000"]]],
    ['image/x-pict', [[10, "\000\021", [[12, "\002\377", [[14, "\f\000", [[16, "\377\376"]]]]]]]]],
    ['image/x-pict', [[522, "\000\021", [[524, "\002\377", [[526, "\f\000", [[528, "\377\376"]]]]]]]]],
    ['image/x-portable-bitmap', [[0, 'P1', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]], [0, 'P4', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]]]],
    ['image/x-portable-graymap', [[0, 'P2', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]], [0, 'P5', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]]]],
    ['image/x-portable-pixmap', [[0, 'P3', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]], [0, 'P6', [[2, "\n"], [2, ' '], [2, "\t"], [2, "\r"]]]]],
    ['image/x-quicktime', [[4, 'idat']]],
    ['image/x-sigma-x3f', [[0, 'FOVb']]],
    ['image/x-skencil', [[0, '##Sketch']]],
    ['image/x-sun-raster', [[0, "Y\246j\225"]]],
    ['image/x-tga', [[1, "\000\002", [[16, "\b"], [16, "\020"], [16, "\030"], [16, ' ']]]]],
    ['image/x-win-bitmap', [[0, "\000\000\002\000", [[5, "\000"]]]]],
    ['image/x-xcf', [[0, 'gimp xcf file'], [0, 'gimp xcf v']]],
    ['image/x-xcursor', [[0, 'Xcur']]],
    ['image/x-xfig', [[0, '#FIG']]],
    ['image/x-xpixmap', [[0, '/* XPM']]],
    ['message/news', [[0, 'Article'], [0, 'Path:'], [0, 'Xref:']]],
    ['message/rfc822', [[0, '#! rnews'], [0, 'Forward to'], [0, 'From:'], [0, 'N#! rnews'], [0, 'Pipe to'], [0, 'Received:'], [0, 'Relay-Version:'], [0, 'Return-Path:'], [0, 'Return-path:'], [0, 'Subject: ']]],
    ['model/iges', [[72, "S      1\n"], [72, "S0000001\n"]]],
    ['model/stl', [[0, 'solid'], [0, 'SOLID']]],
    ['model/vrml', [[0, '#VRML ']]],
    ['text/cache-manifest', [[0, 'CACHE MANIFEST', [[14, ' '], [14, "\t"], [14, "\n"], [14, "\r"]]]]],
    ['text/calendar', [[0, 'BEGIN:VCALENDAR'], [0, 'begin:vcalendar']]],
    ['text/html', [[0..256, '<!DOCTYPE HTML'], [0..256, '<!doctype html'], [0..256, '<HEAD'], [0..256, '<head'], [0..256, '<TITLE'], [0..256, '<title'], [0..256, '<HTML'], [0..256, '<html'], [0..256, '<SCRIPT'], [0..256, '<script'], [0, '<BODY'], [0, '<body'], [0, '<!--'], [0, '<h1'], [0, '<H1'], [0, '<!doctype HTML'], [0, '<!DOCTYPE html']]],
    ['text/plain', [[0, 'This is TeX,'], [0, 'This is METAFONT,']]],
    ['text/spreadsheet', [[0, 'ID;']]],
    ['text/troff', [[0, ".\\\""], [0, "'\\\""], [0, "'.\\\""], [0, "\\\""]]],
    ['text/vcard', [[0, 'BEGIN:VCARD'], [0, 'begin:vcard']]],
    ['text/vnd.graphviz', [[0, 'digraph '], [0, 'strict digraph '], [0, 'graph '], [0, 'strict graph ']]],
    ['text/vnd.qt.linguist', [[0..256, '<TS']]],
    ['text/vnd.sun.j2me.app-descriptor', [[0, 'MIDlet-']]],
    ['text/vtt', [[0, 'WEBVTT']]],
    ['text/x-bibtex', [[0, '% This file was created with JabRef']]],
    ['text/x-dbus-service', [[0..256, "\n[D-BUS Service]\n"], [0, "[D-BUS Service]\n"]]],
    ['text/x-emacs-lisp', [[0, "\n("], [0, ";ELC\023\000\000\000"]]],
    ['text/x-gettext-translation-template', [[0..256, "#, fuzzy\nmsgid \"\"\nmsgstr \"\"\n\"Project-Id-Version:"]]],
    ['text/x-google-video-pointer', [[0, '#.download.the.free.Google.Video.Player'], [0, '# download the free Google Video Player']]],
    ['text/x-iMelody', [[0, 'BEGIN:IMELODY']]],
    ['text/x-iptables', [[0..256, '/etc/sysconfig/iptables'], [0..256, '*filter', [[0..256, ':INPUT', [[0..256, ':FORWARD', [[0..256, ':OUTPUT']]]]]]], [0..256, '-A INPUT', [[0..256, '-A FORWARD', [[0..256, '-A OUTPUT']]]]], [0..256, '-P INPUT', [[0..256, '-P FORWARD', [[0..256, '-P OUTPUT']]]]]]],
    ['text/x-ldif', [[0, 'dn: cn='], [0, 'dn: mail=']]],
    ['text/x-lua', [[2..16, '/bin/lua'], [2..16, '/bin/luajit'], [2..16, '/bin/env lua'], [2..16, '/bin/env luajit']]],
    ['text/x-makefile', [[0, '#!/usr/bin/make'], [0, '#! /usr/bin/make']]],
    ['text/x-matlab', [[0, 'function']]],
    ['text/x-microdvd', [[0, '{1}'], [0, '{0}'], [0..6, '}{']]],
    ['text/x-modelica', [[0, 'function']]],
    ['text/x-modelica', [[0, 'class']]],
    ['text/x-modelica', [[0, 'model']]],
    ['text/x-modelica', [[0, 'record']]],
    ['text/x-mpsub', [[0..256, 'FORMAT=']]],
    ['text/x-mrml', [[0, '<mrml ']]],
    ['text/x-ms-regedit', [[0, 'REGEDIT'], [0, 'Windows Registry Editor Version 5.00'], [0, "\377\376W\000i\000n\000d\000o\000w\000s\000 \000R\000e\000g\000i\000s\000t\000r\000y\000 \000E\000d\000i\000t\000o\000r\000"]]],
    ['text/x-mup', [[0, '//!Mup']]],
    ['text/x-patch', [[0, "diff\t"], [0, 'diff '], [0, "***\t"], [0, '*** '], [0, '=== '], [0, '--- '], [0, "Only in\t"], [0, 'Only in '], [0, 'Common subdirectories: '], [0, 'Index:']]],
    ['text/x-python', [[0, '#!/bin/python'], [0, '#! /bin/python'], [0, "eval \"exec /bin/python"], [0, '#!/usr/bin/python'], [0, '#! /usr/bin/python'], [0, "eval \"exec /usr/bin/python"], [0, '#!/usr/local/bin/python'], [0, '#! /usr/local/bin/python'], [0, "eval \"exec /usr/local/bin/python"], [2..16, '/bin/env python']]],
    ['text/x-rpm-spec', [[0, 'Summary: '], [0, '%define ']]],
    ['text/x-ssa', [[0..256, '[Script Info]'], [0..256, 'Dialogue: ']]],
    ['text/x-subviewer', [[0, '[INFORMATION]']]],
    ['text/x-systemd-unit', [[0..256, "\n[Unit]\n"], [0..256, "\n[Install]\n"], [0..256, "\n[Automount]\n"], [0..256, "\n[Mount]\n"], [0..256, "\n[Path]\n"], [0..256, "\n[Scope]\n"], [0..256, "\n[Service]\n"], [0..256, "\n[Slice]\n"], [0..256, "\n[Socket]\n"], [0..256, "\n[Swap]\n"], [0..256, "\n[Timer]\n"], [0, "[Unit]\n"], [0, "[Install]\n"], [0, "[Automount]\n"], [0, "[Mount]\n"], [0, "[Path]\n"], [0, "[Scope]\n"], [0, "[Service]\n"], [0, "[Slice]\n"], [0, "[Socket]\n"], [0, "[Swap]\n"], [0, "[Timer]\n"]]],
    ['text/x-tex', [[1, 'documentclass']]],
    ['text/x-uuencode', [[0, 'begin ']]],
    ['text/xmcd', [[0, '# xmcd']]],
    ['video/3gpp', [[4, 'ftyp3ge'], [4, 'ftyp3gg'], [4, 'ftyp3gp'], [4, 'ftyp3gs']]],
    ['video/3gpp2', [[4, 'ftyp3g2']]],
    ['video/annodex', [[0, 'OggS', [[28, "fishead\000", [[56..512, "CMML\000\000\000\000"]]]]]]],
    ['video/dv', []],
    ['video/mj2', []],
    ['video/mp2t', [[0, 'G', [[188, 'G', [[376, 'G', [[564, 'G', [[752, 'G']]]]]]]]], [4, 'G', [[196, 'G', [[388, 'G', [[580, 'G', [[772, 'G']]]]]]]]]]],
    ['video/vnd.mpegurl', [[0, '#EXTM4U']]],
    ['video/x-flic', [[0, "\021\257"], [0, "\022\257"]]],
    ['video/x-mng', [[0, "\212MNG\r\n\032\n"]]],
    ['video/x-nsv', [[0, 'NSVf']]],
    ['video/x-sgi-movie', [[0, 'MOVI']]],
    ['x-epoc/x-sisx-app', [[0, "z\032 \020"]]],
    ['application/x-archive', [[0, '<ar>'], [0, '!<arch>']]],
    ['application/x-riff', [[0, 'RIFF']]],
    ['application/x-executable', [[0, "\177ELF", [[5, "\001", [[16, "\002\000"]]]]], [0, "\177ELF", [[5, "\002", [[16, "\000\002"]]]]], [0, 'MZ'], [0, "\034R"], [0, "\020\001"], [0, "\021\001"], [0, "\203\001"]]],
    ['application/x-iff', [[0, 'FORM']]],
    ['application/x-perl', [[0..256, "\n=pod"], [0..256, "\n=head1 NAME"], [0..256, "\n=head1 DESCRIPTION"]]],
    ['application/xml', [[0, '<?xml'], [0, '<!--']]],
    ['audio/basic', [[0, '.snd']]],
    ['audio/x-mod', [[0, 'MTM'], [0, 'MMD0'], [0, 'MMD1'], [0, 'if', [[110, '@', [[111, "\200"]]]]], [0, 'JN', [[110, '@', [[111, "\200"]]]]], [0, 'MAS_UTrack_V00']]],
    ['video/x-javafx', [[0, 'FLV']]],
    ['application/x-mobipocket-ebook', [[60, 'TEXtREAd']]],
    ['text/x-csrc', [[0, '/*'], [0, '//'], [0, '#include']]],
    ['text/x-objcsrc', [[0, '#import']]],
    ['application/mbox', [[0, 'From ']]],
    ['image/x-tga', [[1, "\001\001"], [1, "\001\t"], [1, "\000\003"], [1, "\000\n"], [1, "\000\v"]]],
    ['text/x-matlab', [[0, '%']]],
    ['text/x-matlab', [[0, '##']]],
    ['text/x-modelica', [[0, '//']]],
    ['text/x-tex', [[0, '%']]],
  ]
end

= Action View

Action View is a framework for handling view template lookup and rendering, and provides
view helpers that assist when building HTML forms, Atom feeds and more.
Template formats that Action View handles are ERB (embedded Ruby, typically
used to inline short Ruby snippets inside HTML), and XML Builder.

You can read more about Action View in the {Action View Overview}[https://edgeguides.rubyonrails.org/action_view_overview.html] guide.

== Download and installation

The latest version of Action View can be installed with RubyGems:

  $ gem install actionview

Source code can be downloaded as part of the Rails project on GitHub:

* https://github.com/rails/rails/tree/main/actionview


== License

Action View is released under the MIT license:

* https://opensource.org/licenses/MIT


== Support

API documentation is at

* https://api.rubyonrails.org

Bug reports for the Ruby on Rails project can be filed here:

* https://github.com/rails/rails/issues

Feature requests should be discussed on the rails-core mailing list here:

* https://discuss.rubyonrails.org/c/rubyonrails-core

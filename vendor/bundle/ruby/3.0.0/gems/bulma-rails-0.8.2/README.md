# bulma-rails

Integrates [Bulma](http://bulma.io/) with the rails asset pipeline.

A modern CSS framework based on Flexbox.

## Installation

Add this line to your application's Gemfile:

    gem "bulma-rails", "~> 0.8.2"

And then execute:

    $ bundle

## Usage

To import all assets in your Rails project, add the following line to your application.scss:
``` ruby
@import "bulma";
```

For information about customizing Bulma,
see: [http://bulma.io/documentation/overview/start/](http://bulma.io/documentation/overview/start/)

## Contributing

1. Fork it
2. Create your feature branch (`git checkout -b my-new-feature`)
3. Commit your changes (`git commit -am 'Added some feature'`)
4. Push to the branch (`git push origin my-new-feature`)
5. Create new Pull Request

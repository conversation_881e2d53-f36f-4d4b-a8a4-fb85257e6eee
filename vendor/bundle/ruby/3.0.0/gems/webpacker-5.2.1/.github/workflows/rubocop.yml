name: Rubocop

on: [push, pull_request]

jobs:
  build:
    name: Rubocop
    runs-on: ${{ matrix.os }}
    env:
      BUNDLE_JOBS: 4
      BUNDLE_RETRY: 3
    strategy:
      matrix:
        os: [ubuntu-latest]
        ruby: [
          2.7
        ]

    steps:
      - uses: actions/checkout@v2
      - uses: actions/cache@v2
        with:
          path: /home/<USER>/bundle
          key: bundle-use-ruby-gems-${{ hashFiles('**/Gemfile.lock') }}
          restore-keys: |
            bundle-use-ruby-gems-

      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}

      - name: Bundle install
        run: |
          gem install bundler -v 2.1.4
          bundle config path /home/<USER>/bundle
          bundle install

      - name: Ruby linter
        run: bundle exec rubocop

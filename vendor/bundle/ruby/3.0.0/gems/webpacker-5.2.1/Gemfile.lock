PATH
  remote: .
  specs:
    webpacker (5.2.1)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (6.0.3.2)
      actionpack (= 6.0.3.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (6.0.3.2)
      actionpack (= 6.0.3.2)
      activejob (= 6.0.3.2)
      activerecord (= 6.0.3.2)
      activestorage (= 6.0.3.2)
      activesupport (= 6.0.3.2)
      mail (>= 2.7.1)
    actionmailer (6.0.3.2)
      actionpack (= 6.0.3.2)
      actionview (= 6.0.3.2)
      activejob (= 6.0.3.2)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (6.0.3.2)
      actionview (= 6.0.3.2)
      activesupport (= 6.0.3.2)
      rack (~> 2.0, >= 2.0.8)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (6.0.3.2)
      actionpack (= 6.0.3.2)
      activerecord (= 6.0.3.2)
      activestorage (= 6.0.3.2)
      activesupport (= 6.0.3.2)
      nokogiri (>= 1.8.5)
    actionview (6.0.3.2)
      activesupport (= 6.0.3.2)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (6.0.3.2)
      activesupport (= 6.0.3.2)
      globalid (>= 0.3.6)
    activemodel (6.0.3.2)
      activesupport (= 6.0.3.2)
    activerecord (6.0.3.2)
      activemodel (= 6.0.3.2)
      activesupport (= 6.0.3.2)
    activestorage (6.0.3.2)
      actionpack (= 6.0.3.2)
      activejob (= 6.0.3.2)
      activerecord (= 6.0.3.2)
      marcel (~> 0.3.1)
    activesupport (6.0.3.2)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
      zeitwerk (~> 2.2, >= 2.2.2)
    ast (2.4.1)
    builder (3.2.4)
    byebug (11.1.3)
    concurrent-ruby (1.1.6)
    crass (1.0.6)
    erubi (1.9.0)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    i18n (1.8.5)
      concurrent-ruby (~> 1.0)
    jaro_winkler (1.5.4)
    loofah (2.6.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (0.3.3)
      mimemagic (~> 0.3.2)
    method_source (1.0.0)
    mimemagic (0.3.5)
    mini_mime (1.0.2)
    mini_portile2 (2.4.0)
    minitest (5.14.1)
    nio4r (2.5.2)
    nokogiri (1.10.10)
      mini_portile2 (~> 2.4.0)
    parallel (1.19.2)
    parser (2.7.1.4)
      ast (~> 2.4.1)
    rack (2.2.3)
    rack-proxy (0.6.5)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (6.0.3.2)
      actioncable (= 6.0.3.2)
      actionmailbox (= 6.0.3.2)
      actionmailer (= 6.0.3.2)
      actionpack (= 6.0.3.2)
      actiontext (= 6.0.3.2)
      actionview (= 6.0.3.2)
      activejob (= 6.0.3.2)
      activemodel (= 6.0.3.2)
      activerecord (= 6.0.3.2)
      activestorage (= 6.0.3.2)
      activesupport (= 6.0.3.2)
      bundler (>= 1.3.0)
      railties (= 6.0.3.2)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    railties (6.0.3.2)
      actionpack (= 6.0.3.2)
      activesupport (= 6.0.3.2)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.20.3, < 2.0)
    rainbow (3.0.0)
    rake (13.0.1)
    rubocop (0.68.1)
      jaro_winkler (~> 1.5.1)
      parallel (~> 1.10)
      parser (>= 2.5, != 2.5.1.1)
      rainbow (>= 2.2.2, < 4.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 1.6)
    rubocop-performance (1.3.0)
      rubocop (>= 0.68.0)
    ruby-progressbar (1.10.1)
    semantic_range (2.3.0)
    sprockets (4.0.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.1)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    thor (1.0.1)
    thread_safe (0.3.6)
    tzinfo (1.2.7)
      thread_safe (~> 0.1)
    unicode-display_width (1.5.0)
    websocket-driver (0.7.3)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.4.0)

PLATFORMS
  ruby

DEPENDENCIES
  bundler (>= 1.3.0)
  byebug
  minitest (~> 5.0)
  rack-proxy
  rails
  rake (>= 11.1)
  rubocop (< 0.69)
  rubocop-performance
  semantic_range
  webpacker!

BUNDLED WITH
   2.1.4

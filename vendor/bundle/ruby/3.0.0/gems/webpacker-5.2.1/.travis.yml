language: ruby
dist: xenial
before_install:
  - gem install rubygems-update && update_rubygems
  - yes | rvm @global do gem install bundler -v 2.1.4 || true
rvm:
  - 2.4.9
  - 2.5.5
  - 2.6.5
  - 2.7.0
  - ruby-head
gemfile:
  - gemfiles/Gemfile-rails.5.2.x
  - gemfiles/Gemfile-rails.6.0.x
  - gemfiles/Gemfile-rails-edge
cache:
  bundler: true
  directories:
    - node_modules
  yarn: true

install:
  - bundle install --jobs 3 --retry 3
  - nvm install 12
  - node -v
  - npm i -g yarn
  - yarn
script:
  - yarn lint
  - yarn test
  - bundle exec rubocop
  - bundle exec rake test
matrix:
  allow_failures:
    - gemfile: gemfiles/Gemfile-rails-edge
    - rvm: ruby-head
  exclude:
    - rvm: 2.4.9
      gemfile: gemfiles/Gemfile-rails-edge
    - rvm: 2.5.5
      gemfile: gemfiles/Gemfile-rails-edge
    - rvm: 2.4.9
      gemfile: gemfiles/Gemfile-rails.6.0.x

{"name": "@rails/webpacker", "version": "5.2.1", "description": "Use webpack to manage app-like JavaScript modules in Rails", "main": "package/index.js", "files": ["package", "lib/install/config/webpacker.yml"], "engines": {"node": ">=10.17.0", "yarn": ">=1 <2"}, "dependencies": {"@babel/core": "^7.11.1", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-object-rest-spread": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-destructuring": "^7.10.1", "@babel/plugin-transform-regenerator": "^7.10.1", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@babel/runtime": "^7.11.2", "babel-loader": "^8.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-macros": "^2.8.0", "case-sensitive-paths-webpack-plugin": "^2.3.0", "compression-webpack-plugin": "^4.0.0", "core-js": "^3.6.5", "css-loader": "^3.5.3", "file-loader": "^6.0.0", "flatted": "^3.0.4", "glob": "^7.1.6", "js-yaml": "^3.14.0", "mini-css-extract-plugin": "^0.9.0", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^5.0.3", "path-complete-extname": "^1.0.0", "pnp-webpack-plugin": "^1.6.4", "postcss-flexbugs-fixes": "^4.2.1", "postcss-import": "^12.0.1", "postcss-loader": "^3.0.0", "postcss-preset-env": "^6.7.0", "postcss-safe-parser": "^4.0.2", "regenerator-runtime": "^0.13.7", "sass-loader": "^8.0.2", "style-loader": "^1.2.1", "terser-webpack-plugin": "^4.0.0", "webpack": "^4.44.1", "webpack-assets-manifest": "^3.1.1", "webpack-cli": "^3.3.12", "webpack-sources": "^1.4.3"}, "devDependencies": {"eslint": "^7.6.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.5", "jest": "^26.2.2"}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/))\\.jsx?$", "roots": ["<rootDir>/package"]}, "scripts": {"test": "jest", "lint": "eslint package/"}, "repository": {"type": "git", "url": "git+https://github.com/rails/webpacker.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rails/webpacker/issues"}, "homepage": "https://github.com/rails/webpacker"}
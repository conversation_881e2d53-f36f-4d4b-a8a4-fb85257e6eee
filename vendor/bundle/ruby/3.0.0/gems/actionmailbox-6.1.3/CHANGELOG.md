## Rails 6.1.3 (February 17, 2021) ##

*   No changes.


## Rails ******* (February 10, 2021) ##

*   No changes.


## Rails 6.1.2 (February 09, 2021) ##

*   No changes.


## Rails 6.1.1 (January 07, 2021) ##

*   No changes.


## Rails 6.1.0 (December 09, 2020) ##

*   Change default queue name of the incineration (`:action_mailbox_incineration`) and
    routing (`:action_mailbox_routing`) jobs to be the job adapter's default (`:default`).

    *<PERSON>*

*   Sendgrid ingress now passes through the envelope recipient as `X-Original-To`.

    *<PERSON>*

*   Update Mandrill inbound email route to respond appropriately to HEAD requests for URL health checks from Mandrill.

    *<PERSON>*

*   Add way to deliver emails via source instead of filling out a form through the conductor interface.

    *DHH*

*   Mailgun ingress now passes through the envelope recipient as `X-Original-To`.

    *<PERSON><PERSON><PERSON>*

*   Deprecate `Rails.application.credentials.action_mailbox.api_key` and `MAILGUN_INGRESS_API_KEY` in favor of `Rails.application.credentials.action_mailbox.signing_key` and `MAILGUN_INGRESS_SIGNING_KEY`.

    *<PERSON><PERSON><PERSON><PERSON>*

*   Allow easier creation of multi-part emails from the `create_inbound_email_from_mail` and `receive_inbound_email_from_mail` test helpers.

    *<PERSON>ld*

*   Fix Bcc header not being included with emails from `create_inbound_email_from` test helpers.

    *jduff*

*   Add `ApplicationMailbox.mailbox_for` to expose mailbox routing.

    *James Dabbs*


Please check [6-0-stable](https://github.com/rails/rails/blob/6-0-stable/actionmailbox/CHANGELOG.md) for previous changes.

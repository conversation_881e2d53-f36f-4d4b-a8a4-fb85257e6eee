name: Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:

  build:
    strategy:
      matrix:
        ruby-version:
          # - "2.3"
          - "2.4"
          - "2.5"
          - "2.6"
          - "2.7"
        platform: [ubuntu-latest]

    runs-on: ${{ matrix.platform }}
    steps:

    - uses: actions/checkout@v2

    - name: Set up Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ matrix.ruby-version }}

    - name: Install dependencies
      run: bundle install

    - name: Run tests
      run: bundle exec rake

name: builder
document: http://builder.rubyforge.org
download: http://rubyforge.org/frs/?group_id=415
description: >
    <p>This package contains Builder, a simple ruby library for
    building XML document quickly and easily.</p>

    <p>Here's an example:</p>

    <pre>
      xml = Builder::XmlMarkup.new(:indent=>2)
      xml.person {
        xml.first_name("<PERSON>")
        xml.last_name("<PERSON><PERSON>")
      }
      puts xml.target!
    </pre>

    <p>Produces:</p>
    
    <pre>
      &lt;person&gt;
        &lt;first_name&gt;Jim&lt;/first_name&gt;
        &lt;last_name&gt;<PERSON><PERSON>&lt;/last_name&gt;
      &lt;/person&gt;
    </pre>


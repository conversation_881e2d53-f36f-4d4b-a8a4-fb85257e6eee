#!/usr/bin/env ruby
# frozen_string_literal: true
#--
# Copyright 2004, 2006 by <PERSON> (<EMAIL>).
# All rights reserved.

# Permission is granted for use, copying, modification, distribution,
# and distribution of modified versions of this work as long as the
# above copyright notice is included.
#++

######################################################################
# BlankSlate has been promoted to a top level name and is now
# available as a standalone gem.  We make the name available in the
# Builder namespace for compatibility.
#
module Builder
  if Object::const_defined?(:BasicObject)
    BlankSlate = ::BasicObject
  else
    require 'blankslate'
    BlankSlate = ::BlankSlate
  end
end

`Guard::LiveReload` is a volunteer effort, so the easier it is to reproduce your problem, the faster it may get solved.

Please try to provide enough info to reproduce a problem, including:

1. Running `bundle exec guard -d` to show full debugging output.

2. Any console output your browser shows.

3. Mentioning what you're doing and how (local/remote livereload, which browser, which OS, which extension, with `<PERSON><PERSON>`, `<PERSON><PERSON>`, `<PERSON><PERSON><PERSON>` or whatever, etc.).

3. Ideally, providing a small repository with your setup so we can reproduce the issue ourselves.

4. Going through our [Troubleshooting](https://github.com/guard/guard-livereload/wiki/Troubleshooting) guide to see if there's a quick fix for your problem already.

5. It's better to create a new issue (with full info above) than to add to an existing issue (unless you're absolutely sure it's exactly the same issue). It's better to link issues to each other instead.

That's it!

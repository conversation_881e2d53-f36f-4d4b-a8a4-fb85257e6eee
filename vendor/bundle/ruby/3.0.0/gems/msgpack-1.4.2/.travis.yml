language: ruby

branches:
  only:
    - master

gemfile:
  - Gemfile

before_install:
  # This is only for Ruby 2.5.0, Bundler 1.16.1 and rubygems 2.7.3
  # See https://github.com/travis-ci/travis-ci/issues/8969
  - "[ \"x2.7.3\" = \"x\"$(gem --version) ] && gem update --system --no-document || echo \"no need to update rubygem\""

# http://rubies.travis-ci.org/
matrix:
  include:
    - rvm: 2.4.5
      os: linux
    - rvm: 2.5.8
      os: linux
    - rvm: 2.6.6
      os: linux
    - rvm: 2.6.6
      os: osx
    - rvm: 2.7.1
      os: linux
    - rvm: ruby-head
      os: linux
    - rvm: jruby-head
      os: linux
    - rvm: jruby-19mode
      os: linux
  allow_failures:
    - rvm: 2.6.1
      os: osx
    - rvm: ruby-head
    - rvm: jruby-head
    - rvm: jruby-19mode

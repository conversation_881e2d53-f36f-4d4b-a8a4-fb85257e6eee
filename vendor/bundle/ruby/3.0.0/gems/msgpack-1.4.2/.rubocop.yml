# This configuration was generated by `rubocop --auto-gen-config`
# on 2015-03-09 17:42:55 +0100 using RuboCop version 0.29.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

AllCops:
  TargetRubyVersion: 2.4

# Offense count: 3
Lint/AmbiguousOperator:
  Enabled: false

# Offense count: 1
# Configuration parameters: AllowSafeAssignment.
Lint/AssignmentInCondition:
  Enabled: false

# Offense count: 1
Security/Eval:
  Enabled: false

# Offense count: 3
# Cop supports --auto-correct.
Lint/UnusedBlockArgument:
  Enabled: false

# Offense count: 35
# Cop supports --auto-correct.
Lint/UnusedMethodArgument:
  Enabled: false

# Offense count: 128
Lint/Void:
  Enabled: false


class NilClass
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class TrueClass
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class FalseClass
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Fixnum < Integer
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Bignum < Integer
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Float < Numeric
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class String
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Array
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Hash
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end

class Symbol
  #
  # Same as MessagePack.to_msgpack(self[, packer]).
  #
  # @return [String] serialized data
  #
  def to_msgpack(packer=nil)
  end
end


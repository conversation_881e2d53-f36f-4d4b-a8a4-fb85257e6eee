08-07-2019
  - Version 1.0.2
  - Update mime types from upstream

14-08-2018
  - Version 1.0.1
  - Update mime types from upstream
  - Add lookup_by_extension to the public API

08-11-2017
  - Version 1.0.0
  - Other than the version number, no difference from 0.1.4

11-08-2017
  - Version 0.1.4
  - Return preferred extension when looking up by content type


28-03-2016

  - Version 0.1.3
  - Prefer non-obsolete mime types to obsolete ones

14-12-2016

  - Version 0.1.2
  - Backwards compat with ancient Ruby to match mail gem

14-12-2016

  - Version 0.1.1
  - Adjusted API to be more consistent

14-12-2016

  - Version 0.1.0
  - Initial version

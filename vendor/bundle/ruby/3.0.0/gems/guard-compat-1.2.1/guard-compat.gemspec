# coding: utf-8
lib = File.expand_path('../lib', __FILE__)
$LOAD_PATH.unshift(lib) unless $LOAD_PATH.include?(lib)
require 'guard/compat/version'

Gem::Specification.new do |spec|
  spec.name          = 'guard-compat'
  spec.version       = Guard::Compat::VERSION
  spec.authors       = ['<PERSON><PERSON><PERSON>']
  spec.email         = ['<EMAIL>']
  spec.summary       = 'Tools for developing Guard compatible plugins'
  spec.description   = 'Helps creating valid Guard plugins and testing them'
  spec.homepage      = ''
  spec.license       = 'MIT'

  spec.files         = `git ls-files -z`.split("\x0")
  spec.executables   = spec.files.grep(%r{^bin/}) { |f| File.basename(f) }
  spec.test_files    = spec.files.grep(%r{^(test|spec|features)/})
  spec.require_paths = ['lib']

  spec.add_development_dependency 'bundler', '~> 1.7'
  spec.add_development_dependency 'rake', '~> 10.0'
end

RUBY/EventMachine RELEASE NOTES

--------------------------------------------------
Version: 0.9.0, released xxXXX07
Added Erlang-like distributed-computing features

--------------------------------------------------
Version: 0.8.0, released 23Jun07
Added an epoll implementation for Linux 2.6 kernels.
Added evented #popen.

--------------------------------------------------
Version: 0.7.3, released 22May07
Added a large variety of small features. See the ChangeLog.

--------------------------------------------------
Version: 0.7.1, released xxNov06
Added protocol handlers for line-oriented protocols.
Various bug fixes.

--------------------------------------------------
Version: 0.7.0, released 20Nov06
Added a fix in em.cpp/ConnectToServer to fix a fatal exception that
occurred in FreeBSD when connecting successfully to a remote server.

--------------------------------------------------
Version: 0.6.0, released xxJul06
Added deferred operations, suggested by Don Stocks, <EMAIL>.

--------------------------------------------------
Version: 0.5.4, released xxJun06
Added get_peername support for streams and datagrams.

--------------------------------------------------
Version: 0.5.3, released 17May06
Fixed bugs in extconf.rb, thanks to Daniel Harple, <EMAIL>.
Added proper setup.rb and rake tasks, thanks to Austin Ziegler.
Fixed a handful of reported problems with builds on various platforms.

--------------------------------------------------
Version: 0.5.2, released 05May06
Made several nonvisible improvements to the Windows
implementation.
Added an exception-handling patch contributed by Jeff Rose, <EMAIL>.
Added a dir-config patch contributed anonymously.
Supported builds on Solaris.

--------------------------------------------------
Version: 0.5.1, released 05May06
Made it possible to pass a Class rather than a Module
to a protocol handler.
Added Windows port.

--------------------------------------------------
Version: 0.5.0, released 30Apr06
Added a preliminary SSL/TLS extension. This will probably
change over the next few releases.

--------------------------------------------------
Version: 0.4.5, released 29Apr06
Changed ext files so the ruby.h is installed after unistd.h
otherwise it doesn't compile on gcc 4.1

--------------------------------------------------
Version: 0.4.2, released 19Apr06
Changed the Ruby-glue so the extension will play nicer
in the sandbox with Ruby threads.
Added an EventMachine::run_without_threads API to
switch off the thread-awareness for better performance
in programs that do not spin any Ruby threads.

--------------------------------------------------
Version: 0.4.1, released 15Apr06
Reworked the shared-object interface to make it easier to
use EventMachine from languages other than Ruby.

--------------------------------------------------
Version: 0.3.2, released 12Apr06
Added support for a user-supplied block in EventMachine#connect.

--------------------------------------------------
Version: 0.3.1, released 11Apr06
Fixed bug that prevented EventMachine from being run multiple
times in a single process.

--------------------------------------------------
Version: 0.3.0, released 10Apr06
Added method EventHandler::Connection::post_init

--------------------------------------------------
Version: 0.2.0, released 10Apr06
Added method EventHandler::stop



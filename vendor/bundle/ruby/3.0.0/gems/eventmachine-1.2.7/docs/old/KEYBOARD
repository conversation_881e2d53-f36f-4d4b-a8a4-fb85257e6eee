EventMachine (EM) can respond to keyboard events. This gives your event-driven
programs the ability to respond to input from local users.

Programming EM to handle keyboard input in Ruby is simplicity itself. Just use
EventMachine#open_keyboard, and supply the name of a Ruby module or class that
will receive the input:

 require 'rubygems'
 require 'eventmachine'

 module <PERSON><PERSON>eyboardHandler
 	def receive_data keystrokes
 		puts "I received the following data from the keyboard: #{keystrokes}"
 	end
 end

 EM.run {
 	EM.open_keyboard(MyKeyboardHandler)
 }

If you want EM to send line-buffered keyboard input to your program, just
include the LineText2 protocol module in your handler class or module:

 require 'rubygems'
 require 'eventmachine'

 module MyKeyboardHandler
 	include EM::Protocols::LineText2
 	def receive_line data
 		puts "I received the following line from the keyboard: #{data}"
 	end
 end

 EM.run {
 	EM.open_keyboard(MyKeyboardHandler)
 }

As we said, simplicity itself. You can call EventMachine#open_keyboard at any
time while the EM reactor loop is running. In other words, the method
invocation may appear anywhere in an EventMachine#run block, or in any code
invoked in the #run block.


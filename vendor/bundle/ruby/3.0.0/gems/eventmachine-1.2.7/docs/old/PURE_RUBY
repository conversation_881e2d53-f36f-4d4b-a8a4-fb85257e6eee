EventMachine is supplied in three alternative versions.

1) A version that includes a Ruby extension written in C++. This version requires compilation;
2) A version for JRuby that contains a precompiled JAR file written in Java;
3) A pure Ruby version that has no external dependencies and can run in any Ruby environment.

The Java version of EventMachine is packaged in a distinct manner and must be installed using a
special procedure. This version is described fully in a different document, and not considered
further here.

The C++ and pure-Ruby versions, however, are shipped in the same distribution. You use the same
files (either tarball or Ruby gem) to install both of these versions.

If you intend to use the C++ version, you must successfully compile EventMachine after you install it.
(The gem installation attempts to perform this step automatically.)

If you choose not to compile the EventMachine C++ extension, or if your compilation fails for any
reason, you still have a fully-functional installation of the pure-Ruby version of EM.

However, for technical reasons, a default EM installation (whether or not the compilation succeeds)
will always assume that the compiled ("extension") implementation should be used.

If you want your EM program to use the pure Ruby version, you must specifically request it. There
are two ways to do this: by setting either a Ruby global variable, or an environment string.

The following code will invoke the pure-Ruby implementation of EM:

 $eventmachine_library = :pure_ruby
 require 'eventmachine'

 EM.library_type #=> "pure_ruby"

Notice that this requires a code change and is not the preferred way to select pure Ruby, unless
for some reason you are absolutely sure you will never want the compiled implementation.

Setting the following environment string has the same effect:

 export EVENTMACHINE_LIBRARY="pure_ruby"

This technique gives you the flexibility to select either version at runtime with no code changes.

Support

The EventMachine development team has committed to support precisely the same APIs for all the
various implementations of EM.

This means that you can expect any EM program to behave identically, whether you use pure Ruby,
the compiled C++ extension, or JRuby. Deviations from this behavior are to be considered bugs
and should be reported as such.

There is a small number of exceptions to this rule, which arise from underlying platform
distinctions. Notably, EM#epoll is a silent no-op in the pure Ruby implementation.


When Should You Use the Pure-Ruby Implementation of EM?


Use the pure Ruby implementation of EM when you must support a platform for which no C++ compiler
is available, or on which the standard EM C++ code can't be compiled.

Keep in mind that you don't need a C++ compiler in order to deploy EM applications that rely on
the compiled version, so long as appropriate C++ runtime libraries are available on the target platform.

In extreme cases, you may find that you can develop software with the compiled EM version, but are
not allowed to install required runtime libraries on the deployment system(s). This would be another
case in which the pure Ruby implementation can be useful.

In general you should avoid the pure Ruby version of EM when performance and scalability are important.
EM in pure Ruby will necessarily run slower than the compiled version. Depending on your application
this may or may not be a key issue.

Also, since EPOLL is not supported in pure Ruby, your applications will be affected by Ruby's built-in
limit of 1024 file and socket descriptors that may be open in a single process. For maximum scalability
and performance, always use EPOLL if possible.


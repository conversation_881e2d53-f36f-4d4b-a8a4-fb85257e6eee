# EventMachine documentation guides #

Welcome to the documentation guides for [EventMachine](http://github.com/eventmachine/eventmachine),
a fast and simple event-processing library for Ruby programs (à la JB<PERSON> Netty, Twisted, Node.js
and so on).

## Guide list ##

 * {file:docs/GettingStarted.md Getting started with EventMachine}
 * {file:docs/EventDrivenServers.md Writing event-driven servers}
 * {file:docs/EventDrivenClients.md Writing event-driven clients}
 * {file:docs/ConnectionFailureAndRecovery.md Connection Failure and Recovery}
 * {file:docs/TLS.md TLS (aka SSL)}
 * {file:docs/Ecosystem.md EventMachine ecosystem}: Thin, Goliath, em-http-request, em-websockets, Proxymachine and beyond
 * {file:docs/BlockingEventLoop.md On blocking the event loop: why it is harmful for performance and how to avoid it}
 * {file:docs/LightweightConcurrency.md Lightweight concurrency with EventMachine}
 * {file:docs/Deferrables.md Deferrables}
 * {file:docs/ModernKernelInputOutputAPIs.md Brief introduction to epoll, kqueue, select}
 * {file:docs/WorkingWithOtherIOSources.md Working with other IO sources such as the keyboard}


## Tell us what you think! ##

Please take a moment and tell us what you think about this guide on the [EventMachine mailing list](http://bit.ly/jW3cR3)
or in the #eventmachine channel on irc.freenode.net: what was unclear? What wasn't covered?
Maybe you don't like the guide style or the grammar and spelling are incorrect? Reader feedback is
key to making documentation better.

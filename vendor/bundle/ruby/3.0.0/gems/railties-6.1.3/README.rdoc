= Railties -- Gluing the Engine to the Rails

Railties is responsible for gluing all frameworks together. Overall, it:

* handles the bootstrapping process for a Rails application;

* manages the +rails+ command line interface;

* and provides the Rails generators core.


== Download

The latest version of Railties can be installed with RubyGems:

* gem install railties

Source code can be downloaded as part of the Rails project on GitHub

* https://github.com/rails/rails/tree/main/railties

== License

Railties is released under the MIT license:

* https://opensource.org/licenses/MIT

== Support

API documentation is at

* https://api.rubyonrails.org

Bug reports can be filed for the Ruby on Rails project here:

* https://github.com/rails/rails/issues

Feature requests should be discussed on the rails-core mailing list here:

* https://discuss.rubyonrails.org/c/rubyonrails-core

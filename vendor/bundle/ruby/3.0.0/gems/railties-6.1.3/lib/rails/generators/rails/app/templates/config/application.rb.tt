require_relative "boot"

<% if include_all_railties? -%>
require "rails/all"
<% else -%>
require "rails"
# Pick the frameworks you want:
require "active_model/railtie"
<%= comment_if :skip_active_job %>require "active_job/railtie"
<%= comment_if :skip_active_record %>require "active_record/railtie"
<%= comment_if :skip_active_storage %>require "active_storage/engine"
require "action_controller/railtie"
<%= comment_if :skip_action_mailer %>require "action_mailer/railtie"
<%= comment_if :skip_action_mailbox %>require "action_mailbox/engine"
<%= comment_if :skip_action_text %>require "action_text/engine"
require "action_view/railtie"
<%= comment_if :skip_action_cable %>require "action_cable/engine"
<%= comment_if :skip_sprockets %>require "sprockets/railtie"
<%= comment_if :skip_test %>require "rails/test_unit/railtie"
<% end -%>

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module <%= app_const_base %>
  class Application < Rails::Application
<%- if !options.dummy_app? -%>
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults <%= build(:config_target_version) %>
<%- else -%>
    config.load_defaults Rails::VERSION::STRING.to_f
<%- end -%>

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
<%- if options.api? -%>

    # Only loads a smaller set of middleware suitable for API only apps.
    # Middleware like session, flash, cookies can be added back manually.
    # Skip views, helpers and assets when generating a new resource.
    config.api_only = true
<%- elsif !depends_on_system_test? -%>

    # Don't generate system test files.
    config.generators.system_tests = nil
<%- end -%>
  end
end

Description:
    Generates new asset placeholders. Pass the asset name, either CamelCased
    or under_scored.

    To create an asset within a folder, specify the asset's name as a
    path like 'parent/name'.

    This generates a stylesheet stub in app/assets/stylesheets.

    If Sass 3 is available, stylesheets will be generated with the .scss extension.

Example:
    `bin/rails generate assets posts`

    Posts assets.
        Stylesheet:     app/assets/stylesheets/posts.css

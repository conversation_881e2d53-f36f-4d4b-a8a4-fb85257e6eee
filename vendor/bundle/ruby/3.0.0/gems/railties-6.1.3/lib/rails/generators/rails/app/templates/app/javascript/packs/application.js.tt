// This file is automatically compiled by Webpack, along with any other files
// present in this directory. You're encouraged to place your actual application logic in
// a relevant structure within app/javascript and only use these pack files to reference
// that code so it'll be compiled.

import Rails from "@rails/ujs"
<%- unless options[:skip_turbolinks] -%>
import Turbolinks from "turbolinks"
<%- end -%>
<%- unless skip_active_storage? -%>
import * as ActiveStorage from "@rails/activestorage"
<%- end -%>
<%- unless options[:skip_action_cable] -%>
import "channels"
<%- end -%>

Rails.start()
<%- unless options[:skip_turbolinks] -%>
Turbolinks.start()
<%- end -%>
<%- unless skip_active_storage? -%>
ActiveStorage.start()
<%- end -%>

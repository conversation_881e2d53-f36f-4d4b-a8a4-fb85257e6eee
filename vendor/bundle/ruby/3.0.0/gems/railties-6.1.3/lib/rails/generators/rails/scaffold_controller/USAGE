Description:
    Generates a scaffolded controller, its seven RESTful actions and related
    views. Pass the model name, either CamelCased or under_scored. The
    controller name is retrieved as a pluralized version of the model name.

    To create a controller within a module, specify the model name as a
    path like 'parent_module/controller_name'.

    This generates a controller class in app/controllers and invokes helper,
    template engine and test framework generators.

Example:
    `bin/rails generate scaffold_controller CreditCard`

    Credit card controller with URLs like /credit_cards.
        Controller: app/controllers/credit_cards_controller.rb
        Test:       test/controllers/credit_cards_controller_test.rb
        Views:      app/views/credit_cards/index.html.erb [...]
        Helper:     app/helpers/credit_cards_helper.rb

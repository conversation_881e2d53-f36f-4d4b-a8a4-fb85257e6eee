# Oracle/OCI 11g or higher recommended
#
# Requires Ruby/OCI8:
#  https://github.com/kubo/ruby-oci8
#
# Specify your database using any valid connection syntax, such as a
# tnsnames.ora service name, or an SQL connect string of the form:
#
#   //host:[port][/service name]
#
# By default prefetch_rows (OCI_ATTR_PREFETCH_ROWS) is set to 100. And
# until true bind variables are supported, cursor_sharing is set by default
# to 'similar'. Both can be changed in the configuration below; the defaults
# are equivalent to specifying:
#
#  prefetch_rows: 100
#  cursor_sharing: similar
#
default: &default
  adapter: oracle_enhanced
  pool: <%%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  username: <%= app_name %>
  password:

development:
  <<: *default
  database: <%= app_name %>_development

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: <%= app_name %>_test

# As with config/credentials.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password or a full connection URL as an environment
# variable when you boot the app. For example:
#
#   DATABASE_URL="oracle-enhanced://myuser:mypass@localhost/somedatabase"
#
# (Note that the adapter name uses a dash instead of an underscore.)
#
# If the connection URL is provided in the special DATABASE_URL environment
# variable, Rails will automatically merge its configuration values on top of
# the values provided in this file. Alternatively, you can specify a connection
# URL environment variable explicitly:
#
#   production:
#     url: <%%= ENV['MY_APP_DATABASE_URL'] %>
#
# Read https://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full overview on how database connection configuration can be specified.
#
production:
  <<: *default
  database: <%= app_name %>_production
  username: <%= app_name %>
  password: <%%= ENV['<%= app_name.upcase %>_DATABASE_PASSWORD'] %>

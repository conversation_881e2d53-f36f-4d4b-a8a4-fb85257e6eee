# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

<% if sqlite3? -%>
# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-*

<% end -%>
# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
<% if keeps? -%>
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep
<% end -%>

<% unless skip_active_storage? -%>
# Ignore uploaded files in development.
/storage/*
<% if keeps? -%>
!/storage/.keep
<% end -%>
<% end -%>
<% unless options.api? -%>

/public/assets
<% end -%>
.byebug_history

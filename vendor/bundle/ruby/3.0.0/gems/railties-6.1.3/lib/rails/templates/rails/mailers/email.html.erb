<!DOCTYPE html>
<html><head>
<title><%= @page_title %></title>
<meta name="viewport" content="width=device-width" />
<style type="text/css">
  html, body, iframe {
    height: 100%;
  }

  body {
    margin: 0;
  }

  header {
    width: 100%;
    padding: 10px 0 0 0;
    margin: 0;
    background: white;
    font: 12px "Lucida Grande", sans-serif;
    border-bottom: 1px solid #dedede;
    overflow: hidden;
  }

  dl {
    margin: 0 0 10px 0;
    padding: 0;
  }

  dt {
    width: 80px;
    padding: 1px;
    float: left;
    clear: left;
    text-align: right;
    color: #7f7f7f;
  }

  dd {
    margin-left: 90px; /* 80px + 10px */
    padding: 1px;
  }

  dd:empty:before {
    content: "\00a0"; // &nbsp;
  }

  iframe {
    border: 0;
    width: 100%;
  }
</style>
</head>

<body>
<header>
  <dl>
    <% if @email.respond_to?(:smtp_envelope_from) && Array(@email.from) != Array(@email.smtp_envelope_from) %>
      <dt>SMTP-From:</dt>
      <dd><%= @email.smtp_envelope_from %></dd>
    <% end %>

    <% if @email.respond_to?(:smtp_envelope_to) && @email.to != @email.smtp_envelope_to %>
      <dt>SMTP-To:</dt>
      <dd><%= @email.smtp_envelope_to %></dd>
    <% end %>

    <dt>From:</dt>
    <dd><%= @email.header['from'] %></dd>

    <% if @email.reply_to %>
      <dt>Reply-To:</dt>
      <dd><%= @email.header['reply-to'] %></dd>
    <% end %>

    <dt>To:</dt>
    <dd><%= @email.header['to'] %></dd>

    <% if @email.cc %>
      <dt>CC:</dt>
      <dd><%= @email.header['cc'] %></dd>
    <% end %>

    <dt>Date:</dt>
    <dd><%= Time.current.rfc2822 %></dd>

    <dt>Subject:</dt>
    <dd><strong><%= @email.subject %></strong></dd>

    <% unless @email.attachments.nil? || @email.attachments.empty? %>
      <dt>Attachments:</dt>
      <dd>
        <% @email.attachments.each do |a| %>
          <% filename = a.respond_to?(:original_filename) ? a.original_filename : a.filename %>
          <%= link_to filename, "data:application/octet-stream;charset=utf-8;base64,#{Base64.encode64(a.body.to_s)}", download: filename %>
        <% end %>
      </dd>
    <% end %>

    <dt>Format:</dt>
    <% if @email.multipart? %>
      <dd>
        <select id="part" onchange="refreshBody(false);">
          <option <%= request.format == Mime[:html] ? 'selected' : '' %> value="<%= part_query('text/html') %>">View as HTML email</option>
          <option <%= request.format == Mime[:text] ? 'selected' : '' %> value="<%= part_query('text/plain') %>">View as plain-text email</option>
        </select>
      </dd>
    <% else %>
      <dd id="mime_type" data-mime-type="<%= part_query(@email.mime_type) %>"><%= @email.mime_type == 'text/html' ? 'HTML email' : 'plain-text email' %></dd>
    <% end %>

    <% if I18n.available_locales.count > 1 %>
      <dt>Locale:</dt>
      <dd>
        <select id="locale" onchange="refreshBody(true);">
          <% I18n.available_locales.each do |locale| %>
            <option <%= I18n.locale == locale ? 'selected' : '' %> value="<%= locale_query(locale) %>"><%= locale %></option>
          <% end %>
        </select>
      </dd>
    <% end %>
  </dl>
</header>

<% if @part && @part.mime_type %>
  <iframe seamless name="messageBody" src="?<%= part_query(@part.mime_type) %>"></iframe>
<% else %>
  <p>
    You are trying to preview an email that does not have any content.
    This is probably because the <em>mail</em> method has not been called in <em><%= @preview.preview_name %>#<%= @email_action %></em>.
  </p>
<% end %>

<script>
  function refreshBody(reload) {
    var part_select = document.querySelector('select#part');
    var locale_select = document.querySelector('select#locale');
    var iframe = document.getElementsByName('messageBody')[0];
    var part_param = part_select ?
      part_select.options[part_select.selectedIndex].value :
      document.querySelector('#mime_type').dataset.mimeType;
    var locale_param = locale_select ? locale_select.options[locale_select.selectedIndex].value : null;
    var fresh_location;
    if (locale_param) {
      fresh_location = '?' + part_param + '&' + locale_param;
    } else {
      fresh_location = '?' + part_param;
    }
    iframe.contentWindow.location = fresh_location;

    var url = location.pathname.replace(/\.(txt|html)$/, '');
    var format = /html/.test(part_param) ? '.html' : '.txt';
    var state_to_replace = locale_param ? (url + format + '?' + locale_param) : (url + format);

    if (reload) {
      location.href = state_to_replace;
    } else if (history.replaceState) {
      window.history.replaceState({}, '', state_to_replace);
    }
  }
</script>

</body>
</html>

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

<% if options[:skip_gemspec] -%>
<%= '# ' if options.dev? || options.edge? || options.master? -%>gem 'rails', '<%= Array(rails_version_specifier).join("', '") %>'
<% else -%>
# Specify your gem's dependencies in <%= name %>.gemspec.
gemspec
<% end -%>
<% unless options[:skip_active_record] -%>

group :development do
  gem '<%= gem_for_database[0] %>'
end
<% end -%>

<% if options.dev? || options.edge? -%>
# Your gem is dependent on dev or edge Rails. Once you can lock this
# dependency down to a specific version, move it to your gemspec.
<% max_width = gemfile_entries.map { |g| g.name.length }.max -%>
<% gemfile_entries.each do |gem| -%>
<% if gem.comment -%>

# <%= gem.comment %>
<% end -%>
<%= gem.commented_out ? '# ' : '' %>gem '<%= gem.name %>'<%= %(, '#{gem.version}') if gem.version -%>
<% if gem.options.any? -%>
, <%= gem.options.map { |k,v|
  "#{k}: #{v.inspect}" }.join(', ') %>
<% end -%>
<% end -%>

<% end -%>
<% if RUBY_ENGINE == 'ruby' -%>
# To use a debugger
# gem 'byebug', group: [:development, :test]
<% end -%>
<% if RUBY_PLATFORM.match(/bccwin|cygwin|emx|mingw|mswin|wince|java/) -%>

gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
<% end -%>

# If you are using mssql, derby, hsqldb, or h2 with one of the
# ActiveRecord JDBC adapters, install the appropriate driver, e.g.,:
#   gem install activerecord-jdbcmssql-adapter
#
# Configure using Gemfile:
#   gem 'activerecord-jdbcmssql-adapter'
#
# development:
#   adapter: mssql
#   username: <%= app_name %>
#   password:
#   host: localhost
#   database: <%= app_name %>_development
#
# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
#
# test:
#   adapter: mssql
#   username: <%= app_name %>
#   password:
#   host: localhost
#   database: <%= app_name %>_test
#
# production:
#   adapter: mssql
#   username: <%= app_name %>
#   password:
#   host: localhost
#   database: <%= app_name %>_production

# If you are using oracle, db2, sybase, informix or prefer to use the plain
# JDBC adapter, configure your database setting as the example below (requires
# you to download and manually install the database vendor's JDBC driver .jar
# file). See your driver documentation for the appropriate driver class and
# connection string:

default: &default
  adapter: jdbc
  pool: <%%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  username: <%= app_name %>
  password:
  driver:

development:
  <<: *default
  url: jdbc:db://localhost/<%= app_name %>_development

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  url: jdbc:db://localhost/<%= app_name %>_test

# As with config/credentials.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password as an environment variable when you boot the
# app. Read https://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full overview on how database connection configuration can be specified.
#
production:
  url: jdbc:db://localhost/<%= app_name %>_production
  username: <%= app_name %>
  password: <%%= ENV['<%= app_name.upcase %>_DATABASE_PASSWORD'] %>

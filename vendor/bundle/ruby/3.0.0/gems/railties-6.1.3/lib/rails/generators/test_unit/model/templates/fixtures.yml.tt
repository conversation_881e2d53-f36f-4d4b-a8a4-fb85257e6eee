# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html
<% unless attributes.empty? -%>
<% %w(one two).each do |name| %>
<%= name %>:
<% attributes.each do |attribute| -%>
  <%- if attribute.password_digest? -%>
  password_digest: <%%= BCrypt::Password.create('secret') %>
  <%- elsif attribute.reference? -%>
  <%= yaml_key_value(attribute.column_name.delete_suffix("_id"), attribute.default || name) %>
  <%- elsif !attribute.virtual? -%>
  <%= yaml_key_value(attribute.column_name, attribute.default) %>
  <%- end -%>
  <%- if attribute.polymorphic? -%>
  <%= yaml_key_value("#{attribute.name}_type", attribute.human_name) %>
  <%- end -%>
<% end -%>
<% end -%>
<% else -%>

# This model initially had no columns defined. If you add columns to the
# model remove the '{}' from the fixture names and add the columns immediately
# below each fixture, per the syntax in the comments below
#
one: {}
# column: value
#
two: {}
# column: value
<% end -%>

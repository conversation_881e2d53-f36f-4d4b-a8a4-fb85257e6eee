ENV['RAILS_ENV'] ||= 'test'
require_relative "../config/environment"
require "rails/test_help"

class ActiveSupport::TestCase
  # Run tests in parallel with specified workers
<% if Process.respond_to?(:fork) && !Gem.win_platform? -%>
  parallelize(workers: :number_of_processors)
<%- else -%>
  parallelize(workers: :number_of_processors, with: :threads)
<% end -%>

<% unless options[:skip_active_record] -%>
  # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
  fixtures :all

<% end -%>
  # Add more helper methods to be used by all tests here...
end

Description:
    Generates a new generator at lib/generators. Pass the generator name as an argument,
    either CamelCased or snake_cased.

Example:
    `bin/rails generate generator Awesome`

    creates a standard awesome generator:
        lib/generators/awesome/
        lib/generators/awesome/awesome_generator.rb
        lib/generators/awesome/USAGE
        lib/generators/awesome/templates/
        test/lib/generators/awesome_generator_test.rb

/.bundle/
/doc/
/log/*.log
/pkg/
/tmp/
<% if with_dummy_app? -%>
<% if sqlite3? -%>
/<%= dummy_path %>/db/*.sqlite3
/<%= dummy_path %>/db/*.sqlite3-*
<% end -%>
/<%= dummy_path %>/log/*.log
<% unless options[:skip_javascript] -%>
/<%= dummy_path %>/node_modules/
/<%= dummy_path %>/yarn-error.log
<% end -%>
<% unless skip_active_storage? -%>
/<%= dummy_path %>/storage/
<% end -%>
/<%= dummy_path %>/tmp/
<% end -%>
.byebug_history

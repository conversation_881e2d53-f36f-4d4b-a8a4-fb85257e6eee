=== ** DEPRECATED **

Rails 5.2 has introduced a new `credentials` API that replaces Rails secrets.
Please use the Rails `credentials` commands instead.
Run `rails credentials:help` for more information.

=== Storing Encrypted Secrets in Source Control

The Rails `secrets` commands helps encrypting secrets to slim a production
environment's `ENV` hash. It's also useful for atomic deploys: no need to
coordinate key changes to get everything working as the keys are shipped
with the code.

=== Setup

Run `bin/rails secrets:setup` to opt in and generate the `config/secrets.yml.key`
and `config/secrets.yml.enc` files.

The latter contains all the keys to be encrypted while the former holds the
encryption key.

Don't lose the key! Put it in a password manager your team can access.
Should you lose it no one, including you, will be able to access any encrypted
secrets.
Don't commit the key! Add `config/secrets.yml.key` to your source control's
ignore file. If you use Git, <PERSON><PERSON> handles this for you.

<PERSON><PERSON> also looks for the key in `ENV["RAILS_MASTER_KEY"]` if that's easier to
manage.

You could prepend that to your server's start command like this:

   RAILS_MASTER_KEY="im-the-master-now-hahaha" server.start


The `config/secrets.yml.enc` has much the same format as `config/secrets.yml`:

   production:
     secret_key_base: so-secret-very-hidden-wow
     payment_processing_gateway_key: much-safe-very-gaedwey-wow

But that's where the similarities between `secrets.yml` and `secrets.yml.enc`
end, e.g. no keys from `secrets.yml` will be moved to `secrets.yml.enc` and
be encrypted.

A `shared:` top level key is also supported such that any keys there is merged
into the other environments.

Additionally, Rails won't read encrypted secrets out of the box even if you have
the key. Add this:

   config.read_encrypted_secrets = true

to the environment you'd like to read encrypted secrets. `bin/rails secrets:setup`
inserts this into the production environment by default.

=== Editing Secrets

After `bin/rails secrets:setup`, run `bin/rails secrets:edit`.

That command opens a temporary file in `$EDITOR` with the decrypted contents of
`config/secrets.yml.enc` to edit the encrypted secrets.

When the temporary file is next saved the contents are encrypted and written to
`config/secrets.yml.enc` while the file itself is destroyed to prevent secrets
from leaking.

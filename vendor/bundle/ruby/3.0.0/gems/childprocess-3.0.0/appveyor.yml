version: '1.0.{build}'

environment:
  matrix:
    - CH<PERSON><PERSON><PERSON>CE<PERSON>_POSIX_SPAWN: true
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 23-x64
    - CHIL<PERSON>ROCESS_POSIX_SPAWN: false
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 23-x64
    - CH<PERSON><PERSON>ROCESS_POSIX_SPAWN: true
      CH<PERSON><PERSON>ROCESS_UNSET: should-be-unset
      RUBY_VERSION: 24-x64
    - CHILDPROCESS_POSIX_SPAWN: false
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 24-x64
    - CH<PERSON><PERSON>ROCESS_POSIX_SPAWN: true
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 25-x64
    - CH<PERSON><PERSON>ROCESS_POSIX_SPAWN: false
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 25-x64
    - CH<PERSON><PERSON>ROCESS_POSIX_SPAWN: true
      CHILDPROCESS_UNSET: should-be-unset
      RUBY_VERSION: 26-x64
    - <PERSON><PERSON><PERSON><PERSON>CESS_POSIX_SPAWN: false
      CHIL<PERSON>ROCESS_UNSET: should-be-unset
      RUBY_VERSION: 26-x64

install:
  - set PATH=C:\Ruby%RUBY_VERSION%\bin;%PATH%
  - bundle install

build: off

before_test:
  - ruby -v
  - gem -v
  - bundle -v

test_script:
  - bundle exec rake

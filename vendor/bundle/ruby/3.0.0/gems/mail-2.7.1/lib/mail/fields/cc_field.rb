# encoding: utf-8
# frozen_string_literal: true
# 
# = Carbon Copy Field
# 
# The Cc field inherits from StructuredField and handles the Cc: header
# field in the email.
# 
# Sending cc to a mail message will instantiate a Mail::Field object that
# has a <PERSON>c<PERSON>ield as its field type.  This includes all Mail::CommonAddress
# module instance metods.
# 
# Only one Cc field can appear in a header, though it can have multiple
# addresses and groups of addresses.
# 
# == Examples:
# 
#  mail = Mail.new
#  mail.cc = '<PERSON><PERSON> <<EMAIL>>, <EMAIL>'
#  mail.cc    #=> ['<EMAIL>', '<EMAIL>']
#  mail[:cc]  #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::CcField:0x180e1c4
#  mail['cc'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::CcField:0x180e1c4
#  mail['Cc'] #=> '#<Mail::Field:0x180e5e8 @field=#<Mail::CcField:0x180e1c4
# 
#  mail[:cc].encoded   #=> 'Cc: <PERSON><PERSON> <<EMAIL>>, <EMAIL>\r\n'
# <AUTHOR> <EMAIL>, <EMAIL>'
#  mail[:cc].addresses #=> ['<EMAIL>', '<EMAIL>']
# <AUTHOR> <EMAIL>', '<EMAIL>']
# 
require 'mail/fields/common/common_address'

module Mail
  class CcField < StructuredField
    
    include Mail::CommonAddress
    
    FIELD_NAME = 'cc'
    CAPITALIZED_FIELD = 'Cc'
    
    def initialize(value = nil, charset = 'utf-8')
      self.charset = charset
      super(CAPITALIZED_FIELD, value, charset)
      self
    end
    
    def encoded
      do_encode(CAPITALIZED_FIELD)
    end
    
    def decoded
      do_decode
    end
    
  end
end

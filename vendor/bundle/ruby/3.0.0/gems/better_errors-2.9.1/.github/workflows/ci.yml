name: CI

on:
  push:
    branches: [ master ]
  schedule:
    - cron: '0 0 12,26 * *' # roughly every two weeks to run on new Ruby versions
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        ruby:
          - 2.2
          - 2.3
          - 2.4
          - 2.5
          - 2.6
          - 2.7
          # - ruby-head
          # - truffleruby-head
        gemfile:
          # These are located in the gemfiles/ folder
          - rails42
          - rails50
          - rails51
          - rails52
          - rails60
          - rails42_haml
          - rails50_haml
          - rails51_haml
          - rails52_haml
          - rails60_haml
          - rails42_boc
          - rails50_boc
          - rails51_boc
          - rails52_boc
          - rails60_boc
          - rack
          - rack_boc
          # - pry09
          # - pry10
          # - pry11
        exclude:
          - { ruby: 2.2,              gemfile: rails60 }
          - { ruby: 2.2,              gemfile: rails60_boc }
          - { ruby: 2.2,              gemfile: rails60_haml }
          - { ruby: 2.3,              gemfile: rails42 }
          - { ruby: 2.3,              gemfile: rails42_boc }
          - { ruby: 2.3,              gemfile: rails42_haml }
          - { ruby: 2.3,              gemfile: rails60 }
          - { ruby: 2.3,              gemfile: rails60_boc }
          - { ruby: 2.3,              gemfile: rails60_haml }
          - { ruby: 2.4,              gemfile: rails42 }
          - { ruby: 2.4,              gemfile: rails42_boc }
          - { ruby: 2.4,              gemfile: rails42_haml }
          - { ruby: 2.4,              gemfile: rails60 }
          - { ruby: 2.4,              gemfile: rails60_boc }
          - { ruby: 2.4,              gemfile: rails60_haml }
          - { ruby: 2.5,              gemfile: rails42 }
          - { ruby: 2.5,              gemfile: rails42_boc }
          - { ruby: 2.5,              gemfile: rails42_haml }
          - { ruby: 2.6,              gemfile: rails42 }
          - { ruby: 2.6,              gemfile: rails42_boc }
          - { ruby: 2.6,              gemfile: rails42_haml }
          - { ruby: 2.7,              gemfile: rails42 }
          - { ruby: 2.7,              gemfile: rails42_boc }
          - { ruby: 2.7,              gemfile: rails42_haml }
          # - { ruby: ruby-head,        gemfile: rails42 }
          # - { ruby: ruby-head,        gemfile: rails42_boc }
          # - { ruby: ruby-head,        gemfile: rails42_haml }
          - { ruby: truffleruby-head, gemfile: rails42_boc }
          - { ruby: truffleruby-head, gemfile: rails50_boc }
          - { ruby: truffleruby-head, gemfile: rails51_boc }
          - { ruby: truffleruby-head, gemfile: rails52_boc }
          - { ruby: truffleruby-head, gemfile: rails60_boc }
          - { ruby: truffleruby-head, gemfile: rack_boc }

    steps:

    - uses: actions/checkout@v2

    - name: Set up Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ matrix.ruby }}

    - uses: actions/cache@v2
      with:
        path: gemfiles/vendor/bundle
        key: v3-${{ runner.os }}-branch-${{ github.ref }}-ruby-${{ matrix.ruby }}-gemfile-${{ matrix.gemfile }}-${{ hashFiles(format('gemfiles/{0}.gemfile', matrix.gemfile)) }}
        restore-keys: |
          v3-${{ runner.os }}-branch-${{ github.ref }}-ruby-${{ matrix.ruby }}-gemfile-${{ matrix.gemfile }}
          v3-${{ runner.os }}-branch-master-ruby-${{ matrix.ruby }}-gemfile-${{ matrix.gemfile }}

    - name: Bundle install
      run: |
        bundle config path vendor/bundle
        bundle install --jobs 4 --retry 3
      env:
        BUNDLE_GEMFILE: gemfiles/${{ matrix.gemfile }}.gemfile

    - name: RSpec
      run: bundle exec rspec -f doc --color
      env:
        BUNDLE_GEMFILE: gemfiles/${{ matrix.gemfile }}.gemfile

    - name: Report to Coveralls
      uses: coverallsapp/github-action@v1.1.2
      with:
        github-token: ${{ secrets.github_token }}
        flag-name: test-${{ matrix.ruby }}-${{ matrix.gemfile }}
        parallel: true


  finish:
    needs: test
    runs-on: ubuntu-latest
    steps:

    - name: Report completion to Coveralls
      uses: coverallsapp/github-action@v1.1.2
      with:
        github-token: ${{ secrets.github_token }}
        parallel-finished: true

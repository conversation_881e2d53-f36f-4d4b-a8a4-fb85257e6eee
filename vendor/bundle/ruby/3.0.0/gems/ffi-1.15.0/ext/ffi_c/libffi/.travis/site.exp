# Copyright (C) 2008, 2010, 2018, 2019  <PERSON>

# Make sure we look in the right place for the board description files.
if ![info exists boards_dir] {
    set boards_dir {}
}

lappend boards_dir $::env(TRAVIS_BUILD_DIR)/.travis

verbose "Global Config File: target_triplet is $target_triplet" 2
global target_list

case "$target_triplet" in {
    { "bfin-elf" } {
	set target_list "bfin-sim"
    }
    { "m32r-elf" } {
	set target_list "m32r-sim"
    }
    { "moxie-elf" } {
	set target_list "moxie-sim"
    }
    { "or1k-elf" } {
	set target_list "or1k-sim"
    }
}


= Changelog

== 0.5.1 / 2014-04-23

- new features:
  - Support for receiving binary messages

- changed:
  - Allow additional close codes to be sent by apps
  - Raise better errors on missing Sec-WebSocket-Key2
  - Updated http_parser.rb dependency to 0.6.0

- bug fixes:
  - Abort if HTTP request URI is invalid
  - Force close connections that have been sent a close handshake after a timeout

- improved spec compliance on:
  - Missing continuation frames
  - Fragmented control frames
  - Close behaviour after protocol errors

== 0.5.0 / 2013-03-05

- new features:
  - onclose handler is now passed a hash containing  was_clean (set to true in drafts 03 and above when a connection is closed with a closing handshake, either by the server or the client), the close code, and reason (drafts 06 and above). Close code 1005 indicates that no code was supplied, and 1006 that the connection was closed abnormally.
  - use Connection#support_close_codes? to easily check whether close codes are supported by the WebSocket protocol (drafts 06 and above)
  - closes connection with 1007 close code if text frame contains invalid UTF8
  - added Handshake#secure? for checking whether the connection is secure (either ssl or behind an ssl proxy)

- changed:
  - Defaults to sending no close code rather than 1000 (consistent with browsers)
  - Allows sending a 3xxx close code
  - Renamed Connection#close_websocket to Connection#close (again for consistency with browsers). Old method is available till 0.6.
  - Sends reasons with internally generated closure (previously only sent code)
  - Echos close code when replying to close handshake

== 0.4.0 / 2013-01-22

- new features:
  - on_open handler is now passed a handshake object which exposes the request headers, path, and query parameters
  - Easily access the protocol version via Handshake#protocol_version
  - Easily access the origin via Handshake#origin

- changed:
  - Removed Connection#request - change to using handshake passed to on_open

- internals:
  - Uses the http_parser.rb gem

== 0.3.8 / 2012-07-12

- bug fixes:
  - Fixed support for Ruby 1.8.7 which was broken in 0.3.7

== 0.3.7 / 2012-07-11

- new features:
  - Supports sending 1009 error code when incoming frame is too large to handle, and added associated exception class WSMessageTooBigError [Martyn Loughran]
  - Supports overriding the maximum frame size by setting the max_frame_size accessor on the connection object (in bytes). Default unchanged at 10MB. [Martyn Loughran]

- bug fixes:
  - Fixes some encoding issues on Ruby 1.9 [Dingding Ye]
  - Raises a HandshakeError if WS header is empty [Markus Fenske]
  - Connection#send would mutate passed string to BINARY encoding. The fix still mutates the string by forcing the encoding back to UTF-8 before returning, but if the passed string was encoded as UTF-8 this is equivalent [Martyn Loughran]

== 0.3.6 / 2011-12-23

- new features:
  - Supports sending ping & pong messages
  - Supports binding to received ping & pong messages

== 0.3.5 / 2011-10-24

- new features:
  - Support WebSocket draft 13

== 0.3.2 / 2011-10-09

- bugfixes:
  - Handling of messages with > 2 frames
  - Encode string passed to onmessage handler as UTF-8 on Ruby 1.9
  - Add 10MB frame length limit to all draft versions

== 0.3.1 / 2011-07-28

- new features:
  - Support WebSocket drafts 07 & 08

== 0.3.0 / 2011-05-06

- new features:
  - Support WebSocket drafts 05 & 06
- changes:
  - Accept request headers in a case insensitive manner
  - Change handling of errors. Previously some application errors were caught
    internally and were invisible unless an onerror callback was supplied. See
    readme for details

== 0.2.1 / 2011-03-01

- bugfixes:
  - Performance improvements to draft 76 framing
  - Limit frame lengths for draft 76
  - Better error handling for draft 76 handshake
  - Ruby 1.9 support

== 0.2.0 / 2010-11-23

- new features:
  - Support for WebSocket draft 03
- bugfixes:
  - Handle case when handshake split into two receive_data calls
  - Stricter regexp matching of frames

== 0.1.4 / 2010-08-23

- new features:
  - Allow custom ssl certificate to be used by passing :tls_options
  - Protect against errors caused by non limited frame lengths
  - Use custom exceptions rather than RuntimeError
- bugfixes:
  - Handle invalid HTTP request with HandshakeError

== 0.1.3 / 2010-07-18

- new features:
  - onerror callback
- bugfixes:
  - proper handling of zero spaces in key1 or key2(prevent ZeroDivisionError)
  - convert received data to utf-8 to prevent ruby 1.9 errors
  - fix handling of null bytes within a frame

== 0.1.2 / 2010-06-16

- new features:
  - none
- bugfixes:
  - allow $ character inside header key

== 0.1.1 / 2010-06-13

- new features:
  - wss/ssl support
- bugfixes:
  - can't & strings

== 0.1.0 / 2010-06-12

- initial release
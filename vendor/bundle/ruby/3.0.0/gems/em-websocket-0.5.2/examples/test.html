<html>
  <head>
    <script>
      function init() {
        function debug(string) {
          var element = document.getElementById("debug");
          var p = document.createElement("p");
          p.appendChild(document.createTextNode(string));
          element.appendChild(p);
        }

        var Socket = "MozWebSocket" in window ? MozWebSocket : WebSocket;
        var ws = new Socket("ws://localhost:8080/foo/bar?hello=world");
        ws.onmessage = function(evt) { debug("Received: " + evt.data); };
        ws.onclose = function(event) {
          debug("Closed - code: " + event.code + ", reason: " + event.reason + ", wasClean: " + event.wasClean);
        };
        ws.onopen = function() {
          debug("connected...");
          ws.send("hello server");
          ws.send("hello again");
        };
      };
    </script>
  </head>
  <body onload="init();">
    <div id="debug"></div>
  </body>
</html>

dlext = RbConfig::CONFIG['DLEXT']
direc = File.dirname(__FILE__)

require 'rake/clean'
require 'rubygems/package_task'
require "#{direc}/lib/method_source/version"

CLOBBER.include("**/*.#{dlext}", "**/*~", "**/*#*", "**/*.log", "**/*.o")
CLEAN.include("ext/**/*.#{dlext}", "ext/**/*.log", "ext/**/*.o",
              "ext/**/*~", "ext/**/*#*", "ext/**/*.obj", "**/*.rbc",
              "ext/**/*.def", "ext/**/*.pdb", "**/*_flymake*.*", "**/*_flymake")

def apply_spec_defaults(s)
  s.name = "method_source"
  s.summary = "retrieve the sourcecode for a method"
  s.version = MethodSource::VERSION
  s.date = Time.now.strftime '%Y-%m-%d'
  s.author = "<PERSON> (banisterfiend)"
  s.email = '<EMAIL>'
  s.description = s.summary
  s.require_path = 'lib'
  s.license = 'MIT'

  s.add_development_dependency("rspec","~>3.6")
  s.add_development_dependency("rake", "~>0.9")
  s.homepage = "http://banisterfiend.wordpress.com"
  s.has_rdoc = 'yard'
  s.files = `git ls-files`.split("\n")
  s.test_files = `git ls-files -- spec/*`.split("\n")
end

require "rspec/core/rake_task"
RSpec::Core::RakeTask.new(:spec) do |t|
  t.ruby_opts = %w[-w]
end

desc "reinstall gem"
task :reinstall => :gems do
  sh "gem uninstall method_source" rescue nil
  sh "gem install #{direc}/pkg/method_source-#{MethodSource::VERSION}.gem"
end

desc "Set up and run tests"
task :default => [:spec]

desc "Build the gemspec file"
task :gemspec => "ruby:gemspec"

namespace :ruby do
  spec = Gem::Specification.new do |s|
    apply_spec_defaults(s)
    s.platform = Gem::Platform::RUBY
  end

  Gem::PackageTask.new(spec) do |pkg|
    pkg.need_zip = false
    pkg.need_tar = false
  end

  desc  "Generate gemspec file"
  task :gemspec do
    File.open("#{spec.name}.gemspec", "w") do |f|
      f << spec.to_ruby
    end
  end
end

desc "build all platform gems at once"
task :gems => [:rmgems, "ruby:gem"]

desc "remove all platform gems"
task :rmgems => ["ruby:clobber_package"]

desc "build and push latest gems"
task :pushgems => :gems do
  chdir("#{direc}/pkg") do
    Dir["*.gem"].each do |gemfile|
      sh "gem push #{gemfile}"
    end
  end
end

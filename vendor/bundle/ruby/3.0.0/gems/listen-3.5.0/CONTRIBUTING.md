Contribute to Listen
===================

File an issue
-------------

If you haven't already, first see [TROUBLESHOOTING](https://github.com/guard/listen/blob/master/README.md#Issues-and-Troubleshooting) for known issues, solutions and workarounds.

You can report bugs and feature requests to [GitHub Issues](https://github.com/guard/listen/issues).

**Please don't ask question in the issue tracker**, instead ask them in our
[Google group](http://groups.google.com/group/guard-dev) or on `#guard` (irc.freenode.net).

Try to figure out where the issue belongs to: Is it an issue with Listen itself or with Guard?


**It's most likely that your bug gets resolved faster if you provide as much information as possible!**

The MOST useful information is debugging output from Listen (`LISTEN_GEM_DEBUGGING=1`) - see [TROUBLESHOOTING](https://github.com/guard/listen/blob/master/README.md#Issues-and-Troubleshooting) for details.


Development
-----------

* Documentation hosted at [RubyDoc](http://rubydoc.info/github/guard/listen/master/frames).
* Source hosted at [GitHub](https://github.com/guard/listen).

Pull requests are very welcome! Please try to follow these simple rules if applicable:

* Please create a topic branch for every separate change you make.
* Make sure your patches are well tested. All specs run with `rake spec` must pass.
* Update the [Yard](http://yardoc.org/) documentation.
* Update the [README](https://github.com/guard/listen/blob/master/README.md).
* Please **do not change** the version number.

The title of your PR will automatically be included in the release notes for the next version of the gem. A maintainer can add one of the following GitHub labels to the PR to automatically categorize it when the release notes are generated:

- ⚠️ Breaking
- ✨ Feature
- 🐛 Bug Fix
- 📚 Docs
- 🏠 Housekeeping

For questions please join us in our [Google group](http://groups.google.com/group/guard-dev) or on
`#guard` (irc.freenode.net).

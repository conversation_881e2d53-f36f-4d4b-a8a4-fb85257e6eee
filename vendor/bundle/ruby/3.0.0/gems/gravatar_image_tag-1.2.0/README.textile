!http://s.gravatar.com/images/logo.png(Gravatar Logo)!

h1. Gravatar Image Tag Plugin

Rails view helper for grabbing "Gravatar":http://en.gravatar.com/ images.  The goal here is to be configurable and have those configuration points documented!

h2. Install as a Ruby Gem

h3. Rails 2

p. Include the following line in your Rails environment

<pre># config/environment
  config.gem 'gravatar_image_tag'</pre>

p. Then ensure the gem is installed by running the following rake task from the your application root.

<pre>rake gems:install</pre>

h3. Rails 3

p. Include the following line in your Rails environment

<pre># Gemfile
  gem 'gravatar_image_tag'</pre>

p. Then ensure the gem is installed by running the following command from the application root.

<pre>bundle install</pre>

h2. Install as a Ruby on Rails Plugin

<pre>./script/plugin install git://github.com/mdeering/gravatar_image_tag.git</pre>

h2. Usage

h3. Gravatar Image Tag

p. Once you have installed it as a plugin for your rails app usage is simple.

<pre>gravatar_image_tag('<EMAIL>'.gsub('spam', 'mdeering'), :alt => '<PERSON> Deering')</pre>

*Boom* here is my gravatar !http://www.gravatar.com/avatar/4da9ad2bd4a2d1ce3c428e32c423588a(Michael Deering)!

h3. Gravatar Image URL

p. You can also return just the Gravatar URL:

<pre>gravatar_image_url('<EMAIL>'.gsub('spam', 'mdeering'), filetype: :png, rating: 'pg', size: 15, secure:false )</pre>

p. Useful when used in your inline CSS.

<pre><div class="gravatar" style="background:transparent url(<%= gravatar_image_url('<EMAIL>'.gsub('spam', 'mdeering'), size: 100) %>) 0px 0px no-repeat; width:100px; height:100px;"></div></pre>

h2. Configuration

h3. Global configuration points

<pre># config/initializers/gravatar_image_tag.rb
GravatarImageTag.configure do |config|
  config.default_image           = nil   # Set this to use your own default gravatar image rather then serving up Gravatar's default image [ 'http://example.com/images/default_gravitar.jpg', :identicon, :monsterid, :wavatar, 404 ].
  config.filetype                = nil   # Set this if you require a specific image file format ['gif', 'jpg' or 'png'].  Gravatar's default is png
  config.include_size_attributes = true  # The height and width attributes of the generated img will be set to avoid page jitter as the gravatars load.  Set to false to leave these attributes off.
  config.rating                  = nil   # Set this if you change the rating of the images that will be returned ['G', 'PG', 'R', 'X']. Gravatar's default is G
  config.size                    = nil   # Set this to globally set the size of the gravatar image returned (1..512). Gravatar's default is 80
  config.secure                  = false # Set this to true if you require secure images on your pages.
end
</pre>

h3. Setting the default image inline

p. *Splat* the default gravatar image !http://www.gravatar.com/avatar/0c821f675f132d790b3f25e79da739a7(Default Gravatar Image)!

p. You can set the default gravatar image inline as follows:

<pre>gravatar_image_tag('junk', :alt => 'Github Default Gravatar', :gravatar => { :default => 'https://assets.github.com/images/gravatars/gravatar-140.png' })</pre>

p. *Ka-Pow* !https://assets.github.com/images/gravatars/gravatar-140.png(Github Default Gravatar)!

p. Other options supported besides an image url to fall back on include the following:

* :identicon !http://www.gravatar.com/avatar/0c821f675f132d790b3f25e79da739a7?default=identicon(Identicon Avatar)!
* :monsterid !http://www.gravatar.com/avatar/0c821f675f132d790b3f25e79da739a7?default=monsterid(Monster Id Avatar)!
* :wavatar !http://www.gravatar.com/avatar/0c821f675f132d790b3f25e79da739a7?default=wavatar(Wavatar Avatar)!
* 404: !http://www.gravatar.com/avatar/0c821f675f132d790b3f25e79da739a7?default=404(Not Found)!

h3. Setting the default image size

p. You can set the gravatar image size inline as follows:

<pre>gravatar_image_tag('<EMAIL>'.gsub('spam', 'mdeering'), :alt => 'Michael Deering', :class => 'some-class', :gravatar => { :size => 15 })</pre>

*Mini Me!* !(some-class)http://www.gravatar.com/avatar/4da9ad2bd4a2d1ce3c428e32c423588a?size=15(Michael Deering)!

h3. Grabbing gravatars from the secure gravatar server.

p. You can make a request for a gravatar from the secure server at https://secure.gravatar.com by passing the _:gravatar => { :secure => true }_ option to the gravatar_image_tag call.

<pre>gravatar_image_tag('<EMAIL>'.gsub('spam', 'mdeering'), :alt => 'Michael Deering', :gravatar => { :secure => true } )</pre>

Delivered by a secure url! !https://secure.gravatar.com/avatar/4da9ad2bd4a2d1ce3c428e32c423588a(Michael Deering)!

h3. Using Gravatar's built in rating system

p. You can set the gravatar rating inline as follows:

<pre>gravatar_image_tag('<EMAIL>'.gsub('spam', 'mdeering'), :alt => 'Michael Deering', :gravatar => { :rating => 'pg' } )</pre>

h3. Specifying a filetype

p. You can set the gravatar filetype inline as follows:

<pre>gravatar_image_tag('<EMAIL>'.gsub('spam', 'mdeering'), :alt => 'Michael Deering', :gravatar => { :filetype => :gif } )</pre>

h2. Credits

The ideas and methods for this plugin are from expanding upon my original blog post "Adding Gravatar To Your Website Or Blog (Gravatar Rails)":http://mdeering.com/posts/005-adding-gravitar-to-your-website-or-blog

Copyright (c) 2009-2010 "Michael Deering(Ruby on Rails Development Edmonton)":http://mdeering.com, released under the MIT license

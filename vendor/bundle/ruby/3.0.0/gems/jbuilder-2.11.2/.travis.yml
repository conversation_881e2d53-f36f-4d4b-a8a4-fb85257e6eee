language: ruby

cache: bundler

before_install:
  - "gem install bundler -v '<2'"

rvm:
  - 2.2.10
  - 2.3.8
  - 2.4.10
  - 2.5.8
  - 2.6.6
  - 2.7.1
  - ruby-head

gemfile:
  - gemfiles/rails_5_0.gemfile
  - gemfiles/rails_5_1.gemfile
  - gemfiles/rails_5_2.gemfile
  - gemfiles/rails_6_0.gemfile
  - gemfiles/rails_head.gemfile

matrix:
  exclude:
    - rvm: 2.7.1
      gemfile: gemfiles/rails_5_0.gemfile
    - rvm: 2.7.1
      gemfile: gemfiles/rails_5_1.gemfile
    - rvm: 2.2.10
      gemfile: gemfiles/rails_5_2.gemfile
    - rvm: 2.7.1
      gemfile: gemfiles/rails_5_2.gemfile
    - rvm: 2.2.10
      gemfile: gemfiles/rails_6_0.gemfile
    - rvm: 2.3.8
      gemfile: gemfiles/rails_6_0.gemfile
    - rvm: 2.4.10
      gemfile: gemfiles/rails_6_0.gemfile
    - rvm: 2.2.10
      gemfile: gemfiles/rails_head.gemfile
    - rvm: 2.3.8
      gemfile: gemfiles/rails_head.gemfile
    - rvm: 2.4.10
      gemfile: gemfiles/rails_head.gemfile
  allow_failures:
    - rvm: ruby-head
    - gemfile: gemfiles/rails_head.gemfile
  fast_finish: true

notifications:
  email: false


<style>
  p { display: block; }
</style>

<div id="referrer"><%= referrer %></div>
<h1>This is a test</h1>

<h2 class="no text"></h2>
<h2 class="no text"></h2>
<h2 class="head" id="h2one">Header Class Test One</h2>
<h2 class="head" id="h2two">Header Class Test Two</h2>
<h2 class="head" id="h2_">Header Class Test Three</h2>
<h2 class="head">Header Class Test Four</h2>
<h2 class="head">Header Class <span style="display: none">Random</span>Test Five</h2>

<span class="number">42</span>
<span>Other span</span>

<p class="para" id="first" data-random="abc\def" style="line-height: 25px;">
  Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
  tempor incididunt ut <a href="/with_simple_html" title="awesome title" class="simple">labore</a>
  et dolore magna aliqua. Ut enim ad minim veniam,
  quis nostrud exercitation <a href="/foo" id="foo" data-test-id="test-foo">ullamco</a> laboris nisi
  ut aliquip ex ea commodo consequat.
  <a href="/with_simple_html" aria-label="Go to simple" data-test-id="test-simple"><img id="first_image" width="20" height="20" alt="awesome image" src="data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs="/></a>
</p>

<p class="para" id="second" style="display: inline;">
  Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
  dolore eu fugiat <a href="/redirect" id="red">Redirect</a> pariatur. Excepteur sint occaecat cupidatat non proident,
  sunt in culpa qui officia
  text   with &nbsp;
  whitespace
  id est laborum.
</p>

<p>
  <input type="text" id="test_field" spellcheck="TRUE" data-test-id="test_id" value="monkey"/>
  <input type="text" readonly="readonly" spellcheck="FALSE" value="should not change" />
  <textarea id="normal" data-other-test-id="test_id">
banana</textarea>
  <textarea id="additional_newline">

banana</textarea>
  <textarea readonly="readonly">textarea should not change</textarea>
  <a href="/redirect_back">BackToMyself</a>
  <a title="twas a fine link" href="/redirect">A link came first</a>
  <a title="a fine link" href="/with_simple_html">A link</a>
  <a title="a fine link with data method" data-method="delete" href="/delete">A link with data-method</a>
  <a title="a fine link with capitalized data method" data-method="DELETE" href="/delete">A link with capitalized data-method</a>
  <a>No Href</a>
  <a href="">Blank Href</a>
  <a href="#">Blank Anchor</a>
  <a href="javascript:void(0)">Blank JS Anchor</a>
  <a href="#anchor">Normal Anchor</a>
  <a href="/with_simple_html#anchor">Anchor on different page</a>
  <a href="/with_html#anchor">Anchor on same page</a>
  <a href="with_html">Relative</a>
  <a href="//<%= request.host_with_port %>/foo">Protocol</a>
  <input type="text" checked="checked" id="checked_field">
  <a href="/redirect"><img width="20" height="20" alt="very fine image" /></a>
  <a href="/with_simple_html"><img width="20" height="20" alt="fine image" /></a>
  <a href="/with_simple_html" disabled="disabled">Disabled link</a>

  <a href="?query_string=true">Naked Query String</a>
  <% if params[:query_string] %>
    <em>Query String sent</em>
  <% end %>
</p>

<div id="hidden" style="display: none;">
  <div id="hidden_via_ancestor">Inside element with hidden ancestor</div>
  <a href="/with_simple_html" title="hidden link" class="simple">hidden link</a>
</div>

<div id="hidden_attr" hidden="hidden">
  <a id="hidden_attr_via_ancestor">hidden attribute ancestor link</a>
</div>


<div id="hidden-text">
  Some of this text is <em style="display:none">hidden!</em>
</div>

<div id="some-hidden-text">
  Some of this text is not hidden <em style="display:none">and some is hidden</em>
</div>

<div style="display: none;">
  <a id="first_invisble" class="hidden">first hidden link</a>
</div>

<div style="display: none;">
  <a id="invisible" class="visibility hidden">hidden link</a>
</div>

<div>
  <a id="visible" class="visibility">visible link</a>
</div>

<div>
  Number 42
</div>

<ul>
  <li class="beatle guitarist" id="john">John</li>
  <li class="beatle bassist drummer" id="paul">Paul</li>
  <li class="beatle guitarist" id="george">George</li>
  <li class="beatle drummer" id="ringo">Ringo</li>
</ul>

<div>
  <div class="singular">singular</div>
  <div class="multiple">multiple one</div>
  <div class="multiple">multiple two</div>
  <div class="almost_singular but_not_quite">almost singular but not quite</div>
  <div class="almost_singular">almost singular</div>
</div>

<input type="text" disabled="disabled" name="disabled_text" value="This is disabled"/>
<input type="hidden" id="hidden_input" name="hidden_input" value="This is a hidden input"/>

<div>
  <a id="link_placeholder">No href</a>
  <a id="link_blank_href" href="">Blank href</a>
</div>

<div id="uppercase" style="text-transform: uppercase;">
  text here
</div>

<div id="ancestor3">
  Ancestor
  <div id="ancestor2">
    Ancestor
    <div id="ancestor1">
      Ancestor
      <div id="child">Child</div>
    </div>
    <div id="ancestor1_sibiling">
      ASibling
    </div>
  </div>
  <button id="ancestor_button" type="submit" disabled>
    <img id="button_img" width="20" height="20" alt="button img"/>
  </button>
</div>

<div id="sibling_test">
  <div id="sibling_wrapper" data-pre=true>
    <div id="pre_sibling" data-pre=true>Pre Sibling</div>
    <div id="mid_sibling">Mid Sibling</div>
    <div id="post_sibling" data-post=true>Post Sibling</div>
  </div>
  <div id="other_sibling_wrapper" data-post=true>
    <div data-pre=true>Pre Sibling</div>
    <div data-post=true>Post Sibling</div>
  </div>
</div>

<div id='1escape.me' class="2escape">needs escaping</div>

<div id="normalized">
  Some text<div>More   text</div>
  <div> And more text</div>
  Even more &nbsp;&nbsp; text

  on multiple lines
</div>

<div id="non_visible_normalized" style="display: none">
  Some text<div>More   text</div>
  <div> And more text</div>
  Even more &nbsp;&nbsp; text

  on multiple lines
</div>

<div id="ws">
&#x20;&#x1680;&#x2000;&#x2001;&#x2002; &#x2003;&#x2004;&nbsp;&#x2005; &#x2006;&#x2007;&#x2008;&#x2009;&#x200A;&#x202F;&#x205F;&#x3000;
</div>

<details id="closed_details">
  <summary>
    <h6>Something</h6>
  </summary>
  <ul>
    <li>Random</li>
    <li>Things</li>
  </ul>
</details>

<details id="open_details" open>
  <summary>
    <h6>Summary</h6>
  </summary>
  <div>
    Contents
  </div>
</details>

<template id="template">
  <input />
</template>

<a href="/download.csv" download>Download Me</a>
<a href="/download.csv" download="other.csv">Download Other</a>

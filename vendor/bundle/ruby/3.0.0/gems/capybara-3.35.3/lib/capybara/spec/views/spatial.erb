<html>
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <title>spatial</title>
    <style>
      #spatial {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;
        grid-auto-rows: minmax(100px, auto);
      }
      .footer {
        grid-column: 1 / 4;
      }
    </style>
  </head>

  <body id="spatial">
    <div class="corner">1</div>
    <div class="distance">2</div>
    <div class="corner">3</div>
    <div>4</div>
    <div class="center">5</div>
    <div>6</div>
    <div class="corner">7</div>
    <div>8</div>
    <div class="corner">9</div>
    <div class="footer distance">10</div>
  </body>
</html>


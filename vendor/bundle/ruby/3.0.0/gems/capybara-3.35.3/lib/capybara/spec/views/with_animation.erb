
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <title>with_animation</title>
    <script src="/jquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/jquery-ui.js" type="text/javascript" charset="utf-8"></script>
    <script src="/test.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
      $(document).on('transitionend', function(){
        $(document.body).append('<div>Transition Ended</div>')
      });
      $(document).on('animationend', function(){
        $(document.body).append('<div>Animation Ended</div>')
      });
      $(document).on('contextmenu', function(e){
        e.preventDefault();
      });
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }

      body {
        min-height: 2000px;
      }

      .transition.away {
          width: 0%;
      }

      a {
          display: inline-block;
          width: 100%;
          overflow: hidden;
      }

      a::after {
        content: "";
        width: 0px;
        height: 0px;
        background-color: blue;
      }

      a:not(.away) {
        height: 20px;
      }

      a.transition {
        transition: all 3s ease-in-out;
      }

      @keyframes animation {
          0%   {height: 20px; width: 100%;}
          100% {height: 0px; width: 0%;}
      }

      a.animation.away {
        animation-name: animation;
        animation-duration: 3s;
        animation-fill-mode: forwards;
      }

      @keyframes pseudo_grow {
        100% { height: 100px, width: 100px };
      }

      a.animation.pseudo::after {
        animation: pseudo_grow 3s forwards;
      }
    </style>
  </head>

  <body id="with_animation">
    <a href='#' class='transition' onclick='this.classList.add("away")'>transition me away</a>
    <a href='#' class='animation' onclick='this.classList.add("away")' oncontextmenu='this.classList.add("pseudo")'>
      animate me away
    </a>
  </body>
</html>


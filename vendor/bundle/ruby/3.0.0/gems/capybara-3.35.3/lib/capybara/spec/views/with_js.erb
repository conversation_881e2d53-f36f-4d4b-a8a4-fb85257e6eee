
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <title>with_js</title>
    <script src="/jquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/jquery-ui.js" type="text/javascript" charset="utf-8"></script>
    <script src="/test.js" type="text/javascript" charset="utf-8"></script>
  </head>

  <body id="with_js">
    <h1>FooBar</h1>

    <p id="change">This is text</p>
    <a id="drag_link" href='#'>This link is non-HTML5 draggable</a>
    <div id="drag">
      <p>This is a draggable element.</p>
    </div>
    <div id="drop">
      <p>It should be dropped here.</p>
    </div>
    <div id="drop_scroll">
      <p>It should be dropped here.</p>
    </div>
    <div id="drop_html5_scroll" class="drop">
      <p>It should be dropped here.</p>
    </div>
    <div id="drag_html5" draggable="true">
      <p>This is an HTML5 draggable element.</p>
    </div>
    <a id="drag_link_html5" href="#"><p>This is an HTML5 draggable link</p></a>
    <div id="drop_html5" class="drop">
      <p>It should be dropped here.</p>
    </div>

    <p><a href="#" id="clickable">Click me</a></p>
    <p><a href="#" id="slow-click">Slowly</a></p>
    <p><span id="aria-button" role="button">ARIA button</span></p>
    <p><span role="button">ARIA button2</span></p>

    <p>
      <select id="waiter">
        <option>Foo</option>
        <option>My Waiting Option</option>
      </select>
    </p>

    <p>
      <input type="text" name="with_focus_event" value="" id="with_focus_event"/>
    </p>

    <p>
      <input type="text" name="with_change_event" value="default value" id="with_change_event"/>
    </p>

    <p>
      <div contenteditable='true' id='existing_content_editable'>Editable content</div>
      <div contenteditable='true' id='blank_content_editable' style='height: 1em;'></div>
      <div contenteditable='true' id='existing_content_editable_child_parent' style='height: 1em;'>
        Some content
        <div id='existing_content_editable_child' style='height: 1em;'>Content</div>
      </div>
    </p>

    <p>
      <input type="checkbox" id="checkbox_with_event"/>
    </p>

    <p>
      <input type="submit" id="fire_ajax_request" value="Fire Ajax Request"/>
    </p>

    <p>
      <a id="reload-link" href="#">Reload!</a>
      <em>this won't change</em>
      <div id="reload-me"><em>waiting to be reloaded</em></div>
    </p>

    <p>
      <a id="reload-list" href="#">Fetch new list!</a>
      <ul id="the-list">
        <li>Item 1</li>
        <li>Item 2</li>
      </ul>
    </p>

    <p>
      <a href="#" id="change-title">Change title</a>
    </p>

    <p>
      <a href="#" id="change-size">Change size</a>
    </p>

    <p id="click-test">Click me</p>

    <p>
      <a href="#" id="open-alert">Open alert</a>
      <a href="/with_html" id="alert-page-change">Alert page change</a>
    </p>

    <p>
      <a href="#" id="open-delayed-alert">Open delayed alert</a>
      <a href="#" id="open-slow-alert">Open slow alert</a>
    </p>

    <p>
      <a href="#" id="open-confirm">Open confirm</a>
    </p>

    <p>
      <a href="#" id="open-twice">Open check twice</a>
    </p>

    <p>
      <a href="#" id="open-prompt">Open prompt</a>
    </p>

    <p>
      <a href="#" id="open-prompt-with-default">Open defaulted prompt</a>
    </p>

    <p>
      <input id="disable-on-click"/>
    </p>
    <p>
      <input id="set-storage"/>
    </p>
    <p>
      <a href="#" id="delayed-page-change">Change page</a>
      <a href="/with_html?options[]=things">Non-escaped query options</a>
      <a href="/with_html?options%5B%5D=things">Escaped query options</a>
    </p>

    <p>
      <input type="test" name="with-key-events" id="with-key-events">
      <p id="key-events-output"></p>
    </p>

    <p>
      <input type="file" id="hidden_file" style="opacity:0; display: none;">
      <label for="hidden_file">Label for hidden file input</label>
    </p>

    <p>
      <input type="file" id="multiple-file" multiple="multiple"/>
    </p>

    <div id="drag_scroll">
      <p>This is a draggable element.</p>
    </div>
    <div id="drag_html5_scroll" draggable="true">
      <p>This is an HTML5 draggable element.</p>
    </div>

    <div id="shadow"></div>
    <script type="text/javascript">
      // a javascript comment
      var aVar = 123;
    </script>
  </body>
</html>


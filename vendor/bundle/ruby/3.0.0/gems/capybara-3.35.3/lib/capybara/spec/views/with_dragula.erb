<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <title>with_dragula</title>

  </head>

  <body id="with_dragula">
    <div id="sortable">
      <div class="item1">Item 1</div>
      <div class="item2">Item 2</div>
      <div class="item3">Item 3</div>
      <div class="item4">Item 4</div>
      <div class="item5">Item 5</div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.2/dragula.js" type="text/javascript"></script>
    <script>
      let container = document.getElementById("sortable");
      dragula([container]);
      container.className = "ready"
    </script>
  </body>
</html>


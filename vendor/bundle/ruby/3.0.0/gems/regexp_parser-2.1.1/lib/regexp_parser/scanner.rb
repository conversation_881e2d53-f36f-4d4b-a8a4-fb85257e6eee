# -*- warn-indent:false;  -*-

# line 1 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"

# line 649 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"


# THIS IS A GENERATED FILE, DO NOT EDIT DIRECTLY
# This file was generated from lib/regexp_parser/scanner/scanner.rl

require 'regexp_parser/error'

class Regexp::Scanner
  # General scanner error (catch all)
  class ScannerError < Regexp::Parser::Error; end

  # Base for all scanner validation errors
  class ValidationError < Regexp::Parser::Error
    def initialize(reason)
      super reason
    end
  end

  # Unexpected end of pattern
  class PrematureEndError < ScannerError
    def initialize(where = '')
      super "Premature end of pattern at #{where}"
    end
  end

  # Invalid sequence format. Used for escape sequences, mainly.
  class InvalidSequenceError < ValidationError
    def initialize(what = 'sequence', where = '')
      super "Invalid #{what} at #{where}"
    end
  end

  # Invalid group. Used for named groups.
  class InvalidGroupError < ValidationError
    def initialize(what, reason)
      super "Invalid #{what}, #{reason}."
    end
  end

  # Invalid groupOption. Used for inline options.
  class InvalidGroupOption < ValidationError
    def initialize(option, text)
      super "Invalid group option #{option} in #{text}"
    end
  end

  # Invalid back reference. Used for name a number refs/calls.
  class InvalidBackrefError < ValidationError
    def initialize(what, reason)
      super "Invalid back reference #{what}, #{reason}"
    end
  end

  # The property name was not recognized by the scanner.
  class UnknownUnicodePropertyError < ValidationError
    def initialize(name)
      super "Unknown unicode character property name #{name}"
    end
  end

  # Scans the given regular expression text, or Regexp object and collects the
  # emitted token into an array that gets returned at the end. If a block is
  # given, it gets called for each emitted token.
  #
  # This method may raise errors if a syntax error is encountered.
  # --------------------------------------------------------------------------
  def self.scan(input_object, options: nil, &block)
    new.scan(input_object, options: options, &block)
  end

  def scan(input_object, options: nil, &block)
    self.literal = nil
    stack = []

    input = input_object.is_a?(Regexp) ? input_object.source : input_object
    self.free_spacing = free_spacing?(input_object, options)
    self.spacing_stack = [{:free_spacing => free_spacing, :depth => 0}]

    data  = input.unpack("c*") if input.is_a?(String)
    eof   = data.length

    self.tokens = []
    self.block  = block_given? ? block : nil

    self.set_depth = 0
    self.group_depth = 0
    self.conditional_stack = []
    self.char_pos = 0

    
# line 96 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
class << self
	attr_accessor :_re_scanner_trans_keys
	private :_re_scanner_trans_keys, :_re_scanner_trans_keys=
end
self._re_scanner_trans_keys = [
	0, 0, -128, -65, -128, -65, 
	-128, -65, 41, 41, 39, 
	57, 39, 39, 33, 62, 
	62, 62, 39, 60, 39, 57, 
	39, 39, 48, 57, 39, 
	57, 39, 57, 48, 57, 
	39, 39, 45, 62, 62, 62, 
	48, 57, 48, 62, 43, 
	62, 48, 57, 62, 62, 
	39, 60, 39, 57, 39, 39, 
	48, 57, 39, 57, 39, 
	57, 48, 57, 45, 62, 
	62, 62, 48, 57, 48, 62, 
	43, 62, 48, 57, 48, 
	57, 48, 125, 44, 125, 
	123, 123, 9, 122, 9, 125, 
	9, 122, -128, -65, -128, 
	-65, 38, 38, 94, 120, 
	97, 120, 108, 115, 110, 112, 
	117, 117, 109, 109, 58, 
	58, 93, 93, 104, 104, 
	97, 97, 99, 99, 105, 105, 
	105, 105, 108, 108, 97, 
	97, 110, 110, 107, 107, 
	110, 110, 116, 116, 114, 114, 
	108, 108, 105, 105, 103, 
	103, 105, 105, 116, 116, 
	114, 114, 97, 97, 112, 112, 
	104, 104, 111, 111, 119, 
	119, 101, 101, 114, 114, 
	114, 117, 105, 105, 110, 110, 
	110, 110, 99, 99, 112, 
	112, 97, 97, 99, 99, 
	101, 101, 112, 112, 112, 112, 
	111, 111, 114, 114, 100, 
	100, 100, 100, -128, -65, 
	-128, -65, 45, 45, 92, 92, 
	92, 92, 45, 45, 92, 
	92, 92, 92, 48, 123, 
	48, 102, 48, 102, 48, 102, 
	48, 102, 9, 125, 9, 
	125, 9, 125, 9, 125, 
	9, 125, 9, 125, 48, 123, 
	39, 39, 41, 41, 41, 
	57, 62, 62, -128, 127, 
	-62, -12, 1, 127, 1, 127, 
	9, 32, 33, 126, 10, 
	10, 63, 63, 33, 126, 
	33, 126, 62, 62, 43, 63, 
	43, 63, 43, 63, 65, 
	122, 44, 57, 43, 63, 
	68, 119, 80, 112, -62, 125, 
	-128, -65, -128, -65, -128, 
	-65, 38, 38, 38, 93, 
	58, 58, 67, 120, -62, 125, 
	-128, -65, -128, -65, -128, 
	-65, 48, 55, 48, 55, 
	77, 77, 45, 45, 0, 0, 
	67, 99, 45, 45, 0, 
	0, 92, 92, 48, 102, 
	39, 60, 39, 57, 49, 57, 
	41, 57, 45, 62, 0
]

class << self
	attr_accessor :_re_scanner_key_spans
	private :_re_scanner_key_spans, :_re_scanner_key_spans=
end
self._re_scanner_key_spans = [
	0, 64, 64, 64, 1, 19, 1, 30, 
	1, 22, 19, 1, 10, 19, 19, 10, 
	1, 18, 1, 10, 15, 20, 10, 1, 
	22, 19, 1, 10, 19, 19, 10, 18, 
	1, 10, 15, 20, 10, 10, 78, 82, 
	1, 114, 117, 114, 64, 64, 1, 27, 
	24, 8, 3, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	4, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 64, 
	64, 1, 1, 1, 1, 1, 1, 76, 
	55, 55, 55, 55, 117, 117, 117, 117, 
	117, 117, 76, 1, 1, 17, 1, 256, 
	51, 127, 127, 24, 94, 1, 1, 94, 
	94, 1, 21, 21, 21, 58, 14, 21, 
	52, 33, 188, 64, 64, 64, 1, 56, 
	1, 54, 188, 64, 64, 64, 8, 8, 
	1, 1, 0, 33, 1, 0, 1, 55, 
	22, 19, 9, 17, 18
]

class << self
	attr_accessor :_re_scanner_index_offsets
	private :_re_scanner_index_offsets, :_re_scanner_index_offsets=
end
self._re_scanner_index_offsets = [
	0, 0, 65, 130, 195, 197, 217, 219, 
	250, 252, 275, 295, 297, 308, 328, 348, 
	359, 361, 380, 382, 393, 409, 430, 441, 
	443, 466, 486, 488, 499, 519, 539, 550, 
	569, 571, 582, 598, 619, 630, 641, 720, 
	803, 805, 920, 1038, 1153, 1218, 1283, 1285, 
	1313, 1338, 1347, 1351, 1353, 1355, 1357, 1359, 
	1361, 1363, 1365, 1367, 1369, 1371, 1373, 1375, 
	1377, 1379, 1381, 1383, 1385, 1387, 1389, 1391, 
	1393, 1395, 1397, 1399, 1401, 1403, 1405, 1407, 
	1409, 1414, 1416, 1418, 1420, 1422, 1424, 1426, 
	1428, 1430, 1432, 1434, 1436, 1438, 1440, 1442, 
	1507, 1572, 1574, 1576, 1578, 1580, 1582, 1584, 
	1661, 1717, 1773, 1829, 1885, 2003, 2121, 2239, 
	2357, 2475, 2593, 2670, 2672, 2674, 2692, 2694, 
	2951, 3003, 3131, 3259, 3284, 3379, 3381, 3383, 
	3478, 3573, 3575, 3597, 3619, 3641, 3700, 3715, 
	3737, 3790, 3824, 4013, 4078, 4143, 4208, 4210, 
	4267, 4269, 4324, 4513, 4578, 4643, 4708, 4717, 
	4726, 4728, 4730, 4731, 4765, 4767, 4768, 4770, 
	4826, 4849, 4869, 4879, 4897
]

class << self
	attr_accessor :_re_scanner_indicies
	private :_re_scanner_indicies, :_re_scanner_indicies=
end
self._re_scanner_indicies = [
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	1, 1, 1, 1, 1, 1, 1, 1, 
	0, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 0, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 0, 6, 5, 8, 7, 7, 
	7, 7, 7, 4, 7, 7, 4, 4, 
	4, 4, 4, 4, 4, 4, 4, 4, 
	7, 8, 7, 10, 9, 9, 9, 9, 
	9, 9, 9, 9, 9, 9, 9, 4, 
	9, 9, 4, 4, 4, 4, 4, 4, 
	4, 4, 4, 4, 9, 9, 9, 10, 
	8, 9, 8, 9, 12, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 13, 11, 15, 14, 14, 14, 14, 
	14, 16, 14, 14, 17, 18, 18, 18, 
	18, 18, 18, 18, 18, 18, 14, 15, 
	14, 19, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 11, 15, 11, 11, 11, 
	11, 11, 11, 11, 11, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 19, 11, 
	15, 11, 11, 11, 20, 11, 20, 11, 
	11, 18, 18, 18, 18, 18, 18, 18, 
	18, 18, 18, 11, 19, 19, 19, 19, 
	19, 19, 19, 19, 19, 19, 11, 15, 
	11, 22, 21, 21, 23, 24, 24, 24, 
	24, 24, 24, 24, 24, 24, 21, 21, 
	21, 21, 15, 21, 15, 21, 25, 24, 
	24, 24, 24, 24, 24, 24, 24, 24, 
	11, 25, 25, 25, 25, 25, 25, 25, 
	25, 25, 25, 11, 11, 11, 11, 15, 
	11, 26, 11, 26, 11, 11, 24, 24, 
	24, 24, 24, 24, 24, 24, 24, 24, 
	11, 11, 11, 11, 15, 11, 25, 25, 
	25, 25, 25, 25, 25, 25, 25, 25, 
	11, 15, 11, 27, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	11, 11, 11, 11, 11, 11, 11, 11, 
	28, 11, 30, 29, 29, 29, 29, 29, 
	31, 29, 29, 11, 32, 32, 32, 32, 
	32, 32, 32, 32, 32, 29, 30, 29, 
	33, 32, 32, 32, 32, 32, 32, 32, 
	32, 32, 11, 30, 11, 11, 11, 11, 
	11, 11, 11, 11, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 33, 11, 30, 
	11, 11, 11, 34, 11, 34, 11, 11, 
	32, 32, 32, 32, 32, 32, 32, 32, 
	32, 32, 11, 33, 33, 33, 33, 33, 
	33, 33, 33, 33, 33, 11, 36, 35, 
	35, 11, 37, 37, 37, 37, 37, 37, 
	37, 37, 37, 35, 35, 35, 35, 30, 
	35, 30, 35, 38, 37, 37, 37, 37, 
	37, 37, 37, 37, 37, 11, 38, 38, 
	38, 38, 38, 38, 38, 38, 38, 38, 
	11, 11, 11, 11, 30, 11, 39, 11, 
	39, 11, 11, 37, 37, 37, 37, 37, 
	37, 37, 37, 37, 37, 11, 11, 11, 
	11, 30, 11, 38, 38, 38, 38, 38, 
	38, 38, 38, 38, 38, 11, 41, 41, 
	41, 41, 41, 41, 41, 41, 41, 41, 
	40, 41, 41, 41, 41, 41, 41, 41, 
	41, 41, 41, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 42, 40, 
	41, 40, 40, 40, 43, 43, 43, 43, 
	43, 43, 43, 43, 43, 43, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 40, 40, 40, 40, 40, 40, 40, 
	40, 42, 40, 44, 45, 46, 46, 46, 
	46, 46, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 46, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 46, 46, 45, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 45, 45, 
	45, 46, 45, 45, 45, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 45, 
	45, 45, 47, 46, 45, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 45, 
	46, 46, 46, 46, 46, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 46, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 46, 46, 45, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 45, 45, 45, 46, 45, 45, 45, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 45, 45, 45, 45, 46, 45, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 45, 45, 48, 45, 46, 46, 
	46, 46, 46, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 46, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 46, 46, 45, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 45, 
	45, 45, 46, 45, 45, 45, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	45, 45, 45, 45, 46, 45, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	46, 46, 46, 46, 46, 46, 46, 46, 
	45, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 49, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 49, 53, 52, 56, 55, 55, 
	57, 58, 59, 60, 55, 55, 61, 55, 
	55, 55, 55, 62, 55, 55, 55, 63, 
	55, 55, 64, 55, 65, 55, 66, 67, 
	55, 57, 58, 59, 60, 55, 55, 61, 
	55, 55, 55, 55, 62, 55, 55, 55, 
	63, 55, 55, 64, 55, 65, 55, 66, 
	67, 55, 68, 55, 55, 55, 55, 55, 
	55, 69, 55, 70, 55, 71, 55, 72, 
	55, 73, 55, 74, 55, 75, 55, 76, 
	55, 73, 55, 77, 55, 78, 55, 73, 
	55, 79, 55, 80, 55, 81, 55, 73, 
	55, 82, 55, 83, 55, 84, 55, 73, 
	55, 85, 55, 86, 55, 87, 55, 73, 
	55, 88, 55, 89, 55, 90, 55, 73, 
	55, 91, 55, 92, 55, 93, 55, 73, 
	55, 94, 55, 55, 95, 55, 96, 55, 
	87, 55, 97, 55, 87, 55, 98, 55, 
	99, 55, 100, 55, 73, 55, 101, 55, 
	92, 55, 102, 55, 103, 55, 73, 55, 
	60, 55, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 104, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 104, 107, 45, 109, 108, 
	111, 108, 112, 45, 114, 113, 116, 113, 
	117, 117, 117, 117, 117, 117, 117, 117, 
	117, 117, 45, 45, 45, 45, 45, 45, 
	45, 117, 117, 117, 117, 117, 117, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 117, 117, 117, 117, 117, 117, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 118, 45, 119, 119, 119, 
	119, 119, 119, 119, 119, 119, 119, 45, 
	45, 45, 45, 45, 45, 45, 119, 119, 
	119, 119, 119, 119, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 119, 119, 
	119, 119, 119, 119, 45, 120, 120, 120, 
	120, 120, 120, 120, 120, 120, 120, 45, 
	45, 45, 45, 45, 45, 45, 120, 120, 
	120, 120, 120, 120, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 120, 120, 
	120, 120, 120, 120, 45, 121, 121, 121, 
	121, 121, 121, 121, 121, 121, 121, 45, 
	45, 45, 45, 45, 45, 45, 121, 121, 
	121, 121, 121, 121, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 121, 121, 
	121, 121, 121, 121, 45, 122, 122, 122, 
	122, 122, 122, 122, 122, 122, 122, 45, 
	45, 45, 45, 45, 45, 45, 122, 122, 
	122, 122, 122, 122, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 122, 122, 
	122, 122, 122, 122, 45, 118, 118, 118, 
	118, 118, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 118, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 123, 123, 123, 123, 
	123, 123, 123, 123, 123, 123, 45, 45, 
	45, 45, 45, 45, 45, 123, 123, 123, 
	123, 123, 123, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 123, 123, 123, 
	123, 123, 123, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 121, 45, 118, 118, 118, 118, 118, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 118, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 124, 124, 124, 124, 124, 124, 
	124, 124, 124, 124, 45, 45, 45, 45, 
	45, 45, 45, 124, 124, 124, 124, 124, 
	124, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 124, 124, 124, 124, 124, 
	124, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 121, 
	45, 118, 118, 118, 118, 118, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	118, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	125, 125, 125, 125, 125, 125, 125, 125, 
	125, 125, 45, 45, 45, 45, 45, 45, 
	45, 125, 125, 125, 125, 125, 125, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 125, 125, 125, 125, 125, 125, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 121, 45, 118, 
	118, 118, 118, 118, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 118, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 126, 126, 
	126, 126, 126, 126, 126, 126, 126, 126, 
	45, 45, 45, 45, 45, 45, 45, 126, 
	126, 126, 126, 126, 126, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 126, 
	126, 126, 126, 126, 126, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 121, 45, 118, 118, 118, 
	118, 118, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 118, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 127, 127, 127, 127, 
	127, 127, 127, 127, 127, 127, 45, 45, 
	45, 45, 45, 45, 45, 127, 127, 127, 
	127, 127, 127, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 127, 127, 127, 
	127, 127, 127, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 121, 45, 118, 118, 118, 118, 118, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 118, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 121, 
	45, 129, 129, 129, 129, 129, 129, 129, 
	129, 129, 129, 128, 128, 128, 128, 128, 
	128, 128, 129, 129, 129, 129, 129, 129, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 129, 129, 129, 129, 129, 129, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 128, 128, 128, 128, 
	128, 128, 128, 128, 45, 128, 132, 131, 
	133, 130, 133, 130, 130, 130, 130, 130, 
	130, 134, 134, 134, 134, 134, 134, 134, 
	134, 134, 134, 130, 132, 135, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 136, 136, 
	136, 136, 136, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 137, 
	137, 137, 137, 137, 137, 137, 137, 138, 
	138, 138, 138, 138, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 139, 140, 
	140, 141, 142, 140, 140, 140, 143, 144, 
	145, 146, 140, 140, 147, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 148, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 149, 150, 151, 152, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 153, 154, 151, 140, 137, 140, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 2, 2, 2, 
	2, 2, 2, 2, 2, 3, 3, 3, 
	3, 3, 3, 3, 3, 3, 3, 3, 
	3, 3, 3, 3, 3, 136, 136, 136, 
	136, 136, 155, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 155, 155, 155, 155, 155, 155, 155, 
	155, 137, 155, 137, 137, 137, 137, 137, 
	137, 137, 137, 138, 138, 138, 138, 138, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 137, 137, 137, 137, 137, 137, 
	137, 137, 139, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 137, 156, 139, 139, 139, 139, 139, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 156, 156, 156, 156, 156, 156, 
	156, 156, 139, 156, 140, 140, 155, 155, 
	140, 140, 140, 155, 155, 155, 155, 140, 
	140, 155, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 155, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 155, 155, 
	155, 155, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 140, 140, 
	140, 140, 140, 140, 140, 140, 155, 155, 
	155, 140, 155, 158, 141, 160, 159, 163, 
	162, 5, 162, 162, 162, 164, 165, 161, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	8, 162, 166, 163, 8, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 8, 162, 161, 162, 
	161, 162, 162, 162, 161, 161, 161, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 167, 
	162, 161, 161, 161, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 162, 162, 162, 162, 162, 
	162, 162, 162, 161, 162, 8, 9, 170, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 169, 169, 169, 169, 169, 
	169, 169, 169, 170, 169, 172, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 171, 171, 171, 171, 171, 171, 171, 
	171, 172, 171, 174, 173, 173, 173, 173, 
	173, 173, 173, 173, 173, 173, 173, 173, 
	173, 173, 173, 173, 173, 173, 173, 174, 
	173, 176, 176, 175, 175, 175, 175, 176, 
	175, 175, 175, 177, 175, 175, 175, 175, 
	175, 175, 175, 175, 175, 175, 175, 175, 
	175, 175, 176, 175, 175, 175, 175, 175, 
	175, 175, 176, 175, 175, 175, 175, 178, 
	175, 175, 175, 179, 175, 175, 175, 175, 
	175, 175, 175, 175, 175, 175, 175, 175, 
	175, 175, 176, 175, 181, 180, 180, 180, 
	43, 43, 43, 43, 43, 43, 43, 43, 
	43, 43, 180, 183, 182, 182, 182, 182, 
	182, 182, 182, 182, 182, 182, 182, 182, 
	182, 182, 182, 182, 182, 182, 182, 183, 
	182, 184, 45, 45, 45, 184, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 184, 
	184, 45, 45, 45, 184, 184, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 184, 45, 45, 45, 184, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	184, 45, 45, 45, 184, 45, 185, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 45, 45, 
	45, 45, 45, 45, 45, 45, 185, 45, 
	186, 186, 186, 186, 186, 186, 186, 186, 
	186, 186, 186, 186, 186, 186, 186, 186, 
	186, 186, 186, 186, 186, 186, 186, 186, 
	186, 186, 186, 186, 186, 186, 187, 187, 
	187, 187, 187, 187, 187, 187, 187, 187, 
	187, 187, 187, 187, 187, 187, 188, 188, 
	188, 188, 188, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 189, 50, 190, 50, 189, 189, 
	189, 189, 50, 191, 189, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 189, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 192, 193, 194, 195, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 189, 189, 189, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 50, 50, 50, 
	50, 50, 50, 50, 50, 196, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 51, 51, 
	51, 51, 51, 51, 51, 51, 196, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 197, 
	197, 197, 197, 197, 197, 197, 197, 196, 
	198, 196, 200, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 199, 199, 199, 199, 199, 199, 199, 
	199, 201, 199, 204, 203, 206, 206, 205, 
	205, 205, 206, 205, 205, 205, 205, 206, 
	205, 205, 206, 205, 205, 206, 205, 205, 
	205, 206, 205, 205, 205, 206, 206, 206, 
	205, 205, 205, 206, 206, 206, 206, 206, 
	206, 205, 206, 205, 205, 205, 205, 205, 
	206, 205, 206, 205, 206, 206, 206, 206, 
	206, 206, 206, 205, 207, 207, 207, 207, 
	207, 207, 207, 207, 207, 207, 207, 207, 
	207, 207, 207, 207, 207, 207, 207, 207, 
	207, 207, 207, 207, 207, 207, 207, 207, 
	207, 207, 208, 208, 208, 208, 208, 208, 
	208, 208, 208, 208, 208, 208, 208, 208, 
	208, 208, 209, 209, 209, 209, 209, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 210, 105, 
	105, 105, 210, 210, 210, 210, 105, 105, 
	210, 105, 211, 212, 212, 212, 212, 212, 
	212, 212, 213, 213, 105, 105, 105, 105, 
	105, 210, 105, 45, 45, 214, 215, 105, 
	105, 45, 215, 105, 105, 45, 105, 216, 
	105, 105, 217, 105, 215, 215, 105, 105, 
	105, 215, 215, 105, 45, 210, 210, 210, 
	210, 105, 105, 218, 218, 107, 215, 218, 
	218, 105, 215, 105, 105, 105, 105, 105, 
	218, 105, 217, 105, 218, 215, 218, 219, 
	218, 215, 220, 105, 45, 210, 210, 210, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 105, 105, 105, 105, 105, 105, 105, 
	105, 221, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 106, 106, 106, 106, 106, 106, 
	106, 106, 221, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 222, 222, 222, 222, 222, 
	222, 222, 222, 221, 224, 224, 224, 224, 
	224, 224, 224, 224, 223, 226, 226, 226, 
	226, 226, 226, 226, 226, 225, 228, 108, 
	230, 229, 108, 232, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 113, 113, 113, 113, 113, 
	113, 113, 113, 233, 113, 235, 234, 113, 
	116, 113, 237, 237, 237, 237, 237, 237, 
	237, 237, 237, 237, 236, 236, 236, 236, 
	236, 236, 236, 237, 237, 237, 237, 237, 
	237, 236, 236, 236, 236, 236, 236, 236, 
	236, 236, 236, 236, 236, 236, 236, 236, 
	236, 236, 236, 236, 236, 236, 236, 236, 
	236, 236, 236, 237, 237, 237, 237, 237, 
	237, 236, 239, 238, 238, 238, 238, 238, 
	240, 238, 238, 238, 241, 241, 241, 241, 
	241, 241, 241, 241, 241, 238, 238, 242, 
	238, 132, 131, 131, 131, 131, 131, 243, 
	131, 131, 243, 243, 243, 243, 243, 243, 
	243, 243, 243, 243, 131, 134, 134, 134, 
	134, 134, 134, 134, 134, 134, 243, 133, 
	243, 243, 243, 243, 243, 243, 134, 134, 
	134, 134, 134, 134, 134, 134, 134, 134, 
	243, 243, 135, 135, 243, 243, 243, 243, 
	243, 243, 243, 243, 243, 243, 135, 135, 
	135, 135, 132, 135, 0
]

class << self
	attr_accessor :_re_scanner_trans_targs
	private :_re_scanner_trans_targs, :_re_scanner_trans_targs=
end
self._re_scanner_trans_targs = [
	119, 120, 1, 2, 119, 4, 119, 6, 
	119, 8, 129, 119, 10, 17, 11, 119, 
	12, 16, 14, 13, 15, 18, 19, 23, 
	21, 20, 22, 25, 31, 26, 119, 27, 
	29, 28, 30, 32, 33, 35, 34, 36, 
	119, 38, 135, 39, 41, 0, 42, 43, 
	137, 138, 138, 44, 138, 138, 138, 138, 
	48, 49, 60, 64, 68, 72, 76, 80, 
	85, 89, 91, 94, 50, 57, 51, 55, 
	52, 53, 54, 138, 56, 58, 59, 61, 
	62, 63, 65, 66, 67, 69, 70, 71, 
	73, 74, 75, 77, 78, 79, 81, 83, 
	82, 84, 86, 87, 88, 90, 92, 93, 
	146, 146, 95, 98, 146, 152, 146, 154, 
	101, 146, 155, 146, 157, 104, 107, 105, 
	106, 146, 108, 109, 110, 111, 112, 113, 
	146, 159, 160, 115, 116, 160, 117, 118, 
	3, 121, 122, 123, 124, 125, 119, 126, 
	119, 130, 131, 119, 132, 119, 133, 119, 
	119, 134, 119, 119, 119, 119, 119, 119, 
	127, 119, 128, 119, 5, 119, 7, 119, 
	119, 119, 119, 119, 119, 119, 119, 119, 
	119, 119, 9, 24, 119, 37, 119, 119, 
	136, 40, 139, 140, 141, 138, 142, 143, 
	144, 138, 138, 138, 138, 45, 138, 138, 
	46, 138, 138, 138, 47, 145, 145, 147, 
	148, 149, 146, 150, 150, 146, 97, 146, 
	100, 146, 146, 103, 114, 146, 96, 146, 
	151, 146, 146, 146, 153, 146, 99, 146, 
	156, 158, 146, 102, 146, 146, 160, 161, 
	162, 163, 164, 160
]

class << self
	attr_accessor :_re_scanner_trans_actions
	private :_re_scanner_trans_actions, :_re_scanner_trans_actions=
end
self._re_scanner_trans_actions = [
	1, 2, 0, 0, 3, 4, 5, 0, 
	6, 0, 7, 8, 0, 0, 0, 9, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 10, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	11, 0, 0, 0, 0, 0, 0, 0, 
	13, 14, 15, 0, 16, 17, 18, 19, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 20, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	21, 22, 0, 0, 24, 0, 25, 0, 
	0, 26, 0, 27, 0, 0, 0, 0, 
	0, 28, 0, 0, 0, 0, 0, 0, 
	29, 0, 30, 0, 0, 31, 0, 0, 
	0, 0, 0, 0, 0, 0, 34, 35, 
	36, 0, 0, 37, 0, 38, 39, 40, 
	41, 39, 42, 43, 44, 45, 46, 47, 
	48, 49, 0, 50, 0, 51, 0, 52, 
	53, 54, 55, 56, 57, 58, 59, 60, 
	61, 62, 0, 0, 63, 0, 64, 65, 
	67, 0, 0, 39, 39, 68, 0, 39, 
	69, 70, 71, 72, 73, 0, 74, 75, 
	0, 76, 77, 78, 0, 79, 80, 0, 
	39, 39, 81, 82, 83, 84, 0, 85, 
	0, 86, 87, 0, 0, 88, 0, 89, 
	0, 90, 91, 92, 39, 93, 0, 94, 
	39, 0, 95, 0, 96, 97, 98, 39, 
	39, 39, 39, 99
]

class << self
	attr_accessor :_re_scanner_to_state_actions
	private :_re_scanner_to_state_actions, :_re_scanner_to_state_actions=
end
self._re_scanner_to_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 32, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	66, 66, 66, 0, 0, 0, 0, 0, 
	0, 66, 66, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	66, 0, 0, 0, 0
]

class << self
	attr_accessor :_re_scanner_from_state_actions
	private :_re_scanner_from_state_actions, :_re_scanner_from_state_actions=
end
self._re_scanner_from_state_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 33, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	33, 33, 33, 0, 0, 0, 0, 0, 
	0, 33, 33, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	33, 0, 0, 0, 0
]

class << self
	attr_accessor :_re_scanner_eof_actions
	private :_re_scanner_eof_actions, :_re_scanner_eof_actions=
end
self._re_scanner_eof_actions = [
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	12, 12, 12, 12, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 23, 23, 0, 23, 23, 0, 23, 
	23, 23, 23, 23, 23, 23, 23, 23, 
	23, 23, 23, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 23, 0, 0, 0, 0, 0, 
	0, 0, 23, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0
]

class << self
	attr_accessor :_re_scanner_eof_trans
	private :_re_scanner_eof_trans, :_re_scanner_eof_trans=
end
self._re_scanner_eof_trans = [
	0, 1, 1, 1, 5, 5, 5, 5, 
	1, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 12, 12, 12, 
	12, 12, 12, 12, 12, 41, 41, 41, 
	0, 0, 0, 0, 50, 50, 53, 55, 
	55, 55, 55, 55, 55, 55, 55, 55, 
	55, 55, 55, 55, 55, 55, 55, 55, 
	55, 55, 55, 55, 55, 55, 55, 55, 
	55, 55, 55, 55, 55, 55, 55, 55, 
	55, 55, 55, 55, 55, 55, 55, 55, 
	55, 55, 55, 55, 55, 55, 55, 105, 
	105, 0, 0, 111, 0, 0, 116, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 131, 131, 131, 131, 0, 
	156, 156, 157, 157, 156, 158, 160, 162, 
	162, 169, 170, 172, 174, 176, 181, 183, 
	0, 0, 0, 197, 197, 197, 197, 200, 
	203, 0, 0, 222, 222, 222, 224, 226, 
	228, 228, 228, 232, 232, 232, 232, 237, 
	0, 244, 244, 244, 244
]

class << self
	attr_accessor :re_scanner_start
end
self.re_scanner_start = 119;
class << self
	attr_accessor :re_scanner_first_final
end
self.re_scanner_first_final = 119;
class << self
	attr_accessor :re_scanner_error
end
self.re_scanner_error = 0;

class << self
	attr_accessor :re_scanner_en_char_type
end
self.re_scanner_en_char_type = 136;
class << self
	attr_accessor :re_scanner_en_unicode_property
end
self.re_scanner_en_unicode_property = 137;
class << self
	attr_accessor :re_scanner_en_character_set
end
self.re_scanner_en_character_set = 138;
class << self
	attr_accessor :re_scanner_en_set_escape_sequence
end
self.re_scanner_en_set_escape_sequence = 145;
class << self
	attr_accessor :re_scanner_en_escape_sequence
end
self.re_scanner_en_escape_sequence = 146;
class << self
	attr_accessor :re_scanner_en_conditional_expression
end
self.re_scanner_en_conditional_expression = 160;
class << self
	attr_accessor :re_scanner_en_main
end
self.re_scanner_en_main = 119;


# line 739 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
    
# line 1077 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
begin
	p ||= 0
	pe ||= data.length
	cs = re_scanner_start
	top = 0
	ts = nil
	te = nil
	act = 0
end

# line 740 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
    
# line 1090 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
begin
	testEof = false
	_slen, _trans, _keys, _inds, _acts, _nacts = nil
	_goto_level = 0
	_resume = 10
	_eof_trans = 15
	_again = 20
	_test_eof = 30
	_out = 40
	while true
	if _goto_level <= 0
	if p == pe
		_goto_level = _test_eof
		next
	end
	if cs == 0
		_goto_level = _out
		next
	end
	end
	if _goto_level <= _resume
	case _re_scanner_from_state_actions[cs] 
	when 33 then
# line 1 "NONE"
		begin
ts = p
		end
# line 1118 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
	end
	_keys = cs << 1
	_inds = _re_scanner_index_offsets[cs]
	_slen = _re_scanner_key_spans[cs]
	_wide = data[p].ord
	_trans = if (   _slen > 0 && 
			_re_scanner_trans_keys[_keys] <= _wide && 
			_wide <= _re_scanner_trans_keys[_keys + 1] 
		    ) then
			_re_scanner_indicies[ _inds + _wide - _re_scanner_trans_keys[_keys] ] 
		 else 
			_re_scanner_indicies[ _inds + _slen ]
		 end
	end
	if _goto_level <= _eof_trans
	cs = _re_scanner_trans_targs[_trans]
	if _re_scanner_trans_actions[_trans] != 0
	case _re_scanner_trans_actions[_trans]
	when 35 then
# line 159 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth + 1 		end
	when 4 then
# line 160 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth - 1 		end
	when 39 then
# line 1 "NONE"
		begin
te = p+1
		end
	when 67 then
# line 12 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/char_type.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts-1, te)
      when '\d'; emit(:type, :digit,      text)
      when '\D'; emit(:type, :nondigit,   text)
      when '\h'; emit(:type, :hex,        text)
      when '\H'; emit(:type, :nonhex,     text)
      when '\s'; emit(:type, :space,      text)
      when '\S'; emit(:type, :nonspace,   text)
      when '\w'; emit(:type, :word,       text)
      when '\W'; emit(:type, :nonword,    text)
      when '\R'; emit(:type, :linebreak,  text)
      when '\X'; emit(:type, :xgrapheme,  text)
      end
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 13 then
# line 16 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/property.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts-1, te)
      type = (text[1] == 'P') ^ (text[3] == '^') ? :nonproperty : :property

      name = data[ts+2..te-2].pack('c*').gsub(/[\^\s_\-]/, '').downcase

      token = self.class.short_prop_map[name] || self.class.long_prop_map[name]
      raise UnknownUnicodePropertyError.new(name) unless token

      self.emit(type, token.to_sym, text)

      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 17 then
# line 187 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin  # special case, emits two tokens
      emit(:literal, :literal, '-')
      emit(:set, :intersection, '&&')
     end
		end
	when 72 then
# line 192 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te)
      if tokens.last[1] == :open
        emit(:set, :negate, text)
      else
        emit(:literal, :literal, text)
      end
     end
		end
	when 74 then
# line 213 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:set, :intersection, copy(data, ts, te))
     end
		end
	when 70 then
# line 217 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      	begin
		stack[top] = cs
		top+= 1
		cs = 145
		_goto_level = _again
		next
	end

     end
		end
	when 68 then
# line 247 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:literal, :literal, copy(data, ts, te))
     end
		end
	when 15 then
# line 251 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te)
      emit(:literal, :literal, text)
     end
		end
	when 75 then
# line 201 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      text = copy(data, ts, te)
      # ranges cant start with a subset or intersection/negation/range operator
      if tokens.last[0] == :set
        emit(:literal, :literal, text)
      else
        emit(:set, :range, text)
      end
     end
		end
	when 78 then
# line 221 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:set, :open, copy(data, ts, te))
      	begin
		stack[top] = cs
		top+= 1
		cs = 138
		_goto_level = _again
		next
	end

     end
		end
	when 73 then
# line 251 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      text = copy(data, ts, te)
      emit(:literal, :literal, text)
     end
		end
	when 16 then
# line 201 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      text = copy(data, ts, te)
      # ranges cant start with a subset or intersection/negation/range operator
      if tokens.last[0] == :set
        emit(:literal, :literal, text)
      else
        emit(:set, :range, text)
      end
     end
		end
	when 19 then
# line 221 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      emit(:set, :open, copy(data, ts, te))
      	begin
		stack[top] = cs
		top+= 1
		cs = 138
		_goto_level = _again
		next
	end

     end
		end
	when 14 then
# line 251 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      text = copy(data, ts, te)
      emit(:literal, :literal, text)
     end
		end
	when 80 then
# line 260 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      p = p - 1;
      cs = 138;
      	begin
		stack[top] = cs
		top+= 1
		cs = 146
		_goto_level = _again
		next
	end

     end
		end
	when 79 then
# line 266 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:escape, :literal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 84 then
# line 276 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts-1, te)
      emit(:backref, :number, text)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 91 then
# line 282 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:escape, :octal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 81 then
# line 287 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts-1, te)
      when '\.';  emit(:escape, :dot,               text)
      when '\|';  emit(:escape, :alternation,       text)
      when '\^';  emit(:escape, :bol,               text)
      when '\$';  emit(:escape, :eol,               text)
      when '\?';  emit(:escape, :zero_or_one,       text)
      when '\*';  emit(:escape, :zero_or_more,      text)
      when '\+';  emit(:escape, :one_or_more,       text)
      when '\(';  emit(:escape, :group_open,        text)
      when '\)';  emit(:escape, :group_close,       text)
      when '\{';  emit(:escape, :interval_open,     text)
      when '\}';  emit(:escape, :interval_close,    text)
      when '\[';  emit(:escape, :set_open,          text)
      when '\]';  emit(:escape, :set_close,         text)
      when "\\\\";
        emit(:escape, :backslash, text)
      end
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 87 then
# line 308 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      # \b is emitted as backspace only when inside a character set, otherwise
      # it is a word boundary anchor. A syntax might "normalize" it if needed.
      case text = copy(data, ts-1, te)
      when '\a'; emit(:escape, :bell,           text)
      when '\b'; emit(:escape, :backspace,      text)
      when '\e'; emit(:escape, :escape,         text)
      when '\f'; emit(:escape, :form_feed,      text)
      when '\n'; emit(:escape, :newline,        text)
      when '\r'; emit(:escape, :carriage,       text)
      when '\t'; emit(:escape, :tab,            text)
      when '\v'; emit(:escape, :vertical_tab,   text)
      end
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 28 then
# line 324 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts-1, te)
      if text[2].chr == '{'
        emit(:escape, :codepoint_list, text)
      else
        emit(:escape, :codepoint,      text)
      end
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 97 then
# line 334 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:escape, :hex, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 24 then
# line 343 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit_meta_control_sequence(data, ts, te, :control)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 26 then
# line 348 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit_meta_control_sequence(data, ts, te, :meta_sequence)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 85 then
# line 353 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      p = p - 1;
      cs = ((in_set? ? 138 : 119));
      	begin
		stack[top] = cs
		top+= 1
		cs = 136
		_goto_level = _again
		next
	end

     end
		end
	when 86 then
# line 359 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      p = p - 1;
      cs = ((in_set? ? 138 : 119));
      	begin
		stack[top] = cs
		top+= 1
		cs = 137
		_goto_level = _again
		next
	end

     end
		end
	when 22 then
# line 365 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:escape, :literal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 90 then
# line 282 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:escape, :octal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 96 then
# line 334 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:escape, :hex, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 93 then
# line 343 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit_meta_control_sequence(data, ts, te, :control)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 95 then
# line 348 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit_meta_control_sequence(data, ts, te, :meta_sequence)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 88 then
# line 365 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:escape, :literal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 21 then
# line 365 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      emit(:escape, :literal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 89 then
# line 1 "NONE"
		begin
	case act
	when 16 then
	begin begin p = ((te))-1; end

      text = copy(data, ts-1, te)
      emit(:backref, :number, text)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

    end
	when 17 then
	begin begin p = ((te))-1; end

      emit(:escape, :octal, copy(data, ts-1, te))
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

    end
end 
			end
	when 31 then
# line 375 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te-1)
      emit(:conditional, :condition, text)
      emit(:conditional, :condition_close, ')')
     end
		end
	when 98 then
# line 381 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      p = p - 1;
      	begin
		stack[top] = cs
		top+= 1
		cs = 119
		_goto_level = _again
		next
	end

     end
		end
	when 99 then
# line 381 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      p = p - 1;
      	begin
		stack[top] = cs
		top+= 1
		cs = 119
		_goto_level = _again
		next
	end

     end
		end
	when 30 then
# line 381 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      p = p - 1;
      	begin
		stack[top] = cs
		top+= 1
		cs = 119
		_goto_level = _again
		next
	end

     end
		end
	when 37 then
# line 394 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:meta, :dot, copy(data, ts, te))
     end
		end
	when 42 then
# line 398 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      if conditional_stack.last == group_depth
        emit(:conditional, :separator, copy(data, ts, te))
      else
        emit(:meta, :alternation, copy(data, ts, te))
      end
     end
		end
	when 41 then
# line 408 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:anchor, :bol, copy(data, ts, te))
     end
		end
	when 34 then
# line 412 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:anchor, :eol, copy(data, ts, te))
     end
		end
	when 62 then
# line 416 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:keep, :mark, copy(data, ts, te))
     end
		end
	when 61 then
# line 420 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '\\A'; emit(:anchor, :bos,                text)
      when '\\z'; emit(:anchor, :eos,                text)
      when '\\Z'; emit(:anchor, :eos_ob_eol,         text)
      when '\\b'; emit(:anchor, :word_boundary,      text)
      when '\\B'; emit(:anchor, :nonword_boundary,   text)
      when '\\G'; emit(:anchor, :match_start,        text)
      end
     end
		end
	when 40 then
# line 431 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      append_literal(data, ts, te)
     end
		end
	when 51 then
# line 446 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te)

      conditional_stack << group_depth

      emit(:conditional, :open, text[0..-2])
      emit(:conditional, :condition_open, '(')
      	begin
		stack[top] = cs
		top+= 1
		cs = 160
		_goto_level = _again
		next
	end

     end
		end
	when 52 then
# line 477 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te)
      if text[2..-1] =~ /([^\-mixdau:]|^$)|-.*([dau])/
        raise InvalidGroupOption.new($1 || "-#{$2}", text)
      end
      emit_options(text)
     end
		end
	when 50 then
# line 491 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '(?=';  emit(:assertion, :lookahead,    text)
      when '(?!';  emit(:assertion, :nlookahead,   text)
      when '(?<='; emit(:assertion, :lookbehind,   text)
      when '(?<!'; emit(:assertion, :nlookbehind,  text)
      end
     end
		end
	when 6 then
# line 508 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '(?:';  emit(:group, :passive,      text)
      when '(?>';  emit(:group, :atomic,       text)
      when '(?~';  emit(:group, :absence,      text)

      when /^\(\?(?:<>|'')/
        validation_error(:group, 'named group', 'name is empty')

      when /^\(\?<[^>]+>/
        emit(:group, :named_ab,  text)

      when /^\(\?'[^']+'/
        emit(:group, :named_sq,  text)

      end
     end
		end
	when 10 then
# line 549 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when /^\\k(<>|'')/
        validation_error(:backref, 'backreference', 'ref ID is empty')
      when /^\\k(.)[^\p{digit}\-][^+\-]*\D$/
        emit(:backref, $1 == '<' ? :name_ref_ab : :name_ref_sq, text)
      when /^\\k(.)\d+\D$/
        emit(:backref, $1 == '<' ? :number_ref_ab : :number_ref_sq, text)
      when /^\\k(.)-\d+\D$/
        emit(:backref, $1 == '<' ? :number_rel_ref_ab : :number_rel_ref_sq, text)
      when /^\\k(.)[^\p{digit}\-].*[+\-]\d+\D$/
        emit(:backref, $1 == '<' ? :name_recursion_ref_ab : :name_recursion_ref_sq, text)
      when /^\\k(.)-?\d+[+\-]\d+\D$/
        emit(:backref, $1 == '<' ? :number_recursion_ref_ab : :number_recursion_ref_sq, text)
      end
     end
		end
	when 9 then
# line 568 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when /^\\g(<>|'')/
        validation_error(:backref, 'subexpression call', 'ref ID is empty')
      when /^\\g(.)[^\p{digit}+\->][^+\-]*/
        emit(:backref, $1 == '<' ? :name_call_ab : :name_call_sq, text)
      when /^\\g(.)\d+\D$/
        emit(:backref, $1 == '<' ? :number_call_ab : :number_call_sq, text)
      when /^\\g(.)[+-]\d+/
        emit(:backref, $1 == '<' ? :number_rel_call_ab : :number_rel_call_sq, text)
      end
     end
		end
	when 59 then
# line 584 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '?' ;  emit(:quantifier, :zero_or_one,            text)
      when '??';  emit(:quantifier, :zero_or_one_reluctant,  text)
      when '?+';  emit(:quantifier, :zero_or_one_possessive, text)
      end
     end
		end
	when 55 then
# line 592 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '*' ;  emit(:quantifier, :zero_or_more,            text)
      when '*?';  emit(:quantifier, :zero_or_more_reluctant,  text)
      when '*+';  emit(:quantifier, :zero_or_more_possessive, text)
      end
     end
		end
	when 57 then
# line 600 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      case text = copy(data, ts, te)
      when '+' ;  emit(:quantifier, :one_or_more,            text)
      when '+?';  emit(:quantifier, :one_or_more_reluctant,  text)
      when '++';  emit(:quantifier, :one_or_more_possessive, text)
      end
     end
		end
	when 65 then
# line 608 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:quantifier, :interval, copy(data, ts, te))
     end
		end
	when 46 then
# line 623 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      if free_spacing
        emit(:free_space, :comment, copy(data, ts, te))
      else
        # consume only the pound sign (#) and backtrack to do regular scanning
        append_literal(data, ts, ts + 1)
         begin p = (( ts + 1))-1; end

      end
     end
		end
	when 49 then
# line 477 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      text = copy(data, ts, te)
      if text[2..-1] =~ /([^\-mixdau:]|^$)|-.*([dau])/
        raise InvalidGroupOption.new($1 || "-#{$2}", text)
      end
      emit_options(text)
     end
		end
	when 53 then
# line 491 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      case text = copy(data, ts, te)
      when '(?=';  emit(:assertion, :lookahead,    text)
      when '(?!';  emit(:assertion, :nlookahead,   text)
      when '(?<='; emit(:assertion, :lookbehind,   text)
      when '(?<!'; emit(:assertion, :nlookbehind,  text)
      end
     end
		end
	when 47 then
# line 526 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      text = copy(data, ts, te)
      emit(:group, :capture, text)
     end
		end
	when 58 then
# line 584 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      case text = copy(data, ts, te)
      when '?' ;  emit(:quantifier, :zero_or_one,            text)
      when '??';  emit(:quantifier, :zero_or_one_reluctant,  text)
      when '?+';  emit(:quantifier, :zero_or_one_possessive, text)
      end
     end
		end
	when 54 then
# line 592 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      case text = copy(data, ts, te)
      when '*' ;  emit(:quantifier, :zero_or_more,            text)
      when '*?';  emit(:quantifier, :zero_or_more_reluctant,  text)
      when '*+';  emit(:quantifier, :zero_or_more_possessive, text)
      end
     end
		end
	when 56 then
# line 600 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      case text = copy(data, ts, te)
      when '+' ;  emit(:quantifier, :one_or_more,            text)
      when '+?';  emit(:quantifier, :one_or_more_reluctant,  text)
      when '++';  emit(:quantifier, :one_or_more_possessive, text)
      end
     end
		end
	when 64 then
# line 608 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:quantifier, :interval, copy(data, ts, te))
     end
		end
	when 63 then
# line 613 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      append_literal(data, ts, te)
     end
		end
	when 60 then
# line 619 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      	begin
		stack[top] = cs
		top+= 1
		cs = 146
		_goto_level = _again
		next
	end

     end
		end
	when 45 then
# line 623 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      if free_spacing
        emit(:free_space, :comment, copy(data, ts, te))
      else
        # consume only the pound sign (#) and backtrack to do regular scanning
        append_literal(data, ts, ts + 1)
         begin p = (( ts + 1))-1; end

      end
     end
		end
	when 44 then
# line 633 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      if free_spacing
        emit(:free_space, :whitespace, copy(data, ts, te))
      else
        append_literal(data, ts, te)
      end
     end
		end
	when 43 then
# line 644 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      append_literal(data, ts, te)
     end
		end
	when 3 then
# line 477 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      text = copy(data, ts, te)
      if text[2..-1] =~ /([^\-mixdau:]|^$)|-.*([dau])/
        raise InvalidGroupOption.new($1 || "-#{$2}", text)
      end
      emit_options(text)
     end
		end
	when 11 then
# line 613 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      append_literal(data, ts, te)
     end
		end
	when 8 then
# line 619 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      	begin
		stack[top] = cs
		top+= 1
		cs = 146
		_goto_level = _again
		next
	end

     end
		end
	when 1 then
# line 1 "NONE"
		begin
	case act
	when 0 then
	begin	begin
		cs = 0
		_goto_level = _again
		next
	end
end
	when 40 then
	begin begin p = ((te))-1; end

      text = copy(data, ts, te)
      if text[2..-1] =~ /([^\-mixdau:]|^$)|-.*([dau])/
        raise InvalidGroupOption.new($1 || "-#{$2}", text)
      end
      emit_options(text)
    end
	when 41 then
	begin begin p = ((te))-1; end

      case text = copy(data, ts, te)
      when '(?=';  emit(:assertion, :lookahead,    text)
      when '(?!';  emit(:assertion, :nlookahead,   text)
      when '(?<='; emit(:assertion, :lookbehind,   text)
      when '(?<!'; emit(:assertion, :nlookbehind,  text)
      end
    end
	when 55 then
	begin begin p = ((te))-1; end

      append_literal(data, ts, te)
    end
end 
			end
	when 77 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 221 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit(:set, :open, copy(data, ts, te))
      	begin
		stack[top] = cs
		top+= 1
		cs = 138
		_goto_level = _again
		next
	end

     end
		end
	when 18 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 221 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      emit(:set, :open, copy(data, ts, te))
      	begin
		stack[top] = cs
		top+= 1
		cs = 138
		_goto_level = _again
		next
	end

     end
		end
	when 92 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 343 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit_meta_control_sequence(data, ts, te, :control)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 94 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 348 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p
p = p - 1; begin 
      emit_meta_control_sequence(data, ts, te, :meta_sequence)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 25 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 343 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      emit_meta_control_sequence(data, ts, te, :control)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 27 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 348 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 begin p = ((te))-1; end
 begin 
      emit_meta_control_sequence(data, ts, te, :meta_sequence)
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 29 then
# line 153 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    validation_error(:sequence, 'sequence', text)
  		end
# line 339 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

     end
		end
	when 5 then
# line 160 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth - 1 		end
# line 462 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:group, :comment, copy(data, ts, te))
     end
		end
	when 36 then
# line 160 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth - 1 		end
# line 531 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      if conditional_stack.last == group_depth + 1
        conditional_stack.pop
        emit(:conditional, :close, copy(data, ts, te))
      else
        if spacing_stack.length > 1 &&
           spacing_stack.last[:depth] == group_depth + 1
          spacing_stack.pop
          self.free_spacing = spacing_stack.last[:free_spacing]
        end

        emit(:group, :close, copy(data, ts, te))
      end
     end
		end
	when 38 then
# line 161 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.set_depth   = set_depth   + 1 		end
# line 437 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:set, :open, copy(data, ts, te))
      	begin
		stack[top] = cs
		top+= 1
		cs = 138
		_goto_level = _again
		next
	end

     end
		end
	when 71 then
# line 162 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.set_depth   = set_depth   - 1 		end
# line 168 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      emit(:set, :close, copy(data, ts, te))
      if in_set?
        	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

      else
        	begin
		cs = 119
		_goto_level = _again
		next
	end

      end
     end
		end
	when 76 then
# line 162 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.set_depth   = set_depth   - 1 		end
# line 177 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin  # special case, emits two tokens
      emit(:literal, :literal, copy(data, ts, te-1))
      emit(:set, :close, copy(data, ts+1, te))
      if in_set?
        	begin
		top -= 1
		cs = stack[top]
		_goto_level = _again
		next
	end

      else
        	begin
		cs = 119
		_goto_level = _again
		next
	end

      end
     end
		end
	when 20 then
# line 162 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.set_depth   = set_depth   - 1 		end
# line 226 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
te = p+1
 begin 
      text = copy(data, ts, te)

      type = :posixclass
      class_name = text[2..-3]
      if class_name[0].chr == '^'
        class_name = class_name[1..-1]
        type = :nonposixclass
      end

      emit(type, class_name.to_sym, text)
     end
		end
	when 69 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 161 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.set_depth   = set_depth   + 1 		end
	when 83 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 276 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
act = 16;		end
	when 82 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 282 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
act = 17;		end
	when 7 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 491 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
act = 41;		end
	when 2 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 644 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
act = 55;		end
	when 48 then
# line 1 "NONE"
		begin
te = p+1
		end
# line 160 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth - 1 		end
# line 159 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
 self.group_depth = group_depth + 1 		end
# line 477 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin
act = 40;		end
# line 2522 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
	end
	end
	end
	if _goto_level <= _again
	case _re_scanner_to_state_actions[cs] 
	when 66 then
# line 1 "NONE"
		begin
ts = nil;		end
	when 32 then
# line 1 "NONE"
		begin
ts = nil;		end
# line 1 "NONE"
		begin
act = 0
		end
# line 2540 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
	end

	if cs == 0
		_goto_level = _out
		next
	end
	p += 1
	if p != pe
		_goto_level = _resume
		next
	end
	end
	if _goto_level <= _test_eof
	if p == eof
	if _re_scanner_eof_trans[cs] > 0
		_trans = _re_scanner_eof_trans[cs] - 1;
		_goto_level = _eof_trans
		next;
	end
	  case _re_scanner_eof_actions[cs]
	when 12 then
# line 8 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/property.rl"
		begin

    raise PrematureEndError.new('unicode property')
  		end
	when 23 then
# line 147 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"
		begin

    text = copy(data, ts ? ts-1 : 0, -1)
    raise PrematureEndError.new( text )
  		end
# line 2574 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner.rb"
	  end
	end

	end
	if _goto_level <= _out
		break
	end
end
	end

# line 741 "/Users/<USER>/code/regexp_parser/lib/regexp_parser/scanner/scanner.rl"

    # to avoid "warning: assigned but unused variable - testEof"
    testEof = testEof

    if cs == re_scanner_error
      text = copy(data, ts ? ts-1 : 0, -1)
      raise ScannerError.new("Scan error at '#{text}'")
    end

    raise PrematureEndError.new("(missing group closing paranthesis) "+
          "[#{group_depth}]") if in_group?
    raise PrematureEndError.new("(missing set closing bracket) "+
          "[#{set_depth}]") if in_set?

    # when the entire expression is a literal run
    emit_literal if literal

    tokens
  end

  # lazy-load property maps when first needed
  require 'yaml'

  def self.short_prop_map
    @short_prop_map ||= YAML.load_file("#{__dir__}/scanner/properties/short.yml")
  end

  def self.long_prop_map
    @long_prop_map ||= YAML.load_file("#{__dir__}/scanner/properties/long.yml")
  end

  # Emits an array with the details of the scanned pattern
  def emit(type, token, text)
    #puts "EMIT: type: #{type}, token: #{token}, text: #{text}, ts: #{ts}, te: #{te}"

    emit_literal if literal

    # Ragel runs with byte-based indices (ts, te). These are of little value to
    # end-users, so we keep track of char-based indices and emit those instead.
    ts_char_pos = char_pos
    te_char_pos = char_pos + text.length

    if block
      block.call type, token, text, ts_char_pos, te_char_pos
    end

    tokens << [type, token, text, ts_char_pos, te_char_pos]

    self.char_pos = te_char_pos
  end

  private

  attr_accessor :tokens, :literal, :block, :free_spacing, :spacing_stack,
                :group_depth, :set_depth, :conditional_stack, :char_pos

  def free_spacing?(input_object, options)
    if options && !input_object.is_a?(String)
      raise ArgumentError, 'options cannot be supplied unless scanning a String'
    end

    options = input_object.options if input_object.is_a?(::Regexp)

    return false unless options

    options & Regexp::EXTENDED != 0
  end

  def in_group?
    group_depth > 0
  end

  def in_set?
    set_depth > 0
  end

  # Copy from ts to te from data as text
  def copy(data, ts, te)
    data[ts...te].pack('c*').force_encoding('utf-8')
  end

  # Appends one or more characters to the literal buffer, to be emitted later
  # by a call to emit_literal.
  def append_literal(data, ts, te)
    self.literal = literal || []
    literal << copy(data, ts, te)
  end

  # Emits the literal run collected by calls to the append_literal method.
  def emit_literal
    text = literal.join
    self.literal = nil
    emit(:literal, :literal, text)
  end

  def emit_options(text)
    token = nil

    # Ruby allows things like '(?-xxxx)' or '(?xx-xx--xx-:abc)'.
    text =~ /\(\?([mixdau]*)(-(?:[mix]*))*(:)?/
    positive, negative, group_local = $1, $2, $3

    if positive.include?('x')
      self.free_spacing = true
    end

    # If the x appears in both, treat it like ruby does, the second cancels
    # the first.
    if negative && negative.include?('x')
      self.free_spacing = false
    end

    if group_local
      spacing_stack << {:free_spacing => free_spacing, :depth => group_depth}
      token = :options
    else
      # switch for parent group level
      spacing_stack.last[:free_spacing] = free_spacing
      token = :options_switch
    end

    emit(:group, token, text)
  end

  def emit_meta_control_sequence(data, ts, te, token)
    if data.last < 0x00 || data.last > 0x7F
      validation_error(:sequence, 'escape', token.to_s)
    end
    emit(:escape, token, copy(data, ts-1, te))
  end

  # Centralizes and unifies the handling of validation related
  # errors.
  def validation_error(type, what, reason)
    case type
    when :group
      error = InvalidGroupError.new(what, reason)
    when :backref
      error = InvalidBackrefError.new(what, reason)
    when :sequence
      error = InvalidSequenceError.new(what, reason)
    end

    raise error # unless @@config.validation_ignore
  end
end # module Regexp::Scanner

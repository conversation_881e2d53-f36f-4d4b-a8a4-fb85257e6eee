#
# THIS FILE IS AUTO-GENERATED BY `rake props:update`, DO NOT EDIT
#
---
adlam: adlam
age=1.1: age=1.1
age=10.0: age=10.0
age=11.0: age=11.0
age=12.0: age=12.0
age=12.1: age=12.1
age=2.0: age=2.0
age=2.1: age=2.1
age=3.0: age=3.0
age=3.1: age=3.1
age=3.2: age=3.2
age=4.0: age=4.0
age=4.1: age=4.1
age=5.0: age=5.0
age=5.1: age=5.1
age=5.2: age=5.2
age=6.0: age=6.0
age=6.1: age=6.1
age=6.2: age=6.2
age=6.3: age=6.3
age=7.0: age=7.0
age=8.0: age=8.0
age=9.0: age=9.0
ahom: ahom
alnum: alnum
alpha: alpha
alphabetic: alphabetic
anatolianhieroglyphs: anatolian_hieroglyphs
any: any
arabic: arabic
armenian: armenian
ascii: ascii
asciihexdigit: ascii_hex_digit
assigned: assigned
avestan: avestan
balinese: balinese
bamum: bamum
bassavah: bassa_vah
batak: batak
bengali: bengali
bhaiksuki: bhaiksuki
bidicontrol: bidi_control
blank: blank
bopomofo: bopomofo
brahmi: brahmi
braille: braille
buginese: buginese
buhid: buhid
canadianaboriginal: canadian_aboriginal
carian: carian
cased: cased
casedletter: cased_letter
caseignorable: case_ignorable
caucasianalbanian: caucasian_albanian
chakma: chakma
cham: cham
changeswhencasefolded: changes_when_casefolded
changeswhencasemapped: changes_when_casemapped
changeswhenlowercased: changes_when_lowercased
changeswhentitlecased: changes_when_titlecased
changeswhenuppercased: changes_when_uppercased
cherokee: cherokee
closepunctuation: close_punctuation
cntrl: cntrl
common: common
connectorpunctuation: connector_punctuation
control: control
coptic: coptic
cuneiform: cuneiform
currencysymbol: currency_symbol
cypriot: cypriot
cyrillic: cyrillic
dash: dash
dashpunctuation: dash_punctuation
decimalnumber: decimal_number
defaultignorablecodepoint: default_ignorable_code_point
deprecated: deprecated
deseret: deseret
devanagari: devanagari
diacritic: diacritic
digit: digit
dogra: dogra
duployan: duployan
egyptianhieroglyphs: egyptian_hieroglyphs
elbasan: elbasan
elymaic: elymaic
emoji: emoji
emojicomponent: emoji_component
emojimodifier: emoji_modifier
emojimodifierbase: emoji_modifier_base
emojipresentation: emoji_presentation
enclosingmark: enclosing_mark
ethiopic: ethiopic
extender: extender
finalpunctuation: final_punctuation
format: format
georgian: georgian
glagolitic: glagolitic
gothic: gothic
grantha: grantha
graph: graph
graphemebase: grapheme_base
graphemeextend: grapheme_extend
graphemelink: grapheme_link
greek: greek
gujarati: gujarati
gunjalagondi: gunjala_gondi
gurmukhi: gurmukhi
han: han
hangul: hangul
hanifirohingya: hanifi_rohingya
hanunoo: hanunoo
hatran: hatran
hebrew: hebrew
hexdigit: hex_digit
hiragana: hiragana
hyphen: hyphen
idcontinue: id_continue
ideographic: ideographic
idsbinaryoperator: ids_binary_operator
idstart: id_start
idstrinaryoperator: ids_trinary_operator
imperialaramaic: imperial_aramaic
inadlam: in_adlam
inaegeannumbers: in_aegean_numbers
inahom: in_ahom
inalchemicalsymbols: in_alchemical_symbols
inalphabeticpresentationforms: in_alphabetic_presentation_forms
inanatolianhieroglyphs: in_anatolian_hieroglyphs
inancientgreekmusicalnotation: in_ancient_greek_musical_notation
inancientgreeknumbers: in_ancient_greek_numbers
inancientsymbols: in_ancient_symbols
inarabic: in_arabic
inarabicextendeda: in_arabic_extended_a
inarabicmathematicalalphabeticsymbols: in_arabic_mathematical_alphabetic_symbols
inarabicpresentationformsa: in_arabic_presentation_forms_a
inarabicpresentationformsb: in_arabic_presentation_forms_b
inarabicsupplement: in_arabic_supplement
inarmenian: in_armenian
inarrows: in_arrows
inavestan: in_avestan
inbalinese: in_balinese
inbamum: in_bamum
inbamumsupplement: in_bamum_supplement
inbasiclatin: in_basic_latin
inbassavah: in_bassa_vah
inbatak: in_batak
inbengali: in_bengali
inbhaiksuki: in_bhaiksuki
inblockelements: in_block_elements
inbopomofo: in_bopomofo
inbopomofoextended: in_bopomofo_extended
inboxdrawing: in_box_drawing
inbrahmi: in_brahmi
inbraillepatterns: in_braille_patterns
inbuginese: in_buginese
inbuhid: in_buhid
inbyzantinemusicalsymbols: in_byzantine_musical_symbols
incarian: in_carian
incaucasianalbanian: in_caucasian_albanian
inchakma: in_chakma
incham: in_cham
incherokee: in_cherokee
incherokeesupplement: in_cherokee_supplement
inchesssymbols: in_chess_symbols
incjkcompatibility: in_cjk_compatibility
incjkcompatibilityforms: in_cjk_compatibility_forms
incjkcompatibilityideographs: in_cjk_compatibility_ideographs
incjkcompatibilityideographssupplement: in_cjk_compatibility_ideographs_supplement
incjkradicalssupplement: in_cjk_radicals_supplement
incjkstrokes: in_cjk_strokes
incjksymbolsandpunctuation: in_cjk_symbols_and_punctuation
incjkunifiedideographs: in_cjk_unified_ideographs
incjkunifiedideographsextensiona: in_cjk_unified_ideographs_extension_a
incjkunifiedideographsextensionb: in_cjk_unified_ideographs_extension_b
incjkunifiedideographsextensionc: in_cjk_unified_ideographs_extension_c
incjkunifiedideographsextensiond: in_cjk_unified_ideographs_extension_d
incjkunifiedideographsextensione: in_cjk_unified_ideographs_extension_e
incjkunifiedideographsextensionf: in_cjk_unified_ideographs_extension_f
incombiningdiacriticalmarks: in_combining_diacritical_marks
incombiningdiacriticalmarksextended: in_combining_diacritical_marks_extended
incombiningdiacriticalmarksforsymbols: in_combining_diacritical_marks_for_symbols
incombiningdiacriticalmarkssupplement: in_combining_diacritical_marks_supplement
incombininghalfmarks: in_combining_half_marks
incommonindicnumberforms: in_common_indic_number_forms
incontrolpictures: in_control_pictures
incoptic: in_coptic
incopticepactnumbers: in_coptic_epact_numbers
incountingrodnumerals: in_counting_rod_numerals
incuneiform: in_cuneiform
incuneiformnumbersandpunctuation: in_cuneiform_numbers_and_punctuation
incurrencysymbols: in_currency_symbols
incypriotsyllabary: in_cypriot_syllabary
incyrillic: in_cyrillic
incyrillicextendeda: in_cyrillic_extended_a
incyrillicextendedb: in_cyrillic_extended_b
incyrillicextendedc: in_cyrillic_extended_c
incyrillicsupplement: in_cyrillic_supplement
indeseret: in_deseret
indevanagari: in_devanagari
indevanagariextended: in_devanagari_extended
indingbats: in_dingbats
indogra: in_dogra
indominotiles: in_domino_tiles
induployan: in_duployan
inearlydynasticcuneiform: in_early_dynastic_cuneiform
inegyptianhieroglyphformatcontrols: in_egyptian_hieroglyph_format_controls
inegyptianhieroglyphs: in_egyptian_hieroglyphs
inelbasan: in_elbasan
inelymaic: in_elymaic
inemoticons: in_emoticons
inenclosedalphanumerics: in_enclosed_alphanumerics
inenclosedalphanumericsupplement: in_enclosed_alphanumeric_supplement
inenclosedcjklettersandmonths: in_enclosed_cjk_letters_and_months
inenclosedideographicsupplement: in_enclosed_ideographic_supplement
inethiopic: in_ethiopic
inethiopicextended: in_ethiopic_extended
inethiopicextendeda: in_ethiopic_extended_a
inethiopicsupplement: in_ethiopic_supplement
ingeneralpunctuation: in_general_punctuation
ingeometricshapes: in_geometric_shapes
ingeometricshapesextended: in_geometric_shapes_extended
ingeorgian: in_georgian
ingeorgianextended: in_georgian_extended
ingeorgiansupplement: in_georgian_supplement
inglagolitic: in_glagolitic
inglagoliticsupplement: in_glagolitic_supplement
ingothic: in_gothic
ingrantha: in_grantha
ingreekandcoptic: in_greek_and_coptic
ingreekextended: in_greek_extended
ingujarati: in_gujarati
ingunjalagondi: in_gunjala_gondi
ingurmukhi: in_gurmukhi
inhalfwidthandfullwidthforms: in_halfwidth_and_fullwidth_forms
inhangulcompatibilityjamo: in_hangul_compatibility_jamo
inhanguljamo: in_hangul_jamo
inhanguljamoextendeda: in_hangul_jamo_extended_a
inhanguljamoextendedb: in_hangul_jamo_extended_b
inhangulsyllables: in_hangul_syllables
inhanifirohingya: in_hanifi_rohingya
inhanunoo: in_hanunoo
inhatran: in_hatran
inhebrew: in_hebrew
inherited: inherited
inhighprivateusesurrogates: in_high_private_use_surrogates
inhighsurrogates: in_high_surrogates
inhiragana: in_hiragana
inideographicdescriptioncharacters: in_ideographic_description_characters
inideographicsymbolsandpunctuation: in_ideographic_symbols_and_punctuation
inimperialaramaic: in_imperial_aramaic
inindicsiyaqnumbers: in_indic_siyaq_numbers
ininscriptionalpahlavi: in_inscriptional_pahlavi
ininscriptionalparthian: in_inscriptional_parthian
inipaextensions: in_ipa_extensions
initialpunctuation: initial_punctuation
injavanese: in_javanese
inkaithi: in_kaithi
inkanaextendeda: in_kana_extended_a
inkanasupplement: in_kana_supplement
inkanbun: in_kanbun
inkangxiradicals: in_kangxi_radicals
inkannada: in_kannada
inkatakana: in_katakana
inkatakanaphoneticextensions: in_katakana_phonetic_extensions
inkayahli: in_kayah_li
inkharoshthi: in_kharoshthi
inkhmer: in_khmer
inkhmersymbols: in_khmer_symbols
inkhojki: in_khojki
inkhudawadi: in_khudawadi
inlao: in_lao
inlatin1supplement: in_latin_1_supplement
inlatinextendeda: in_latin_extended_a
inlatinextendedadditional: in_latin_extended_additional
inlatinextendedb: in_latin_extended_b
inlatinextendedc: in_latin_extended_c
inlatinextendedd: in_latin_extended_d
inlatinextendede: in_latin_extended_e
inlepcha: in_lepcha
inletterlikesymbols: in_letterlike_symbols
inlimbu: in_limbu
inlineara: in_linear_a
inlinearbideograms: in_linear_b_ideograms
inlinearbsyllabary: in_linear_b_syllabary
inlisu: in_lisu
inlowsurrogates: in_low_surrogates
inlycian: in_lycian
inlydian: in_lydian
inmahajani: in_mahajani
inmahjongtiles: in_mahjong_tiles
inmakasar: in_makasar
inmalayalam: in_malayalam
inmandaic: in_mandaic
inmanichaean: in_manichaean
inmarchen: in_marchen
inmasaramgondi: in_masaram_gondi
inmathematicalalphanumericsymbols: in_mathematical_alphanumeric_symbols
inmathematicaloperators: in_mathematical_operators
inmayannumerals: in_mayan_numerals
inmedefaidrin: in_medefaidrin
inmeeteimayek: in_meetei_mayek
inmeeteimayekextensions: in_meetei_mayek_extensions
inmendekikakui: in_mende_kikakui
inmeroiticcursive: in_meroitic_cursive
inmeroitichieroglyphs: in_meroitic_hieroglyphs
inmiao: in_miao
inmiscellaneousmathematicalsymbolsa: in_miscellaneous_mathematical_symbols_a
inmiscellaneousmathematicalsymbolsb: in_miscellaneous_mathematical_symbols_b
inmiscellaneoussymbols: in_miscellaneous_symbols
inmiscellaneoussymbolsandarrows: in_miscellaneous_symbols_and_arrows
inmiscellaneoussymbolsandpictographs: in_miscellaneous_symbols_and_pictographs
inmiscellaneoustechnical: in_miscellaneous_technical
inmodi: in_modi
inmodifiertoneletters: in_modifier_tone_letters
inmongolian: in_mongolian
inmongoliansupplement: in_mongolian_supplement
inmro: in_mro
inmultani: in_multani
inmusicalsymbols: in_musical_symbols
inmyanmar: in_myanmar
inmyanmarextendeda: in_myanmar_extended_a
inmyanmarextendedb: in_myanmar_extended_b
innabataean: in_nabataean
innandinagari: in_nandinagari
innewa: in_newa
innewtailue: in_new_tai_lue
innko: in_nko
innoblock: in_no_block
innumberforms: in_number_forms
innushu: in_nushu
innyiakengpuachuehmong: in_nyiakeng_puachue_hmong
inogham: in_ogham
inolchiki: in_ol_chiki
inoldhungarian: in_old_hungarian
inolditalic: in_old_italic
inoldnortharabian: in_old_north_arabian
inoldpermic: in_old_permic
inoldpersian: in_old_persian
inoldsogdian: in_old_sogdian
inoldsoutharabian: in_old_south_arabian
inoldturkic: in_old_turkic
inopticalcharacterrecognition: in_optical_character_recognition
inoriya: in_oriya
inornamentaldingbats: in_ornamental_dingbats
inosage: in_osage
inosmanya: in_osmanya
inottomansiyaqnumbers: in_ottoman_siyaq_numbers
inpahawhhmong: in_pahawh_hmong
inpalmyrene: in_palmyrene
inpaucinhau: in_pau_cin_hau
inphagspa: in_phags_pa
inphaistosdisc: in_phaistos_disc
inphoenician: in_phoenician
inphoneticextensions: in_phonetic_extensions
inphoneticextensionssupplement: in_phonetic_extensions_supplement
inplayingcards: in_playing_cards
inprivateusearea: in_private_use_area
inpsalterpahlavi: in_psalter_pahlavi
inrejang: in_rejang
inruminumeralsymbols: in_rumi_numeral_symbols
inrunic: in_runic
insamaritan: in_samaritan
insaurashtra: in_saurashtra
inscriptionalpahlavi: inscriptional_pahlavi
inscriptionalparthian: inscriptional_parthian
insharada: in_sharada
inshavian: in_shavian
inshorthandformatcontrols: in_shorthand_format_controls
insiddham: in_siddham
insinhala: in_sinhala
insinhalaarchaicnumbers: in_sinhala_archaic_numbers
insmallformvariants: in_small_form_variants
insmallkanaextension: in_small_kana_extension
insogdian: in_sogdian
insorasompeng: in_sora_sompeng
insoyombo: in_soyombo
inspacingmodifierletters: in_spacing_modifier_letters
inspecials: in_specials
insundanese: in_sundanese
insundanesesupplement: in_sundanese_supplement
insuperscriptsandsubscripts: in_superscripts_and_subscripts
insupplementalarrowsa: in_supplemental_arrows_a
insupplementalarrowsb: in_supplemental_arrows_b
insupplementalarrowsc: in_supplemental_arrows_c
insupplementalmathematicaloperators: in_supplemental_mathematical_operators
insupplementalpunctuation: in_supplemental_punctuation
insupplementalsymbolsandpictographs: in_supplemental_symbols_and_pictographs
insupplementaryprivateuseareaa: in_supplementary_private_use_area_a
insupplementaryprivateuseareab: in_supplementary_private_use_area_b
insuttonsignwriting: in_sutton_signwriting
insylotinagri: in_syloti_nagri
insymbolsandpictographsextendeda: in_symbols_and_pictographs_extended_a
insyriac: in_syriac
insyriacsupplement: in_syriac_supplement
intagalog: in_tagalog
intagbanwa: in_tagbanwa
intags: in_tags
intaile: in_tai_le
intaitham: in_tai_tham
intaiviet: in_tai_viet
intaixuanjingsymbols: in_tai_xuan_jing_symbols
intakri: in_takri
intamil: in_tamil
intamilsupplement: in_tamil_supplement
intangut: in_tangut
intangutcomponents: in_tangut_components
intelugu: in_telugu
inthaana: in_thaana
inthai: in_thai
intibetan: in_tibetan
intifinagh: in_tifinagh
intirhuta: in_tirhuta
intransportandmapsymbols: in_transport_and_map_symbols
inugaritic: in_ugaritic
inunifiedcanadianaboriginalsyllabics: in_unified_canadian_aboriginal_syllabics
inunifiedcanadianaboriginalsyllabicsextended: in_unified_canadian_aboriginal_syllabics_extended
invai: in_vai
invariationselectors: in_variation_selectors
invariationselectorssupplement: in_variation_selectors_supplement
invedicextensions: in_vedic_extensions
inverticalforms: in_vertical_forms
inwancho: in_wancho
inwarangciti: in_warang_citi
inyijinghexagramsymbols: in_yijing_hexagram_symbols
inyiradicals: in_yi_radicals
inyisyllables: in_yi_syllables
inzanabazarsquare: in_zanabazar_square
javanese: javanese
joincontrol: join_control
kaithi: kaithi
kannada: kannada
katakana: katakana
kayahli: kayah_li
kharoshthi: kharoshthi
khmer: khmer
khojki: khojki
khudawadi: khudawadi
lao: lao
latin: latin
lepcha: lepcha
letter: letter
letternumber: letter_number
limbu: limbu
lineara: linear_a
linearb: linear_b
lineseparator: line_separator
lisu: lisu
logicalorderexception: logical_order_exception
lower: lower
lowercase: lowercase
lowercaseletter: lowercase_letter
lycian: lycian
lydian: lydian
mahajani: mahajani
makasar: makasar
malayalam: malayalam
mandaic: mandaic
manichaean: manichaean
marchen: marchen
mark: mark
masaramgondi: masaram_gondi
math: math
mathsymbol: math_symbol
medefaidrin: medefaidrin
meeteimayek: meetei_mayek
mendekikakui: mende_kikakui
meroiticcursive: meroitic_cursive
meroitichieroglyphs: meroitic_hieroglyphs
miao: miao
modi: modi
modifierletter: modifier_letter
modifiersymbol: modifier_symbol
mongolian: mongolian
mro: mro
multani: multani
myanmar: myanmar
nabataean: nabataean
nandinagari: nandinagari
newa: newa
newline: newline
newtailue: new_tai_lue
nko: nko
noncharactercodepoint: noncharacter_code_point
nonspacingmark: nonspacing_mark
number: number
nushu: nushu
nyiakengpuachuehmong: nyiakeng_puachue_hmong
ogham: ogham
olchiki: ol_chiki
oldhungarian: old_hungarian
olditalic: old_italic
oldnortharabian: old_north_arabian
oldpermic: old_permic
oldpersian: old_persian
oldsogdian: old_sogdian
oldsoutharabian: old_south_arabian
oldturkic: old_turkic
openpunctuation: open_punctuation
oriya: oriya
osage: osage
osmanya: osmanya
other: other
otheralphabetic: other_alphabetic
otherdefaultignorablecodepoint: other_default_ignorable_code_point
othergraphemeextend: other_grapheme_extend
otheridcontinue: other_id_continue
otheridstart: other_id_start
otherletter: other_letter
otherlowercase: other_lowercase
othermath: other_math
othernumber: other_number
otherpunctuation: other_punctuation
othersymbol: other_symbol
otheruppercase: other_uppercase
pahawhhmong: pahawh_hmong
palmyrene: palmyrene
paragraphseparator: paragraph_separator
patternsyntax: pattern_syntax
patternwhitespace: pattern_white_space
paucinhau: pau_cin_hau
phagspa: phags_pa
phoenician: phoenician
prependedconcatenationmark: prepended_concatenation_mark
print: print
privateuse: private_use
psalterpahlavi: psalter_pahlavi
punct: punct
punctuation: punctuation
quotationmark: quotation_mark
radical: radical
regionalindicator: regional_indicator
rejang: rejang
runic: runic
samaritan: samaritan
saurashtra: saurashtra
sentenceterminal: sentence_terminal
separator: separator
sharada: sharada
shavian: shavian
siddham: siddham
signwriting: signwriting
sinhala: sinhala
softdotted: soft_dotted
sogdian: sogdian
sorasompeng: sora_sompeng
soyombo: soyombo
space: space
spaceseparator: space_separator
spacingmark: spacing_mark
sundanese: sundanese
surrogate: surrogate
sylotinagri: syloti_nagri
symbol: symbol
syriac: syriac
tagalog: tagalog
tagbanwa: tagbanwa
taile: tai_le
taitham: tai_tham
taiviet: tai_viet
takri: takri
tamil: tamil
tangut: tangut
telugu: telugu
terminalpunctuation: terminal_punctuation
thaana: thaana
thai: thai
tibetan: tibetan
tifinagh: tifinagh
tirhuta: tirhuta
titlecaseletter: titlecase_letter
ugaritic: ugaritic
unassigned: unassigned
unifiedideograph: unified_ideograph
unknown: unknown
upper: upper
uppercase: uppercase
uppercaseletter: uppercase_letter
vai: vai
variationselector: variation_selector
wancho: wancho
warangciti: warang_citi
whitespace: white_space
word: word
xdigit: xdigit
xidcontinue: xid_continue
xidstart: xid_start
xposixpunct: xposixpunct
yi: yi
zanabazarsquare: zanabazar_square
